/**
 * Styles for Layer Manager Component
 */

.layer-manager {
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.layer-manager-header {
  display: flex;
  justify-content: between;
  align-items: center;
  padding: 1rem;
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
}

.layer-manager-header h5 {
  color: #495057;
  font-weight: 600;
}

.layer-actions {
  margin-left: auto;
}

.importing-indicator {
  padding: 0.75rem 1rem;
  background-color: #e3f2fd;
  border-bottom: 1px solid #bbdefb;
}

.layer-list {
  max-height: 500px;
  overflow-y: auto;
}

.no-layers {
  padding: 2rem 1rem;
  text-align: center;
  color: #6c757d;
}

.layer-item {
  padding: 1rem;
  border-bottom: 1px solid #f1f3f4;
  transition: background-color 0.2s ease;
}

.layer-item:hover {
  background-color: #f8f9fa;
}

.layer-item.active {
  background-color: #e8f5e8;
  border-left: 4px solid #28a745;
}

.layer-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 0.75rem;
}

.layer-info {
  flex: 1;
  min-width: 0;
}

.layer-name {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.25rem;
}

.layer-title {
  font-weight: 600;
  color: #495057;
  font-size: 0.95rem;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.active-badge {
  background-color: #28a745;
  color: white;
  font-size: 0.7rem;
  padding: 0.125rem 0.375rem;
  border-radius: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.layer-description {
  margin-bottom: 0.5rem;
}

.layer-description small {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 1.3;
}

.layer-controls {
  display: flex;
  gap: 0.25rem;
  flex-shrink: 0;
}

.layer-stats {
  display: flex;
  gap: 1rem;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
}

.stat-group {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.stat-label {
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: 500;
}

.stat-value {
  font-size: 0.8rem;
  color: #495057;
  font-weight: 600;
}

.layer-metadata {
  font-size: 0.7rem;
  color: #6c757d;
  line-height: 1.2;
}

/* Modal styles */
.modal-content {
  border-radius: 8px;
  border: none;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.modal-header {
  background-color: #f8f9fa;
  border-bottom: 1px solid #dee2e6;
  border-radius: 8px 8px 0 0;
}

.modal-title {
  font-weight: 600;
  color: #495057;
}

.modal-body .form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.modal-body .form-select,
.modal-body .form-control {
  border-radius: 6px;
  border: 1px solid #ced4da;
}

.modal-body .form-check-label {
  font-size: 0.9rem;
  color: #495057;
}

/* Responsive design */
@media (max-width: 768px) {
  .layer-manager-header {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .layer-actions {
    margin-left: 0;
  }
  
  .layer-header {
    flex-direction: column;
    gap: 0.75rem;
  }
  
  .layer-controls {
    align-self: flex-end;
  }
  
  .layer-stats {
    gap: 0.75rem;
  }
  
  .stat-group {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.125rem;
  }
}

@media (max-width: 576px) {
  .layer-item {
    padding: 0.75rem;
  }
  
  .layer-manager-header {
    padding: 0.75rem;
  }
  
  .layer-title {
    font-size: 0.9rem;
  }
  
  .layer-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .stat-group {
    flex-direction: row;
    align-items: center;
    gap: 0.25rem;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .layer-manager {
    background-color: #2d3748;
    border-color: #4a5568;
  }
  
  .layer-manager-header {
    background-color: #4a5568;
    border-bottom-color: #718096;
  }
  
  .layer-manager-header h5 {
    color: #e2e8f0;
  }
  
  .layer-item {
    border-bottom-color: #4a5568;
  }
  
  .layer-item:hover {
    background-color: #4a5568;
  }
  
  .layer-item.active {
    background-color: #2f855a;
    border-left-color: #48bb78;
  }
  
  .layer-title {
    color: #e2e8f0;
  }
  
  .stat-label {
    color: #a0aec0;
  }
  
  .stat-value {
    color: #e2e8f0;
  }
  
  .layer-metadata {
    color: #a0aec0;
  }
  
  .importing-indicator {
    background-color: #2a4365;
    border-bottom-color: #3182ce;
    color: #e2e8f0;
  }
  
  .no-layers {
    color: #a0aec0;
  }
  
  .modal-content {
    background-color: #2d3748;
    color: #e2e8f0;
  }
  
  .modal-header {
    background-color: #4a5568;
    border-bottom-color: #718096;
  }
  
  .modal-title {
    color: #e2e8f0;
  }
  
  .modal-body .form-label {
    color: #e2e8f0;
  }
  
  .modal-body .form-select,
  .modal-body .form-control {
    background-color: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
  }
  
  .modal-body .form-check-label {
    color: #e2e8f0;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .layer-manager,
  .layer-item,
  .modal-content {
    border-width: 2px;
  }
  
  .layer-item.active {
    border-left-width: 6px;
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .layer-item {
    transition: none;
  }
  
  .importing-indicator .spinner-border {
    animation: none;
  }
}

/* Custom scrollbar */
.layer-list::-webkit-scrollbar {
  width: 8px;
}

.layer-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.layer-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

.layer-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Focus styles for accessibility */
.layer-controls .btn:focus {
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.modal-body .form-select:focus,
.modal-body .form-control:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

/* Animation for active state */
.layer-item.active {
  animation: slideInLeft 0.3s ease-out;
}

@keyframes slideInLeft {
  from {
    border-left-width: 0;
    background-color: #f8f9fa;
  }
  to {
    border-left-width: 4px;
    background-color: #e8f5e8;
  }
}

/* Dropdown menu styling */
.layer-controls .dropdown-menu {
  border-radius: 6px;
  border: 1px solid #dee2e6;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  min-width: 150px;
}

.layer-controls .dropdown-item {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  transition: background-color 0.15s ease;
}

.layer-controls .dropdown-item:hover {
  background-color: #f8f9fa;
}

.layer-controls .dropdown-item.text-danger:hover {
  background-color: #f8d7da;
  color: #721c24;
}
