/**
 * API service for Purple Team Workspace management
 */

import axios, { AxiosResponse } from 'axios';
import {
  Workspace,
  WorkspaceCreate,
  WorkspaceUpdate,
  WorkspaceDetail,
  WorkspaceList,
  WorkspaceListRequest,
  WorkspaceStats
} from '../../types/workspace';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8010';
const WORKSPACE_BASE_PATH = '/api/v1/workspaces';

class WorkspaceAPI {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  private getAuthHeaders(): Record<string, string> {
    const token = localStorage.getItem('access_token');
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  private async handleRequest<T>(request: Promise<AxiosResponse<T>>): Promise<T> {
    try {
      const response = await request;
      return response.data;
    } catch (error: any) {
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail);
      }
      throw new Error(error.message || 'An unexpected error occurred');
    }
  }

  // Workspace CRUD operations
  async createWorkspace(workspaceData: WorkspaceCreate): Promise<Workspace> {
    const request = axios.post(
      `${this.baseURL}${WORKSPACE_BASE_PATH}/`,
      workspaceData,
      { headers: { ...this.getAuthHeaders(), 'Content-Type': 'application/json' } }
    );
    return this.handleRequest(request);
  }

  async listWorkspaces(params: WorkspaceListRequest = {}): Promise<WorkspaceList> {
    const queryParams = new URLSearchParams();
    
    if (params.page !== undefined) queryParams.append('page', params.page.toString());
    if (params.per_page !== undefined) queryParams.append('per_page', params.per_page.toString());
    if (params.search) queryParams.append('search', params.search);
    if (params.is_public !== undefined) queryParams.append('is_public', params.is_public.toString());
    if (params.is_active !== undefined) queryParams.append('is_active', params.is_active.toString());

    const request = axios.get(
      `${this.baseURL}${WORKSPACE_BASE_PATH}/?${queryParams.toString()}`,
      { headers: this.getAuthHeaders() }
    );
    return this.handleRequest(request);
  }

  async getWorkspace(workspaceId: number): Promise<WorkspaceDetail> {
    const request = axios.get(
      `${this.baseURL}${WORKSPACE_BASE_PATH}/${workspaceId}`,
      { headers: this.getAuthHeaders() }
    );
    return this.handleRequest(request);
  }

  async updateWorkspace(workspaceId: number, workspaceData: WorkspaceUpdate): Promise<Workspace> {
    const request = axios.put(
      `${this.baseURL}${WORKSPACE_BASE_PATH}/${workspaceId}`,
      workspaceData,
      { headers: { ...this.getAuthHeaders(), 'Content-Type': 'application/json' } }
    );
    return this.handleRequest(request);
  }

  async deleteWorkspace(workspaceId: number): Promise<void> {
    const request = axios.delete(
      `${this.baseURL}${WORKSPACE_BASE_PATH}/${workspaceId}`,
      { headers: this.getAuthHeaders() }
    );
    await this.handleRequest(request);
  }

  // Statistics
  async getWorkspaceStats(): Promise<WorkspaceStats> {
    const request = axios.get(
      `${this.baseURL}${WORKSPACE_BASE_PATH}/stats`,
      { headers: this.getAuthHeaders() }
    );
    return this.handleRequest(request);
  }
}

// Export singleton instance
export const workspaceAPI = new WorkspaceAPI();
export default workspaceAPI;
