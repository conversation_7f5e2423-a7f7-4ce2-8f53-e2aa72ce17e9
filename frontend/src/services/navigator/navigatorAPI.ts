/**
 * API service for MITRE ATT&CK Navigator integration
 */

import axios, { AxiosResponse } from 'axios';
import {
  NavigatorConfig,
  NavigatorLayer,
  LayerGenerationOptions,
  CampaignComparisonRequest,
  NavigatorAPIService,
  NavigatorError,
  LayerValidationResult
} from '../../types/navigator';

const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:8010';
const NAVIGATOR_BASE_PATH = '/api/v1/execution-framework/mitre/navigator';

class NavigatorAPI implements NavigatorAPIService {
  private baseURL: string;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
  }

  private getAuthHeaders(): Record<string, string> {
    const token = localStorage.getItem('access_token');
    return token ? { Authorization: `Bearer ${token}` } : {};
  }

  private async handleRequest<T>(request: Promise<AxiosResponse<T>>): Promise<T> {
    try {
      const response = await request;
      return response.data;
    } catch (error: any) {
      if (error.response?.data?.detail) {
        throw new Error(error.response.data.detail);
      }
      throw new Error(error.message || 'An unexpected error occurred');
    }
  }

  async getConfig(): Promise<NavigatorConfig> {
    const request = axios.get(
      `${this.baseURL}${NAVIGATOR_BASE_PATH}/config`,
      { headers: this.getAuthHeaders() }
    );
    return this.handleRequest(request);
  }

  async getRealtimeLayer(options: LayerGenerationOptions = {}): Promise<NavigatorLayer> {
    const params = new URLSearchParams();
    
    if (options.campaign_id !== undefined) {
      params.append('campaign_id', options.campaign_id.toString());
    }
    if (options.include_execution_status !== undefined) {
      params.append('include_execution_status', options.include_execution_status.toString());
    }
    if (options.include_coverage_metrics !== undefined) {
      params.append('include_coverage_metrics', options.include_coverage_metrics.toString());
    }

    const request = axios.get(
      `${this.baseURL}${NAVIGATOR_BASE_PATH}/layers/realtime?${params.toString()}`,
      { headers: this.getAuthHeaders() }
    );
    return this.handleRequest(request);
  }

  async getCampaignLayer(campaignId: number, includeMetadata: boolean = true): Promise<NavigatorLayer> {
    const params = new URLSearchParams();
    params.append('include_metadata', includeMetadata.toString());

    const request = axios.get(
      `${this.baseURL}${NAVIGATOR_BASE_PATH}/layers/campaign/${campaignId}?${params.toString()}`,
      { headers: this.getAuthHeaders() }
    );
    return this.handleRequest(request);
  }

  async getCampaignComparison(campaignIds: number[]): Promise<NavigatorLayer> {
    const request = axios.post(
      `${this.baseURL}${NAVIGATOR_BASE_PATH}/layers/comparison`,
      campaignIds,
      { headers: { ...this.getAuthHeaders(), 'Content-Type': 'application/json' } }
    );
    return this.handleRequest(request);
  }

  async getCoverageSummary(options: LayerGenerationOptions = {}): Promise<NavigatorLayer> {
    const params = new URLSearchParams();
    
    if (options.include_unexecuted !== undefined) {
      params.append('include_unexecuted', options.include_unexecuted.toString());
    }
    if (options.min_coverage_threshold !== undefined) {
      params.append('min_coverage_threshold', options.min_coverage_threshold.toString());
    }

    const request = axios.get(
      `${this.baseURL}${NAVIGATOR_BASE_PATH}/layers/coverage-summary?${params.toString()}`,
      { headers: this.getAuthHeaders() }
    );
    return this.handleRequest(request);
  }

  async getRecentExecutions(days: number = 7): Promise<NavigatorLayer> {
    const params = new URLSearchParams();
    params.append('days', days.toString());

    const request = axios.get(
      `${this.baseURL}${NAVIGATOR_BASE_PATH}/layers/recent-executions?${params.toString()}`,
      { headers: this.getAuthHeaders() }
    );
    return this.handleRequest(request);
  }

  async getGapsAnalysis(tactic?: string, platform?: string): Promise<NavigatorLayer> {
    const params = new URLSearchParams();
    
    if (tactic) {
      params.append('tactic', tactic);
    }
    if (platform) {
      params.append('platform', platform);
    }

    const request = axios.get(
      `${this.baseURL}${NAVIGATOR_BASE_PATH}/layers/gaps-analysis?${params.toString()}`,
      { headers: this.getAuthHeaders() }
    );
    return this.handleRequest(request);
  }

  // Additional utility methods
  async validateLayer(layer: NavigatorLayer): Promise<LayerValidationResult> {
    try {
      // Basic validation logic
      const errors: NavigatorError[] = [];
      const warnings: NavigatorError[] = [];

      // Check required fields
      if (!layer.name) {
        errors.push({ code: 'MISSING_NAME', message: 'Layer name is required' });
      }
      if (!layer.domain) {
        errors.push({ code: 'MISSING_DOMAIN', message: 'Layer domain is required' });
      }
      if (!layer.techniques || !Array.isArray(layer.techniques)) {
        errors.push({ code: 'MISSING_TECHNIQUES', message: 'Layer techniques array is required' });
      }

      // Check technique format
      if (layer.techniques) {
        layer.techniques.forEach((technique, index) => {
          if (!technique.techniqueID) {
            errors.push({
              code: 'MISSING_TECHNIQUE_ID',
              message: `Technique at index ${index} is missing techniqueID`
            });
          }
          if (technique.score < 0 || technique.score > 100) {
            warnings.push({
              code: 'INVALID_SCORE',
              message: `Technique ${technique.techniqueID} has score outside 0-100 range`
            });
          }
        });
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings
      };
    } catch (error: any) {
      return {
        isValid: false,
        errors: [{ code: 'VALIDATION_ERROR', message: error.message }],
        warnings: []
      };
    }
  }

  async exportLayer(layer: NavigatorLayer, format: 'json' | 'svg' | 'png' = 'json'): Promise<Blob> {
    try {
      if (format === 'json') {
        const jsonString = JSON.stringify(layer, null, 2);
        return new Blob([jsonString], { type: 'application/json' });
      }
      
      // For SVG/PNG exports, we would need to integrate with the Navigator's export functionality
      // This is a placeholder implementation
      throw new Error(`Export format ${format} not yet implemented`);
    } catch (error: any) {
      throw new Error(`Failed to export layer: ${error.message}`);
    }
  }

  async importLayer(file: File): Promise<NavigatorLayer> {
    try {
      const text = await file.text();
      const layer = JSON.parse(text) as NavigatorLayer;
      
      // Validate the imported layer
      const validation = await this.validateLayer(layer);
      if (!validation.isValid) {
        throw new Error(`Invalid layer file: ${validation.errors.map(e => e.message).join(', ')}`);
      }
      
      return layer;
    } catch (error: any) {
      throw new Error(`Failed to import layer: ${error.message}`);
    }
  }

  // Cache management
  private layerCache = new Map<string, { layer: NavigatorLayer; timestamp: number }>();
  private readonly CACHE_TTL = 5 * 60 * 1000; // 5 minutes

  private getCacheKey(method: string, params: any): string {
    return `${method}_${JSON.stringify(params)}`;
  }

  private getCachedLayer(key: string): NavigatorLayer | null {
    const cached = this.layerCache.get(key);
    if (cached && Date.now() - cached.timestamp < this.CACHE_TTL) {
      return cached.layer;
    }
    return null;
  }

  private setCachedLayer(key: string, layer: NavigatorLayer): void {
    this.layerCache.set(key, { layer, timestamp: Date.now() });
  }

  async getRealtimeLayerCached(options: LayerGenerationOptions = {}): Promise<NavigatorLayer> {
    const cacheKey = this.getCacheKey('realtime', options);
    const cached = this.getCachedLayer(cacheKey);
    
    if (cached) {
      return cached;
    }
    
    const layer = await this.getRealtimeLayer(options);
    this.setCachedLayer(cacheKey, layer);
    return layer;
  }

  clearCache(): void {
    this.layerCache.clear();
  }

  // WebSocket support for real-time updates
  private websocket: WebSocket | null = null;
  private eventListeners: Map<string, Function[]> = new Map();

  connectWebSocket(): void {
    if (this.websocket?.readyState === WebSocket.OPEN) {
      return;
    }

    const token = localStorage.getItem('access_token');
    const wsUrl = `${this.baseURL.replace('http', 'ws')}/ws/navigator?token=${token}`;
    
    this.websocket = new WebSocket(wsUrl);
    
    this.websocket.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        this.emit(data.type, data.payload);
      } catch (error) {
        console.error('Failed to parse WebSocket message:', error);
      }
    };
    
    this.websocket.onclose = () => {
      // Attempt to reconnect after 5 seconds
      setTimeout(() => this.connectWebSocket(), 5000);
    };
  }

  disconnectWebSocket(): void {
    if (this.websocket) {
      this.websocket.close();
      this.websocket = null;
    }
  }

  on(event: string, callback: Function): void {
    if (!this.eventListeners.has(event)) {
      this.eventListeners.set(event, []);
    }
    this.eventListeners.get(event)!.push(callback);
  }

  off(event: string, callback: Function): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      const index = listeners.indexOf(callback);
      if (index > -1) {
        listeners.splice(index, 1);
      }
    }
  }

  private emit(event: string, data: any): void {
    const listeners = this.eventListeners.get(event);
    if (listeners) {
      listeners.forEach(callback => callback(data));
    }
  }
}

// Export singleton instance
export const navigatorAPI = new NavigatorAPI();
export default navigatorAPI;
