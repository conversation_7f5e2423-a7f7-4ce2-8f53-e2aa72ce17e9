import React from 'react';
import CampaignForm from '../../components/campaigns/CampaignForm';
import { Campaign } from '../../types/campaign';

const CreateCampaign: React.FC = () => {
  const handleSubmit = async (data: Partial<Campaign>) => {
    const response = await fetch('/api/campaigns', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    });

    if (!response.ok) {
      throw new Error('Failed to create campaign');
    }
  };

  return <CampaignForm onSubmit={handleSubmit} />;
};

export default CreateCampaign; 