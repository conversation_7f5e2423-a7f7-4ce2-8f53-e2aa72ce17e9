/**
 * Purple Team Workspaces Page
 * 
 * Main page for managing purple team workspaces
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import {
  Workspace,
  WorkspaceCreate,
  WorkspaceFilter,
  WorkspaceStats
} from '../../types/workspace';
import WorkspaceList from '../../components/Workspaces/WorkspaceList';
import WorkspaceCreateModal from '../../components/Workspaces/WorkspaceCreateModal';
import WorkspaceStatsCard from '../../components/Workspaces/WorkspaceStatsCard';
import { workspaceAPI } from '../../services/workspace/workspaceAPI';
import './WorkspacesPage.css';

const WorkspacesPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  
  // State
  const [workspaces, setWorkspaces] = useState<Workspace[]>([]);
  const [stats, setStats] = useState<WorkspaceStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showCreateModal, setShowCreateModal] = useState(false);
  
  // Pagination and filtering
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalWorkspaces, setTotalWorkspaces] = useState(0);
  const [filters, setFilters] = useState<WorkspaceFilter>({
    search: searchParams.get('search') || undefined,
    is_public: searchParams.get('is_public') ? searchParams.get('is_public') === 'true' : undefined,
    is_active: searchParams.get('is_active') ? searchParams.get('is_active') === 'true' : undefined
  });

  // Load workspaces
  const loadWorkspaces = useCallback(async (page: number = 1, newFilters?: WorkspaceFilter) => {
    try {
      setIsLoading(true);
      setError(null);
      
      const filtersToUse = newFilters || filters;
      const response = await workspaceAPI.listWorkspaces({
        page,
        per_page: 20,
        ...filtersToUse
      });
      
      setWorkspaces(response.workspaces);
      setTotalWorkspaces(response.total);
      setTotalPages(Math.ceil(response.total / 20));
      setCurrentPage(page);
      
    } catch (err: any) {
      setError(`Failed to load workspaces: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [filters]);

  // Load workspace statistics
  const loadStats = useCallback(async () => {
    try {
      const workspaceStats = await workspaceAPI.getWorkspaceStats();
      setStats(workspaceStats);
    } catch (err: any) {
      console.error('Failed to load workspace stats:', err);
    }
  }, []);

  // Initial load
  useEffect(() => {
    loadWorkspaces(1);
    loadStats();
  }, []);

  // Handle filter changes
  const handleFilterChange = useCallback((newFilters: Partial<WorkspaceFilter>) => {
    const updatedFilters = { ...filters, ...newFilters };
    setFilters(updatedFilters);
    
    // Update URL params
    const newParams = new URLSearchParams();
    if (updatedFilters.search) newParams.set('search', updatedFilters.search);
    if (updatedFilters.is_public !== undefined) newParams.set('is_public', updatedFilters.is_public.toString());
    if (updatedFilters.is_active !== undefined) newParams.set('is_active', updatedFilters.is_active.toString());
    setSearchParams(newParams);
    
    // Reload workspaces with new filters
    loadWorkspaces(1, updatedFilters);
  }, [filters, setSearchParams, loadWorkspaces]);

  // Handle page change
  const handlePageChange = useCallback((page: number) => {
    loadWorkspaces(page);
  }, [loadWorkspaces]);

  // Handle workspace creation
  const handleCreateWorkspace = useCallback(async (workspaceData: WorkspaceCreate) => {
    try {
      const newWorkspace = await workspaceAPI.createWorkspace(workspaceData);
      setShowCreateModal(false);
      
      // Reload workspaces and stats
      await loadWorkspaces(1);
      await loadStats();
      
      // Navigate to the new workspace
      navigate(`/workspaces/${newWorkspace.id}`);
      
    } catch (err: any) {
      throw new Error(`Failed to create workspace: ${err.message}`);
    }
  }, [loadWorkspaces, loadStats, navigate]);

  // Handle workspace click
  const handleWorkspaceClick = useCallback((workspace: Workspace) => {
    navigate(`/workspaces/${workspace.id}`);
  }, [navigate]);

  if (error) {
    return (
      <div className="workspaces-page">
        <div className="container-fluid">
          <div className="row">
            <div className="col-12">
              <div className="alert alert-danger">
                <h4>Error Loading Workspaces</h4>
                <p>{error}</p>
                <button 
                  className="btn btn-outline-danger"
                  onClick={() => loadWorkspaces(1)}
                >
                  Retry
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="workspaces-page">
      <div className="container-fluid">
        {/* Header */}
        <div className="row mb-4">
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h1 className="h3 mb-0">Purple Team Workspaces</h1>
                <p className="text-muted mb-0">Collaborative spaces for purple team exercises</p>
              </div>
              <button 
                className="btn btn-primary"
                onClick={() => setShowCreateModal(true)}
              >
                <i className="fas fa-plus me-2"></i>
                Create Workspace
              </button>
            </div>
          </div>
        </div>

        {/* Statistics */}
        {stats && (
          <div className="row mb-4">
            <div className="col-12">
              <WorkspaceStatsCard stats={stats} />
            </div>
          </div>
        )}

        {/* Filters */}
        <div className="row mb-3">
          <div className="col-12">
            <div className="workspace-filters">
              <div className="row g-3">
                <div className="col-md-4">
                  <div className="input-group">
                    <span className="input-group-text">
                      <i className="fas fa-search"></i>
                    </span>
                    <input
                      type="text"
                      className="form-control"
                      placeholder="Search workspaces..."
                      value={filters.search || ''}
                      onChange={(e) => handleFilterChange({ search: e.target.value || undefined })}
                    />
                  </div>
                </div>
                
                <div className="col-md-3">
                  <select
                    className="form-select"
                    value={filters.is_public === undefined ? 'all' : filters.is_public.toString()}
                    onChange={(e) => {
                      const value = e.target.value;
                      handleFilterChange({ 
                        is_public: value === 'all' ? undefined : value === 'true' 
                      });
                    }}
                  >
                    <option value="all">All Workspaces</option>
                    <option value="true">Public Only</option>
                    <option value="false">Private Only</option>
                  </select>
                </div>
                
                <div className="col-md-3">
                  <select
                    className="form-select"
                    value={filters.is_active === undefined ? 'all' : filters.is_active.toString()}
                    onChange={(e) => {
                      const value = e.target.value;
                      handleFilterChange({ 
                        is_active: value === 'all' ? undefined : value === 'true' 
                      });
                    }}
                  >
                    <option value="all">All Status</option>
                    <option value="true">Active Only</option>
                    <option value="false">Inactive Only</option>
                  </select>
                </div>
                
                <div className="col-md-2">
                  <button
                    className="btn btn-outline-secondary w-100"
                    onClick={() => {
                      setFilters({});
                      setSearchParams(new URLSearchParams());
                      loadWorkspaces(1, {});
                    }}
                  >
                    Clear Filters
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Workspace List */}
        <div className="row">
          <div className="col-12">
            <WorkspaceList
              workspaces={workspaces}
              isLoading={isLoading}
              onWorkspaceClick={handleWorkspaceClick}
              currentPage={currentPage}
              totalPages={totalPages}
              totalWorkspaces={totalWorkspaces}
              onPageChange={handlePageChange}
            />
          </div>
        </div>

        {/* Create Workspace Modal */}
        {showCreateModal && (
          <WorkspaceCreateModal
            isOpen={showCreateModal}
            onClose={() => setShowCreateModal(false)}
            onSubmit={handleCreateWorkspace}
          />
        )}
      </div>
    </div>
  );
};

export default WorkspacesPage;
