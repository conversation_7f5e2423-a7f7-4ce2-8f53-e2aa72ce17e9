/**
 * Styles for Navigator Page
 */

.navigator-page {
  min-height: 100vh;
  background-color: #f8f9fa;
  padding: 1rem 0;
}

.navigator-stats {
  display: flex;
  gap: 1rem;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 0.5rem 1rem;
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  min-width: 100px;
}

.stat-label {
  font-size: 0.75rem;
  color: #6c757d;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 0.25rem;
}

.stat-value {
  font-size: 1.25rem;
  font-weight: 600;
  color: #495057;
}

.navigator-controls-panel {
  background-color: #ffffff;
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid #dee2e6;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.navigator-controls-panel .form-label {
  font-weight: 600;
  color: #495057;
  margin-bottom: 0.5rem;
}

.navigator-controls-panel .form-select,
.navigator-controls-panel .form-control {
  border-radius: 6px;
  border: 1px solid #ced4da;
  transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.navigator-controls-panel .form-select:focus,
.navigator-controls-panel .form-control:focus {
  border-color: #86b7fe;
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.navigator-controls-panel .form-check {
  margin-bottom: 0;
}

.navigator-controls-panel .form-check-label {
  font-size: 0.875rem;
  color: #495057;
  cursor: pointer;
}

.navigator-controls-panel .input-group-text {
  background-color: #e9ecef;
  border-color: #ced4da;
  font-size: 0.875rem;
}

.navigator-container {
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid #dee2e6;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07);
  overflow: hidden;
}

.navigator-loading-state {
  background-color: #ffffff;
  border-radius: 8px;
  border: 1px solid #dee2e6;
}

.navigator-loading-state .spinner-border {
  width: 3rem;
  height: 3rem;
}

/* Responsive design */
@media (max-width: 768px) {
  .navigator-page {
    padding: 0.5rem 0;
  }
  
  .navigator-stats {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .stat-item {
    flex-direction: row;
    justify-content: space-between;
    min-width: auto;
  }
  
  .navigator-controls-panel {
    padding: 1rem;
  }
  
  .navigator-controls-panel .row.g-3 {
    --bs-gutter-x: 1rem;
  }
  
  .navigator-controls-panel .col-md-3,
  .navigator-controls-panel .col-md-6 {
    margin-bottom: 1rem;
  }
  
  .navigator-controls-panel .d-flex.gap-3 {
    flex-direction: column;
    gap: 0.75rem !important;
  }
}

@media (max-width: 576px) {
  .navigator-page .container-fluid {
    padding-left: 0.75rem;
    padding-right: 0.75rem;
  }
  
  .navigator-page h1 {
    font-size: 1.5rem;
  }
  
  .navigator-controls-panel {
    padding: 0.75rem;
  }
  
  .navigator-controls-panel .form-label {
    font-size: 0.875rem;
  }
  
  .navigator-controls-panel .form-select,
  .navigator-controls-panel .form-control {
    font-size: 0.875rem;
  }
}

/* Dark theme support */
@media (prefers-color-scheme: dark) {
  .navigator-page {
    background-color: #1a1d23;
  }
  
  .stat-item {
    background-color: #2d3748;
    border-color: #4a5568;
  }
  
  .stat-label {
    color: #a0aec0;
  }
  
  .stat-value {
    color: #e2e8f0;
  }
  
  .navigator-controls-panel {
    background-color: #2d3748;
    border-color: #4a5568;
  }
  
  .navigator-controls-panel .form-label {
    color: #e2e8f0;
  }
  
  .navigator-controls-panel .form-select,
  .navigator-controls-panel .form-control {
    background-color: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
  }
  
  .navigator-controls-panel .form-select:focus,
  .navigator-controls-panel .form-control:focus {
    background-color: #4a5568;
    border-color: #63b3ed;
    color: #e2e8f0;
  }
  
  .navigator-controls-panel .form-check-label {
    color: #e2e8f0;
  }
  
  .navigator-controls-panel .input-group-text {
    background-color: #4a5568;
    border-color: #718096;
    color: #e2e8f0;
  }
  
  .navigator-container {
    background-color: #2d3748;
    border-color: #4a5568;
  }
  
  .navigator-loading-state {
    background-color: #2d3748;
    border-color: #4a5568;
    color: #e2e8f0;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .navigator-controls-panel,
  .navigator-container,
  .stat-item {
    border-width: 2px;
  }
  
  .navigator-controls-panel .form-select:focus,
  .navigator-controls-panel .form-control:focus {
    border-width: 2px;
    box-shadow: 0 0 0 3px rgba(13, 110, 253, 0.5);
  }
}

/* Reduced motion */
@media (prefers-reduced-motion: reduce) {
  .navigator-controls-panel .form-select,
  .navigator-controls-panel .form-control {
    transition: none;
  }
  
  .navigator-loading-state .spinner-border {
    animation: none;
  }
}

/* Print styles */
@media print {
  .navigator-page {
    background-color: white;
  }
  
  .navigator-controls-panel {
    display: none;
  }
  
  .navigator-stats {
    display: none;
  }
  
  .navigator-container {
    border: 1px solid #000;
    box-shadow: none;
  }
}

/* Focus styles for accessibility */
.navigator-controls-panel .form-check-input:focus {
  box-shadow: 0 0 0 0.25rem rgba(13, 110, 253, 0.25);
}

.navigator-controls-panel .form-select:focus,
.navigator-controls-panel .form-control:focus {
  outline: none;
}

/* Loading animation */
@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

.navigator-loading-state {
  animation: pulse 2s infinite;
}

/* Hover effects */
.stat-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.navigator-controls-panel .form-check-label:hover {
  color: #0d6efd;
}

/* Custom scrollbar for select elements */
.navigator-controls-panel select[multiple] {
  scrollbar-width: thin;
  scrollbar-color: #ced4da #f8f9fa;
}

.navigator-controls-panel select[multiple]::-webkit-scrollbar {
  width: 8px;
}

.navigator-controls-panel select[multiple]::-webkit-scrollbar-track {
  background: #f8f9fa;
  border-radius: 4px;
}

.navigator-controls-panel select[multiple]::-webkit-scrollbar-thumb {
  background: #ced4da;
  border-radius: 4px;
}

.navigator-controls-panel select[multiple]::-webkit-scrollbar-thumb:hover {
  background: #adb5bd;
}
