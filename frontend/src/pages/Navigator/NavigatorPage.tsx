/**
 * MITRE ATT&CK Navigator Page
 * 
 * Main page component for the interactive MITRE Navigator integration
 */

import React, { useState, useEffect, useCallback } from 'react';
import { useSearchParams } from 'react-router-dom';
import {
  NavigatorLayer,
  NavigatorConfig,
  NavigatorTechnique,
  LayerType,
  LayerGenerationOptions
} from '../../types/navigator';
import NavigatorViewer from '../../components/MitreNavigator/NavigatorViewer';
import { navigatorAPI } from '../../services/navigator/navigatorAPI';
import './NavigatorPage.css';

const NavigatorPage: React.FC = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const [currentLayer, setCurrentLayer] = useState<NavigatorLayer | null>(null);
  const [config, setConfig] = useState<NavigatorConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [layerType, setLayerType] = useState<LayerType>('realtime');
  const [campaigns, setCampaigns] = useState<Array<{ id: number; name: string }>>([]);
  const [selectedCampaigns, setSelectedCampaigns] = useState<number[]>([]);
  const [layerOptions, setLayerOptions] = useState<LayerGenerationOptions>({
    include_execution_status: true,
    include_coverage_metrics: true,
    include_unexecuted: false,
    min_coverage_threshold: 0,
    days: 7
  });

  // Load initial data
  useEffect(() => {
    const loadInitialData = async () => {
      try {
        setIsLoading(true);
        
        // Load Navigator configuration
        const navigatorConfig = await navigatorAPI.getConfig();
        setConfig(navigatorConfig);
        
        // Load campaigns list (mock for now)
        setCampaigns([
          { id: 1, name: 'Red Team Exercise 2024-Q1' },
          { id: 2, name: 'Purple Team Assessment' },
          { id: 3, name: 'Continuous Validation' }
        ]);
        
        // Load initial layer based on URL params
        const initialLayerType = (searchParams.get('layer') as LayerType) || 'realtime';
        const campaignId = searchParams.get('campaign');
        
        setLayerType(initialLayerType);
        
        if (campaignId) {
          setLayerOptions(prev => ({ ...prev, campaign_id: parseInt(campaignId) }));
        }
        
        await loadLayer(initialLayerType, { 
          ...layerOptions, 
          campaign_id: campaignId ? parseInt(campaignId) : undefined 
        });
        
      } catch (err: any) {
        setError(`Failed to load Navigator: ${err.message}`);
      } finally {
        setIsLoading(false);
      }
    };

    loadInitialData();
  }, []);

  // Load layer based on type and options
  const loadLayer = useCallback(async (type: LayerType, options: LayerGenerationOptions = {}) => {
    try {
      setIsLoading(true);
      setError(null);
      
      let layer: NavigatorLayer;
      
      switch (type) {
        case 'realtime':
          layer = await navigatorAPI.getRealtimeLayer(options);
          break;
        case 'campaign':
          if (options.campaign_id) {
            layer = await navigatorAPI.getCampaignLayer(options.campaign_id, true);
          } else {
            throw new Error('Campaign ID required for campaign layer');
          }
          break;
        case 'comparison':
          if (selectedCampaigns.length > 0) {
            layer = await navigatorAPI.getCampaignComparison(selectedCampaigns);
          } else {
            throw new Error('Select campaigns to compare');
          }
          break;
        case 'coverage':
          layer = await navigatorAPI.getCoverageSummary(options);
          break;
        case 'recent':
          layer = await navigatorAPI.getRecentExecutions(options.days);
          break;
        case 'gaps':
          layer = await navigatorAPI.getGapsAnalysis(options.tactic, options.platform);
          break;
        default:
          throw new Error(`Unknown layer type: ${type}`);
      }
      
      setCurrentLayer(layer);
      
      // Update URL params
      const newParams = new URLSearchParams(searchParams);
      newParams.set('layer', type);
      if (options.campaign_id) {
        newParams.set('campaign', options.campaign_id.toString());
      } else {
        newParams.delete('campaign');
      }
      setSearchParams(newParams);
      
    } catch (err: any) {
      setError(`Failed to load ${type} layer: ${err.message}`);
    } finally {
      setIsLoading(false);
    }
  }, [selectedCampaigns, searchParams, setSearchParams]);

  // Handle layer type change
  const handleLayerTypeChange = useCallback((newType: LayerType) => {
    setLayerType(newType);
    loadLayer(newType, layerOptions);
  }, [layerOptions, loadLayer]);

  // Handle options change
  const handleOptionsChange = useCallback((newOptions: Partial<LayerGenerationOptions>) => {
    const updatedOptions = { ...layerOptions, ...newOptions };
    setLayerOptions(updatedOptions);
    loadLayer(layerType, updatedOptions);
  }, [layerType, layerOptions, loadLayer]);

  // Handle technique click
  const handleTechniqueClick = useCallback((technique: NavigatorTechnique) => {
    console.log('Technique clicked:', technique);
    // TODO: Show technique details modal or navigate to technique page
  }, []);

  // Handle layer change
  const handleLayerChange = useCallback((layer: NavigatorLayer) => {
    setCurrentLayer(layer);
  }, []);

  if (error) {
    return (
      <div className="navigator-page">
        <div className="container-fluid">
          <div className="row">
            <div className="col-12">
              <div className="alert alert-danger">
                <h4>Navigator Error</h4>
                <p>{error}</p>
                <button 
                  className="btn btn-outline-danger"
                  onClick={() => window.location.reload()}
                >
                  Reload Page
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="navigator-page">
      <div className="container-fluid">
        {/* Header */}
        <div className="row mb-3">
          <div className="col-12">
            <div className="d-flex justify-content-between align-items-center">
              <div>
                <h1 className="h3 mb-0">MITRE ATT&CK Navigator</h1>
                <p className="text-muted mb-0">Interactive technique coverage visualization</p>
              </div>
              <div className="navigator-stats">
                {currentLayer && (
                  <div className="d-flex gap-3">
                    <div className="stat-item">
                      <span className="stat-label">Techniques:</span>
                      <span className="stat-value">{currentLayer.techniques.length}</span>
                    </div>
                    <div className="stat-item">
                      <span className="stat-label">Layer:</span>
                      <span className="stat-value">{currentLayer.name}</span>
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Controls */}
        <div className="row mb-3">
          <div className="col-12">
            <div className="navigator-controls-panel">
              <div className="row g-3">
                {/* Layer Type Selection */}
                <div className="col-md-3">
                  <label className="form-label">Layer Type</label>
                  <select 
                    className="form-select"
                    value={layerType}
                    onChange={(e) => handleLayerTypeChange(e.target.value as LayerType)}
                  >
                    <option value="realtime">Real-time Coverage</option>
                    <option value="campaign">Campaign Specific</option>
                    <option value="comparison">Campaign Comparison</option>
                    <option value="coverage">Coverage Summary</option>
                    <option value="recent">Recent Executions</option>
                    <option value="gaps">Coverage Gaps</option>
                  </select>
                </div>

                {/* Campaign Selection */}
                {(layerType === 'campaign' || layerType === 'comparison') && (
                  <div className="col-md-3">
                    <label className="form-label">
                      {layerType === 'comparison' ? 'Campaigns to Compare' : 'Campaign'}
                    </label>
                    {layerType === 'comparison' ? (
                      <select 
                        className="form-select"
                        multiple
                        value={selectedCampaigns.map(String)}
                        onChange={(e) => {
                          const values = Array.from(e.target.selectedOptions, option => parseInt(option.value));
                          setSelectedCampaigns(values);
                        }}
                      >
                        {campaigns.map(campaign => (
                          <option key={campaign.id} value={campaign.id}>
                            {campaign.name}
                          </option>
                        ))}
                      </select>
                    ) : (
                      <select 
                        className="form-select"
                        value={layerOptions.campaign_id || ''}
                        onChange={(e) => handleOptionsChange({ 
                          campaign_id: e.target.value ? parseInt(e.target.value) : undefined 
                        })}
                      >
                        <option value="">Select Campaign</option>
                        {campaigns.map(campaign => (
                          <option key={campaign.id} value={campaign.id}>
                            {campaign.name}
                          </option>
                        ))}
                      </select>
                    )}
                  </div>
                )}

                {/* Additional Options */}
                <div className="col-md-6">
                  <label className="form-label">Options</label>
                  <div className="d-flex gap-3 align-items-center">
                    <div className="form-check">
                      <input 
                        className="form-check-input"
                        type="checkbox"
                        id="includeExecution"
                        checked={layerOptions.include_execution_status}
                        onChange={(e) => handleOptionsChange({ include_execution_status: e.target.checked })}
                      />
                      <label className="form-check-label" htmlFor="includeExecution">
                        Execution Status
                      </label>
                    </div>
                    <div className="form-check">
                      <input 
                        className="form-check-input"
                        type="checkbox"
                        id="includeCoverage"
                        checked={layerOptions.include_coverage_metrics}
                        onChange={(e) => handleOptionsChange({ include_coverage_metrics: e.target.checked })}
                      />
                      <label className="form-check-label" htmlFor="includeCoverage">
                        Coverage Metrics
                      </label>
                    </div>
                    {layerType === 'recent' && (
                      <div className="input-group" style={{ width: '150px' }}>
                        <input 
                          type="number"
                          className="form-control form-control-sm"
                          value={layerOptions.days}
                          onChange={(e) => handleOptionsChange({ days: parseInt(e.target.value) })}
                          min="1"
                          max="365"
                        />
                        <span className="input-group-text">days</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigator */}
        <div className="row">
          <div className="col-12">
            <div className="navigator-container">
              {isLoading ? (
                <div className="navigator-loading-state">
                  <div className="d-flex justify-content-center align-items-center" style={{ height: '600px' }}>
                    <div className="text-center">
                      <div className="spinner-border text-primary mb-3" role="status">
                        <span className="visually-hidden">Loading...</span>
                      </div>
                      <p>Loading MITRE ATT&CK Navigator...</p>
                    </div>
                  </div>
                </div>
              ) : (
                <NavigatorViewer
                  layer={currentLayer || undefined}
                  config={config || undefined}
                  height="700px"
                  onTechniqueClick={handleTechniqueClick}
                  onLayerChange={handleLayerChange}
                  showControls={true}
                  showLegend={true}
                  allowEdit={false}
                />
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default NavigatorPage;
