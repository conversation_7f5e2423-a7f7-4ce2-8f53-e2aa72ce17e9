"""
Comprehensive data validation tests.

This module contains extensive tests for data validation, schema compliance,
and data integrity checks across all framework types and data formats.
"""

import pytest
import json
import csv
import io
from typing import Dict, List, Any
from datetime import datetime
import uuid

from api.services.validation_service import (
    ISFDataValidator,
    NISTCSFDataValidator,
    ValidationResult,
    ValidationSeverity
)
from api.schemas.isf import ISFImportRequest, ISFVersionCreate
from api.schemas.nist_csf import NISTCSFImportRequest, NISTCSFVersionCreate
from api.schemas.mappings import MappingCreate, BulkMappingRequest


class TestISFDataValidation:
    """Comprehensive tests for ISF data validation."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = ISFDataValidator()
    
    def test_valid_isf_complete_structure(self):
        """Test validation of complete valid ISF structure."""
        valid_data = {
            "version": "2020.1",
            "description": "Complete ISF framework for testing",
            "release_date": "2020-01-01",
            "security_areas": [
                {
                    "area_id": "SG",
                    "name": "Security Governance",
                    "description": "Security governance and management",
                    "controls": [
                        {
                            "control_id": "SG1",
                            "name": "Security Policy",
                            "description": "Establish and maintain security policy",
                            "control_type": "policy",
                            "maturity_level": "basic",
                            "implementation_guidance": "Develop comprehensive security policy",
                            "references": ["ISO 27001:2013 A.5.1.1"]
                        },
                        {
                            "control_id": "SG2",
                            "name": "Security Organization",
                            "description": "Establish security organization structure",
                            "control_type": "administrative",
                            "maturity_level": "intermediate"
                        }
                    ]
                },
                {
                    "area_id": "RM",
                    "name": "Risk Management",
                    "description": "Risk management processes",
                    "controls": [
                        {
                            "control_id": "RM1",
                            "name": "Risk Assessment",
                            "description": "Conduct regular risk assessments",
                            "control_type": "administrative",
                            "maturity_level": "advanced"
                        }
                    ]
                }
            ]
        }
        
        result = self.validator.validate_json_structure(valid_data)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
        assert len(result.warnings) == 0
    
    def test_isf_missing_required_fields(self):
        """Test validation with missing required fields."""
        test_cases = [
            # Missing version
            {
                "security_areas": []
            },
            # Missing security_areas
            {
                "version": "2020.1"
            },
            # Missing area_id in security area
            {
                "version": "2020.1",
                "security_areas": [
                    {
                        "name": "Security Governance",
                        "controls": []
                    }
                ]
            },
            # Missing control_id in control
            {
                "version": "2020.1",
                "security_areas": [
                    {
                        "area_id": "SG",
                        "name": "Security Governance",
                        "controls": [
                            {
                                "name": "Security Policy"
                            }
                        ]
                    }
                ]
            }
        ]
        
        for i, invalid_data in enumerate(test_cases):
            result = self.validator.validate_json_structure(invalid_data)
            assert result.is_valid is False, f"Test case {i} should be invalid"
            assert len(result.errors) > 0, f"Test case {i} should have errors"
    
    def test_isf_invalid_field_formats(self):
        """Test validation with invalid field formats."""
        test_cases = [
            # Invalid version format
            {
                "version": "invalid-version",
                "security_areas": []
            },
            # Invalid area_id format
            {
                "version": "2020.1",
                "security_areas": [
                    {
                        "area_id": "invalid_area_id_123",
                        "name": "Test Area",
                        "controls": []
                    }
                ]
            },
            # Invalid control_type
            {
                "version": "2020.1",
                "security_areas": [
                    {
                        "area_id": "SG",
                        "name": "Security Governance",
                        "controls": [
                            {
                                "control_id": "SG1",
                                "name": "Security Policy",
                                "control_type": "invalid_type"
                            }
                        ]
                    }
                ]
            },
            # Invalid maturity_level
            {
                "version": "2020.1",
                "security_areas": [
                    {
                        "area_id": "SG",
                        "name": "Security Governance",
                        "controls": [
                            {
                                "control_id": "SG1",
                                "name": "Security Policy",
                                "maturity_level": "invalid_level"
                            }
                        ]
                    }
                ]
            }
        ]
        
        for i, invalid_data in enumerate(test_cases):
            result = self.validator.validate_json_structure(invalid_data)
            assert result.is_valid is False, f"Test case {i} should be invalid"
            assert len(result.errors) > 0, f"Test case {i} should have errors"
    
    def test_isf_duplicate_ids(self):
        """Test validation with duplicate IDs."""
        # Duplicate area IDs
        duplicate_areas_data = {
            "version": "2020.1",
            "security_areas": [
                {
                    "area_id": "SG",
                    "name": "Security Governance 1",
                    "controls": []
                },
                {
                    "area_id": "SG",  # Duplicate
                    "name": "Security Governance 2",
                    "controls": []
                }
            ]
        }
        
        result = self.validator.validate_json_structure(duplicate_areas_data)
        assert result.is_valid is False
        assert any("duplicate" in error.lower() for error in result.errors)
        
        # Duplicate control IDs within same area
        duplicate_controls_data = {
            "version": "2020.1",
            "security_areas": [
                {
                    "area_id": "SG",
                    "name": "Security Governance",
                    "controls": [
                        {
                            "control_id": "SG1",
                            "name": "Security Policy 1"
                        },
                        {
                            "control_id": "SG1",  # Duplicate
                            "name": "Security Policy 2"
                        }
                    ]
                }
            ]
        }
        
        result = self.validator.validate_json_structure(duplicate_controls_data)
        assert result.is_valid is False
        assert any("duplicate" in error.lower() for error in result.errors)
    
    def test_isf_csv_validation(self):
        """Test ISF CSV format validation."""
        # Valid CSV data
        valid_csv_rows = [
            {
                "area_id": "SG",
                "area_name": "Security Governance",
                "control_id": "SG1",
                "control_name": "Security Policy",
                "control_type": "policy",
                "maturity_level": "basic",
                "description": "Establish security policy"
            },
            {
                "area_id": "RM",
                "area_name": "Risk Management",
                "control_id": "RM1",
                "control_name": "Risk Assessment",
                "control_type": "administrative",
                "maturity_level": "intermediate",
                "description": "Conduct risk assessments"
            }
        ]
        
        result = self.validator.validate_csv_structure(valid_csv_rows)
        assert result.is_valid is True
        assert len(result.errors) == 0
        
        # Invalid CSV data - missing required columns
        invalid_csv_rows = [
            {
                "area_id": "SG",
                # Missing control_id and control_name
                "description": "Test description"
            }
        ]
        
        result = self.validator.validate_csv_structure(invalid_csv_rows)
        assert result.is_valid is False
        assert len(result.errors) > 0
    
    def test_isf_edge_cases(self):
        """Test ISF validation edge cases."""
        edge_cases = [
            # Empty security areas
            {
                "version": "2020.1",
                "security_areas": []
            },
            # Security area with no controls
            {
                "version": "2020.1",
                "security_areas": [
                    {
                        "area_id": "SG",
                        "name": "Security Governance",
                        "controls": []
                    }
                ]
            },
            # Very long field values
            {
                "version": "2020.1",
                "description": "A" * 10000,  # Very long description
                "security_areas": [
                    {
                        "area_id": "SG",
                        "name": "Security Governance",
                        "controls": [
                            {
                                "control_id": "SG1",
                                "name": "B" * 1000,  # Very long name
                                "description": "C" * 5000  # Very long description
                            }
                        ]
                    }
                ]
            }
        ]
        
        for i, edge_case in enumerate(edge_cases):
            result = self.validator.validate_json_structure(edge_case)
            # Edge cases might be valid or generate warnings
            if not result.is_valid:
                assert len(result.errors) > 0, f"Edge case {i} should have errors if invalid"


class TestNISTCSFDataValidation:
    """Comprehensive tests for NIST CSF data validation."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = NISTCSFDataValidator()
    
    def test_valid_nist_csf_complete_structure(self):
        """Test validation of complete valid NIST CSF structure."""
        valid_data = {
            "version": "2.0",
            "description": "Complete NIST CSF 2.0 framework",
            "release_date": "2024-02-26",
            "functions": [
                {
                    "function_id": "GV",
                    "name": "Govern",
                    "description": "Governance function",
                    "categories": [
                        {
                            "category_id": "GV.OC",
                            "name": "Organizational Context",
                            "description": "Organizational context category",
                            "subcategories": [
                                {
                                    "subcategory_id": "GV.OC-01",
                                    "name": "Organizational mission",
                                    "description": "Organizational mission subcategory",
                                    "implementation_examples": [
                                        "Establish mission statement",
                                        "Define organizational objectives"
                                    ],
                                    "informative_references": [
                                        "ISO/IEC 27001:2013 A.5.1.1"
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    "function_id": "ID",
                    "name": "Identify",
                    "description": "Identify function",
                    "categories": [
                        {
                            "category_id": "ID.AM",
                            "name": "Asset Management",
                            "description": "Asset management category",
                            "subcategories": [
                                {
                                    "subcategory_id": "ID.AM-01",
                                    "name": "Physical devices and systems",
                                    "description": "Physical devices and systems subcategory"
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        
        result = self.validator.validate_hierarchical_structure(valid_data)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_nist_csf_missing_required_fields(self):
        """Test NIST CSF validation with missing required fields."""
        test_cases = [
            # Missing version
            {
                "functions": []
            },
            # Missing functions
            {
                "version": "2.0"
            },
            # Missing function_id
            {
                "version": "2.0",
                "functions": [
                    {
                        "name": "Govern",
                        "categories": []
                    }
                ]
            },
            # Missing category_id
            {
                "version": "2.0",
                "functions": [
                    {
                        "function_id": "GV",
                        "name": "Govern",
                        "categories": [
                            {
                                "name": "Organizational Context",
                                "subcategories": []
                            }
                        ]
                    }
                ]
            }
        ]
        
        for i, invalid_data in enumerate(test_cases):
            result = self.validator.validate_hierarchical_structure(invalid_data)
            assert result.is_valid is False, f"Test case {i} should be invalid"
            assert len(result.errors) > 0, f"Test case {i} should have errors"
    
    def test_nist_csf_invalid_id_formats(self):
        """Test NIST CSF validation with invalid ID formats."""
        test_cases = [
            # Invalid function_id format
            {
                "version": "2.0",
                "functions": [
                    {
                        "function_id": "INVALID",
                        "name": "Invalid Function",
                        "categories": []
                    }
                ]
            },
            # Invalid category_id format
            {
                "version": "2.0",
                "functions": [
                    {
                        "function_id": "GV",
                        "name": "Govern",
                        "categories": [
                            {
                                "category_id": "INVALID.FORMAT",
                                "name": "Invalid Category",
                                "subcategories": []
                            }
                        ]
                    }
                ]
            },
            # Invalid subcategory_id format
            {
                "version": "2.0",
                "functions": [
                    {
                        "function_id": "GV",
                        "name": "Govern",
                        "categories": [
                            {
                                "category_id": "GV.OC",
                                "name": "Organizational Context",
                                "subcategories": [
                                    {
                                        "subcategory_id": "INVALID.FORMAT.123",
                                        "name": "Invalid Subcategory"
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
        
        for i, invalid_data in enumerate(test_cases):
            result = self.validator.validate_hierarchical_structure(invalid_data)
            assert result.is_valid is False, f"Test case {i} should be invalid"
            assert len(result.errors) > 0, f"Test case {i} should have errors"
    
    def test_nist_csf_duplicate_ids(self):
        """Test NIST CSF validation with duplicate IDs."""
        # Duplicate function IDs
        duplicate_functions_data = {
            "version": "2.0",
            "functions": [
                {
                    "function_id": "GV",
                    "name": "Govern 1",
                    "categories": []
                },
                {
                    "function_id": "GV",  # Duplicate
                    "name": "Govern 2",
                    "categories": []
                }
            ]
        }
        
        result = self.validator.validate_hierarchical_structure(duplicate_functions_data)
        assert result.is_valid is False
        assert any("duplicate" in error.lower() for error in result.errors)
    
    def test_nist_csf_version_specific_validation(self):
        """Test version-specific NIST CSF validation."""
        # Test CSF 2.0 with Govern function (should be valid)
        csf_20_data = {
            "version": "2.0",
            "functions": [
                {
                    "function_id": "GV",
                    "name": "Govern",
                    "categories": []
                }
            ]
        }
        
        result = self.validator.validate_hierarchical_structure(csf_20_data)
        assert result.is_valid is True
        
        # Test CSF 1.1 with Govern function (should generate warning)
        csf_11_data = {
            "version": "1.1",
            "functions": [
                {
                    "function_id": "GV",
                    "name": "Govern",
                    "categories": []
                }
            ]
        }
        
        result = self.validator.validate_hierarchical_structure(csf_11_data)
        # Should be valid but with warnings about version mismatch
        assert len(result.warnings) > 0


class TestMappingDataValidation:
    """Comprehensive tests for mapping data validation."""
    
    def test_valid_mapping_creation(self):
        """Test validation of valid mapping data."""
        valid_mapping = MappingCreate(
            source_framework="mitre_attack",
            source_id="T1566",
            target_framework="isf",
            target_id="EM1",
            mapping_type="mitigates",
            confidence_score=0.85,
            effectiveness_score=0.78,
            rationale="Email security controls effectively mitigate phishing attacks",
            evidence=["Semantic similarity: 0.82", "Keyword overlap: 0.75"]
        )
        
        # Pydantic validation should pass
        assert valid_mapping.source_framework == "mitre_attack"
        assert valid_mapping.confidence_score == 0.85
        assert 0.0 <= valid_mapping.confidence_score <= 1.0
        assert 0.0 <= valid_mapping.effectiveness_score <= 1.0
    
    def test_invalid_mapping_data(self):
        """Test validation of invalid mapping data."""
        invalid_cases = [
            # Invalid confidence score
            {
                "source_framework": "mitre_attack",
                "source_id": "T1566",
                "target_framework": "isf",
                "target_id": "EM1",
                "mapping_type": "mitigates",
                "confidence_score": 1.5,  # > 1.0
                "rationale": "Test"
            },
            # Invalid effectiveness score
            {
                "source_framework": "mitre_attack",
                "source_id": "T1566",
                "target_framework": "isf",
                "target_id": "EM1",
                "mapping_type": "mitigates",
                "effectiveness_score": -0.1,  # < 0.0
                "rationale": "Test"
            },
            # Invalid mapping type
            {
                "source_framework": "mitre_attack",
                "source_id": "T1566",
                "target_framework": "isf",
                "target_id": "EM1",
                "mapping_type": "invalid_type",
                "rationale": "Test"
            }
        ]
        
        for invalid_data in invalid_cases:
            with pytest.raises(ValueError):
                MappingCreate(**invalid_data)
    
    def test_bulk_mapping_validation(self):
        """Test validation of bulk mapping requests."""
        valid_bulk_request = BulkMappingRequest(
            technique_ids=["T1566", "T1078", "T1055"],
            target_framework="isf",
            options={
                "min_confidence": 0.7,
                "max_suggestions": 5,
                "include_rationale": True
            }
        )
        
        assert len(valid_bulk_request.technique_ids) == 3
        assert valid_bulk_request.target_framework == "isf"
        assert valid_bulk_request.options["min_confidence"] == 0.7


class TestDataIntegrityValidation:
    """Tests for data integrity and consistency validation."""
    
    def test_cross_reference_validation(self):
        """Test validation of cross-references between data elements."""
        # Test ISF data with invalid area references
        invalid_isf_data = {
            "version": "2020.1",
            "security_areas": [
                {
                    "area_id": "SG",
                    "name": "Security Governance",
                    "controls": [
                        {
                            "control_id": "RM1",  # Control ID doesn't match area
                            "name": "Risk Assessment",
                            "area_reference": "RM"  # References non-existent area
                        }
                    ]
                }
            ]
        }
        
        validator = ISFDataValidator()
        result = validator.validate_json_structure(invalid_isf_data)
        
        # Should detect inconsistency
        assert len(result.warnings) > 0 or len(result.errors) > 0
    
    def test_version_consistency_validation(self):
        """Test validation of version consistency."""
        # Test data claiming to be version 2.0 but missing new features
        inconsistent_data = {
            "version": "2.0",
            "functions": [
                # Missing Govern function that should be in 2.0
                {
                    "function_id": "ID",
                    "name": "Identify",
                    "categories": []
                }
            ]
        }
        
        validator = NISTCSFDataValidator()
        result = validator.validate_hierarchical_structure(inconsistent_data)
        
        # Should generate warnings about version inconsistency
        assert len(result.warnings) > 0
    
    def test_data_completeness_validation(self):
        """Test validation of data completeness."""
        # Test framework with minimal required data
        minimal_data = {
            "version": "2020.1",
            "security_areas": [
                {
                    "area_id": "SG",
                    "name": "Security Governance",
                    "controls": [
                        {
                            "control_id": "SG1",
                            "name": "Security Policy"
                            # Missing description, type, etc.
                        }
                    ]
                }
            ]
        }
        
        validator = ISFDataValidator()
        result = validator.validate_json_structure(minimal_data)
        
        # Should be valid but may have warnings about missing optional fields
        assert result.is_valid is True
        # May have warnings about missing recommended fields


class TestPerformanceValidation:
    """Tests for validation performance with large datasets."""
    
    def test_large_dataset_validation_performance(self):
        """Test validation performance with large datasets."""
        import time
        
        # Create large ISF dataset
        large_data = {
            "version": "2020.1",
            "security_areas": []
        }
        
        # Generate 100 security areas with 50 controls each
        for area_num in range(100):
            area = {
                "area_id": f"A{area_num:02d}",
                "name": f"Area {area_num}",
                "controls": []
            }
            
            for control_num in range(50):
                control = {
                    "control_id": f"A{area_num:02d}C{control_num:02d}",
                    "name": f"Control {area_num}-{control_num}",
                    "description": f"Description for control {area_num}-{control_num}",
                    "control_type": "technical",
                    "maturity_level": "intermediate"
                }
                area["controls"].append(control)
            
            large_data["security_areas"].append(area)
        
        # Validate large dataset
        validator = ISFDataValidator()
        start_time = time.time()
        result = validator.validate_json_structure(large_data)
        end_time = time.time()
        
        validation_time = end_time - start_time
        
        # Should complete validation in reasonable time
        assert validation_time < 10.0, f"Validation too slow: {validation_time:.2f}s"
        assert result.is_valid is True
        
        print(f"Validated 5000 controls in {validation_time:.2f}s")


if __name__ == '__main__':
    pytest.main([__file__, "-v"])
