"""
Unit tests for progress tracker service.

This module contains comprehensive unit tests for the progress tracking functionality
including task creation, progress updates, completion handling, and error scenarios.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import threading
import time
import uuid

from api.services.progress_tracker import (
    TaskStatus,
    TaskStage,
    ProgressUpdate,
    TaskProgress,
    ProgressTracker,
    progress_tracker
)


class TestTaskStatus:
    """Test cases for TaskStatus enum."""
    
    def test_task_status_values(self):
        """Test TaskStatus enum values."""
        assert TaskStatus.PENDING.value == "pending"
        assert TaskStatus.RUNNING.value == "running"
        assert TaskStatus.COMPLETED.value == "completed"
        assert TaskStatus.FAILED.value == "failed"
        assert TaskStatus.CANCELLED.value == "cancelled"


class TestTaskStage:
    """Test cases for TaskStage enum."""
    
    def test_task_stage_values(self):
        """Test TaskStage enum values."""
        assert TaskStage.INITIALIZING.value == "initializing"
        assert TaskStage.VALIDATING.value == "validating"
        assert TaskStage.PROCESSING.value == "processing"
        assert TaskStage.FINALIZING.value == "finalizing"
        assert TaskStage.CLEANUP.value == "cleanup"


class TestProgressUpdate:
    """Test cases for ProgressUpdate dataclass."""
    
    def test_progress_update_creation(self):
        """Test ProgressUpdate creation."""
        timestamp = datetime.utcnow()
        update = ProgressUpdate(
            timestamp=timestamp,
            stage=TaskStage.PROCESSING,
            progress_percentage=50.0,
            message="Processing data",
            details={"processed": 500, "total": 1000}
        )
        
        assert update.timestamp == timestamp
        assert update.stage == TaskStage.PROCESSING
        assert update.progress_percentage == 50.0
        assert update.message == "Processing data"
        assert update.details["processed"] == 500
        assert update.details["total"] == 1000
    
    def test_progress_update_minimal(self):
        """Test ProgressUpdate with minimal parameters."""
        timestamp = datetime.utcnow()
        update = ProgressUpdate(
            timestamp=timestamp,
            stage=TaskStage.INITIALIZING,
            progress_percentage=0.0,
            message="Starting"
        )
        
        assert update.timestamp == timestamp
        assert update.stage == TaskStage.INITIALIZING
        assert update.progress_percentage == 0.0
        assert update.message == "Starting"
        assert update.details is None


class TestTaskProgress:
    """Test cases for TaskProgress dataclass."""
    
    def test_task_progress_creation(self):
        """Test TaskProgress creation."""
        task_id = "test-task-123"
        start_time = datetime.utcnow()
        
        progress = TaskProgress(
            task_id=task_id,
            status=TaskStatus.RUNNING,
            current_stage=TaskStage.PROCESSING,
            progress_percentage=25.0,
            start_time=start_time,
            total_items=1000,
            processed_items=250
        )
        
        assert progress.task_id == task_id
        assert progress.status == TaskStatus.RUNNING
        assert progress.current_stage == TaskStage.PROCESSING
        assert progress.progress_percentage == 25.0
        assert progress.start_time == start_time
        assert progress.total_items == 1000
        assert progress.processed_items == 250
        assert progress.end_time is None
        assert progress.errors == []
        assert progress.warnings == []
        assert progress.updates == []
    
    def test_elapsed_time_property(self):
        """Test elapsed_time property calculation."""
        start_time = datetime.utcnow() - timedelta(minutes=5)
        progress = TaskProgress(
            task_id="test",
            status=TaskStatus.RUNNING,
            current_stage=TaskStage.PROCESSING,
            progress_percentage=50.0,
            start_time=start_time
        )
        
        elapsed = progress.elapsed_time
        assert isinstance(elapsed, timedelta)
        assert elapsed.total_seconds() > 250  # Should be around 5 minutes
        assert elapsed.total_seconds() < 350  # Allow some variance
    
    def test_elapsed_time_with_end_time(self):
        """Test elapsed_time property with end_time set."""
        start_time = datetime.utcnow() - timedelta(minutes=10)
        end_time = start_time + timedelta(minutes=5)
        
        progress = TaskProgress(
            task_id="test",
            status=TaskStatus.COMPLETED,
            current_stage=TaskStage.FINALIZING,
            progress_percentage=100.0,
            start_time=start_time,
            end_time=end_time
        )
        
        elapsed = progress.elapsed_time
        assert elapsed == timedelta(minutes=5)
    
    def test_is_complete_property(self):
        """Test is_complete property."""
        # Test incomplete statuses
        for status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
            progress = TaskProgress(
                task_id="test",
                status=status,
                current_stage=TaskStage.PROCESSING,
                progress_percentage=50.0,
                start_time=datetime.utcnow()
            )
            assert progress.is_complete is False
        
        # Test complete statuses
        for status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
            progress = TaskProgress(
                task_id="test",
                status=status,
                current_stage=TaskStage.FINALIZING,
                progress_percentage=100.0,
                start_time=datetime.utcnow()
            )
            assert progress.is_complete is True
    
    def test_to_dict_method(self):
        """Test to_dict method."""
        start_time = datetime.utcnow()
        progress = TaskProgress(
            task_id="test-123",
            status=TaskStatus.RUNNING,
            current_stage=TaskStage.PROCESSING,
            progress_percentage=75.0,
            start_time=start_time,
            total_items=1000,
            processed_items=750,
            current_message="Processing items",
            metadata={"source": "test"}
        )
        
        result = progress.to_dict()
        
        assert result["task_id"] == "test-123"
        assert result["status"] == "running"
        assert result["current_stage"] == "processing"
        assert result["progress_percentage"] == 75.0
        assert result["start_time"] == start_time.isoformat()
        assert result["end_time"] is None
        assert result["total_items"] == 1000
        assert result["processed_items"] == 750
        assert result["current_message"] == "Processing items"
        assert result["metadata"]["source"] == "test"
        assert "elapsed_time" in result


class TestProgressTracker:
    """Test cases for ProgressTracker class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.tracker = ProgressTracker()
    
    def test_initialization(self):
        """Test ProgressTracker initialization."""
        assert self.tracker._tasks == {}
        assert isinstance(self.tracker._lock, type(threading.RLock()))
        assert self.tracker._callbacks == {}
    
    def test_create_task_with_id(self):
        """Test creating task with specific ID."""
        task_id = "custom-task-123"
        total_items = 1000
        metadata = {"source": "test"}
        
        result_id = self.tracker.create_task(
            task_id=task_id,
            total_items=total_items,
            metadata=metadata
        )
        
        assert result_id == task_id
        assert task_id in self.tracker._tasks
        
        task = self.tracker._tasks[task_id]
        assert task.task_id == task_id
        assert task.status == TaskStatus.PENDING
        assert task.current_stage == TaskStage.INITIALIZING
        assert task.progress_percentage == 0.0
        assert task.total_items == total_items
        assert task.metadata == metadata
    
    def test_create_task_auto_id(self):
        """Test creating task with auto-generated ID."""
        result_id = self.tracker.create_task(total_items=500)
        
        assert result_id is not None
        assert len(result_id) > 0
        assert result_id in self.tracker._tasks
        
        # Should be a valid UUID
        uuid.UUID(result_id)  # Will raise exception if invalid
    
    def test_create_task_duplicate_id(self):
        """Test creating task with duplicate ID raises error."""
        task_id = "duplicate-task"
        self.tracker.create_task(task_id=task_id)
        
        with pytest.raises(ValueError, match="already exists"):
            self.tracker.create_task(task_id=task_id)
    
    def test_start_task(self):
        """Test starting a task."""
        task_id = self.tracker.create_task(total_items=100)
        message = "Task started successfully"
        
        self.tracker.start_task(task_id, message)
        
        task = self.tracker._tasks[task_id]
        assert task.status == TaskStatus.RUNNING
        assert task.current_message == message
        assert len(task.updates) == 1
        assert task.updates[0].stage == TaskStage.INITIALIZING
        assert task.updates[0].progress_percentage == 0.0
    
    def test_start_task_not_found(self):
        """Test starting non-existent task raises error."""
        with pytest.raises(ValueError, match="not found"):
            self.tracker.start_task("non-existent-task")
    
    def test_update_progress(self):
        """Test updating task progress."""
        task_id = self.tracker.create_task(total_items=1000)
        self.tracker.start_task(task_id)
        
        self.tracker.update_progress(
            task_id=task_id,
            stage=TaskStage.PROCESSING,
            progress_percentage=50.0,
            message="Half way done",
            processed_items=500,
            details={"current_file": "data.csv"}
        )
        
        task = self.tracker._tasks[task_id]
        assert task.current_stage == TaskStage.PROCESSING
        assert task.progress_percentage == 50.0
        assert task.current_message == "Half way done"
        assert task.processed_items == 500
        assert len(task.updates) == 2  # Start + update
        assert task.updates[-1].details["current_file"] == "data.csv"
    
    def test_update_progress_with_eta(self):
        """Test progress update calculates ETA."""
        task_id = self.tracker.create_task(total_items=1000)
        self.tracker.start_task(task_id)
        
        # Simulate some time passing
        task = self.tracker._tasks[task_id]
        task.start_time = datetime.utcnow() - timedelta(seconds=10)
        
        self.tracker.update_progress(
            task_id=task_id,
            stage=TaskStage.PROCESSING,
            progress_percentage=25.0,
            message="Quarter done"
        )
        
        task = self.tracker._tasks[task_id]
        assert task.estimated_completion is not None
        assert task.estimated_completion > datetime.utcnow()
    
    def test_update_progress_bounds_checking(self):
        """Test progress percentage bounds checking."""
        task_id = self.tracker.create_task()
        self.tracker.start_task(task_id)
        
        # Test negative percentage
        self.tracker.update_progress(
            task_id=task_id,
            stage=TaskStage.PROCESSING,
            progress_percentage=-10.0,
            message="Negative test"
        )
        
        task = self.tracker._tasks[task_id]
        assert task.progress_percentage == 0.0
        
        # Test over 100 percentage
        self.tracker.update_progress(
            task_id=task_id,
            stage=TaskStage.PROCESSING,
            progress_percentage=150.0,
            message="Over 100 test"
        )
        
        task = self.tracker._tasks[task_id]
        assert task.progress_percentage == 100.0
    
    def test_complete_task(self):
        """Test completing a task."""
        task_id = self.tracker.create_task()
        self.tracker.start_task(task_id)
        
        completion_message = "Task completed successfully"
        completion_metadata = {"final_count": 1000}
        
        self.tracker.complete_task(
            task_id=task_id,
            message=completion_message,
            metadata=completion_metadata
        )
        
        task = self.tracker._tasks[task_id]
        assert task.status == TaskStatus.COMPLETED
        assert task.progress_percentage == 100.0
        assert task.current_message == completion_message
        assert task.end_time is not None
        assert task.metadata["final_count"] == 1000
        assert task.is_complete is True
    
    def test_fail_task(self):
        """Test failing a task."""
        task_id = self.tracker.create_task()
        self.tracker.start_task(task_id)
        
        error_message = "Task failed due to error"
        error_details = {"error_code": "E001"}
        
        self.tracker.fail_task(
            task_id=task_id,
            error_message=error_message,
            details=error_details
        )
        
        task = self.tracker._tasks[task_id]
        assert task.status == TaskStatus.FAILED
        assert task.current_message == error_message
        assert task.end_time is not None
        assert error_message in task.errors
        assert task.is_complete is True
    
    def test_cancel_task(self):
        """Test cancelling a task."""
        task_id = self.tracker.create_task()
        self.tracker.start_task(task_id)
        
        cancel_message = "Task cancelled by user"
        
        self.tracker.cancel_task(task_id, cancel_message)
        
        task = self.tracker._tasks[task_id]
        assert task.status == TaskStatus.CANCELLED
        assert task.current_message == cancel_message
        assert task.end_time is not None
        assert task.is_complete is True
    
    def test_cancel_completed_task_error(self):
        """Test cancelling already completed task raises error."""
        task_id = self.tracker.create_task()
        self.tracker.start_task(task_id)
        self.tracker.complete_task(task_id)
        
        with pytest.raises(ValueError, match="already complete"):
            self.tracker.cancel_task(task_id)
    
    def test_add_error(self):
        """Test adding error to task."""
        task_id = self.tracker.create_task()
        error_message = "Non-fatal error occurred"
        
        self.tracker.add_error(task_id, error_message)
        
        task = self.tracker._tasks[task_id]
        assert error_message in task.errors
        assert task.status != TaskStatus.FAILED  # Should not fail the task
    
    def test_add_warning(self):
        """Test adding warning to task."""
        task_id = self.tracker.create_task()
        warning_message = "Warning: potential issue detected"
        
        self.tracker.add_warning(task_id, warning_message)
        
        task = self.tracker._tasks[task_id]
        assert warning_message in task.warnings
    
    def test_get_progress(self):
        """Test getting task progress."""
        task_id = self.tracker.create_task(total_items=100)
        self.tracker.start_task(task_id, "Starting task")
        
        progress = self.tracker.get_progress(task_id)
        
        assert isinstance(progress, dict)
        assert progress["task_id"] == task_id
        assert progress["status"] == "running"
        assert progress["total_items"] == 100
    
    def test_get_progress_not_found(self):
        """Test getting progress for non-existent task raises error."""
        with pytest.raises(ValueError, match="not found"):
            self.tracker.get_progress("non-existent-task")
    
    def test_get_all_tasks(self):
        """Test getting all tasks."""
        task1_id = self.tracker.create_task()
        task2_id = self.tracker.create_task()
        
        all_tasks = self.tracker.get_all_tasks()
        
        assert isinstance(all_tasks, dict)
        assert task1_id in all_tasks
        assert task2_id in all_tasks
        assert len(all_tasks) == 2
    
    def test_cleanup_completed_tasks(self):
        """Test cleaning up old completed tasks."""
        # Create and complete some tasks
        old_task_id = self.tracker.create_task()
        self.tracker.start_task(old_task_id)
        self.tracker.complete_task(old_task_id)
        
        recent_task_id = self.tracker.create_task()
        self.tracker.start_task(recent_task_id)
        self.tracker.complete_task(recent_task_id)
        
        # Make one task old
        old_task = self.tracker._tasks[old_task_id]
        old_task.end_time = datetime.utcnow() - timedelta(hours=25)
        
        # Cleanup tasks older than 24 hours
        cleaned_count = self.tracker.cleanup_completed_tasks(max_age_hours=24)
        
        assert cleaned_count == 1
        assert old_task_id not in self.tracker._tasks
        assert recent_task_id in self.tracker._tasks
    
    def test_register_callback(self):
        """Test registering progress callback."""
        task_id = self.tracker.create_task()
        callback_called = []
        
        def test_callback(tid, task_progress):
            callback_called.append((tid, task_progress.status))
        
        self.tracker.register_callback(task_id, test_callback)
        self.tracker.start_task(task_id)
        
        assert len(callback_called) == 1
        assert callback_called[0][0] == task_id
        assert callback_called[0][1] == TaskStatus.RUNNING
    
    def test_callback_error_handling(self):
        """Test callback error handling doesn't break progress tracking."""
        task_id = self.tracker.create_task()
        
        def failing_callback(tid, task_progress):
            raise Exception("Callback error")
        
        self.tracker.register_callback(task_id, failing_callback)
        
        # Should not raise exception
        self.tracker.start_task(task_id)
        
        task = self.tracker._tasks[task_id]
        assert task.status == TaskStatus.RUNNING
    
    def test_update_limit(self):
        """Test that updates list is limited to prevent memory issues."""
        task_id = self.tracker.create_task()
        self.tracker.start_task(task_id)
        
        # Add many updates
        for i in range(150):
            self.tracker.update_progress(
                task_id=task_id,
                stage=TaskStage.PROCESSING,
                progress_percentage=i / 150 * 100,
                message=f"Update {i}"
            )
        
        task = self.tracker._tasks[task_id]
        assert len(task.updates) <= 100  # Should be limited
    
    def test_thread_safety(self):
        """Test thread safety of progress tracker."""
        task_id = self.tracker.create_task(total_items=1000)
        self.tracker.start_task(task_id)
        
        def update_progress():
            for i in range(10):
                self.tracker.update_progress(
                    task_id=task_id,
                    stage=TaskStage.PROCESSING,
                    progress_percentage=i * 10,
                    message=f"Thread update {i}"
                )
                time.sleep(0.01)
        
        # Start multiple threads
        threads = []
        for _ in range(3):
            thread = threading.Thread(target=update_progress)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        task = self.tracker._tasks[task_id]
        assert task.status == TaskStatus.RUNNING
        assert len(task.updates) > 1  # Should have multiple updates


class TestGlobalProgressTracker:
    """Test cases for global progress tracker instance."""
    
    def test_global_instance_exists(self):
        """Test that global progress tracker instance exists."""
        assert progress_tracker is not None
        assert isinstance(progress_tracker, ProgressTracker)
    
    def test_global_instance_functionality(self):
        """Test basic functionality of global instance."""
        task_id = progress_tracker.create_task(total_items=10)
        progress_tracker.start_task(task_id, "Global test")
        
        progress = progress_tracker.get_progress(task_id)
        assert progress["status"] == "running"
        
        progress_tracker.complete_task(task_id, "Global test complete")
        
        progress = progress_tracker.get_progress(task_id)
        assert progress["status"] == "completed"


if __name__ == '__main__':
    pytest.main([__file__])
