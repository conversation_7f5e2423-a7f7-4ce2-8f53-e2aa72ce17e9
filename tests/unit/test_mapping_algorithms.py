"""
Unit tests for mapping algorithms.

This module contains comprehensive unit tests for the mapping algorithm classes
including semantic analysis, keyword analysis, effectiveness scoring, and
quality assessment functionality.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
import math
from typing import Dict, List, Any

from api.services.mapping_algorithms import (
    SemanticAnalyzer,
    KeywordAnalyzer,
    EffectivenessScorer,
    MappingSuggestion,
    EffectivenessFactors,
    CrossFrameworkMappingAlgorithm,
    QualityAssessor,
    MLEffectivenessPredictor
)


class TestSemanticAnalyzer:
    """Test cases for SemanticAnalyzer class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.analyzer = SemanticAnalyzer()
    
    def test_initialization(self):
        """Test SemanticAnalyzer initialization."""
        assert self.analyzer.stop_words is not None
        assert len(self.analyzer.stop_words) > 0
        assert self.analyzer.security_synonyms is not None
        assert 'malware' in self.analyzer.security_synonyms
    
    def test_calculate_similarity_identical_texts(self):
        """Test similarity calculation for identical texts."""
        text1 = "This is a test for phishing detection"
        text2 = "This is a test for phishing detection"
        
        similarity = self.analyzer.calculate_similarity(text1, text2)
        assert similarity == 1.0
    
    def test_calculate_similarity_completely_different_texts(self):
        """Test similarity calculation for completely different texts."""
        text1 = "Phishing email security controls"
        text2 = "Database backup procedures"
        
        similarity = self.analyzer.calculate_similarity(text1, text2)
        assert 0.0 <= similarity <= 1.0
        assert similarity < 0.5  # Should be low similarity
    
    def test_calculate_similarity_security_domain_weighting(self):
        """Test that security domain terms get higher weighting."""
        text1 = "Phishing attack prevention"
        text2 = "Email security controls"
        
        similarity = self.analyzer.calculate_similarity(text1, text2)
        assert similarity > 0.3  # Should have reasonable similarity due to security context
    
    def test_tokenize_and_normalize(self):
        """Test text tokenization and normalization."""
        text = "This is a TEST with Special-Characters! And numbers123"
        tokens = self.analyzer._tokenize_and_normalize(text)
        
        assert 'test' in tokens
        assert 'special' in tokens
        assert 'characters' in tokens
        assert 'numbers' in tokens
        assert 'this' not in tokens  # Stop word should be removed
        assert 'and' not in tokens   # Stop word should be removed
    
    def test_expand_with_synonyms(self):
        """Test synonym expansion functionality."""
        tokens = {'malware', 'detection'}
        expanded = self.analyzer._expand_with_synonyms(tokens)
        
        assert 'malware' in expanded
        assert 'virus' in expanded  # Should be expanded from synonyms
        assert 'trojan' in expanded  # Should be expanded from synonyms
        assert 'detection' in expanded
    
    def test_calculate_security_domain_weight(self):
        """Test security domain weight calculation."""
        tokens1 = {'phishing', 'email', 'attack'}
        tokens2 = {'security', 'controls', 'email'}
        
        weight = self.analyzer._calculate_security_domain_weight(tokens1, tokens2)
        assert 0.0 <= weight <= 1.0
        assert weight > 0  # Should have some weight due to security terms
    
    def test_empty_text_similarity(self):
        """Test similarity calculation with empty texts."""
        similarity = self.analyzer.calculate_similarity("", "")
        assert similarity == 0.0
        
        similarity = self.analyzer.calculate_similarity("test", "")
        assert similarity == 0.0
        
        similarity = self.analyzer.calculate_similarity("", "test")
        assert similarity == 0.0
    
    def test_similarity_bounds(self):
        """Test that similarity scores are always within bounds."""
        test_cases = [
            ("malware detection system", "antivirus software"),
            ("phishing email attack", "social engineering"),
            ("encryption algorithm", "cryptographic protocol"),
            ("firewall configuration", "network security"),
            ("incident response", "security breach handling")
        ]
        
        for text1, text2 in test_cases:
            similarity = self.analyzer.calculate_similarity(text1, text2)
            assert 0.0 <= similarity <= 1.0


class TestKeywordAnalyzer:
    """Test cases for KeywordAnalyzer class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.analyzer = KeywordAnalyzer()
    
    def test_initialization(self):
        """Test KeywordAnalyzer initialization."""
        assert self.analyzer.security_keywords is not None
        assert 'attack_vectors' in self.analyzer.security_keywords
        assert 'defense_mechanisms' in self.analyzer.security_keywords
    
    def test_calculate_overlap_identical_texts(self):
        """Test overlap calculation for identical texts."""
        text1 = "Phishing email security controls"
        text2 = "Phishing email security controls"
        
        overlap = self.analyzer.calculate_overlap(text1, text2)
        assert overlap == 1.0
    
    def test_calculate_overlap_no_overlap(self):
        """Test overlap calculation for texts with no overlap."""
        text1 = "Database backup procedures"
        text2 = "Network firewall configuration"
        
        overlap = self.analyzer.calculate_overlap(text1, text2)
        assert 0.0 <= overlap <= 1.0
    
    def test_extract_keywords(self):
        """Test keyword extraction functionality."""
        text = "Phishing email attack detection using firewall and antivirus"
        keywords = self.analyzer._extract_keywords(text)
        
        assert 'phishing' in keywords
        assert 'email' in keywords
        assert 'attack' in keywords
        assert 'firewall' in keywords
        assert 'antivirus' in keywords
        assert keywords['phishing'] > 0  # Should have positive weight
    
    def test_calculate_weighted_overlap(self):
        """Test weighted overlap calculation."""
        keywords1 = {'phishing': 2.0, 'email': 1.5, 'attack': 1.8}
        keywords2 = {'phishing': 1.5, 'security': 1.0, 'email': 1.2}
        
        overlap = self.analyzer._calculate_weighted_overlap(keywords1, keywords2)
        assert 0.0 <= overlap <= 1.0
        assert overlap > 0  # Should have overlap due to common keywords
    
    def test_calculate_context_match(self):
        """Test context matching functionality."""
        text1 = "Implement email security controls to prevent phishing attacks"
        text2 = "Deploy antivirus software to detect malware threats"
        
        context_score = self.analyzer._calculate_context_match(text1, text2)
        assert 0.0 <= context_score <= 1.0
    
    def test_sentences_have_similar_structure(self):
        """Test sentence structure similarity detection."""
        sent1 = "implement security controls to prevent attacks"
        sent2 = "deploy firewall rules to block threats"
        
        similar = self.analyzer._sentences_have_similar_structure(sent1, sent2)
        assert isinstance(similar, bool)
    
    def test_empty_text_overlap(self):
        """Test overlap calculation with empty texts."""
        overlap = self.analyzer.calculate_overlap("", "")
        assert overlap == 0.0
        
        overlap = self.analyzer.calculate_overlap("test", "")
        assert overlap == 0.0
    
    def test_overlap_bounds(self):
        """Test that overlap scores are always within bounds."""
        test_cases = [
            ("malware detection", "antivirus protection"),
            ("phishing prevention", "email security"),
            ("firewall rules", "network protection"),
            ("incident response", "security monitoring"),
            ("encryption keys", "cryptographic security")
        ]
        
        for text1, text2 in test_cases:
            overlap = self.analyzer.calculate_overlap(text1, text2)
            assert 0.0 <= overlap <= 1.0


class TestEffectivenessScorer:
    """Test cases for EffectivenessScorer class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.scorer = EffectivenessScorer()
    
    def test_initialization(self):
        """Test EffectivenessScorer initialization."""
        assert self.scorer.factor_weights is not None
        assert len(self.scorer.factor_weights) == 5
        assert sum(self.scorer.factor_weights.values()) == 1.0  # Weights should sum to 1
    
    def test_calculate_effectiveness(self):
        """Test effectiveness calculation."""
        technique = {
            'technique_id': 'T1566',
            'name': 'Phishing',
            'description': 'Email-based social engineering attack',
            'tactics': ['initial-access']
        }
        
        control = {
            'control_id': 'EM1',
            'name': 'Email Security',
            'description': 'Email security controls and filtering',
            'control_type': 'technical',
            'maturity_level': 'intermediate'
        }
        
        result = self.scorer.calculate_effectiveness(technique, control)
        
        assert 'score' in result
        assert 'confidence' in result
        assert 'factors' in result
        assert 0.0 <= result['score'] <= 1.0
        assert 0.0 <= result['confidence'] <= 1.0
    
    def test_assess_implementation_complexity(self):
        """Test implementation complexity assessment."""
        technique = {'tactics': ['initial-access', 'persistence']}
        control = {'control_type': 'technical'}
        
        complexity = self.scorer._assess_implementation_complexity(technique, control)
        assert 0.0 <= complexity <= 1.0
    
    def test_assess_cost_effectiveness(self):
        """Test cost-effectiveness assessment."""
        technique = {'technique_id': 'T1566'}
        control = {'maturity_level': 'basic'}
        
        cost_eff = self.scorer._assess_cost_effectiveness(technique, control)
        assert 0.0 <= cost_eff <= 1.0
    
    def test_assess_technical_feasibility(self):
        """Test technical feasibility assessment."""
        technique = {'technique_id': 'T1566'}
        control = {'control_type': 'policy'}
        
        feasibility = self.scorer._assess_technical_feasibility(technique, control)
        assert 0.0 <= feasibility <= 1.0
    
    def test_assess_organizational_impact(self):
        """Test organizational impact assessment."""
        technique = {'technique_id': 'T1566'}
        control = {'control_type': 'administrative'}
        
        impact = self.scorer._assess_organizational_impact(technique, control)
        assert 0.0 <= impact <= 1.0
    
    def test_assess_coverage_completeness(self):
        """Test coverage completeness assessment."""
        technique = {
            'description': 'phishing email attack social engineering'
        }
        control = {
            'description': 'email security filtering phishing protection'
        }
        
        coverage = self.scorer._assess_coverage_completeness(technique, control)
        assert 0.0 <= coverage <= 1.0
        assert coverage > 0  # Should have some coverage due to keyword overlap
    
    def test_calculate_confidence(self):
        """Test confidence calculation."""
        factors = EffectivenessFactors(
            implementation_complexity=0.6,
            cost_effectiveness=0.7,
            technical_feasibility=0.8,
            organizational_impact=0.6,
            coverage_completeness=0.7
        )
        
        confidence = self.scorer._calculate_confidence(factors)
        assert 0.0 <= confidence <= 1.0
    
    def test_effectiveness_factors_weighted_score(self):
        """Test EffectivenessFactors weighted score calculation."""
        factors = EffectivenessFactors(
            implementation_complexity=0.8,
            cost_effectiveness=0.7,
            technical_feasibility=0.9,
            organizational_impact=0.6,
            coverage_completeness=0.8
        )
        
        weighted_score = factors.weighted_score
        assert 0.0 <= weighted_score <= 1.0
    
    def test_missing_data_handling(self):
        """Test handling of missing data in technique/control."""
        technique = {}  # Empty technique
        control = {}    # Empty control
        
        result = self.scorer.calculate_effectiveness(technique, control)
        
        assert 'score' in result
        assert 'confidence' in result
        assert 'factors' in result
        # Should handle gracefully without errors


class TestMappingSuggestion:
    """Test cases for MappingSuggestion dataclass."""
    
    def test_mapping_suggestion_creation(self):
        """Test MappingSuggestion creation and properties."""
        suggestion = MappingSuggestion(
            source_id='T1566',
            target_id='EM1',
            mapping_type='mitigates',
            confidence_score=0.85,
            effectiveness_score=0.78,
            semantic_similarity=0.82,
            keyword_overlap=0.75,
            rationale='Strong semantic match for phishing controls'
        )
        
        assert suggestion.source_id == 'T1566'
        assert suggestion.target_id == 'EM1'
        assert suggestion.mapping_type == 'mitigates'
        assert suggestion.confidence_score == 0.85
        assert suggestion.effectiveness_score == 0.78
        assert suggestion.semantic_similarity == 0.82
        assert suggestion.keyword_overlap == 0.75
        assert suggestion.rationale == 'Strong semantic match for phishing controls'
        assert suggestion.evidence == []  # Should initialize empty list
    
    def test_mapping_suggestion_with_evidence(self):
        """Test MappingSuggestion with evidence."""
        evidence = ['keyword match: phishing', 'semantic similarity: 0.82']
        suggestion = MappingSuggestion(
            source_id='T1566',
            target_id='EM1',
            mapping_type='mitigates',
            confidence_score=0.85,
            effectiveness_score=0.78,
            semantic_similarity=0.82,
            keyword_overlap=0.75,
            rationale='Test rationale',
            evidence=evidence
        )
        
        assert suggestion.evidence == evidence
        assert len(suggestion.evidence) == 2


class TestCrossFrameworkMappingAlgorithm:
    """Test cases for CrossFrameworkMappingAlgorithm class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.algorithm = CrossFrameworkMappingAlgorithm()
    
    def test_initialization(self):
        """Test algorithm initialization."""
        assert self.algorithm.semantic_analyzer is not None
        assert self.algorithm.keyword_analyzer is not None
        assert self.algorithm.effectiveness_scorer is not None
        assert isinstance(self.algorithm.confidence_threshold, float)
        assert 0.0 <= self.algorithm.confidence_threshold <= 1.0
    
    @patch('api.services.mapping_algorithms.SemanticAnalyzer')
    @patch('api.services.mapping_algorithms.KeywordAnalyzer')
    @patch('api.services.mapping_algorithms.EffectivenessScorer')
    def test_generate_suggestions(self, mock_scorer, mock_keyword, mock_semantic):
        """Test suggestion generation."""
        # Mock the analyzers
        mock_semantic.return_value.calculate_similarity.return_value = 0.8
        mock_keyword.return_value.calculate_overlap.return_value = 0.7
        mock_scorer.return_value.calculate_effectiveness.return_value = {
            'score': 0.75,
            'confidence': 0.85,
            'factors': {}
        }
        
        technique = {
            'technique_id': 'T1566',
            'name': 'Phishing',
            'description': 'Email-based attack'
        }
        
        controls = [
            {
                'control_id': 'EM1',
                'name': 'Email Security',
                'description': 'Email protection controls'
            }
        ]
        
        suggestions = self.algorithm.generate_suggestions(technique, controls)
        
        assert isinstance(suggestions, list)
        assert len(suggestions) > 0
        assert isinstance(suggestions[0], MappingSuggestion)
    
    def test_calculate_confidence_score(self):
        """Test confidence score calculation."""
        semantic_sim = 0.8
        keyword_overlap = 0.7
        effectiveness = {'score': 0.75, 'confidence': 0.85}
        
        confidence = self.algorithm._calculate_confidence_score(
            semantic_sim, keyword_overlap, effectiveness
        )
        
        assert 0.0 <= confidence <= 1.0
    
    def test_filter_by_confidence(self):
        """Test filtering suggestions by confidence threshold."""
        suggestions = [
            MappingSuggestion('T1', 'C1', 'mitigates', 0.9, 0.8, 0.85, 0.75, 'High conf'),
            MappingSuggestion('T1', 'C2', 'mitigates', 0.5, 0.6, 0.55, 0.45, 'Low conf'),
            MappingSuggestion('T1', 'C3', 'mitigates', 0.8, 0.7, 0.75, 0.65, 'Med conf')
        ]
        
        filtered = self.algorithm._filter_by_confidence(suggestions, 0.7)
        
        assert len(filtered) == 2  # Should filter out the low confidence one
        assert all(s.confidence_score >= 0.7 for s in filtered)
    
    def test_rank_suggestions(self):
        """Test suggestion ranking."""
        suggestions = [
            MappingSuggestion('T1', 'C1', 'mitigates', 0.7, 0.8, 0.75, 0.65, 'Med'),
            MappingSuggestion('T1', 'C2', 'mitigates', 0.9, 0.8, 0.85, 0.75, 'High'),
            MappingSuggestion('T1', 'C3', 'mitigates', 0.6, 0.7, 0.65, 0.55, 'Low')
        ]
        
        ranked = self.algorithm._rank_suggestions(suggestions)
        
        assert len(ranked) == 3
        assert ranked[0].confidence_score >= ranked[1].confidence_score
        assert ranked[1].confidence_score >= ranked[2].confidence_score
    
    def test_empty_controls_list(self):
        """Test handling of empty controls list."""
        technique = {'technique_id': 'T1566', 'name': 'Test', 'description': 'Test'}
        controls = []
        
        suggestions = self.algorithm.generate_suggestions(technique, controls)
        assert suggestions == []
    
    def test_invalid_technique_data(self):
        """Test handling of invalid technique data."""
        technique = {}  # Missing required fields
        controls = [{'control_id': 'C1', 'name': 'Test', 'description': 'Test'}]
        
        # Should handle gracefully without throwing exceptions
        suggestions = self.algorithm.generate_suggestions(technique, controls)
        assert isinstance(suggestions, list)


class TestQualityAssessor:
    """Test cases for QualityAssessor class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.assessor = QualityAssessor()
    
    def test_initialization(self):
        """Test QualityAssessor initialization."""
        assert hasattr(self.assessor, 'quality_thresholds')
        assert hasattr(self.assessor, 'weight_factors')
    
    def test_assess_mapping_quality(self):
        """Test mapping quality assessment."""
        mapping = {
            'source_id': 'T1566',
            'target_id': 'EM1',
            'confidence_score': 0.85,
            'effectiveness_score': 0.78,
            'semantic_similarity': 0.82,
            'keyword_overlap': 0.75,
            'validation_status': 'approved'
        }
        
        quality_score = self.assessor.assess_mapping_quality(mapping)
        assert 0.0 <= quality_score <= 1.0
    
    def test_generate_quality_report(self):
        """Test quality report generation."""
        mappings = [
            {
                'mapping_id': 1,
                'confidence_score': 0.9,
                'effectiveness_score': 0.85,
                'semantic_similarity': 0.88,
                'keyword_overlap': 0.82
            },
            {
                'mapping_id': 2,
                'confidence_score': 0.6,
                'effectiveness_score': 0.55,
                'semantic_similarity': 0.58,
                'keyword_overlap': 0.52
            }
        ]
        
        report = self.assessor.generate_quality_report(mappings)
        
        assert 'overall_quality' in report
        assert 'quality_distribution' in report
        assert 'recommendations' in report
        assert 'statistics' in report


class TestMLEffectivenessPredictor:
    """Test cases for MLEffectivenessPredictor class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.predictor = MLEffectivenessPredictor()
    
    def test_initialization(self):
        """Test MLEffectivenessPredictor initialization."""
        assert hasattr(self.predictor, 'model')
        assert hasattr(self.predictor, 'feature_extractors')
    
    def test_extract_features(self):
        """Test feature extraction."""
        technique = {
            'technique_id': 'T1566',
            'name': 'Phishing',
            'description': 'Email-based social engineering',
            'tactics': ['initial-access']
        }
        
        control = {
            'control_id': 'EM1',
            'name': 'Email Security',
            'description': 'Email filtering and protection',
            'control_type': 'technical'
        }
        
        features = self.predictor._extract_features(technique, control)
        
        assert isinstance(features, list)
        assert len(features) > 0
        assert all(isinstance(f, (int, float)) for f in features)
    
    def test_predict_effectiveness(self):
        """Test effectiveness prediction."""
        technique = {
            'technique_id': 'T1566',
            'name': 'Phishing',
            'description': 'Email attack'
        }
        
        control = {
            'control_id': 'EM1',
            'name': 'Email Security',
            'description': 'Email protection'
        }
        
        prediction = self.predictor.predict_effectiveness(technique, control)
        
        assert 'effectiveness_score' in prediction
        assert 'confidence' in prediction
        assert 0.0 <= prediction['effectiveness_score'] <= 1.0
        assert 0.0 <= prediction['confidence'] <= 1.0
    
    @patch('api.services.mapping_algorithms.MLEffectivenessPredictor._train_model')
    def test_train_model(self, mock_train):
        """Test model training."""
        training_data = [
            {
                'technique': {'technique_id': 'T1566'},
                'control': {'control_id': 'EM1'},
                'effectiveness': 0.8
            }
        ]
        
        self.predictor.train_model(training_data)
        mock_train.assert_called_once_with(training_data)


if __name__ == '__main__':
    pytest.main([__file__])
