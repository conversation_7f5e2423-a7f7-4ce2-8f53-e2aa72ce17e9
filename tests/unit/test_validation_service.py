"""
Unit tests for validation service.

This module contains comprehensive unit tests for the validation service classes
including ISF data validation, NIST CSF validation, and base validation functionality.
"""

import pytest
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, List, Any

from api.services.validation_service import (
    ValidationSeverity,
    ValidationIssue,
    ValidationResult,
    BaseValidator,
    ISFDataValidator,
    NISTCSFDataValidator
)


class TestValidationSeverity:
    """Test cases for ValidationSeverity enum."""
    
    def test_validation_severity_values(self):
        """Test ValidationSeverity enum values."""
        assert ValidationSeverity.ERROR.value == "error"
        assert ValidationSeverity.WARNING.value == "warning"
        assert ValidationSeverity.INFO.value == "info"


class TestValidationIssue:
    """Test cases for ValidationIssue dataclass."""
    
    def test_validation_issue_creation(self):
        """Test ValidationIssue creation."""
        issue = ValidationIssue(
            severity=ValidationSeverity.ERROR,
            message="Test error message",
            field="test_field",
            value="test_value",
            suggestion="Fix the test field",
            line_number=42
        )
        
        assert issue.severity == ValidationSeverity.ERROR
        assert issue.message == "Test error message"
        assert issue.field == "test_field"
        assert issue.value == "test_value"
        assert issue.suggestion == "Fix the test field"
        assert issue.line_number == 42
    
    def test_validation_issue_minimal(self):
        """Test ValidationIssue with minimal parameters."""
        issue = ValidationIssue(
            severity=ValidationSeverity.WARNING,
            message="Test warning"
        )
        
        assert issue.severity == ValidationSeverity.WARNING
        assert issue.message == "Test warning"
        assert issue.field is None
        assert issue.value is None
        assert issue.suggestion is None
        assert issue.line_number is None


class TestValidationResult:
    """Test cases for ValidationResult dataclass."""
    
    def test_validation_result_initialization(self):
        """Test ValidationResult initialization."""
        result = ValidationResult(is_valid=True)
        
        assert result.is_valid is True
        assert result.issues == []
        assert result.errors == []
        assert result.warnings == []
        assert result.info == []
    
    def test_add_error_issue(self):
        """Test adding error issue."""
        result = ValidationResult(is_valid=True)
        result.add_issue(ValidationSeverity.ERROR, "Test error")
        
        assert result.is_valid is False
        assert len(result.issues) == 1
        assert len(result.errors) == 1
        assert result.errors[0] == "Test error"
        assert result.issues[0].severity == ValidationSeverity.ERROR
    
    def test_add_warning_issue(self):
        """Test adding warning issue."""
        result = ValidationResult(is_valid=True)
        result.add_issue(ValidationSeverity.WARNING, "Test warning")
        
        assert result.is_valid is True  # Warnings don't invalidate
        assert len(result.issues) == 1
        assert len(result.warnings) == 1
        assert result.warnings[0] == "Test warning"
    
    def test_add_info_issue(self):
        """Test adding info issue."""
        result = ValidationResult(is_valid=True)
        result.add_issue(ValidationSeverity.INFO, "Test info")
        
        assert result.is_valid is True  # Info doesn't invalidate
        assert len(result.issues) == 1
        assert len(result.info) == 1
        assert result.info[0] == "Test info"
    
    def test_add_multiple_issues(self):
        """Test adding multiple issues of different severities."""
        result = ValidationResult(is_valid=True)
        
        result.add_issue(ValidationSeverity.ERROR, "Error 1")
        result.add_issue(ValidationSeverity.WARNING, "Warning 1")
        result.add_issue(ValidationSeverity.INFO, "Info 1")
        result.add_issue(ValidationSeverity.ERROR, "Error 2")
        
        assert result.is_valid is False  # Errors invalidate
        assert len(result.issues) == 4
        assert len(result.errors) == 2
        assert len(result.warnings) == 1
        assert len(result.info) == 1
    
    def test_add_issue_with_kwargs(self):
        """Test adding issue with additional keyword arguments."""
        result = ValidationResult(is_valid=True)
        result.add_issue(
            ValidationSeverity.ERROR,
            "Test error",
            field="test_field",
            value="test_value",
            suggestion="Fix it"
        )
        
        issue = result.issues[0]
        assert issue.field == "test_field"
        assert issue.value == "test_value"
        assert issue.suggestion == "Fix it"


class TestBaseValidator:
    """Test cases for BaseValidator class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = BaseValidator()
        self.validator.required_fields = {'field1', 'field2'}
        self.validator.optional_fields = {'field3', 'field4'}
        self.validator.field_patterns = {
            'field1': r'^[A-Z]{2,3}$',
            'field2': r'^\d{4}\.\d+$'
        }
        self.validator.field_constraints = {
            'field3': {
                'min_length': 5,
                'max_length': 50,
                'allowed_values': ['value1', 'value2', 'value3']
            }
        }
    
    def test_validate_required_fields_success(self):
        """Test successful required field validation."""
        data = {'field1': 'ABC', 'field2': '2020.1', 'field3': 'optional'}
        result = ValidationResult(is_valid=True)
        
        self.validator.validate_required_fields(data, result)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_required_fields_missing(self):
        """Test required field validation with missing fields."""
        data = {'field1': 'ABC'}  # Missing field2
        result = ValidationResult(is_valid=True)
        
        self.validator.validate_required_fields(data, result)
        
        assert result.is_valid is False
        assert len(result.errors) == 1
        assert "field2" in result.errors[0]
    
    def test_validate_required_fields_empty(self):
        """Test required field validation with empty fields."""
        data = {'field1': 'ABC', 'field2': ''}  # Empty field2
        result = ValidationResult(is_valid=True)
        
        self.validator.validate_required_fields(data, result)
        
        assert result.is_valid is False
        assert len(result.errors) == 1
        assert "field2" in result.errors[0]
        assert "empty" in result.errors[0]
    
    def test_validate_required_fields_none(self):
        """Test required field validation with None values."""
        data = {'field1': 'ABC', 'field2': None}  # None field2
        result = ValidationResult(is_valid=True)
        
        self.validator.validate_required_fields(data, result)
        
        assert result.is_valid is False
        assert len(result.errors) == 1
    
    def test_validate_field_patterns_success(self):
        """Test successful field pattern validation."""
        data = {'field1': 'ABC', 'field2': '2020.1'}
        result = ValidationResult(is_valid=True)
        
        self.validator.validate_field_patterns(data, result)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_field_patterns_failure(self):
        """Test field pattern validation failure."""
        data = {'field1': 'invalid', 'field2': 'invalid'}
        result = ValidationResult(is_valid=True)
        
        self.validator.validate_field_patterns(data, result)
        
        assert result.is_valid is False
        assert len(result.errors) == 2
    
    def test_validate_field_constraints_success(self):
        """Test successful field constraint validation."""
        data = {'field3': 'value1'}
        result = ValidationResult(is_valid=True)
        
        self.validator.validate_field_constraints(data, result)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_field_constraints_length_violation(self):
        """Test field constraint validation with length violations."""
        data = {'field3': 'abc'}  # Too short
        result = ValidationResult(is_valid=True)
        
        self.validator.validate_field_constraints(data, result)
        
        assert result.is_valid is False
        assert len(result.errors) == 1
        assert "too short" in result.errors[0]
    
    def test_validate_field_constraints_max_length_violation(self):
        """Test field constraint validation with max length violation."""
        data = {'field3': 'a' * 60}  # Too long
        result = ValidationResult(is_valid=True)
        
        self.validator.validate_field_constraints(data, result)
        
        assert result.is_valid is False
        assert len(result.errors) == 1
        assert "too long" in result.errors[0]
    
    def test_validate_field_constraints_invalid_value(self):
        """Test field constraint validation with invalid value."""
        data = {'field3': 'invalid_value'}
        result = ValidationResult(is_valid=True)
        
        self.validator.validate_field_constraints(data, result)
        
        assert result.is_valid is False
        assert len(result.errors) == 1
        assert "invalid value" in result.errors[0]


class TestISFDataValidator:
    """Test cases for ISFDataValidator class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = ISFDataValidator()
    
    def test_initialization(self):
        """Test ISFDataValidator initialization."""
        assert 'version' in self.validator.required_fields
        assert 'security_areas' in self.validator.required_fields
        assert 'description' in self.validator.optional_fields
        assert 'version' in self.validator.field_patterns
        assert 'control_type' in self.validator.field_constraints
    
    def test_validate_json_structure_success(self):
        """Test successful JSON structure validation."""
        data = {
            'version': '2020.1',
            'security_areas': [
                {
                    'area_id': 'SG',
                    'name': 'Security Governance',
                    'controls': [
                        {
                            'control_id': 'SG1',
                            'name': 'Security Policy'
                        }
                    ]
                }
            ]
        }
        
        result = self.validator.validate_json_structure(data)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_json_structure_missing_required(self):
        """Test JSON structure validation with missing required fields."""
        data = {
            'version': '2020.1'
            # Missing security_areas
        }
        
        result = self.validator.validate_json_structure(data)
        
        assert result.is_valid is False
        assert len(result.errors) > 0
    
    def test_validate_json_structure_unsupported_version(self):
        """Test JSON structure validation with unsupported version."""
        data = {
            'version': '9999.9',  # Unsupported version
            'security_areas': []
        }
        
        with patch('api.core.config.isf_config') as mock_config:
            mock_config.SUPPORTED_VERSIONS = ['2020.1', '2021.1']
            result = self.validator.validate_json_structure(data)
        
        assert len(result.warnings) > 0
        assert any('not in supported versions' in w for w in result.warnings)
    
    def test_validate_csv_structure_success(self):
        """Test successful CSV structure validation."""
        csv_rows = [
            {
                'area_id': 'SG',
                'control_id': 'SG1',
                'control_name': 'Security Policy',
                'control_type': 'policy'
            },
            {
                'area_id': 'RM',
                'control_id': 'RM1',
                'control_name': 'Risk Management',
                'control_type': 'administrative'
            }
        ]
        
        result = self.validator.validate_csv_structure(csv_rows)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_csv_structure_empty(self):
        """Test CSV structure validation with empty data."""
        csv_rows = []
        
        result = self.validator.validate_csv_structure(csv_rows)
        
        assert result.is_valid is False
        assert len(result.errors) == 1
        assert "empty" in result.errors[0]
    
    def test_validate_csv_structure_missing_columns(self):
        """Test CSV structure validation with missing required columns."""
        csv_rows = [
            {
                'area_id': 'SG',
                # Missing control_id and control_name
                'description': 'Test description'
            }
        ]
        
        result = self.validator.validate_csv_structure(csv_rows)
        
        assert result.is_valid is False
        assert len(result.errors) > 0
        assert any('Missing required CSV columns' in e for e in result.errors)
    
    def test_validate_security_areas_success(self):
        """Test successful security areas validation."""
        security_areas = [
            {
                'area_id': 'SG',
                'name': 'Security Governance',
                'controls': [
                    {
                        'control_id': 'SG1',
                        'name': 'Security Policy'
                    }
                ]
            }
        ]
        result = ValidationResult(is_valid=True)
        
        self.validator._validate_security_areas(security_areas, result)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_security_areas_not_list(self):
        """Test security areas validation when not a list."""
        security_areas = "not a list"
        result = ValidationResult(is_valid=True)
        
        self.validator._validate_security_areas(security_areas, result)
        
        assert result.is_valid is False
        assert len(result.errors) == 1
        assert "must be a list" in result.errors[0]
    
    def test_validate_security_areas_duplicate_ids(self):
        """Test security areas validation with duplicate area IDs."""
        security_areas = [
            {
                'area_id': 'SG',
                'name': 'Security Governance 1'
            },
            {
                'area_id': 'SG',  # Duplicate
                'name': 'Security Governance 2'
            }
        ]
        result = ValidationResult(is_valid=True)
        
        self.validator._validate_security_areas(security_areas, result)
        
        assert result.is_valid is False
        assert any('Duplicate area ID' in e for e in result.errors)
    
    def test_validate_controls_success(self):
        """Test successful controls validation."""
        controls = [
            {
                'control_id': 'SG1',
                'name': 'Security Policy',
                'control_type': 'policy'
            }
        ]
        result = ValidationResult(is_valid=True)
        
        self.validator._validate_controls(controls, 'test_path', result)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_controls_duplicate_ids(self):
        """Test controls validation with duplicate control IDs."""
        controls = [
            {
                'control_id': 'SG1',
                'name': 'Security Policy 1'
            },
            {
                'control_id': 'SG1',  # Duplicate
                'name': 'Security Policy 2'
            }
        ]
        result = ValidationResult(is_valid=True)
        
        self.validator._validate_controls(controls, 'test_path', result)
        
        assert result.is_valid is False
        assert any('Duplicate control ID' in e for e in result.errors)
    
    def test_validate_csv_row_success(self):
        """Test successful CSV row validation."""
        row = {
            'area_id': 'SG',
            'control_id': 'SG1',
            'control_name': 'Security Policy'
        }
        result = ValidationResult(is_valid=True)
        
        self.validator._validate_csv_row(row, 1, result)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_csv_row_missing_required(self):
        """Test CSV row validation with missing required fields."""
        row = {
            'area_id': '',  # Empty required field
            'control_id': 'SG1',
            'control_name': 'Security Policy'
        }
        result = ValidationResult(is_valid=True)
        
        self.validator._validate_csv_row(row, 1, result)
        
        assert result.is_valid is False
        assert len(result.errors) > 0


class TestNISTCSFDataValidator:
    """Test cases for NISTCSFDataValidator class."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.validator = NISTCSFDataValidator()
    
    def test_initialization(self):
        """Test NISTCSFDataValidator initialization."""
        assert 'version' in self.validator.required_fields
        assert 'functions' in self.validator.required_fields
        assert 'version' in self.validator.field_patterns
        assert 'function_id' in self.validator.field_patterns
    
    def test_validate_hierarchical_structure_success(self):
        """Test successful hierarchical structure validation."""
        data = {
            'version': '2.0',
            'functions': [
                {
                    'function_id': 'GV',
                    'name': 'Govern',
                    'categories': [
                        {
                            'category_id': 'GV.OC',
                            'name': 'Organizational Context',
                            'subcategories': [
                                {
                                    'subcategory_id': 'GV.OC-01',
                                    'name': 'Organizational mission'
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        
        result = self.validator.validate_hierarchical_structure(data)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_hierarchical_structure_missing_required(self):
        """Test hierarchical structure validation with missing required fields."""
        data = {
            'version': '2.0'
            # Missing functions
        }
        
        result = self.validator.validate_hierarchical_structure(data)
        
        assert result.is_valid is False
        assert len(result.errors) > 0
    
    def test_validate_functions_success(self):
        """Test successful functions validation."""
        functions = [
            {
                'function_id': 'GV',
                'name': 'Govern',
                'categories': []
            }
        ]
        result = ValidationResult(is_valid=True)
        
        self.validator._validate_functions(functions, result)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_functions_not_list(self):
        """Test functions validation when not a list."""
        functions = "not a list"
        result = ValidationResult(is_valid=True)
        
        self.validator._validate_functions(functions, result)
        
        assert result.is_valid is False
        assert len(result.errors) == 1
        assert "must be a list" in result.errors[0]
    
    def test_validate_functions_duplicate_ids(self):
        """Test functions validation with duplicate function IDs."""
        functions = [
            {
                'function_id': 'GV',
                'name': 'Govern 1'
            },
            {
                'function_id': 'GV',  # Duplicate
                'name': 'Govern 2'
            }
        ]
        result = ValidationResult(is_valid=True)
        
        self.validator._validate_functions(functions, result)
        
        assert result.is_valid is False
        assert any('Duplicate function ID' in e for e in result.errors)
    
    def test_validate_categories_success(self):
        """Test successful categories validation."""
        categories = [
            {
                'category_id': 'GV.OC',
                'name': 'Organizational Context',
                'subcategories': []
            }
        ]
        result = ValidationResult(is_valid=True)
        
        self.validator._validate_categories(categories, 'test_path', result)
        
        assert result.is_valid is True
        assert len(result.errors) == 0
    
    def test_validate_subcategories_success(self):
        """Test successful subcategories validation."""
        subcategories = [
            {
                'subcategory_id': 'GV.OC-01',
                'name': 'Organizational mission'
            }
        ]
        result = ValidationResult(is_valid=True)
        
        self.validator._validate_subcategories(subcategories, 'test_path', result)
        
        assert result.is_valid is True
        assert len(result.errors) == 0


if __name__ == '__main__':
    pytest.main([__file__])
