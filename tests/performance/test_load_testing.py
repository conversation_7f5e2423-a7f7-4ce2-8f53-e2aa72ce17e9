"""
Performance and load testing for the API.

This module contains comprehensive performance tests including load testing,
stress testing, and performance benchmarking for scalability validation.
"""

import pytest
import asyncio
import time
import statistics
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any
import httpx
from unittest.mock import patch, MagicMock
import json
import random

from api.main import app
from api.services.mapping_algorithms import CrossFrameworkMappingAlgorithm
from api.services.progress_tracker import ProgressTracker
from api.services.validation_service import ISFDataValidator


class PerformanceMetrics:
    """Class to collect and analyze performance metrics."""
    
    def __init__(self):
        self.response_times = []
        self.success_count = 0
        self.error_count = 0
        self.start_time = None
        self.end_time = None
    
    def add_response_time(self, response_time: float, success: bool = True):
        """Add a response time measurement."""
        self.response_times.append(response_time)
        if success:
            self.success_count += 1
        else:
            self.error_count += 1
    
    def start_timing(self):
        """Start timing the test."""
        self.start_time = time.time()
    
    def end_timing(self):
        """End timing the test."""
        self.end_time = time.time()
    
    @property
    def total_requests(self) -> int:
        """Total number of requests."""
        return len(self.response_times)
    
    @property
    def success_rate(self) -> float:
        """Success rate as percentage."""
        if self.total_requests == 0:
            return 0.0
        return (self.success_count / self.total_requests) * 100
    
    @property
    def average_response_time(self) -> float:
        """Average response time in seconds."""
        if not self.response_times:
            return 0.0
        return statistics.mean(self.response_times)
    
    @property
    def median_response_time(self) -> float:
        """Median response time in seconds."""
        if not self.response_times:
            return 0.0
        return statistics.median(self.response_times)
    
    @property
    def p95_response_time(self) -> float:
        """95th percentile response time in seconds."""
        if not self.response_times:
            return 0.0
        sorted_times = sorted(self.response_times)
        index = int(0.95 * len(sorted_times))
        return sorted_times[min(index, len(sorted_times) - 1)]
    
    @property
    def p99_response_time(self) -> float:
        """99th percentile response time in seconds."""
        if not self.response_times:
            return 0.0
        sorted_times = sorted(self.response_times)
        index = int(0.99 * len(sorted_times))
        return sorted_times[min(index, len(sorted_times) - 1)]
    
    @property
    def requests_per_second(self) -> float:
        """Requests per second."""
        if not self.start_time or not self.end_time:
            return 0.0
        duration = self.end_time - self.start_time
        if duration == 0:
            return 0.0
        return self.total_requests / duration
    
    def get_summary(self) -> Dict[str, Any]:
        """Get performance summary."""
        return {
            "total_requests": self.total_requests,
            "success_count": self.success_count,
            "error_count": self.error_count,
            "success_rate": self.success_rate,
            "average_response_time": self.average_response_time,
            "median_response_time": self.median_response_time,
            "p95_response_time": self.p95_response_time,
            "p99_response_time": self.p99_response_time,
            "requests_per_second": self.requests_per_second,
            "total_duration": self.end_time - self.start_time if self.start_time and self.end_time else 0
        }


class TestAPILoadTesting:
    """Load testing for API endpoints."""
    
    @pytest.fixture
    def auth_headers(self):
        """Mock authentication headers."""
        return {"Authorization": "Bearer test-token"}
    
    @pytest.fixture
    def base_url(self):
        """Base URL for testing."""
        return "http://localhost:8000"
    
    async def make_request(self, client: httpx.AsyncClient, method: str, url: str, **kwargs) -> Dict[str, Any]:
        """Make a single HTTP request and measure response time."""
        start_time = time.time()
        try:
            response = await client.request(method, url, **kwargs)
            end_time = time.time()
            return {
                "response_time": end_time - start_time,
                "status_code": response.status_code,
                "success": 200 <= response.status_code < 300
            }
        except Exception as e:
            end_time = time.time()
            return {
                "response_time": end_time - start_time,
                "status_code": 0,
                "success": False,
                "error": str(e)
            }
    
    @pytest.mark.asyncio
    async def test_concurrent_isf_search_requests(self, auth_headers, base_url):
        """Test concurrent ISF search requests."""
        metrics = PerformanceMetrics()
        concurrent_users = 50
        requests_per_user = 10
        
        async with httpx.AsyncClient() as client:
            metrics.start_timing()
            
            # Create tasks for concurrent requests
            tasks = []
            for user in range(concurrent_users):
                for request in range(requests_per_user):
                    task = self.make_request(
                        client,
                        "GET",
                        f"{base_url}/api/v1/isf/search",
                        params={"query": f"security policy {user}", "limit": 10},
                        headers=auth_headers
                    )
                    tasks.append(task)
            
            # Execute all requests concurrently
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            metrics.end_timing()
            
            # Collect metrics
            for result in results:
                if isinstance(result, dict):
                    metrics.add_response_time(result["response_time"], result["success"])
                else:
                    metrics.add_response_time(0.0, False)
        
        # Analyze results
        summary = metrics.get_summary()
        
        # Performance assertions
        assert summary["success_rate"] >= 95.0, f"Success rate too low: {summary['success_rate']}%"
        assert summary["average_response_time"] <= 2.0, f"Average response time too high: {summary['average_response_time']}s"
        assert summary["p95_response_time"] <= 5.0, f"95th percentile response time too high: {summary['p95_response_time']}s"
        assert summary["requests_per_second"] >= 10.0, f"Throughput too low: {summary['requests_per_second']} RPS"
        
        print(f"Load Test Results: {summary}")
    
    @pytest.mark.asyncio
    async def test_mapping_suggestions_performance(self, auth_headers, base_url):
        """Test performance of mapping suggestions under load."""
        metrics = PerformanceMetrics()
        concurrent_requests = 20
        
        # Sample technique IDs for testing
        technique_ids = ["T1566", "T1078", "T1055", "T1003", "T1059"]
        
        async with httpx.AsyncClient() as client:
            metrics.start_timing()
            
            tasks = []
            for i in range(concurrent_requests):
                technique_id = random.choice(technique_ids)
                task = self.make_request(
                    client,
                    "GET",
                    f"{base_url}/api/v1/mappings/mitre-to-isf/{technique_id}/suggestions",
                    headers=auth_headers
                )
                tasks.append(task)
            
            results = await asyncio.gather(*tasks, return_exceptions=True)
            metrics.end_timing()
            
            for result in results:
                if isinstance(result, dict):
                    metrics.add_response_time(result["response_time"], result["success"])
        
        summary = metrics.get_summary()
        
        # Performance assertions for mapping suggestions (more complex operation)
        assert summary["success_rate"] >= 90.0, f"Success rate too low: {summary['success_rate']}%"
        assert summary["average_response_time"] <= 5.0, f"Average response time too high: {summary['average_response_time']}s"
        assert summary["p95_response_time"] <= 10.0, f"95th percentile response time too high: {summary['p95_response_time']}s"
        
        print(f"Mapping Suggestions Performance: {summary}")
    
    @pytest.mark.asyncio
    async def test_bulk_operations_performance(self, auth_headers, base_url):
        """Test performance of bulk operations."""
        metrics = PerformanceMetrics()
        
        # Test bulk mapping suggestions
        bulk_request_data = {
            "technique_ids": ["T1566", "T1078", "T1055", "T1003", "T1059"] * 10,  # 50 techniques
            "target_framework": "isf",
            "options": {
                "min_confidence": 0.7,
                "max_suggestions": 5
            }
        }
        
        async with httpx.AsyncClient(timeout=30.0) as client:
            metrics.start_timing()
            
            result = await self.make_request(
                client,
                "POST",
                f"{base_url}/api/v1/mappings/suggestions/bulk",
                json=bulk_request_data,
                headers=auth_headers
            )
            
            metrics.end_timing()
            metrics.add_response_time(result["response_time"], result["success"])
        
        summary = metrics.get_summary()
        
        # Bulk operations should complete within reasonable time
        assert summary["success_rate"] >= 95.0, f"Bulk operation failed: {summary['success_rate']}%"
        assert summary["average_response_time"] <= 30.0, f"Bulk operation too slow: {summary['average_response_time']}s"
        
        print(f"Bulk Operations Performance: {summary}")


class TestMemoryPerformance:
    """Memory usage and performance testing."""
    
    def test_large_dataset_processing(self):
        """Test memory usage with large datasets."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB
        
        # Create large dataset
        large_dataset = []
        for i in range(10000):
            large_dataset.append({
                "control_id": f"C{i:05d}",
                "name": f"Control {i}",
                "description": f"Description for control {i} " * 50,  # Long description
                "control_type": "technical",
                "maturity_level": "intermediate"
            })
        
        # Process dataset
        validator = ISFDataValidator()
        for item in large_dataset:
            # Simulate processing
            pass
        
        peak_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = peak_memory - initial_memory
        
        # Memory usage should be reasonable
        assert memory_increase <= 500, f"Memory usage too high: {memory_increase}MB"
        
        print(f"Memory usage: {initial_memory:.2f}MB -> {peak_memory:.2f}MB (+{memory_increase:.2f}MB)")
    
    def test_mapping_algorithm_performance(self):
        """Test mapping algorithm performance with large datasets."""
        algorithm = CrossFrameworkMappingAlgorithm()
        
        # Create test technique
        technique = {
            "technique_id": "T1566",
            "name": "Phishing",
            "description": "Adversaries may send phishing messages to gain access to victim systems."
        }
        
        # Create large control dataset
        controls = []
        for i in range(1000):
            controls.append({
                "control_id": f"C{i:04d}",
                "name": f"Control {i}",
                "description": f"Security control {i} for protection against various threats",
                "control_type": "technical"
            })
        
        start_time = time.time()
        suggestions = algorithm.generate_suggestions(technique, controls)
        end_time = time.time()
        
        processing_time = end_time - start_time
        
        # Should process 1000 controls in reasonable time
        assert processing_time <= 10.0, f"Algorithm too slow: {processing_time:.2f}s"
        assert len(suggestions) > 0, "No suggestions generated"
        
        print(f"Mapping algorithm processed 1000 controls in {processing_time:.2f}s")


class TestConcurrencyPerformance:
    """Concurrency and thread safety testing."""
    
    def test_progress_tracker_concurrency(self):
        """Test progress tracker under concurrent load."""
        tracker = ProgressTracker()
        metrics = PerformanceMetrics()
        
        def worker_function(worker_id: int):
            """Worker function for concurrent testing."""
            task_id = tracker.create_task(total_items=100)
            tracker.start_task(task_id, f"Worker {worker_id} task")
            
            # Simulate work with progress updates
            for i in range(10):
                tracker.update_progress(
                    task_id=task_id,
                    stage="processing",
                    progress_percentage=i * 10,
                    message=f"Worker {worker_id} progress {i * 10}%"
                )
                time.sleep(0.01)  # Simulate work
            
            tracker.complete_task(task_id, f"Worker {worker_id} completed")
            return task_id
        
        # Run concurrent workers
        num_workers = 20
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_workers) as executor:
            futures = [executor.submit(worker_function, i) for i in range(num_workers)]
            task_ids = [future.result() for future in as_completed(futures)]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Verify all tasks completed successfully
        assert len(task_ids) == num_workers
        
        for task_id in task_ids:
            progress = tracker.get_progress(task_id)
            assert progress["status"] == "completed"
            assert progress["progress_percentage"] == 100.0
        
        # Performance assertions
        assert total_time <= 5.0, f"Concurrent operations too slow: {total_time:.2f}s"
        
        print(f"Concurrent progress tracking: {num_workers} workers in {total_time:.2f}s")
    
    def test_database_connection_pool_performance(self):
        """Test database connection pool under load."""
        from api.core.database import get_db
        
        def database_operation(operation_id: int):
            """Simulate database operation."""
            start_time = time.time()
            try:
                db = next(get_db())
                # Simulate database query
                time.sleep(0.1)  # Simulate query time
                return time.time() - start_time
            except Exception as e:
                return None
        
        # Run concurrent database operations
        num_operations = 50
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(database_operation, i) for i in range(num_operations)]
            results = [future.result() for future in as_completed(futures)]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Analyze results
        successful_operations = [r for r in results if r is not None]
        success_rate = len(successful_operations) / len(results) * 100
        
        if successful_operations:
            avg_operation_time = statistics.mean(successful_operations)
        else:
            avg_operation_time = 0
        
        # Performance assertions
        assert success_rate >= 95.0, f"Database operation success rate too low: {success_rate}%"
        assert avg_operation_time <= 1.0, f"Database operations too slow: {avg_operation_time:.2f}s"
        
        print(f"Database operations: {success_rate:.1f}% success, avg {avg_operation_time:.3f}s")


class TestStressTesting:
    """Stress testing to find system limits."""
    
    def test_memory_stress(self):
        """Test system behavior under memory stress."""
        import gc
        
        # Create increasingly large datasets until memory pressure
        datasets = []
        max_iterations = 100
        
        for i in range(max_iterations):
            try:
                # Create large dataset
                dataset = [{"data": "x" * 1000} for _ in range(1000)]
                datasets.append(dataset)
                
                # Force garbage collection
                gc.collect()
                
                # Check if we can still allocate memory
                test_allocation = [0] * 1000
                del test_allocation
                
            except MemoryError:
                print(f"Memory limit reached at iteration {i}")
                break
        
        # Clean up
        datasets.clear()
        gc.collect()
        
        # Should handle at least some iterations before memory pressure
        assert len(datasets) > 10, "System ran out of memory too quickly"
    
    def test_cpu_stress(self):
        """Test system behavior under CPU stress."""
        import multiprocessing
        
        def cpu_intensive_task(duration: float):
            """CPU-intensive task for stress testing."""
            start_time = time.time()
            count = 0
            while time.time() - start_time < duration:
                count += 1
                # CPU-intensive operation
                _ = sum(i * i for i in range(1000))
            return count
        
        # Run CPU-intensive tasks on all cores
        num_cores = multiprocessing.cpu_count()
        task_duration = 2.0  # seconds
        
        start_time = time.time()
        
        with ThreadPoolExecutor(max_workers=num_cores) as executor:
            futures = [executor.submit(cpu_intensive_task, task_duration) for _ in range(num_cores)]
            results = [future.result() for future in as_completed(futures)]
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # Should complete within reasonable time even under stress
        assert total_time <= task_duration * 1.5, f"CPU stress test took too long: {total_time:.2f}s"
        assert all(r > 0 for r in results), "Some CPU tasks failed to complete"
        
        print(f"CPU stress test: {num_cores} cores, {total_time:.2f}s")


if __name__ == '__main__':
    pytest.main([__file__, "-v", "-s"])
