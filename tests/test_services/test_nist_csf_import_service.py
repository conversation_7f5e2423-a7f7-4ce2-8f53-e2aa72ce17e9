"""
Test-Driven Development tests for NIST CSF Import Service.

This module contains comprehensive failing tests that drive the implementation
of NIST Cybersecurity Framework 2.0 import service following TDD red-green-refactor cycle.

Tests are designed to fail initially and guide the implementation of:
- NIST CSF hierarchical data processing (Functions → Categories → Subcategories)
- Version management and migration (1.1 to 2.0)
- Implementation examples and informative references import
- Data transformation and normalization
- Hierarchical validation and integrity checks
"""

import pytest
import json
import csv
from datetime import datetime
from typing import Dict, List, Any, Optional
from unittest.mock import Mock, patch
from sqlalchemy.orm import Session

# These imports will fail initially - that's expected in TDD
try:
    from api.services.nist_csf_import_service import (
        NISTCSFImportService,
        NISTCSFDataParser,
        NISTCSFDataValidator,
        NISTCSFImportResult,
        NISTCSFImportError,
        NISTCSFHierarchyBuilder,
        NISTCSFVersionMigrator
    )
    from api.models.nist_csf import (
        NISTCSFVersion,
        NISTCSFFunction,
        NISTCSFCategory,
        NISTCSFSubcategory,
        NISTCSFImplementationExample,
        NISTCSFInformativeReference
    )
except ImportError:
    # Expected to fail initially in TDD approach
    NISTCSFImportService = None
    NISTCSFDataParser = None
    NISTCSFDataValidator = None
    NISTCSFImportResult = None
    NISTCSFImportError = None
    NISTCSFHierarchyBuilder = None
    NISTCSFVersionMigrator = None
    NISTCSFVersion = None
    NISTCSFFunction = None
    NISTCSFCategory = None
    NISTCSFSubcategory = None
    NISTCSFImplementationExample = None
    NISTCSFInformativeReference = None


@pytest.fixture
def sample_nist_csf_json_data():
    """Sample NIST CSF 2.0 framework data in JSON format."""
    return {
        "version": "2.0",
        "release_date": "2024-02-26",
        "description": "NIST Cybersecurity Framework Version 2.0",
        "url": "https://nvlpubs.nist.gov/nistpubs/CSWP/NIST.CSWP.29.pdf",
        "functions": [
            {
                "function_id": "GV",
                "name": "Govern",
                "description": "The organization's cybersecurity risk management strategy, expectations, and policy are established, communicated, and monitored.",
                "order_index": 1,
                "categories": [
                    {
                        "category_id": "GV.OC",
                        "name": "Organizational Context",
                        "description": "The circumstances that frame the organization's risk management decisions are understood.",
                        "order_index": 1,
                        "subcategories": [
                            {
                                "subcategory_id": "GV.OC-01",
                                "name": "Organizational mission is understood and informs cybersecurity risk management",
                                "description": "The organization's mission, objectives, stakeholders, and activities are understood and used to inform cybersecurity roles, responsibilities, and risk management decisions.",
                                "order_index": 1,
                                "implementation_examples": [
                                    {
                                        "example_text": "Establish and communicate organizational mission and objectives",
                                        "example_type": "organizational",
                                        "order_index": 1
                                    },
                                    {
                                        "example_text": "Document stakeholder expectations and requirements",
                                        "example_type": "documentation",
                                        "order_index": 2
                                    }
                                ],
                                "informative_references": [
                                    {
                                        "framework_name": "ISO/IEC 27001:2022",
                                        "reference_id": "5.1",
                                        "description": "Leadership and commitment",
                                        "url": "https://www.iso.org/standard/27001"
                                    },
                                    {
                                        "framework_name": "NIST SP 800-53 Rev. 5",
                                        "reference_id": "PM-1",
                                        "description": "Information Security Program Plan"
                                    }
                                ]
                            }
                        ]
                    }
                ]
            },
            {
                "function_id": "ID",
                "name": "Identify",
                "description": "The organization's current cybersecurity posture is understood.",
                "order_index": 2,
                "categories": [
                    {
                        "category_id": "ID.AM",
                        "name": "Asset Management",
                        "description": "Assets are identified and managed consistent with their relative importance.",
                        "order_index": 1,
                        "subcategories": [
                            {
                                "subcategory_id": "ID.AM-01",
                                "name": "Physical devices and systems are inventoried",
                                "description": "Inventories of physical devices and systems within the organization are maintained.",
                                "order_index": 1,
                                "implementation_examples": [
                                    {
                                        "example_text": "Maintain hardware asset inventory with automated discovery tools",
                                        "example_type": "technical",
                                        "order_index": 1
                                    }
                                ],
                                "informative_references": [
                                    {
                                        "framework_name": "CIS Controls v8",
                                        "reference_id": "1.1",
                                        "description": "Establish and Maintain Detailed Enterprise Asset Inventory"
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    }


@pytest.fixture
def sample_nist_csf_csv_data():
    """Sample NIST CSF framework data in CSV format."""
    csv_data = """function_id,function_name,category_id,category_name,subcategory_id,subcategory_name,subcategory_description
GV,Govern,GV.OC,Organizational Context,GV.OC-01,Organizational mission is understood and informs cybersecurity risk management,The organization's mission and objectives are understood and used to inform cybersecurity decisions
GV,Govern,GV.OC,Organizational Context,GV.OC-02,Internal and external stakeholders are understood,Stakeholder expectations and requirements are identified and documented
ID,Identify,ID.AM,Asset Management,ID.AM-01,Physical devices and systems are inventoried,Inventories of physical devices and systems within the organization are maintained
ID,Identify,ID.AM,Asset Management,ID.AM-02,Software platforms and applications are inventoried,Inventories of software platforms and applications within the organization are maintained"""
    return csv_data


@pytest.mark.unit
@pytest.mark.nist_csf_import
class TestNISTCSFDataParser:
    """Test NIST CSF data parsing functionality."""
    
    def test_parse_json_hierarchical_data_should_fail_initially(self, sample_nist_csf_json_data):
        """Test parsing NIST CSF hierarchical data from JSON - should fail until parser is implemented."""
        if NISTCSFDataParser is None:
            pytest.skip("NISTCSFDataParser not implemented yet - TDD red phase")
            
        parser = NISTCSFDataParser()
        result = parser.parse_json(json.dumps(sample_nist_csf_json_data))
        
        # Assertions that drive implementation requirements
        assert result is not None
        assert result.version == "2.0"
        assert result.release_date == datetime(2024, 2, 26)
        assert len(result.functions) == 2
        
        # Test Govern function
        govern_function = result.functions[0]
        assert govern_function.function_id == "GV"
        assert govern_function.name == "Govern"
        assert len(govern_function.categories) == 1
        
        # Test Organizational Context category
        oc_category = govern_function.categories[0]
        assert oc_category.category_id == "GV.OC"
        assert oc_category.name == "Organizational Context"
        assert len(oc_category.subcategories) == 1
        
        # Test subcategory with implementation examples and references
        subcategory = oc_category.subcategories[0]
        assert subcategory.subcategory_id == "GV.OC-01"
        assert len(subcategory.implementation_examples) == 2
        assert len(subcategory.informative_references) == 2
        
        # Test implementation examples
        example = subcategory.implementation_examples[0]
        assert example.example_type == "organizational"
        assert "mission and objectives" in example.example_text
        
        # Test informative references
        reference = subcategory.informative_references[0]
        assert reference.framework_name == "ISO/IEC 27001:2022"
        assert reference.reference_id == "5.1"
    
    def test_parse_csv_flat_to_hierarchical_should_fail_initially(self, sample_nist_csf_csv_data):
        """Test parsing flat CSV data and building hierarchy - should fail until hierarchy builder is implemented."""
        if NISTCSFDataParser is None:
            pytest.skip("NISTCSFDataParser not implemented yet - TDD red phase")
            
        parser = NISTCSFDataParser()
        result = parser.parse_csv(sample_nist_csf_csv_data, version="2.0")
        
        # Assertions that drive implementation requirements
        assert result is not None
        assert result.version == "2.0"
        assert len(result.functions) == 2
        
        # Verify hierarchy was built correctly from flat data
        govern_function = next(f for f in result.functions if f.function_id == "GV")
        assert len(govern_function.categories) == 1
        assert govern_function.categories[0].category_id == "GV.OC"
        assert len(govern_function.categories[0].subcategories) == 2
        
        identify_function = next(f for f in result.functions if f.function_id == "ID")
        assert len(identify_function.categories) == 1
        assert identify_function.categories[0].category_id == "ID.AM"
        assert len(identify_function.categories[0].subcategories) == 2
    
    def test_parse_nist_official_format_should_fail_initially(self):
        """Test parsing official NIST CSF format - should fail until official format parser is implemented."""
        if NISTCSFDataParser is None:
            pytest.skip("NISTCSFDataParser not implemented yet - TDD red phase")
            
        # Simulate official NIST CSF format structure
        official_format = {
            "Framework": {
                "Version": "2.0",
                "Functions": {
                    "GV": {
                        "Name": "Govern",
                        "Categories": {
                            "GV.OC": {
                                "Name": "Organizational Context",
                                "Subcategories": {
                                    "GV.OC-01": {
                                        "Outcome": "Organizational mission is understood and informs cybersecurity risk management"
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
        
        parser = NISTCSFDataParser()
        result = parser.parse_official_format(json.dumps(official_format))
        
        # Assertions that drive implementation requirements
        assert result is not None
        assert result.version == "2.0"
        assert len(result.functions) == 1
        assert result.functions[0].function_id == "GV"


@pytest.mark.unit
@pytest.mark.nist_csf_import
class TestNISTCSFHierarchyBuilder:
    """Test NIST CSF hierarchy building functionality."""
    
    def test_build_hierarchy_from_flat_data_should_fail_initially(self):
        """Test building hierarchy from flat data - should fail until hierarchy builder is implemented."""
        if NISTCSFHierarchyBuilder is None:
            pytest.skip("NISTCSFHierarchyBuilder not implemented yet - TDD red phase")
            
        flat_data = [
            {
                "function_id": "GV", "function_name": "Govern",
                "category_id": "GV.OC", "category_name": "Organizational Context",
                "subcategory_id": "GV.OC-01", "subcategory_name": "Mission understanding"
            },
            {
                "function_id": "GV", "function_name": "Govern",
                "category_id": "GV.OC", "category_name": "Organizational Context",
                "subcategory_id": "GV.OC-02", "subcategory_name": "Stakeholder understanding"
            },
            {
                "function_id": "GV", "function_name": "Govern",
                "category_id": "GV.RM", "category_name": "Risk Management Strategy",
                "subcategory_id": "GV.RM-01", "subcategory_name": "Risk management processes"
            }
        ]
        
        builder = NISTCSFHierarchyBuilder()
        hierarchy = builder.build_from_flat_data(flat_data)
        
        # Assertions that drive implementation requirements
        assert len(hierarchy.functions) == 1
        govern_function = hierarchy.functions[0]
        assert govern_function.function_id == "GV"
        assert len(govern_function.categories) == 2
        
        # Test first category
        oc_category = next(c for c in govern_function.categories if c.category_id == "GV.OC")
        assert len(oc_category.subcategories) == 2
        
        # Test second category
        rm_category = next(c for c in govern_function.categories if c.category_id == "GV.RM")
        assert len(rm_category.subcategories) == 1
    
    def test_validate_hierarchy_integrity_should_fail_initially(self):
        """Test validating hierarchy integrity - should fail until validation is implemented."""
        if NISTCSFHierarchyBuilder is None:
            pytest.skip("NISTCSFHierarchyBuilder not implemented yet - TDD red phase")
            
        # Create hierarchy with integrity issues
        invalid_hierarchy_data = [
            {
                "function_id": "GV", "function_name": "Govern",
                "category_id": "ID.AM", "category_name": "Asset Management",  # Wrong function prefix
                "subcategory_id": "ID.AM-01", "subcategory_name": "Asset inventory"
            }
        ]
        
        builder = NISTCSFHierarchyBuilder()
        
        with pytest.raises(NISTCSFImportError) as exc_info:
            builder.build_from_flat_data(invalid_hierarchy_data)
        
        assert "Category ID prefix mismatch" in str(exc_info.value)
        assert exc_info.value.error_type == "HIERARCHY_ERROR"


@pytest.mark.unit
@pytest.mark.nist_csf_import
class TestNISTCSFDataValidator:
    """Test NIST CSF data validation functionality."""
    
    def test_validate_function_structure_should_fail_initially(self):
        """Test validating NIST CSF function structure - should fail until validator is implemented."""
        if NISTCSFDataValidator is None:
            pytest.skip("NISTCSFDataValidator not implemented yet - TDD red phase")
            
        valid_functions = ["GV", "ID", "PR", "DE", "RS", "RC"]
        invalid_function_data = {
            "version": "2.0",
            "functions": [
                {
                    "function_id": "XX",  # Invalid function ID
                    "name": "Invalid Function",
                    "categories": []
                }
            ]
        }
        
        validator = NISTCSFDataValidator()
        is_valid, errors = validator.validate_framework_structure(invalid_function_data)
        
        # Assertions that drive implementation requirements
        assert is_valid is False
        assert len(errors) > 0
        assert any("Invalid function_id: XX" in error for error in errors)
    
    def test_validate_id_format_consistency_should_fail_initially(self):
        """Test validating ID format consistency - should fail until format validation is implemented."""
        if NISTCSFDataValidator is None:
            pytest.skip("NISTCSFDataValidator not implemented yet - TDD red phase")
            
        inconsistent_data = {
            "version": "2.0",
            "functions": [
                {
                    "function_id": "GV",
                    "name": "Govern",
                    "categories": [
                        {
                            "category_id": "GV.OC",
                            "name": "Organizational Context",
                            "subcategories": [
                                {
                                    "subcategory_id": "ID.AM-01",  # Wrong function prefix
                                    "name": "Mismatched subcategory"
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        
        validator = NISTCSFDataValidator()
        is_valid, errors = validator.validate_framework_structure(inconsistent_data)
        
        # Assertions that drive implementation requirements
        assert is_valid is False
        assert len(errors) > 0
        assert any("Subcategory ID prefix mismatch" in error for error in errors)
    
    def test_validate_required_fields_should_fail_initially(self):
        """Test validating required fields - should fail until field validation is implemented."""
        if NISTCSFDataValidator is None:
            pytest.skip("NISTCSFDataValidator not implemented yet - TDD red phase")
            
        incomplete_data = {
            "version": "2.0",
            "functions": [
                {
                    "function_id": "GV",
                    # Missing required 'name' field
                    "categories": []
                }
            ]
        }
        
        validator = NISTCSFDataValidator()
        is_valid, errors = validator.validate_framework_structure(incomplete_data)
        
        # Assertions that drive implementation requirements
        assert is_valid is False
        assert len(errors) > 0
        assert any("Missing required field: name" in error for error in errors)


@pytest.mark.unit
@pytest.mark.nist_csf_import
class TestNISTCSFVersionMigrator:
    """Test NIST CSF version migration functionality."""
    
    def test_migrate_from_v1_1_to_v2_0_should_fail_initially(self, db_session: Session):
        """Test migrating from CSF 1.1 to 2.0 - should fail until migrator is implemented."""
        if NISTCSFVersionMigrator is None:
            pytest.skip("NISTCSFVersionMigrator not implemented yet - TDD red phase")
            
        # Create CSF 1.1 data structure (5 functions, no Govern)
        csf_v11_data = {
            "version": "1.1",
            "functions": [
                {"function_id": "ID", "name": "Identify"},
                {"function_id": "PR", "name": "Protect"},
                {"function_id": "DE", "name": "Detect"},
                {"function_id": "RS", "name": "Respond"},
                {"function_id": "RC", "name": "Recover"}
            ]
        }
        
        migrator = NISTCSFVersionMigrator(db_session)
        migration_result = migrator.migrate_to_v2_0(csf_v11_data)
        
        # Assertions that drive implementation requirements
        assert migration_result.success is True
        assert migration_result.source_version == "1.1"
        assert migration_result.target_version == "2.0"
        assert len(migration_result.migrated_functions) == 6  # Added Govern function
        assert "GV" in [f.function_id for f in migration_result.migrated_functions]
        assert len(migration_result.migration_notes) > 0
    
    def test_map_v1_1_subcategories_to_v2_0_should_fail_initially(self):
        """Test mapping v1.1 subcategories to v2.0 - should fail until mapping is implemented."""
        if NISTCSFVersionMigrator is None:
            pytest.skip("NISTCSFVersionMigrator not implemented yet - TDD red phase")
            
        v11_subcategory = {
            "subcategory_id": "ID.AM-1",  # v1.1 format
            "name": "Physical devices and systems within the organization are inventoried"
        }
        
        migrator = NISTCSFVersionMigrator(None)
        v20_mapping = migrator.map_subcategory_v11_to_v20(v11_subcategory)
        
        # Assertions that drive implementation requirements
        assert v20_mapping is not None
        assert v20_mapping["subcategory_id"] == "ID.AM-01"  # v2.0 format
        assert v20_mapping["migration_confidence"] >= 0.8  # High confidence mapping
        assert "migration_notes" in v20_mapping


@pytest.mark.unit
@pytest.mark.nist_csf_import
class TestNISTCSFImportService:
    """Test NIST CSF import service functionality."""
    
    def test_import_csf_v2_0_complete_hierarchy_should_fail_initially(self, db_session: Session, sample_nist_csf_json_data):
        """Test importing complete NIST CSF 2.0 hierarchy - should fail until service is implemented."""
        if NISTCSFImportService is None:
            pytest.skip("NISTCSFImportService not implemented yet - TDD red phase")
            
        service = NISTCSFImportService(db_session)
        result = service.import_from_json(json.dumps(sample_nist_csf_json_data))
        
        # Assertions that drive implementation requirements
        assert isinstance(result, NISTCSFImportResult)
        assert result.success is True
        assert result.version_id is not None
        assert result.imported_functions == 2
        assert result.imported_categories == 2
        assert result.imported_subcategories == 2
        assert result.imported_implementation_examples == 3
        assert result.imported_informative_references == 3
        
        # Verify hierarchical relationships in database
        version = db_session.query(NISTCSFVersion).filter_by(version="2.0").first()
        assert version is not None
        assert len(version.functions) == 2
        
        govern_function = next(f for f in version.functions if f.function_id == "GV")
        assert len(govern_function.categories) == 1
        assert len(govern_function.subcategories) == 1
    
    def test_import_with_version_supersession_should_fail_initially(self, db_session: Session):
        """Test importing with version supersession - should fail until supersession is implemented."""
        if NISTCSFImportService is None or NISTCSFVersion is None:
            pytest.skip("NIST CSF models not implemented yet - TDD red phase")
            
        # Create existing v1.1
        v11_version = NISTCSFVersion(version="1.1", is_current=True)
        db_session.add(v11_version)
        db_session.commit()
        
        v20_data = {
            "version": "2.0",
            "supersedes_version": "1.1",
            "functions": [{"function_id": "GV", "name": "Govern", "categories": []}]
        }
        
        service = NISTCSFImportService(db_session)
        result = service.import_from_json(json.dumps(v20_data))
        
        # Assertions that drive implementation requirements
        assert result.success is True
        assert result.superseded_version_id == v11_version.id
        
        # Verify supersession relationship
        db_session.refresh(v11_version)
        v20_version = db_session.query(NISTCSFVersion).filter_by(version="2.0").first()
        assert v20_version.supersedes_version_id == v11_version.id
        assert v11_version.superseded_by_version == v20_version
        assert v11_version.is_current is False
        assert v20_version.is_current is True
    
    def test_import_implementation_examples_and_references_should_fail_initially(self, db_session: Session):
        """Test importing implementation examples and informative references - should fail until detailed import is implemented."""
        if NISTCSFImportService is None:
            pytest.skip("NISTCSFImportService not implemented yet - TDD red phase")
            
        detailed_data = {
            "version": "2.0",
            "functions": [
                {
                    "function_id": "GV",
                    "name": "Govern",
                    "categories": [
                        {
                            "category_id": "GV.OC",
                            "name": "Organizational Context",
                            "subcategories": [
                                {
                                    "subcategory_id": "GV.OC-01",
                                    "name": "Mission understanding",
                                    "implementation_examples": [
                                        {"example_text": "Example 1", "example_type": "organizational"},
                                        {"example_text": "Example 2", "example_type": "technical"}
                                    ],
                                    "informative_references": [
                                        {"framework_name": "ISO 27001", "reference_id": "5.1"},
                                        {"framework_name": "NIST SP 800-53", "reference_id": "PM-1"}
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        
        service = NISTCSFImportService(db_session)
        result = service.import_from_json(json.dumps(detailed_data))
        
        # Assertions that drive implementation requirements
        assert result.success is True
        assert result.imported_implementation_examples == 2
        assert result.imported_informative_references == 2
        
        # Verify detailed data in database
        subcategory = db_session.query(NISTCSFSubcategory).filter_by(subcategory_id="GV.OC-01").first()
        assert subcategory is not None
        assert len(subcategory.implementation_examples) == 2
        assert len(subcategory.informative_references) == 2
    
    def test_import_hierarchy_validation_should_fail_initially(self, db_session: Session):
        """Test hierarchy validation during import - should fail until validation is implemented."""
        if NISTCSFImportService is None:
            pytest.skip("NISTCSFImportService not implemented yet - TDD red phase")
            
        # Create data with broken hierarchy
        broken_hierarchy_data = {
            "version": "2.0",
            "functions": [
                {
                    "function_id": "GV",
                    "name": "Govern",
                    "categories": [
                        {
                            "category_id": "ID.AM",  # Wrong function prefix
                            "name": "Asset Management",
                            "subcategories": []
                        }
                    ]
                }
            ]
        }
        
        service = NISTCSFImportService(db_session)
        result = service.import_from_json(json.dumps(broken_hierarchy_data))
        
        # Assertions that drive implementation requirements
        assert result.success is False
        assert len(result.errors) > 0
        assert any("hierarchy" in error.lower() for error in result.errors)
        
        # Verify no data was imported due to validation failure
        version = db_session.query(NISTCSFVersion).filter_by(version="2.0").first()
        assert version is None
