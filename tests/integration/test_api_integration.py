"""
Integration tests for API endpoints.

This module contains comprehensive integration tests that test the full API
stack including authentication, database operations, and service interactions.
"""

import pytest
import asyncio
from httpx import AsyncClient
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
import json
from datetime import datetime, timed<PERSON>ta
from typing import Dict, Any

from api.main import app
from api.core.database import get_db
from api.auth.dependencies import get_current_user
from api.models.user import User
from api.models.isf import ISFVersion, SecurityArea, Control
from api.models.nist_csf import NISTCSFVersion, Function, Category, Subcategory
from api.models.mapping import Mapping


# Test fixtures
@pytest.fixture
def test_client():
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def mock_db():
    """Mock database session."""
    db = MagicMock()
    return db


@pytest.fixture
def mock_user():
    """Mock authenticated user."""
    user = User(
        id=1,
        username="test_user",
        email="<EMAIL>",
        role="analyst",
        is_active=True
    )
    return user


@pytest.fixture
def auth_headers():
    """Authentication headers for requests."""
    return {"Authorization": "Bearer test-token"}


@pytest.fixture
def sample_isf_data():
    """Sample ISF framework data for testing."""
    return {
        "version": "2020.1",
        "description": "Test ISF framework",
        "security_areas": [
            {
                "area_id": "SG",
                "name": "Security Governance",
                "description": "Security governance controls",
                "controls": [
                    {
                        "control_id": "SG1",
                        "name": "Security Policy",
                        "description": "Establish security policy",
                        "control_type": "policy",
                        "maturity_level": "basic"
                    }
                ]
            }
        ]
    }


@pytest.fixture
def sample_nist_csf_data():
    """Sample NIST CSF framework data for testing."""
    return {
        "version": "2.0",
        "description": "Test NIST CSF framework",
        "functions": [
            {
                "function_id": "GV",
                "name": "Govern",
                "description": "Governance function",
                "categories": [
                    {
                        "category_id": "GV.OC",
                        "name": "Organizational Context",
                        "description": "Organizational context category",
                        "subcategories": [
                            {
                                "subcategory_id": "GV.OC-01",
                                "name": "Organizational mission",
                                "description": "Organizational mission subcategory"
                            }
                        ]
                    }
                ]
            }
        ]
    }


class TestISFAPIIntegration:
    """Integration tests for ISF API endpoints."""
    
    def test_isf_import_json_success(self, test_client, mock_db, mock_user, auth_headers, sample_isf_data):
        """Test successful ISF JSON import."""
        # Mock dependencies
        app.dependency_overrides[get_db] = lambda: mock_db
        app.dependency_overrides[get_current_user] = lambda: mock_user
        
        # Mock database operations
        mock_db.query.return_value.filter.return_value.first.return_value = None
        mock_db.add = MagicMock()
        mock_db.commit = MagicMock()
        mock_db.refresh = MagicMock()
        
        # Make request
        response = test_client.post(
            "/api/v1/isf/import",
            json={
                "format": "json",
                "data": json.dumps(sample_isf_data),
                "replace_existing": False
            },
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert "version_id" in data
        assert data["imported_security_areas"] == 1
        assert data["imported_controls"] == 1
        
        # Verify database operations
        mock_db.add.assert_called()
        mock_db.commit.assert_called()
        
        # Clean up
        app.dependency_overrides.clear()
    
    def test_isf_import_validation_error(self, test_client, mock_user, auth_headers):
        """Test ISF import with validation errors."""
        app.dependency_overrides[get_current_user] = lambda: mock_user
        
        invalid_data = {
            "version": "invalid",  # Invalid version format
            "security_areas": "not a list"  # Should be a list
        }
        
        response = test_client.post(
            "/api/v1/isf/import",
            json={
                "format": "json",
                "data": json.dumps(invalid_data)
            },
            headers=auth_headers
        )
        
        assert response.status_code == 400
        data = response.json()
        assert data["success"] is False
        assert len(data["errors"]) > 0
        
        app.dependency_overrides.clear()
    
    def test_isf_export_json(self, test_client, mock_db, mock_user, auth_headers):
        """Test ISF JSON export."""
        app.dependency_overrides[get_db] = lambda: mock_db
        app.dependency_overrides[get_current_user] = lambda: mock_user
        
        # Mock database data
        mock_version = ISFVersion(
            id=1,
            version="2020.1",
            description="Test version"
        )
        mock_area = SecurityArea(
            id=1,
            area_id="SG",
            name="Security Governance",
            version_id=1
        )
        mock_control = Control(
            id=1,
            control_id="SG1",
            name="Security Policy",
            area_id=1
        )
        
        mock_db.query.return_value.filter.return_value.first.return_value = mock_version
        mock_db.query.return_value.filter.return_value.all.return_value = [mock_area]
        mock_area.controls = [mock_control]
        
        response = test_client.post(
            "/api/v1/isf/export",
            json={
                "format": "json",
                "version_id": 1
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "download_url" in data
        assert "metadata" in data
        
        app.dependency_overrides.clear()
    
    def test_isf_search(self, test_client, mock_db, mock_user, auth_headers):
        """Test ISF search functionality."""
        app.dependency_overrides[get_db] = lambda: mock_db
        app.dependency_overrides[get_current_user] = lambda: mock_user
        
        # Mock search results
        mock_controls = [
            Control(
                id=1,
                control_id="SG1",
                name="Security Policy",
                description="Security policy control"
            )
        ]
        
        mock_db.query.return_value.filter.return_value.limit.return_value.offset.return_value.all.return_value = mock_controls
        mock_db.query.return_value.filter.return_value.count.return_value = 1
        
        response = test_client.get(
            "/api/v1/isf/search",
            params={
                "query": "security policy",
                "limit": 10,
                "offset": 0
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "results" in data
        assert "total_count" in data
        assert len(data["results"]) == 1
        
        app.dependency_overrides.clear()


class TestNISTCSFAPIIntegration:
    """Integration tests for NIST CSF API endpoints."""
    
    def test_nist_csf_import_success(self, test_client, mock_db, mock_user, auth_headers, sample_nist_csf_data):
        """Test successful NIST CSF import."""
        app.dependency_overrides[get_db] = lambda: mock_db
        app.dependency_overrides[get_current_user] = lambda: mock_user
        
        # Mock database operations
        mock_db.query.return_value.filter.return_value.first.return_value = None
        mock_db.add = MagicMock()
        mock_db.commit = MagicMock()
        mock_db.refresh = MagicMock()
        
        response = test_client.post(
            "/api/v1/nist-csf/import",
            json={
                "format": "json",
                "data": json.dumps(sample_nist_csf_data)
            },
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["success"] is True
        assert "version_id" in data
        assert data["imported_functions"] == 1
        assert data["imported_categories"] == 1
        assert data["imported_subcategories"] == 1
        
        app.dependency_overrides.clear()
    
    def test_nist_csf_migration(self, test_client, mock_db, mock_user, auth_headers):
        """Test NIST CSF version migration."""
        app.dependency_overrides[get_db] = lambda: mock_db
        app.dependency_overrides[get_current_user] = lambda: mock_user
        
        # Mock source version
        mock_source_version = NISTCSFVersion(
            id=1,
            version="1.1",
            description="NIST CSF 1.1"
        )
        mock_db.query.return_value.filter.return_value.first.return_value = mock_source_version
        
        response = test_client.post(
            "/api/v1/nist-csf/migrate",
            json={
                "source_version": "1.1",
                "target_version": "2.0"
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert data["source_version"] == "1.1"
        assert data["target_version"] == "2.0"
        assert "migration_notes" in data
        
        app.dependency_overrides.clear()
    
    def test_nist_csf_functions_list(self, test_client, mock_db, mock_user, auth_headers):
        """Test listing NIST CSF functions."""
        app.dependency_overrides[get_db] = lambda: mock_db
        app.dependency_overrides[get_current_user] = lambda: mock_user
        
        # Mock functions
        mock_functions = [
            Function(
                id=1,
                function_id="GV",
                name="Govern",
                description="Governance function"
            )
        ]
        
        mock_db.query.return_value.filter.return_value.all.return_value = mock_functions
        
        response = test_client.get(
            "/api/v1/nist-csf/functions",
            params={"version": "2.0"},
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]["function_id"] == "GV"
        assert data[0]["name"] == "Govern"
        
        app.dependency_overrides.clear()


class TestMappingAPIIntegration:
    """Integration tests for mapping API endpoints."""
    
    def test_mitre_to_isf_suggestions(self, test_client, mock_db, mock_user, auth_headers):
        """Test MITRE to ISF mapping suggestions."""
        app.dependency_overrides[get_db] = lambda: mock_db
        app.dependency_overrides[get_current_user] = lambda: mock_user
        
        # Mock ISF controls
        mock_controls = [
            Control(
                id=1,
                control_id="EM1",
                name="Email Security",
                description="Email security controls"
            )
        ]
        
        mock_db.query.return_value.all.return_value = mock_controls
        
        with patch('api.services.mapping_algorithms.CrossFrameworkMappingAlgorithm') as mock_algorithm:
            mock_algorithm.return_value.generate_suggestions.return_value = [
                MagicMock(
                    source_id="T1566",
                    target_id="EM1",
                    mapping_type="mitigates",
                    confidence_score=0.85,
                    effectiveness_score=0.78,
                    semantic_similarity=0.82,
                    keyword_overlap=0.75,
                    rationale="Strong semantic match"
                )
            ]
            
            response = test_client.get(
                "/api/v1/mappings/mitre-to-isf/T1566/suggestions",
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "technique_id" in data
            assert "suggestions" in data
            assert len(data["suggestions"]) == 1
            assert data["suggestions"][0]["confidence_score"] == 0.85
        
        app.dependency_overrides.clear()
    
    def test_create_mapping(self, test_client, mock_db, mock_user, auth_headers):
        """Test creating a new mapping."""
        app.dependency_overrides[get_db] = lambda: mock_db
        app.dependency_overrides[get_current_user] = lambda: mock_user
        
        mock_db.add = MagicMock()
        mock_db.commit = MagicMock()
        mock_db.refresh = MagicMock()
        
        mapping_data = {
            "source_framework": "mitre_attack",
            "source_id": "T1566",
            "target_framework": "isf",
            "target_id": "EM1",
            "mapping_type": "mitigates",
            "confidence_score": 0.85,
            "rationale": "Email security controls mitigate phishing attacks"
        }
        
        response = test_client.post(
            "/api/v1/mappings",
            json=mapping_data,
            headers=auth_headers
        )
        
        assert response.status_code == 201
        data = response.json()
        assert data["source_id"] == "T1566"
        assert data["target_id"] == "EM1"
        assert data["confidence_score"] == 0.85
        
        mock_db.add.assert_called()
        mock_db.commit.assert_called()
        
        app.dependency_overrides.clear()
    
    def test_validate_mapping(self, test_client, mock_db, mock_user, auth_headers):
        """Test mapping validation."""
        app.dependency_overrides[get_db] = lambda: mock_db
        app.dependency_overrides[get_current_user] = lambda: mock_user
        
        # Mock existing mapping
        mock_mapping = Mapping(
            id=1,
            source_framework="mitre_attack",
            source_id="T1566",
            target_framework="isf",
            target_id="EM1",
            mapping_type="mitigates",
            confidence_score=0.85
        )
        
        mock_db.query.return_value.filter.return_value.first.return_value = mock_mapping
        mock_db.commit = MagicMock()
        
        response = test_client.post(
            "/api/v1/mappings/1/validate",
            json={
                "validation_status": "approved",
                "reviewer_notes": "Mapping is accurate"
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data["validation_status"] == "approved"
        assert "reviewer_notes" in data
        
        app.dependency_overrides.clear()
    
    def test_bulk_mapping_suggestions(self, test_client, mock_db, mock_user, auth_headers):
        """Test bulk mapping suggestions."""
        app.dependency_overrides[get_db] = lambda: mock_db
        app.dependency_overrides[get_current_user] = lambda: mock_user
        
        mock_db.query.return_value.all.return_value = []  # Mock controls
        
        with patch('api.services.mapping_algorithms.CrossFrameworkMappingAlgorithm') as mock_algorithm:
            mock_algorithm.return_value.generate_suggestions.return_value = []
            
            response = test_client.post(
                "/api/v1/mappings/suggestions/bulk",
                json={
                    "technique_ids": ["T1566", "T1078"],
                    "target_framework": "isf",
                    "options": {
                        "min_confidence": 0.7,
                        "max_suggestions": 5
                    }
                },
                headers=auth_headers
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "results" in data
            assert "summary" in data
        
        app.dependency_overrides.clear()


class TestAuthenticationIntegration:
    """Integration tests for authentication."""
    
    def test_login_success(self, test_client, mock_db):
        """Test successful login."""
        app.dependency_overrides[get_db] = lambda: mock_db
        
        # Mock user lookup
        mock_user = User(
            id=1,
            username="test_user",
            email="<EMAIL>",
            hashed_password="$2b$12$hashed_password",
            role="analyst",
            is_active=True
        )
        mock_db.query.return_value.filter.return_value.first.return_value = mock_user
        
        with patch('api.auth.utils.verify_password') as mock_verify:
            mock_verify.return_value = True
            
            response = test_client.post(
                "/auth/login",
                data={
                    "username": "test_user",
                    "password": "test_password"
                }
            )
            
            assert response.status_code == 200
            data = response.json()
            assert "access_token" in data
            assert data["token_type"] == "bearer"
        
        app.dependency_overrides.clear()
    
    def test_login_invalid_credentials(self, test_client, mock_db):
        """Test login with invalid credentials."""
        app.dependency_overrides[get_db] = lambda: mock_db
        
        mock_db.query.return_value.filter.return_value.first.return_value = None
        
        response = test_client.post(
            "/auth/login",
            data={
                "username": "invalid_user",
                "password": "invalid_password"
            }
        )
        
        assert response.status_code == 401
        data = response.json()
        assert "detail" in data
        
        app.dependency_overrides.clear()
    
    def test_protected_endpoint_without_token(self, test_client):
        """Test accessing protected endpoint without token."""
        response = test_client.get("/api/v1/isf/versions")
        
        assert response.status_code == 401
    
    def test_protected_endpoint_with_invalid_token(self, test_client):
        """Test accessing protected endpoint with invalid token."""
        response = test_client.get(
            "/api/v1/isf/versions",
            headers={"Authorization": "Bearer invalid-token"}
        )
        
        assert response.status_code == 401


class TestDatabaseIntegration:
    """Integration tests for database operations."""
    
    @pytest.mark.asyncio
    async def test_database_connection(self):
        """Test database connection."""
        from api.core.database import engine
        
        # Test connection
        async with engine.begin() as conn:
            result = await conn.execute("SELECT 1")
            assert result.scalar() == 1
    
    def test_isf_model_creation(self, mock_db):
        """Test ISF model creation and relationships."""
        # Create ISF version
        version = ISFVersion(
            version="2020.1",
            description="Test version",
            release_date=datetime.utcnow()
        )
        
        # Create security area
        area = SecurityArea(
            area_id="SG",
            name="Security Governance",
            description="Security governance area",
            version=version
        )
        
        # Create control
        control = Control(
            control_id="SG1",
            name="Security Policy",
            description="Security policy control",
            control_type="policy",
            maturity_level="basic",
            area=area
        )
        
        # Test relationships
        assert control.area == area
        assert area.version == version
        assert control in area.controls
    
    def test_nist_csf_model_creation(self, mock_db):
        """Test NIST CSF model creation and relationships."""
        # Create NIST CSF version
        version = NISTCSFVersion(
            version="2.0",
            description="Test version",
            release_date=datetime.utcnow()
        )
        
        # Create function
        function = Function(
            function_id="GV",
            name="Govern",
            description="Governance function",
            version=version
        )
        
        # Create category
        category = Category(
            category_id="GV.OC",
            name="Organizational Context",
            description="Organizational context category",
            function=function
        )
        
        # Create subcategory
        subcategory = Subcategory(
            subcategory_id="GV.OC-01",
            name="Organizational mission",
            description="Organizational mission subcategory",
            category=category
        )
        
        # Test relationships
        assert subcategory.category == category
        assert category.function == function
        assert function.version == version
        assert subcategory in category.subcategories
        assert category in function.categories


class TestExternalServiceIntegration:
    """Integration tests for external service interactions."""
    
    @patch('httpx.AsyncClient.get')
    async def test_mitre_api_integration(self, mock_get):
        """Test MITRE ATT&CK API integration."""
        # Mock MITRE API response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "objects": [
                {
                    "id": "attack-pattern--a62a8db3-f23a-4d8f-afd6-9dbc77e7813b",
                    "name": "Phishing",
                    "description": "Adversaries may send phishing messages..."
                }
            ]
        }
        mock_get.return_value = mock_response
        
        from api.services.external.mitre_api import MITREAPIClient
        
        client = MITREAPIClient()
        technique = await client.get_technique("T1566")
        
        assert technique["name"] == "Phishing"
        assert "description" in technique
    
    @patch('httpx.AsyncClient.get')
    async def test_nist_api_integration(self, mock_get):
        """Test NIST API integration."""
        # Mock NIST API response
        mock_response = MagicMock()
        mock_response.status_code = 200
        mock_response.json.return_value = {
            "version": "2.0",
            "functions": []
        }
        mock_get.return_value = mock_response
        
        from api.services.external.nist_api import NISTAPIClient
        
        client = NISTAPIClient()
        framework = await client.get_framework("2.0")
        
        assert framework["version"] == "2.0"
        assert "functions" in framework


if __name__ == '__main__':
    pytest.main([__file__])
