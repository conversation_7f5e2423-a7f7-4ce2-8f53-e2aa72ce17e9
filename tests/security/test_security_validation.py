"""
Security testing for the API.

This module contains comprehensive security tests including authentication,
authorization, input validation, and vulnerability prevention testing.
"""

import pytest
import jwt
import time
from datetime import datetime, timedelta
from fastapi.testclient import TestClient
from unittest.mock import patch, MagicMock
import json
import base64
import hashlib
import secrets

from api.main import app
from api.core.database import get_db
from api.auth.dependencies import get_current_user
from api.auth.utils import create_access_token, verify_password, hash_password
from api.models.user import User


class TestAuthenticationSecurity:
    """Security tests for authentication mechanisms."""
    
    def setup_method(self):
        """Set up test client."""
        self.client = TestClient(app)
    
    def test_jwt_token_validation(self):
        """Test JWT token validation security."""
        # Test with valid token
        user_data = {"sub": "test_user", "role": "analyst"}
        valid_token = create_access_token(data=user_data)
        
        # Decode and verify token structure
        decoded = jwt.decode(valid_token, options={"verify_signature": False})
        assert decoded["sub"] == "test_user"
        assert decoded["role"] == "analyst"
        assert "exp" in decoded
        assert "iat" in decoded
    
    def test_jwt_token_expiration(self):
        """Test JWT token expiration handling."""
        # Create expired token
        user_data = {"sub": "test_user", "role": "analyst"}
        expired_token = create_access_token(
            data=user_data,
            expires_delta=timedelta(seconds=-1)  # Already expired
        )
        
        # Test with expired token
        response = self.client.get(
            "/api/v1/isf/versions",
            headers={"Authorization": f"Bearer {expired_token}"}
        )
        
        assert response.status_code == 401
        assert "expired" in response.json()["detail"].lower()
    
    def test_jwt_token_tampering(self):
        """Test JWT token tampering detection."""
        # Create valid token
        user_data = {"sub": "test_user", "role": "analyst"}
        valid_token = create_access_token(data=user_data)
        
        # Tamper with token (change last character)
        tampered_token = valid_token[:-1] + "X"
        
        response = self.client.get(
            "/api/v1/isf/versions",
            headers={"Authorization": f"Bearer {tampered_token}"}
        )
        
        assert response.status_code == 401
    
    def test_malformed_jwt_token(self):
        """Test handling of malformed JWT tokens."""
        malformed_tokens = [
            "invalid.token.format",
            "not-a-jwt-token",
            "",
            "Bearer token-without-bearer-prefix",
            base64.b64encode(b"fake-token").decode(),
        ]
        
        for token in malformed_tokens:
            response = self.client.get(
                "/api/v1/isf/versions",
                headers={"Authorization": f"Bearer {token}"}
            )
            assert response.status_code == 401
    
    def test_password_hashing_security(self):
        """Test password hashing security."""
        password = "test_password_123"
        
        # Test password hashing
        hashed = hash_password(password)
        
        # Verify hash properties
        assert hashed != password  # Should not store plain text
        assert len(hashed) > 50  # Should be sufficiently long
        assert hashed.startswith("$2b$")  # Should use bcrypt
        
        # Test password verification
        assert verify_password(password, hashed) is True
        assert verify_password("wrong_password", hashed) is False
    
    def test_password_timing_attack_resistance(self):
        """Test resistance to timing attacks on password verification."""
        password = "correct_password"
        hashed = hash_password(password)
        
        # Measure timing for correct password
        start_time = time.time()
        verify_password(password, hashed)
        correct_time = time.time() - start_time
        
        # Measure timing for incorrect password
        start_time = time.time()
        verify_password("wrong_password", hashed)
        incorrect_time = time.time() - start_time
        
        # Timing difference should be minimal (within 50ms)
        time_difference = abs(correct_time - incorrect_time)
        assert time_difference < 0.05, f"Timing difference too large: {time_difference:.3f}s"
    
    def test_brute_force_protection(self):
        """Test brute force attack protection."""
        # Simulate multiple failed login attempts
        for i in range(10):
            response = self.client.post(
                "/auth/login",
                data={
                    "username": "test_user",
                    "password": f"wrong_password_{i}"
                }
            )
            assert response.status_code == 401
        
        # After multiple failures, should still respond (no lockout implemented yet)
        # This test documents current behavior and can be updated when rate limiting is added
        response = self.client.post(
            "/auth/login",
            data={
                "username": "test_user",
                "password": "another_wrong_password"
            }
        )
        assert response.status_code == 401


class TestAuthorizationSecurity:
    """Security tests for authorization and access control."""
    
    def setup_method(self):
        """Set up test client."""
        self.client = TestClient(app)
    
    def test_role_based_access_control(self):
        """Test role-based access control enforcement."""
        # Mock different user roles
        roles_and_permissions = [
            ("viewer", ["GET"], ["POST", "PUT", "DELETE"]),
            ("analyst", ["GET", "POST"], ["PUT", "DELETE"]),
            ("admin", ["GET", "POST", "PUT", "DELETE"], [])
        ]
        
        for role, allowed_methods, forbidden_methods in roles_and_permissions:
            user = User(
                id=1,
                username=f"test_{role}",
                email=f"{role}@example.com",
                role=role,
                is_active=True
            )
            
            app.dependency_overrides[get_current_user] = lambda: user
            
            # Test allowed methods
            for method in allowed_methods:
                if method == "GET":
                    response = self.client.get("/api/v1/isf/versions")
                    assert response.status_code != 403, f"{role} should have {method} access"
            
            # Test forbidden methods (would need specific endpoints that check roles)
            # This is a placeholder for when role-specific endpoints are implemented
            
            app.dependency_overrides.clear()
    
    def test_inactive_user_access_denial(self):
        """Test that inactive users are denied access."""
        inactive_user = User(
            id=1,
            username="inactive_user",
            email="<EMAIL>",
            role="analyst",
            is_active=False  # Inactive user
        )
        
        app.dependency_overrides[get_current_user] = lambda: inactive_user
        
        response = self.client.get("/api/v1/isf/versions")
        assert response.status_code == 401
        
        app.dependency_overrides.clear()
    
    def test_privilege_escalation_prevention(self):
        """Test prevention of privilege escalation attacks."""
        # Test that users cannot modify their own roles
        user = User(
            id=1,
            username="test_user",
            email="<EMAIL>",
            role="analyst",
            is_active=True
        )
        
        app.dependency_overrides[get_current_user] = lambda: user
        
        # Attempt to escalate privileges (if such endpoint existed)
        response = self.client.put(
            "/api/v1/users/1",
            json={"role": "admin"},  # Trying to become admin
            headers={"Authorization": "Bearer test-token"}
        )
        
        # Should be forbidden or not found (depending on implementation)
        assert response.status_code in [403, 404, 405]
        
        app.dependency_overrides.clear()


class TestInputValidationSecurity:
    """Security tests for input validation and sanitization."""
    
    def setup_method(self):
        """Set up test client."""
        self.client = TestClient(app)
        
        # Mock authenticated user
        self.mock_user = User(
            id=1,
            username="test_user",
            email="<EMAIL>",
            role="admin",
            is_active=True
        )
        app.dependency_overrides[get_current_user] = lambda: self.mock_user
    
    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
    
    def test_sql_injection_prevention(self):
        """Test SQL injection attack prevention."""
        sql_injection_payloads = [
            "'; DROP TABLE users; --",
            "' OR '1'='1",
            "'; INSERT INTO users (username) VALUES ('hacker'); --",
            "' UNION SELECT * FROM users --",
            "'; UPDATE users SET role='admin' WHERE id=1; --"
        ]
        
        for payload in sql_injection_payloads:
            # Test in search query
            response = self.client.get(
                "/api/v1/isf/search",
                params={"query": payload}
            )
            
            # Should not cause server error (500) - should be handled gracefully
            assert response.status_code != 500, f"SQL injection payload caused server error: {payload}"
            
            # Test in JSON data
            response = self.client.post(
                "/api/v1/isf/import",
                json={
                    "format": "json",
                    "data": json.dumps({"version": payload})
                }
            )
            
            assert response.status_code != 500, f"SQL injection in JSON caused server error: {payload}"
    
    def test_xss_prevention(self):
        """Test Cross-Site Scripting (XSS) attack prevention."""
        xss_payloads = [
            "<script>alert('XSS')</script>",
            "javascript:alert('XSS')",
            "<img src=x onerror=alert('XSS')>",
            "';alert('XSS');//",
            "<svg onload=alert('XSS')>"
        ]
        
        for payload in xss_payloads:
            # Test in framework import
            response = self.client.post(
                "/api/v1/isf/import",
                json={
                    "format": "json",
                    "data": json.dumps({
                        "version": "2020.1",
                        "description": payload,  # XSS payload in description
                        "security_areas": []
                    })
                }
            )
            
            # Should handle gracefully without executing script
            if response.status_code == 200:
                # If successful, ensure payload is escaped/sanitized
                data = response.json()
                assert payload not in str(data), f"XSS payload not sanitized: {payload}"
    
    def test_command_injection_prevention(self):
        """Test command injection attack prevention."""
        command_injection_payloads = [
            "; ls -la",
            "| cat /etc/passwd",
            "&& rm -rf /",
            "`whoami`",
            "$(cat /etc/hosts)"
        ]
        
        for payload in command_injection_payloads:
            response = self.client.post(
                "/api/v1/isf/export",
                json={
                    "format": "csv",
                    "filename": f"export{payload}.csv"  # Command injection in filename
                }
            )
            
            # Should not execute commands
            assert response.status_code != 500, f"Command injection caused server error: {payload}"
    
    def test_path_traversal_prevention(self):
        """Test path traversal attack prevention."""
        path_traversal_payloads = [
            "../../../etc/passwd",
            "..\\..\\..\\windows\\system32\\config\\sam",
            "....//....//....//etc/passwd",
            "%2e%2e%2f%2e%2e%2f%2e%2e%2fetc%2fpasswd",
            "..%252f..%252f..%252fetc%252fpasswd"
        ]
        
        for payload in path_traversal_payloads:
            # Test in file operations
            response = self.client.get(
                f"/api/v1/files/{payload}"  # If such endpoint exists
            )
            
            # Should not allow access to system files
            assert response.status_code in [400, 403, 404], f"Path traversal not prevented: {payload}"
    
    def test_json_bomb_prevention(self):
        """Test JSON bomb attack prevention."""
        # Create deeply nested JSON (JSON bomb)
        nested_json = {"a": {}}
        current = nested_json["a"]
        for i in range(100):  # Create 100 levels of nesting
            current["nested"] = {}
            current = current["nested"]
        
        response = self.client.post(
            "/api/v1/isf/import",
            json={
                "format": "json",
                "data": json.dumps(nested_json)
            }
        )
        
        # Should handle without causing stack overflow or excessive memory usage
        assert response.status_code != 500, "JSON bomb caused server error"
    
    def test_large_payload_handling(self):
        """Test handling of excessively large payloads."""
        # Create large payload (1MB of data)
        large_data = "A" * (1024 * 1024)
        
        response = self.client.post(
            "/api/v1/isf/import",
            json={
                "format": "json",
                "data": large_data
            }
        )
        
        # Should reject or handle gracefully
        assert response.status_code in [400, 413, 422], "Large payload not handled properly"


class TestCryptographicSecurity:
    """Security tests for cryptographic implementations."""
    
    def test_secure_random_generation(self):
        """Test secure random number generation."""
        # Generate multiple random values
        random_values = [secrets.token_hex(32) for _ in range(100)]
        
        # Check uniqueness
        assert len(set(random_values)) == len(random_values), "Random values not unique"
        
        # Check length
        for value in random_values:
            assert len(value) == 64, f"Random value wrong length: {len(value)}"
    
    def test_jwt_secret_security(self):
        """Test JWT secret key security."""
        from api.core.config import settings
        
        # JWT secret should be sufficiently long and complex
        jwt_secret = settings.SECRET_KEY
        
        assert len(jwt_secret) >= 32, "JWT secret too short"
        assert jwt_secret != "secret", "JWT secret is default/weak"
        assert jwt_secret != "your-secret-key", "JWT secret is placeholder"
    
    def test_password_complexity_requirements(self):
        """Test password complexity validation."""
        weak_passwords = [
            "123456",
            "password",
            "admin",
            "qwerty",
            "abc123",
            "password123",
            "12345678"
        ]
        
        for weak_password in weak_passwords:
            # Test user creation with weak password
            response = self.client.post(
                "/auth/register",
                json={
                    "username": "test_user",
                    "email": "<EMAIL>",
                    "password": weak_password
                }
            )
            
            # Should reject weak passwords (if password complexity is implemented)
            # This test documents expected behavior
            if response.status_code == 400:
                assert "password" in response.json()["detail"].lower()


class TestSessionSecurity:
    """Security tests for session management."""
    
    def test_session_fixation_prevention(self):
        """Test session fixation attack prevention."""
        # This test would be relevant if using session-based auth
        # Currently using stateless JWT, so session fixation is not applicable
        pass
    
    def test_concurrent_session_handling(self):
        """Test handling of concurrent sessions."""
        # Create multiple tokens for same user
        user_data = {"sub": "test_user", "role": "analyst"}
        
        token1 = create_access_token(data=user_data)
        token2 = create_access_token(data=user_data)
        
        # Both tokens should be valid (stateless JWT allows this)
        client = TestClient(app)
        
        response1 = client.get(
            "/api/v1/isf/versions",
            headers={"Authorization": f"Bearer {token1}"}
        )
        
        response2 = client.get(
            "/api/v1/isf/versions",
            headers={"Authorization": f"Bearer {token2}"}
        )
        
        # Both should work with stateless JWT
        assert response1.status_code != 401
        assert response2.status_code != 401


class TestDataProtectionSecurity:
    """Security tests for data protection and privacy."""
    
    def test_sensitive_data_exposure_prevention(self):
        """Test prevention of sensitive data exposure."""
        # Test that passwords are not returned in API responses
        mock_user = User(
            id=1,
            username="test_user",
            email="<EMAIL>",
            hashed_password="$2b$12$hashed_password",
            role="analyst",
            is_active=True
        )
        
        app.dependency_overrides[get_current_user] = lambda: mock_user
        
        client = TestClient(app)
        response = client.get("/api/v1/users/me")
        
        if response.status_code == 200:
            user_data = response.json()
            assert "password" not in user_data
            assert "hashed_password" not in user_data
        
        app.dependency_overrides.clear()
    
    def test_error_message_information_disclosure(self):
        """Test that error messages don't disclose sensitive information."""
        client = TestClient(app)
        
        # Test with invalid endpoint
        response = client.get("/api/v1/nonexistent/endpoint")
        
        error_message = response.json().get("detail", "")
        
        # Should not expose internal paths, database info, etc.
        sensitive_patterns = [
            "/home/",
            "/var/",
            "database",
            "connection",
            "traceback",
            "exception"
        ]
        
        for pattern in sensitive_patterns:
            assert pattern.lower() not in error_message.lower(), f"Error message exposes sensitive info: {pattern}"


if __name__ == '__main__':
    pytest.main([__file__, "-v"])
