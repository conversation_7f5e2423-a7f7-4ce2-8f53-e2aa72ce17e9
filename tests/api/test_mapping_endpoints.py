"""
TDD Tests for Cross-Framework Mapping API Endpoints.

This module contains comprehensive failing tests for cross-framework mapping
API endpoints following Test-Driven Development methodology. Tests cover
mapping relationships, effectiveness scoring, bulk operations, and validation.
"""

import pytest
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.main import app
from api.core.database import get_db
from api.models.mapping import Mapping
from api.models.user import User
from api.auth.dependencies import get_current_user
from tests.conftest import TestDatabase


class TestMappingCRUDEndpoints:
    """Test basic CRUD operations for mappings."""
    
    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()
        
        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user
    
    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()
    
    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.email = "<EMAIL>"
        user.is_active = True
        user.roles = ["admin"]
        return user
    
    def test_get_mappings_empty_list(self):
        """Test GET /api/v1/mappings returns empty list when no mappings exist."""
        response = self.client.get("/api/v1/mappings")
        
        assert response.status_code == 200
        data = response.json()
        assert data["mappings"] == []
        assert data["total"] == 0
        assert data["page"] == 1
        assert data["page_size"] == 50
    
    def test_get_mappings_with_pagination(self):
        """Test GET /api/v1/mappings returns paginated mapping list."""
        # This test will fail until we implement the endpoint
        response = self.client.get("/api/v1/mappings?page=1&page_size=10")
        
        assert response.status_code == 200
        data = response.json()
        assert "mappings" in data
        assert "total" in data
        assert "page" in data
        assert "page_size" in data
        assert isinstance(data["mappings"], list)
    
    def test_get_mapping_by_id_not_found(self):
        """Test GET /api/v1/mappings/{id} returns 404 for non-existent mapping."""
        response = self.client.get("/api/v1/mappings/999")
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
        assert "not found" in data["detail"].lower()
    
    def test_get_mapping_by_id_success(self):
        """Test GET /api/v1/mappings/{id} returns mapping details."""
        # This test will fail until we implement the endpoint
        response = self.client.get("/api/v1/mappings/1")
        
        # Should return 200 when mapping exists
        expected_fields = [
            "id", "source_framework", "source_id", "target_framework", 
            "target_id", "mapping_type", "effectiveness_score", 
            "confidence_score", "rationale", "created_by", "created_at"
        ]
        
        if response.status_code == 200:
            data = response.json()
            for field in expected_fields:
                assert field in data
    
    def test_create_mapping_invalid_data(self):
        """Test POST /api/v1/mappings with invalid data returns 422."""
        invalid_data = {
            "source_framework": "",  # Empty source framework should be invalid
            "target_framework": "isf"
        }
        
        response = self.client.post(
            "/api/v1/mappings",
            json=invalid_data
        )
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data
    
    def test_create_mapping_success(self):
        """Test POST /api/v1/mappings creates new mapping."""
        valid_data = {
            "source_framework": "mitre_attack",
            "source_id": "T1566",
            "target_framework": "isf",
            "target_id": "SG1",
            "mapping_type": "mitigates",
            "effectiveness_score": 0.8,
            "confidence_score": 0.9,
            "rationale": "Security policy helps prevent phishing attacks"
        }
        
        response = self.client.post(
            "/api/v1/mappings",
            json=valid_data
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 201
        data = response.json()
        assert data["source_framework"] == valid_data["source_framework"]
        assert data["target_framework"] == valid_data["target_framework"]
        assert data["effectiveness_score"] == valid_data["effectiveness_score"]
        assert "id" in data
        assert "created_at" in data
    
    def test_create_mapping_duplicate(self):
        """Test POST /api/v1/mappings with duplicate mapping returns 409."""
        mapping_data = {
            "source_framework": "mitre_attack",
            "source_id": "T1566",
            "target_framework": "isf",
            "target_id": "SG1",
            "mapping_type": "mitigates"
        }
        
        # First creation should succeed
        response1 = self.client.post("/api/v1/mappings", json=mapping_data)
        
        # Second creation should fail with conflict
        response2 = self.client.post("/api/v1/mappings", json=mapping_data)
        
        assert response2.status_code == 409
        data = response2.json()
        assert "already exists" in data["detail"].lower()
    
    def test_update_mapping_not_found(self):
        """Test PUT /api/v1/mappings/{id} returns 404 for non-existent mapping."""
        update_data = {
            "effectiveness_score": 0.9,
            "rationale": "Updated rationale"
        }
        
        response = self.client.put(
            "/api/v1/mappings/999",
            json=update_data
        )
        
        assert response.status_code == 404
    
    def test_update_mapping_success(self):
        """Test PUT /api/v1/mappings/{id} updates mapping."""
        update_data = {
            "effectiveness_score": 0.9,
            "confidence_score": 0.95,
            "rationale": "Updated rationale with more details"
        }
        
        response = self.client.put(
            "/api/v1/mappings/1",
            json=update_data
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert data["effectiveness_score"] == update_data["effectiveness_score"]
        assert data["confidence_score"] == update_data["confidence_score"]
        assert data["rationale"] == update_data["rationale"]
    
    def test_delete_mapping_not_found(self):
        """Test DELETE /api/v1/mappings/{id} returns 404 for non-existent mapping."""
        response = self.client.delete("/api/v1/mappings/999")
        
        assert response.status_code == 404
    
    def test_delete_mapping_success(self):
        """Test DELETE /api/v1/mappings/{id} soft deletes mapping."""
        response = self.client.delete("/api/v1/mappings/1")
        
        # This will fail until endpoint is implemented
        assert response.status_code == 204


class TestMappingFilteringAndSearch:
    """Test filtering and search capabilities for mappings."""
    
    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()
        
        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user
    
    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()
    
    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user
    
    def test_filter_mappings_by_framework(self):
        """Test filtering mappings by source and target frameworks."""
        response = self.client.get(
            "/api/v1/mappings?source_framework=mitre_attack&target_framework=isf"
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "mappings" in data
        
        # All returned mappings should match the filter
        for mapping in data["mappings"]:
            assert mapping["source_framework"] == "mitre_attack"
            assert mapping["target_framework"] == "isf"
    
    def test_filter_mappings_by_effectiveness_score(self):
        """Test filtering mappings by effectiveness score range."""
        response = self.client.get(
            "/api/v1/mappings?min_effectiveness=0.7&max_effectiveness=0.9"
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "mappings" in data
        
        # All returned mappings should be within the score range
        for mapping in data["mappings"]:
            score = mapping["effectiveness_score"]
            assert 0.7 <= score <= 0.9
    
    def test_filter_mappings_by_mapping_type(self):
        """Test filtering mappings by mapping type."""
        response = self.client.get(
            "/api/v1/mappings?mapping_type=mitigates&mapping_type=detects"
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "mappings" in data
        
        # All returned mappings should have specified types
        for mapping in data["mappings"]:
            assert mapping["mapping_type"] in ["mitigates", "detects"]
    
    def test_search_mappings_by_rationale(self):
        """Test searching mappings by rationale content."""
        response = self.client.get(
            "/api/v1/mappings/search?q=phishing security policy&search_fields=rationale"
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "mappings" in data
        assert "search_metadata" in data
    
    def test_complex_mapping_search(self):
        """Test complex search with multiple parameters."""
        response = self.client.get(
            "/api/v1/mappings/search?"
            "q=access control&"
            "source_framework=mitre_attack&"
            "target_framework=isf&"
            "min_effectiveness=0.8&"
            "mapping_type=mitigates&"
            "created_after=2024-01-01&"
            "order_by=effectiveness_score&"
            "order_direction=desc&"
            "page=1&"
            "page_size=20"
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "mappings" in data
        assert "total" in data
        assert "filters_applied" in data
        assert data["page"] == 1
        assert data["page_size"] == 20
    
    def test_get_mappings_by_source_id(self):
        """Test GET /api/v1/mappings/source/{framework}/{id} returns mappings for source."""
        response = self.client.get("/api/v1/mappings/source/mitre_attack/T1566")
        
        assert response.status_code == 200
        data = response.json()
        assert "mappings" in data
        assert "source_info" in data
        
        # All mappings should have the specified source
        for mapping in data["mappings"]:
            assert mapping["source_framework"] == "mitre_attack"
            assert mapping["source_id"] == "T1566"
    
    def test_get_mappings_by_target_id(self):
        """Test GET /api/v1/mappings/target/{framework}/{id} returns mappings for target."""
        response = self.client.get("/api/v1/mappings/target/isf/SG1")
        
        assert response.status_code == 200
        data = response.json()
        assert "mappings" in data
        assert "target_info" in data
        
        # All mappings should have the specified target
        for mapping in data["mappings"]:
            assert mapping["target_framework"] == "isf"
            assert mapping["target_id"] == "SG1"


class TestBulkMappingOperations:
    """Test bulk operations for mappings."""
    
    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()
        
        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user
    
    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()
    
    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user
    
    def test_bulk_create_mappings(self):
        """Test POST /api/v1/mappings/bulk creates multiple mappings."""
        bulk_data = {
            "mappings": [
                {
                    "source_framework": "mitre_attack",
                    "source_id": "T1566",
                    "target_framework": "isf",
                    "target_id": "SG1",
                    "mapping_type": "mitigates",
                    "effectiveness_score": 0.8
                },
                {
                    "source_framework": "mitre_attack",
                    "source_id": "T1078",
                    "target_framework": "isf",
                    "target_id": "AC2",
                    "mapping_type": "detects",
                    "effectiveness_score": 0.9
                }
            ]
        }
        
        response = self.client.post(
            "/api/v1/mappings/bulk",
            json=bulk_data
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 201
        data = response.json()
        assert "created_count" in data
        assert "mappings" in data
        assert data["created_count"] == 2
    
    def test_bulk_update_mappings(self):
        """Test PUT /api/v1/mappings/bulk updates multiple mappings."""
        bulk_update_data = {
            "updates": [
                {
                    "id": 1,
                    "effectiveness_score": 0.9,
                    "confidence_score": 0.95
                },
                {
                    "id": 2,
                    "effectiveness_score": 0.85,
                    "rationale": "Updated rationale"
                }
            ]
        }
        
        response = self.client.put(
            "/api/v1/mappings/bulk",
            json=bulk_update_data
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "updated_count" in data
        assert "failed_updates" in data
        assert data["updated_count"] == 2
    
    def test_bulk_delete_mappings(self):
        """Test DELETE /api/v1/mappings/bulk deletes multiple mappings."""
        bulk_delete_data = {
            "mapping_ids": [1, 2, 3, 4, 5]
        }
        
        response = self.client.delete(
            "/api/v1/mappings/bulk",
            json=bulk_delete_data
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "deleted_count" in data
        assert "failed_deletions" in data
    
    def test_bulk_import_mappings_with_validation(self):
        """Test bulk import with validation errors."""
        import_data = {
            "mappings": [
                {
                    "source_framework": "mitre_attack",
                    "source_id": "T1566",
                    "target_framework": "isf",
                    "target_id": "SG1",
                    "mapping_type": "mitigates",
                    "effectiveness_score": 0.8
                },
                {
                    "source_framework": "",  # Invalid: empty source framework
                    "source_id": "T1078",
                    "target_framework": "isf",
                    "target_id": "AC2",
                    "mapping_type": "detects"
                },
                {
                    "source_framework": "mitre_attack",
                    "source_id": "T1190",
                    "target_framework": "isf",
                    "target_id": "SG3",
                    "mapping_type": "invalid_type",  # Invalid mapping type
                    "effectiveness_score": 1.5  # Invalid score > 1.0
                }
            ]
        }
        
        response = self.client.post(
            "/api/v1/mappings/bulk",
            json=import_data
        )
        
        assert response.status_code == 207  # Multi-status
        data = response.json()
        assert "created_count" in data
        assert "failed_count" in data
        assert "validation_errors" in data
        assert data["created_count"] == 1  # Only one valid mapping
        assert data["failed_count"] == 2   # Two invalid mappings


class TestMappingEffectivenessAnalysis:
    """Test effectiveness analysis and scoring for mappings."""

    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()

        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user

    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()

    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user

    def test_calculate_mapping_effectiveness(self):
        """Test POST /api/v1/mappings/{id}/calculate-effectiveness calculates effectiveness score."""
        calculation_data = {
            "factors": {
                "coverage_completeness": 0.8,
                "implementation_difficulty": 0.7,
                "detection_accuracy": 0.9,
                "false_positive_rate": 0.1
            },
            "weights": {
                "coverage_completeness": 0.3,
                "implementation_difficulty": 0.2,
                "detection_accuracy": 0.4,
                "false_positive_rate": 0.1
            }
        }

        response = self.client.post(
            "/api/v1/mappings/1/calculate-effectiveness",
            json=calculation_data
        )

        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "effectiveness_score" in data
        assert "calculation_details" in data
        assert "confidence_score" in data
        assert 0.0 <= data["effectiveness_score"] <= 1.0

    def test_bulk_effectiveness_calculation(self):
        """Test bulk effectiveness calculation for multiple mappings."""
        bulk_calculation_data = {
            "mapping_ids": [1, 2, 3],
            "calculation_method": "weighted_average",
            "factors": {
                "coverage_completeness": 0.8,
                "implementation_difficulty": 0.7,
                "detection_accuracy": 0.9
            }
        }

        response = self.client.post(
            "/api/v1/mappings/bulk-calculate-effectiveness",
            json=bulk_calculation_data
        )

        assert response.status_code == 200
        data = response.json()
        assert "calculations" in data
        assert "summary" in data
        assert len(data["calculations"]) == 3

    def test_get_effectiveness_distribution(self):
        """Test GET /api/v1/mappings/effectiveness-distribution returns score distribution."""
        response = self.client.get(
            "/api/v1/mappings/effectiveness-distribution?frameworks=mitre_attack,isf"
        )

        assert response.status_code == 200
        data = response.json()
        assert "distribution" in data
        assert "statistics" in data
        assert "score_ranges" in data["distribution"]
        assert "percentiles" in data["statistics"]

    def test_get_low_effectiveness_mappings(self):
        """Test GET /api/v1/mappings/low-effectiveness returns mappings needing improvement."""
        response = self.client.get(
            "/api/v1/mappings/low-effectiveness?threshold=0.5&limit=20"
        )

        assert response.status_code == 200
        data = response.json()
        assert "mappings" in data
        assert "improvement_suggestions" in data

        # All mappings should have effectiveness below threshold
        for mapping in data["mappings"]:
            assert mapping["effectiveness_score"] < 0.5

    def test_validate_mapping_effectiveness(self):
        """Test POST /api/v1/mappings/{id}/validate-effectiveness validates mapping quality."""
        validation_data = {
            "validation_criteria": {
                "min_effectiveness": 0.7,
                "min_confidence": 0.8,
                "require_rationale": True,
                "check_bidirectional": True
            }
        }

        response = self.client.post(
            "/api/v1/mappings/1/validate-effectiveness",
            json=validation_data
        )

        assert response.status_code == 200
        data = response.json()
        assert "validation_result" in data
        assert "issues_found" in data
        assert "recommendations" in data


class TestCrossFrameworkAnalysis:
    """Test cross-framework analysis capabilities."""

    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()

        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user

    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()

    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user

    def test_get_framework_coverage_matrix(self):
        """Test GET /api/v1/mappings/coverage-matrix returns coverage between frameworks."""
        response = self.client.get(
            "/api/v1/mappings/coverage-matrix?frameworks=mitre_attack,isf,nist_csf"
        )

        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "coverage_matrix" in data
        assert "framework_statistics" in data

        # Matrix should be symmetric
        matrix = data["coverage_matrix"]
        assert "mitre_attack" in matrix
        assert "isf" in matrix
        assert "nist_csf" in matrix

    def test_find_mapping_gaps(self):
        """Test GET /api/v1/mappings/gaps identifies missing mappings."""
        response = self.client.get(
            "/api/v1/mappings/gaps?source_framework=mitre_attack&target_framework=isf&min_priority=medium"
        )

        assert response.status_code == 200
        data = response.json()
        assert "gaps" in data
        assert "gap_statistics" in data
        assert "recommendations" in data

        # Each gap should have priority and rationale
        for gap in data["gaps"]:
            assert "source_id" in gap
            assert "target_candidates" in gap
            assert "priority" in gap

    def test_suggest_new_mappings(self):
        """Test POST /api/v1/mappings/suggest generates mapping suggestions."""
        suggestion_request = {
            "source_framework": "mitre_attack",
            "target_framework": "isf",
            "suggestion_method": "semantic_similarity",
            "min_confidence": 0.7,
            "max_suggestions": 10
        }

        response = self.client.post(
            "/api/v1/mappings/suggest",
            json=suggestion_request
        )

        assert response.status_code == 200
        data = response.json()
        assert "suggestions" in data
        assert "confidence_scores" in data
        assert "methodology" in data

        # Each suggestion should have confidence score
        for suggestion in data["suggestions"]:
            assert "source_id" in suggestion
            assert "target_id" in suggestion
            assert "confidence_score" in suggestion
            assert suggestion["confidence_score"] >= 0.7

    def test_analyze_mapping_quality(self):
        """Test GET /api/v1/mappings/quality-analysis analyzes overall mapping quality."""
        response = self.client.get(
            "/api/v1/mappings/quality-analysis?frameworks=mitre_attack,isf&include_recommendations=true"
        )

        assert response.status_code == 200
        data = response.json()
        assert "quality_metrics" in data
        assert "distribution_analysis" in data
        assert "recommendations" in data

        # Quality metrics should include key indicators
        metrics = data["quality_metrics"]
        assert "average_effectiveness" in metrics
        assert "average_confidence" in metrics
        assert "coverage_percentage" in metrics
        assert "mapping_density" in metrics

    def test_compare_framework_mappings(self):
        """Test GET /api/v1/mappings/compare compares mappings between frameworks."""
        response = self.client.get(
            "/api/v1/mappings/compare?framework1=mitre_attack&framework2=isf&framework3=nist_csf"
        )

        assert response.status_code == 200
        data = response.json()
        assert "comparison_results" in data
        assert "overlap_analysis" in data
        assert "unique_mappings" in data

        # Should show overlaps and unique mappings
        overlap = data["overlap_analysis"]
        assert "common_mappings" in overlap
        assert "framework_specific" in overlap


class TestMappingValidationAndQuality:
    """Test mapping validation and quality assurance."""

    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()

        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user

    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()

    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user

    def test_validate_mapping_consistency(self):
        """Test POST /api/v1/mappings/validate-consistency checks mapping consistency."""
        validation_request = {
            "frameworks": ["mitre_attack", "isf", "nist_csf"],
            "validation_rules": [
                "bidirectional_consistency",
                "effectiveness_threshold",
                "rationale_completeness",
                "duplicate_detection"
            ]
        }

        response = self.client.post(
            "/api/v1/mappings/validate-consistency",
            json=validation_request
        )

        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "validation_results" in data
        assert "inconsistencies" in data
        assert "recommendations" in data

        # Should identify specific issues
        for inconsistency in data["inconsistencies"]:
            assert "type" in inconsistency
            assert "affected_mappings" in inconsistency
            assert "severity" in inconsistency

    def test_check_mapping_completeness(self):
        """Test GET /api/v1/mappings/completeness-check analyzes mapping completeness."""
        response = self.client.get(
            "/api/v1/mappings/completeness-check?source_framework=mitre_attack&target_framework=isf"
        )

        assert response.status_code == 200
        data = response.json()
        assert "completeness_score" in data
        assert "unmapped_sources" in data
        assert "unmapped_targets" in data
        assert "coverage_gaps" in data

    def test_detect_duplicate_mappings(self):
        """Test GET /api/v1/mappings/duplicates detects potential duplicate mappings."""
        response = self.client.get(
            "/api/v1/mappings/duplicates?similarity_threshold=0.9&include_near_duplicates=true"
        )

        assert response.status_code == 200
        data = response.json()
        assert "duplicates" in data
        assert "near_duplicates" in data
        assert "resolution_suggestions" in data

        # Each duplicate group should have similarity score
        for duplicate_group in data["duplicates"]:
            assert "mappings" in duplicate_group
            assert "similarity_score" in duplicate_group
            assert len(duplicate_group["mappings"]) >= 2

    def test_audit_mapping_changes(self):
        """Test GET /api/v1/mappings/audit-trail returns mapping change history."""
        response = self.client.get(
            "/api/v1/mappings/audit-trail?mapping_id=1&include_details=true"
        )

        assert response.status_code == 200
        data = response.json()
        assert "audit_entries" in data
        assert "change_summary" in data

        # Each audit entry should have required fields
        for entry in data["audit_entries"]:
            assert "timestamp" in entry
            assert "user_id" in entry
            assert "action" in entry
            assert "changes" in entry

    def test_generate_mapping_report(self):
        """Test POST /api/v1/mappings/generate-report creates comprehensive mapping report."""
        report_request = {
            "frameworks": ["mitre_attack", "isf"],
            "report_type": "comprehensive",
            "include_sections": [
                "coverage_analysis",
                "effectiveness_distribution",
                "quality_metrics",
                "recommendations"
            ],
            "format": "json"
        }

        response = self.client.post(
            "/api/v1/mappings/generate-report",
            json=report_request
        )

        assert response.status_code == 200
        data = response.json()
        assert "report_metadata" in data
        assert "coverage_analysis" in data
        assert "effectiveness_distribution" in data
        assert "quality_metrics" in data
        assert "recommendations" in data


class TestMappingImportExport:
    """Test mapping import/export functionality."""

    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()

        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user

    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()

    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user

    def test_export_mappings_json(self):
        """Test GET /api/v1/mappings/export/json exports mappings as JSON."""
        response = self.client.get(
            "/api/v1/mappings/export/json?frameworks=mitre_attack,isf&include_metadata=true"
        )

        # This will fail until endpoint is implemented
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"

        data = response.json()
        assert "mappings" in data
        assert "metadata" in data
        assert "export_timestamp" in data["metadata"]

    def test_export_mappings_csv(self):
        """Test GET /api/v1/mappings/export/csv exports mappings as CSV."""
        response = self.client.get(
            "/api/v1/mappings/export/csv?frameworks=mitre_attack,isf&flatten_structure=true"
        )

        assert response.status_code == 200
        assert response.headers["content-type"] == "text/csv"

    def test_import_mappings_json(self):
        """Test POST /api/v1/mappings/import/json imports mappings from JSON."""
        import_data = {
            "mappings": [
                {
                    "source_framework": "mitre_attack",
                    "source_id": "T1566",
                    "target_framework": "isf",
                    "target_id": "SG1",
                    "mapping_type": "mitigates",
                    "effectiveness_score": 0.8,
                    "confidence_score": 0.9,
                    "rationale": "Security policy helps prevent phishing"
                }
            ],
            "import_options": {
                "update_existing": True,
                "validate_references": True,
                "calculate_effectiveness": False
            }
        }

        response = self.client.post(
            "/api/v1/mappings/import/json",
            json=import_data
        )

        assert response.status_code == 201
        data = response.json()
        assert "import_id" in data
        assert "status" in data
        assert "imported_count" in data

    def test_import_mappings_file(self):
        """Test POST /api/v1/mappings/import/file imports mappings from file."""
        # Mock file upload
        files = {
            "file": ("mappings.json", '{"mappings": []}', "application/json")
        }

        response = self.client.post(
            "/api/v1/mappings/import/file",
            files=files
        )

        assert response.status_code == 201
        data = response.json()
        assert "import_id" in data

    def test_get_import_status(self):
        """Test GET /api/v1/mappings/import/{import_id}/status returns import status."""
        response = self.client.get("/api/v1/mappings/import/123/status")

        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "progress" in data
        assert "imported_count" in data
        assert "failed_count" in data
