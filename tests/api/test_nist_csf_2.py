"""
Tests for NIST CSF 2.0 functionality.

This module contains comprehensive tests for the NIST CSF 2.0 system,
including framework import, API endpoints, and data validation.
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime

from api.main import app
from api.database import get_db
from api.models.nist_csf_2 import (
    NISTCSFVersion, NISTCSFFunction, NISTCSFCategory, 
    NISTCSFSubcategory, NISTCSFImplementationExample
)
from api.services.nist_csf_2_import import NISTCSFImportService
from api.schemas.nist_csf_2 import NISTCSFSearchRequest
from tests.conftest import test_db


client = TestClient(app)


class TestNISTCSFImportService:
    """Test suite for NIST CSF 2.0 import service functionality."""
    
    def test_import_nist_csf_2_0_creates_version(self, test_db: Session):
        """Test that NIST CSF 2.0 import creates the correct version."""
        service = NISTCSFImportService(test_db)
        result = service.import_nist_csf_2_0()
        
        assert result["success"] is True
        assert result["version"] == "2.0"
        
        # Verify version was created
        version = test_db.query(NISTCSFVersion).filter(
            NISTCSFVersion.version == "2.0"
        ).first()
        
        assert version is not None
        assert version.is_current is True
        assert version.description is not None
        assert "2024-02-26" in version.release_date
    
    def test_import_nist_csf_2_0_creates_functions(self, test_db: Session):
        """Test that NIST CSF 2.0 import creates all functions."""
        service = NISTCSFImportService(test_db)
        result = service.import_nist_csf_2_0()
        
        assert result["success"] is True
        
        # Verify functions were created
        functions = test_db.query(NISTCSFFunction).all()
        assert len(functions) == 6  # NIST CSF 2.0 has 6 functions
        
        # Check specific functions
        function_ids = [func.function_id for func in functions]
        assert "GV" in function_ids  # Govern (new in 2.0)
        assert "ID" in function_ids  # Identify
        assert "PR" in function_ids  # Protect
        assert "DE" in function_ids  # Detect
        assert "RS" in function_ids  # Respond
        assert "RC" in function_ids  # Recover
    
    def test_import_nist_csf_2_0_creates_categories(self, test_db: Session):
        """Test that NIST CSF 2.0 import creates categories."""
        service = NISTCSFImportService(test_db)
        result = service.import_nist_csf_2_0()
        
        assert result["success"] is True
        
        # Verify categories were created
        categories = test_db.query(NISTCSFCategory).all()
        assert len(categories) > 0
        
        # Check specific categories
        category_ids = [cat.category_id for cat in categories]
        assert "GV.GV" in category_ids  # Governance
        assert "GV.RM" in category_ids  # Risk Management
        assert "ID.AM" in category_ids  # Asset Management
        assert "PR.AC" in category_ids  # Access Control
    
    def test_import_nist_csf_2_0_creates_subcategories(self, test_db: Session):
        """Test that NIST CSF 2.0 import creates subcategories."""
        service = NISTCSFImportService(test_db)
        result = service.import_nist_csf_2_0()
        
        assert result["success"] is True
        
        # Verify subcategories were created
        subcategories = test_db.query(NISTCSFSubcategory).all()
        assert len(subcategories) > 0
        
        # Check specific subcategories
        subcategory_ids = [subcat.subcategory_id for subcat in subcategories]
        assert "GV.GV-01" in subcategory_ids
        assert "ID.AM-01" in subcategory_ids
        assert "PR.AC-01" in subcategory_ids
    
    def test_import_nist_csf_2_0_idempotent(self, test_db: Session):
        """Test that running import twice doesn't create duplicates."""
        service = NISTCSFImportService(test_db)
        
        # First import
        result1 = service.import_nist_csf_2_0()
        assert result1["success"] is True
        
        # Count records
        version_count_1 = test_db.query(NISTCSFVersion).count()
        function_count_1 = test_db.query(NISTCSFFunction).count()
        category_count_1 = test_db.query(NISTCSFCategory).count()
        subcategory_count_1 = test_db.query(NISTCSFSubcategory).count()
        
        # Second import
        result2 = service.import_nist_csf_2_0()
        assert result2["success"] is True
        
        # Count records again
        version_count_2 = test_db.query(NISTCSFVersion).count()
        function_count_2 = test_db.query(NISTCSFFunction).count()
        category_count_2 = test_db.query(NISTCSFCategory).count()
        subcategory_count_2 = test_db.query(NISTCSFSubcategory).count()
        
        # Should be the same (no duplicates)
        assert version_count_1 == version_count_2
        assert function_count_1 == function_count_2
        assert category_count_1 == category_count_2
        assert subcategory_count_1 == subcategory_count_2
    
    def test_import_nist_csf_2_0_sets_current_version(self, test_db: Session):
        """Test that NIST CSF 2.0 import sets the version as current."""
        # Create an existing version
        old_version = NISTCSFVersion(
            version="1.1",
            release_date="2018-04-16",
            description="Old version",
            is_current=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        test_db.add(old_version)
        test_db.commit()
        
        # Import NIST CSF 2.0
        service = NISTCSFImportService(test_db)
        result = service.import_nist_csf_2_0()
        
        assert result["success"] is True
        
        # Check that old version is no longer current
        test_db.refresh(old_version)
        assert old_version.is_current is False
        
        # Check that new version is current
        new_version = test_db.query(NISTCSFVersion).filter(
            NISTCSFVersion.version == "2.0"
        ).first()
        assert new_version.is_current is True
    
    def test_import_nist_csf_2_0_statistics(self, test_db: Session):
        """Test that import returns correct statistics."""
        service = NISTCSFImportService(test_db)
        result = service.import_nist_csf_2_0()
        
        assert result["success"] is True
        assert "statistics" in result
        
        stats = result["statistics"]
        assert stats["functions"] == 6
        assert stats["categories"] > 0
        assert stats["subcategories"] > 0
        assert stats["total_records"] > 6  # version + functions + categories + subcategories


class TestNISTCSFAPI:
    """Test suite for NIST CSF 2.0 API endpoints."""
    
    def test_get_versions_endpoint(self, test_db: Session):
        """Test getting NIST CSF versions via API."""
        # Import test data
        service = NISTCSFImportService(test_db)
        service.import_nist_csf_2_0()
        
        # Test the service directly (would require auth setup for full API test)
        versions = test_db.query(NISTCSFVersion).all()
        assert len(versions) >= 1
        
        # Check version data
        version_2_0 = next((v for v in versions if v.version == "2.0"), None)
        assert version_2_0 is not None
        assert version_2_0.is_current is True
    
    def test_get_current_version_endpoint(self, test_db: Session):
        """Test getting current NIST CSF version via API."""
        # Import test data
        service = NISTCSFImportService(test_db)
        service.import_nist_csf_2_0()
        
        # Test the service directly
        current_version = test_db.query(NISTCSFVersion).filter(
            NISTCSFVersion.is_current == True
        ).first()
        
        assert current_version is not None
        assert current_version.version == "2.0"
    
    def test_get_functions_endpoint(self, test_db: Session):
        """Test getting NIST CSF functions via API."""
        # Import test data
        service = NISTCSFImportService(test_db)
        service.import_nist_csf_2_0()
        
        # Test the service directly
        functions = test_db.query(NISTCSFFunction).order_by(
            NISTCSFFunction.order_index
        ).all()
        
        assert len(functions) == 6
        
        # Check function order and content
        assert functions[0].function_id == "GV"
        assert functions[0].name == "Govern"
        assert functions[1].function_id == "ID"
        assert functions[1].name == "Identify"
    
    def test_get_categories_endpoint(self, test_db: Session):
        """Test getting NIST CSF categories via API."""
        # Import test data
        service = NISTCSFImportService(test_db)
        service.import_nist_csf_2_0()
        
        # Test filtering by function
        govern_categories = test_db.query(NISTCSFCategory).join(
            NISTCSFFunction
        ).filter(
            NISTCSFFunction.function_id == "GV"
        ).all()
        
        assert len(govern_categories) >= 2  # Should have GV.GV and GV.RM at minimum
        
        category_ids = [cat.category_id for cat in govern_categories]
        assert "GV.GV" in category_ids
        assert "GV.RM" in category_ids
    
    def test_get_subcategories_endpoint(self, test_db: Session):
        """Test getting NIST CSF subcategories via API."""
        # Import test data
        service = NISTCSFImportService(test_db)
        service.import_nist_csf_2_0()
        
        # Test filtering by function
        govern_subcategories = test_db.query(NISTCSFSubcategory).join(
            NISTCSFFunction
        ).filter(
            NISTCSFFunction.function_id == "GV"
        ).all()
        
        assert len(govern_subcategories) >= 2  # Should have GV.GV-01 and GV.RM-01 at minimum
        
        subcategory_ids = [subcat.subcategory_id for subcat in govern_subcategories]
        assert "GV.GV-01" in subcategory_ids
        assert "GV.RM-01" in subcategory_ids
    
    def test_search_subcategories_endpoint(self, test_db: Session):
        """Test searching NIST CSF subcategories via API."""
        # Import test data
        service = NISTCSFImportService(test_db)
        service.import_nist_csf_2_0()
        
        # Test search functionality
        from sqlalchemy import or_
        
        # Search for "strategy" subcategories
        search_results = test_db.query(NISTCSFSubcategory).filter(
            or_(
                NISTCSFSubcategory.name.ilike("%strategy%"),
                NISTCSFSubcategory.description.ilike("%strategy%")
            )
        ).all()
        
        assert len(search_results) >= 1
        
        # Should find GV.GV-01 which mentions strategy
        strategy_subcategory = next(
            (s for s in search_results if s.subcategory_id == "GV.GV-01"), 
            None
        )
        assert strategy_subcategory is not None


class TestNISTCSFValidation:
    """Test suite for NIST CSF 2.0 validation and error handling."""
    
    def test_import_handles_database_errors(self, test_db: Session):
        """Test that import handles database errors gracefully."""
        service = NISTCSFImportService(test_db)
        
        # Close the database connection to simulate an error
        test_db.close()
        
        with pytest.raises(Exception):
            service.import_nist_csf_2_0()
    
    def test_import_validates_data_integrity(self, test_db: Session):
        """Test that import validates data integrity."""
        service = NISTCSFImportService(test_db)
        result = service.import_nist_csf_2_0()
        
        assert result["success"] is True
        
        # Verify relationships are correct
        subcategories = test_db.query(NISTCSFSubcategory).all()
        for subcategory in subcategories:
            # Each subcategory should have valid category and function
            assert subcategory.category_id is not None
            assert subcategory.function_id is not None
            assert subcategory.version_id is not None
            
            # Category should exist
            category = test_db.query(NISTCSFCategory).filter(
                NISTCSFCategory.id == subcategory.category_id
            ).first()
            assert category is not None
            
            # Function should exist
            function = test_db.query(NISTCSFFunction).filter(
                NISTCSFFunction.id == subcategory.function_id
            ).first()
            assert function is not None
            
            # Version should exist
            version = test_db.query(NISTCSFVersion).filter(
                NISTCSFVersion.id == subcategory.version_id
            ).first()
            assert version is not None


class TestNISTCSFIntegration:
    """Integration tests for NIST CSF 2.0 with other system components."""
    
    def test_nist_csf_integrates_with_framework_mapping(self, test_db: Session):
        """Test that NIST CSF can be used in framework mappings."""
        # Import NIST CSF data
        service = NISTCSFImportService(test_db)
        result = service.import_nist_csf_2_0()
        assert result["success"] is True
        
        # Test that NIST CSF subcategories can be referenced in mappings
        subcategories = test_db.query(NISTCSFSubcategory).all()
        assert len(subcategories) > 0
        
        # Check that we can find specific subcategories for mapping
        pr_ac_01 = test_db.query(NISTCSFSubcategory).filter(
            NISTCSFSubcategory.subcategory_id == "PR.AC-01"
        ).first()
        
        if pr_ac_01:  # Only test if this subcategory exists in our sample data
            assert pr_ac_01.name is not None
            assert "credential" in pr_ac_01.name.lower() or "identity" in pr_ac_01.name.lower()
    
    def test_nist_csf_export_functionality(self, test_db: Session):
        """Test that NIST CSF data can be exported."""
        # Import NIST CSF data
        service = NISTCSFImportService(test_db)
        result = service.import_nist_csf_2_0()
        assert result["success"] is True
        
        # Test that data can be queried for export
        version = test_db.query(NISTCSFVersion).filter(
            NISTCSFVersion.version == "2.0"
        ).first()
        
        functions = test_db.query(NISTCSFFunction).filter(
            NISTCSFFunction.version_id == version.id
        ).all()
        
        categories = test_db.query(NISTCSFCategory).filter(
            NISTCSFCategory.version_id == version.id
        ).all()
        
        subcategories = test_db.query(NISTCSFSubcategory).filter(
            NISTCSFSubcategory.version_id == version.id
        ).all()
        
        assert len(functions) > 0
        assert len(categories) > 0
        assert len(subcategories) > 0
    
    def test_nist_csf_search_performance(self, test_db: Session):
        """Test that NIST CSF search performs well."""
        import time
        
        # Import NIST CSF data
        service = NISTCSFImportService(test_db)
        service.import_nist_csf_2_0()
        
        # Test search performance
        start_time = time.time()
        
        # Perform a complex search
        from sqlalchemy import or_
        results = test_db.query(NISTCSFSubcategory).filter(
            or_(
                NISTCSFSubcategory.name.ilike("%access%"),
                NISTCSFSubcategory.name.ilike("%control%"),
                NISTCSFSubcategory.name.ilike("%manage%")
            )
        ).all()
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # Search should complete quickly (under 1 second)
        assert execution_time < 1.0
        assert len(results) >= 0  # Should return some results
