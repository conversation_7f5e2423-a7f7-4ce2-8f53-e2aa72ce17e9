"""
TDD Tests for NIST CSF API Endpoints.

This module contains comprehensive failing tests for NIST Cybersecurity Framework
API endpoints following Test-Driven Development methodology. Tests cover hierarchical
data retrieval, version management, cross-framework queries, and error handling.
"""

import pytest
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.main import app
from api.core.database import get_db
from api.models.nist_csf import NISTCSFVersion, Function, Category, Subcategory
from api.models.user import User
from api.auth.dependencies import get_current_user
from tests.conftest import TestDatabase


class TestNISTCSFVersionEndpoints:
    """Test NIST CSF version management endpoints."""
    
    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()
        
        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user
    
    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()
    
    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.email = "<EMAIL>"
        user.is_active = True
        user.roles = ["admin"]
        return user
    
    def test_get_nist_csf_versions_empty_list(self):
        """Test GET /api/v1/nist-csf/versions returns empty list when no versions exist."""
        response = self.client.get("/api/v1/nist-csf/versions")
        
        assert response.status_code == 200
        data = response.json()
        assert data["versions"] == []
        assert data["total"] == 0
        assert data["page"] == 1
        assert data["page_size"] == 50
    
    def test_get_nist_csf_versions_with_pagination(self):
        """Test GET /api/v1/nist-csf/versions returns paginated version list."""
        # This test will fail until we implement the endpoint
        response = self.client.get("/api/v1/nist-csf/versions?page=1&page_size=10")
        
        assert response.status_code == 200
        data = response.json()
        assert "versions" in data
        assert "total" in data
        assert "page" in data
        assert "page_size" in data
        assert isinstance(data["versions"], list)
    
    def test_get_nist_csf_version_by_id_not_found(self):
        """Test GET /api/v1/nist-csf/versions/{id} returns 404 for non-existent version."""
        response = self.client.get("/api/v1/nist-csf/versions/999")
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
        assert "not found" in data["detail"].lower()
    
    def test_get_nist_csf_version_by_id_success(self):
        """Test GET /api/v1/nist-csf/versions/{id} returns version details."""
        # This test will fail until we implement the endpoint
        response = self.client.get("/api/v1/nist-csf/versions/1")
        
        # Should return 200 when version exists
        expected_fields = [
            "id", "version", "release_date", "description", 
            "is_current", "import_date", "created_at", "functions_count"
        ]
        
        if response.status_code == 200:
            data = response.json()
            for field in expected_fields:
                assert field in data
    
    def test_create_nist_csf_version_invalid_data(self):
        """Test POST /api/v1/nist-csf/versions with invalid data returns 422."""
        invalid_data = {
            "version": "",  # Empty version should be invalid
            "description": "Test version"
        }
        
        response = self.client.post(
            "/api/v1/nist-csf/versions",
            json=invalid_data
        )
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data
    
    def test_create_nist_csf_version_success(self):
        """Test POST /api/v1/nist-csf/versions creates new version."""
        valid_data = {
            "version": "2.0",
            "release_date": "2024-02-26",
            "description": "NIST Cybersecurity Framework 2.0",
            "is_current": True
        }
        
        response = self.client.post(
            "/api/v1/nist-csf/versions",
            json=valid_data
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 201
        data = response.json()
        assert data["version"] == valid_data["version"]
        assert data["description"] == valid_data["description"]
        assert "id" in data
        assert "created_at" in data
    
    def test_get_version_hierarchy(self):
        """Test GET /api/v1/nist-csf/versions/{id}/hierarchy returns complete hierarchy."""
        response = self.client.get("/api/v1/nist-csf/versions/1/hierarchy")
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "functions" in data
        assert isinstance(data["functions"], list)
        
        # Check hierarchical structure
        if data["functions"]:
            function = data["functions"][0]
            assert "categories" in function
            if function["categories"]:
                category = function["categories"][0]
                assert "subcategories" in category


class TestNISTCSFFunctionEndpoints:
    """Test NIST CSF function endpoints."""
    
    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()
        
        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user
    
    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()
    
    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user
    
    def test_get_functions_by_version(self):
        """Test GET /api/v1/nist-csf/versions/{version_id}/functions returns functions."""
        response = self.client.get("/api/v1/nist-csf/versions/1/functions")
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "functions" in data
        assert isinstance(data["functions"], list)
    
    def test_get_functions_with_categories(self):
        """Test GET functions with categories included."""
        response = self.client.get(
            "/api/v1/nist-csf/versions/1/functions?include_categories=true"
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "functions" in data
        
        # Check that categories are included
        if data["functions"]:
            function = data["functions"][0]
            assert "categories" in function
    
    def test_get_function_by_id(self):
        """Test GET /api/v1/nist-csf/functions/{id} returns function details."""
        response = self.client.get("/api/v1/nist-csf/functions/1")
        
        # This will fail until endpoint is implemented
        expected_fields = [
            "id", "function_id", "name", "description", 
            "version_id", "order_index", "categories_count"
        ]
        
        if response.status_code == 200:
            data = response.json()
            for field in expected_fields:
                assert field in data
    
    def test_get_function_not_found(self):
        """Test GET /api/v1/nist-csf/functions/{id} returns 404 for non-existent function."""
        response = self.client.get("/api/v1/nist-csf/functions/999")
        
        assert response.status_code == 404
    
    def test_create_function_success(self):
        """Test POST /api/v1/nist-csf/functions creates new function."""
        function_data = {
            "function_id": "ID",
            "name": "Identify",
            "description": "Develop organizational understanding to manage cybersecurity risk",
            "version_id": 1,
            "order_index": 1
        }
        
        response = self.client.post(
            "/api/v1/nist-csf/functions",
            json=function_data
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 201
        data = response.json()
        assert data["function_id"] == function_data["function_id"]
        assert data["name"] == function_data["name"]
    
    def test_update_function_success(self):
        """Test PUT /api/v1/nist-csf/functions/{id} updates function."""
        update_data = {
            "name": "Updated Identify Function",
            "description": "Updated description"
        }
        
        response = self.client.put(
            "/api/v1/nist-csf/functions/1",
            json=update_data
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == update_data["name"]


class TestNISTCSFCategoryEndpoints:
    """Test NIST CSF category endpoints."""
    
    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()
        
        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user
    
    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()
    
    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user
    
    def test_get_categories_by_function(self):
        """Test GET /api/v1/nist-csf/functions/{function_id}/categories returns categories."""
        response = self.client.get("/api/v1/nist-csf/functions/1/categories")
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "categories" in data
        assert isinstance(data["categories"], list)
    
    def test_get_categories_with_subcategories(self):
        """Test GET categories with subcategories included."""
        response = self.client.get(
            "/api/v1/nist-csf/functions/1/categories?include_subcategories=true"
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "categories" in data
        
        # Check that subcategories are included
        if data["categories"]:
            category = data["categories"][0]
            assert "subcategories" in category
    
    def test_get_category_by_id(self):
        """Test GET /api/v1/nist-csf/categories/{id} returns category details."""
        response = self.client.get("/api/v1/nist-csf/categories/1")
        
        # This will fail until endpoint is implemented
        expected_fields = [
            "id", "category_id", "name", "description", 
            "function_id", "order_index", "subcategories_count"
        ]
        
        if response.status_code == 200:
            data = response.json()
            for field in expected_fields:
                assert field in data
    
    def test_create_category_success(self):
        """Test POST /api/v1/nist-csf/categories creates new category."""
        category_data = {
            "category_id": "ID.AM",
            "name": "Asset Management",
            "description": "Physical devices and systems within the organization are inventoried",
            "function_id": 1,
            "order_index": 1
        }
        
        response = self.client.post(
            "/api/v1/nist-csf/categories",
            json=category_data
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 201
        data = response.json()
        assert data["category_id"] == category_data["category_id"]
        assert data["name"] == category_data["name"]


class TestNISTCSFSubcategoryEndpoints:
    """Test NIST CSF subcategory endpoints."""
    
    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()
        
        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user
    
    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()
    
    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user
    
    def test_get_subcategories_by_category(self):
        """Test GET /api/v1/nist-csf/categories/{category_id}/subcategories returns subcategories."""
        response = self.client.get("/api/v1/nist-csf/categories/1/subcategories")
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "subcategories" in data
        assert isinstance(data["subcategories"], list)
    
    def test_get_subcategories_with_pagination(self):
        """Test GET subcategories with pagination parameters."""
        response = self.client.get(
            "/api/v1/nist-csf/categories/1/subcategories?page=1&page_size=10"
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "subcategories" in data
        assert "total" in data
        assert "page" in data
        assert "page_size" in data
    
    def test_get_subcategory_by_id(self):
        """Test GET /api/v1/nist-csf/subcategories/{id} returns subcategory details."""
        response = self.client.get("/api/v1/nist-csf/subcategories/1")
        
        # This will fail until endpoint is implemented
        expected_fields = [
            "id", "subcategory_id", "name", "description", 
            "category_id", "order_index", "implementation_guidance",
            "informative_references", "mappings_count"
        ]
        
        if response.status_code == 200:
            data = response.json()
            for field in expected_fields:
                assert field in data
    
    def test_search_subcategories(self):
        """Test GET /api/v1/nist-csf/subcategories/search with search parameters."""
        response = self.client.get(
            "/api/v1/nist-csf/subcategories/search?q=asset&function_id=1&version_id=1"
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "subcategories" in data
        assert "total" in data
        assert "search_query" in data
    
    def test_create_subcategory_success(self):
        """Test POST /api/v1/nist-csf/subcategories creates new subcategory."""
        subcategory_data = {
            "subcategory_id": "ID.AM-1",
            "name": "Physical devices and systems within the organization are inventoried",
            "description": "Maintain an inventory of physical devices and systems",
            "category_id": 1,
            "order_index": 1,
            "implementation_guidance": "Develop and maintain an asset inventory",
            "informative_references": ["CIS Controls v8: 1.1", "ISO/IEC 27001:2013: A.8.1.1"]
        }
        
        response = self.client.post(
            "/api/v1/nist-csf/subcategories",
            json=subcategory_data
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 201
        data = response.json()
        assert data["subcategory_id"] == subcategory_data["subcategory_id"]
        assert data["name"] == subcategory_data["name"]
    
    def test_bulk_create_subcategories(self):
        """Test POST /api/v1/nist-csf/subcategories/bulk creates multiple subcategories."""
        subcategories_data = {
            "subcategories": [
                {
                    "subcategory_id": "ID.AM-1",
                    "name": "Physical devices and systems are inventoried",
                    "category_id": 1
                },
                {
                    "subcategory_id": "ID.AM-2",
                    "name": "Software platforms and applications are inventoried",
                    "category_id": 1
                }
            ]
        }
        
        response = self.client.post(
            "/api/v1/nist-csf/subcategories/bulk",
            json=subcategories_data
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 201
        data = response.json()
        assert "created_count" in data
        assert "subcategories" in data
        assert data["created_count"] == 2
    
    def test_get_subcategory_mappings(self):
        """Test GET /api/v1/nist-csf/subcategories/{id}/mappings returns subcategory mappings."""
        response = self.client.get("/api/v1/nist-csf/subcategories/1/mappings")
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "mappings" in data
        assert isinstance(data["mappings"], list)


class TestNISTCSFHierarchicalQueries:
    """Test hierarchical data retrieval for NIST CSF."""

    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()

        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user

    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()

    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user

    def test_get_complete_framework_hierarchy(self):
        """Test GET /api/v1/nist-csf/versions/{id}/complete returns full hierarchy."""
        response = self.client.get("/api/v1/nist-csf/versions/1/complete")

        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "version" in data
        assert "functions" in data

        # Verify complete hierarchical structure
        for function in data["functions"]:
            assert "categories" in function
            for category in function["categories"]:
                assert "subcategories" in category

    def test_get_hierarchy_with_depth_limit(self):
        """Test hierarchical query with depth limitation."""
        response = self.client.get(
            "/api/v1/nist-csf/versions/1/hierarchy?max_depth=2"
        )

        assert response.status_code == 200
        data = response.json()
        assert "functions" in data

        # Should include functions and categories but not subcategories
        for function in data["functions"]:
            assert "categories" in function
            for category in function["categories"]:
                assert "subcategories" not in category

    def test_get_hierarchy_with_filtering(self):
        """Test hierarchical query with function filtering."""
        response = self.client.get(
            "/api/v1/nist-csf/versions/1/hierarchy?functions=ID,PR&include_mappings=true"
        )

        assert response.status_code == 200
        data = response.json()
        assert "functions" in data

        # Should only include specified functions
        function_ids = [f["function_id"] for f in data["functions"]]
        assert all(fid in ["ID", "PR"] for fid in function_ids)

    def test_get_function_tree(self):
        """Test GET /api/v1/nist-csf/functions/{id}/tree returns function tree."""
        response = self.client.get("/api/v1/nist-csf/functions/1/tree")

        assert response.status_code == 200
        data = response.json()
        assert "function" in data
        assert "categories" in data["function"]

        # Verify tree structure with counts
        assert "total_categories" in data
        assert "total_subcategories" in data

    def test_get_category_tree(self):
        """Test GET /api/v1/nist-csf/categories/{id}/tree returns category tree."""
        response = self.client.get("/api/v1/nist-csf/categories/1/tree")

        assert response.status_code == 200
        data = response.json()
        assert "category" in data
        assert "subcategories" in data["category"]
        assert "parent_function" in data


class TestNISTCSFImportExportEndpoints:
    """Test NIST CSF import/export endpoints."""

    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()

        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user

    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()

    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user

    def test_import_nist_csf_json_success(self):
        """Test POST /api/v1/nist-csf/import/json imports NIST CSF data."""
        import_data = {
            "version": "2.0",
            "release_date": "2024-02-26",
            "description": "NIST Cybersecurity Framework 2.0",
            "functions": [
                {
                    "function_id": "ID",
                    "name": "Identify",
                    "description": "Develop organizational understanding",
                    "categories": [
                        {
                            "category_id": "ID.AM",
                            "name": "Asset Management",
                            "description": "Physical devices and systems are inventoried",
                            "subcategories": [
                                {
                                    "subcategory_id": "ID.AM-1",
                                    "name": "Physical devices are inventoried",
                                    "description": "Maintain inventory of physical devices"
                                }
                            ]
                        }
                    ]
                }
            ]
        }

        response = self.client.post(
            "/api/v1/nist-csf/import/json",
            json=import_data
        )

        # This will fail until endpoint is implemented
        assert response.status_code == 201
        data = response.json()
        assert "import_id" in data
        assert "status" in data
        assert data["status"] == "completed"

    def test_import_nist_csf_file_upload(self):
        """Test POST /api/v1/nist-csf/import/file uploads and imports NIST CSF file."""
        # Mock file upload
        files = {
            "file": ("nist_csf_data.json", '{"version": "2.0"}', "application/json")
        }

        response = self.client.post(
            "/api/v1/nist-csf/import/file",
            files=files
        )

        # This will fail until endpoint is implemented
        assert response.status_code == 201
        data = response.json()
        assert "import_id" in data

    def test_export_nist_csf_json(self):
        """Test GET /api/v1/nist-csf/export/json exports NIST CSF data."""
        response = self.client.get("/api/v1/nist-csf/export/json?version_id=1")

        # This will fail until endpoint is implemented
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"

    def test_export_nist_csf_csv(self):
        """Test GET /api/v1/nist-csf/export/csv exports NIST CSF data as CSV."""
        response = self.client.get(
            "/api/v1/nist-csf/export/csv?version_id=1&flatten_hierarchy=true"
        )

        assert response.status_code == 200
        assert response.headers["content-type"] == "text/csv"

    def test_export_nist_csf_with_mappings(self):
        """Test export with cross-framework mappings included."""
        response = self.client.get(
            "/api/v1/nist-csf/export/json?version_id=1&include_mappings=true&target_frameworks=isf,mitre_attack"
        )

        assert response.status_code == 200
        data = response.json()
        assert "mappings" in data
        assert "mapping_statistics" in data


class TestNISTCSFCrossFrameworkQueries:
    """Test cross-framework queries for NIST CSF."""

    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()

        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user

    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()

    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user

    def test_get_subcategory_cross_mappings(self):
        """Test GET subcategory with cross-framework mappings."""
        response = self.client.get(
            "/api/v1/nist-csf/subcategories/1/cross-mappings?frameworks=isf,mitre_attack"
        )

        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "subcategory" in data
        assert "cross_mappings" in data
        assert "isf_mappings" in data["cross_mappings"]
        assert "mitre_attack_mappings" in data["cross_mappings"]

    def test_find_related_controls(self):
        """Test finding related controls across frameworks."""
        response = self.client.get(
            "/api/v1/nist-csf/subcategories/1/related-controls?frameworks=isf&min_effectiveness=0.7"
        )

        assert response.status_code == 200
        data = response.json()
        assert "related_controls" in data
        assert "effectiveness_scores" in data

    def test_get_coverage_analysis(self):
        """Test coverage analysis across frameworks."""
        response = self.client.get(
            "/api/v1/nist-csf/versions/1/coverage-analysis?target_framework=isf"
        )

        assert response.status_code == 200
        data = response.json()
        assert "coverage_summary" in data
        assert "covered_subcategories" in data
        assert "uncovered_subcategories" in data
        assert "coverage_percentage" in data

    def test_get_gap_analysis(self):
        """Test gap analysis between NIST CSF and other frameworks."""
        response = self.client.get(
            "/api/v1/nist-csf/versions/1/gap-analysis?compare_frameworks=isf,mitre_attack"
        )

        assert response.status_code == 200
        data = response.json()
        assert "gap_summary" in data
        assert "missing_mappings" in data
        assert "weak_mappings" in data
        assert "recommendations" in data


class TestNISTCSFAdvancedFiltering:
    """Test advanced filtering capabilities for NIST CSF endpoints."""

    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()

        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user

    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()

    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user

    def test_filter_subcategories_by_informative_references(self):
        """Test filtering subcategories by informative references."""
        response = self.client.get(
            "/api/v1/nist-csf/subcategories/search?informative_references=CIS,ISO"
        )

        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "subcategories" in data

        # All returned subcategories should have specified references
        for subcategory in data["subcategories"]:
            refs = subcategory.get("informative_references", [])
            assert any("CIS" in ref or "ISO" in ref for ref in refs)

    def test_complex_hierarchical_search(self):
        """Test complex search across hierarchical structure."""
        response = self.client.get(
            "/api/v1/nist-csf/search?"
            "q=asset management&"
            "functions=ID,PR&"
            "has_mappings=true&"
            "mapping_frameworks=isf&"
            "min_effectiveness=0.8&"
            "order_by=relevance&"
            "include_hierarchy=true"
        )

        assert response.status_code == 200
        data = response.json()
        assert "results" in data
        assert "search_metadata" in data
        assert "hierarchy_context" in data

    def test_filter_by_implementation_guidance(self):
        """Test filtering by implementation guidance content."""
        response = self.client.get(
            "/api/v1/nist-csf/subcategories/search?guidance_contains=inventory,baseline"
        )

        assert response.status_code == 200
        data = response.json()
        assert "subcategories" in data

        # Check that implementation guidance contains specified terms
        for subcategory in data["subcategories"]:
            guidance = subcategory.get("implementation_guidance", "").lower()
            assert "inventory" in guidance or "baseline" in guidance

    def test_date_based_filtering(self):
        """Test filtering by creation and modification dates."""
        response = self.client.get(
            "/api/v1/nist-csf/subcategories/search?"
            "created_after=2024-01-01&"
            "modified_since=2024-02-01&"
            "version_release_after=2024-01-01"
        )

        assert response.status_code == 200
        data = response.json()
        assert "subcategories" in data
        assert "date_filters_applied" in data


class TestNISTCSFPerformanceAndCaching:
    """Test performance optimizations for NIST CSF endpoints."""

    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()

        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user

    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()

    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user

    def test_hierarchical_query_performance(self):
        """Test performance of hierarchical queries with large datasets."""
        response = self.client.get(
            "/api/v1/nist-csf/versions/1/complete?performance_mode=optimized"
        )

        assert response.status_code == 200
        data = response.json()

        # Should include performance metadata
        assert "query_performance" in data.get("metadata", {})
        assert "cache_status" in data.get("metadata", {})

    def test_pagination_with_hierarchical_data(self):
        """Test pagination performance with hierarchical data."""
        response = self.client.get(
            "/api/v1/nist-csf/subcategories/search?page=1&page_size=100&include_hierarchy=true"
        )

        assert response.status_code == 200
        data = response.json()
        assert "subcategories" in data
        assert "pagination" in data
        assert "performance_metrics" in data.get("metadata", {})

    def test_concurrent_hierarchical_requests(self):
        """Test handling of concurrent hierarchical requests."""
        import threading

        results = []

        def make_request():
            response = self.client.get("/api/v1/nist-csf/versions/1/hierarchy")
            results.append(response.status_code)

        # Create multiple threads for concurrent requests
        threads = []
        for _ in range(3):
            thread = threading.Thread(target=make_request)
            threads.append(thread)
            thread.start()

        # Wait for all threads to complete
        for thread in threads:
            thread.join()

        # All requests should succeed
        assert all(status == 200 for status in results)
        assert len(results) == 3
