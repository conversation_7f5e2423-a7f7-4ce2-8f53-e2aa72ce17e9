"""
Tests for Framework Mapping functionality.

This module contains comprehensive tests for the framework mapping system,
including cross-framework mappings, validation, and analytics.
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime

from api.main import app
from api.database import get_db
from api.models.framework_mapping import FrameworkMapping, MappingSet, MappingValidation
from api.services.framework_mapping import FrameworkMappingService
from api.schemas.framework_mapping import (
    FrameworkMappingCreate, MappingSetCreate, MappingValidationCreate,
    MappingSearchRequest
)
from tests.conftest import test_db


client = TestClient(app)


class TestFrameworkMappingService:
    """Test suite for framework mapping service functionality."""
    
    def test_create_mapping(self, test_db: Session):
        """Test creating a new framework mapping."""
        service = FrameworkMappingService(test_db)
        
        mapping_data = FrameworkMappingCreate(
            source_framework="ISF",
            source_control_id="ISF.05.01",
            target_framework="NIST_CSF",
            target_control_id="PR.AC-1",
            mapping_type="direct",
            confidence_level="high",
            confidence_score=0.9,
            description="Access control policy mapping"
        )
        
        mapping = service.create_mapping(mapping_data, "test_user")
        
        assert mapping.id is not None
        assert mapping.source_framework == "ISF"
        assert mapping.source_control_id == "ISF.05.01"
        assert mapping.target_framework == "NIST_CSF"
        assert mapping.target_control_id == "PR.AC-1"
        assert mapping.confidence_score == 0.9
        assert mapping.created_by == "test_user"
    
    def test_create_duplicate_mapping_fails(self, test_db: Session):
        """Test that creating duplicate mappings fails."""
        service = FrameworkMappingService(test_db)
        
        mapping_data = FrameworkMappingCreate(
            source_framework="ISF",
            source_control_id="ISF.05.01",
            target_framework="NIST_CSF",
            target_control_id="PR.AC-1"
        )
        
        # Create first mapping
        service.create_mapping(mapping_data, "test_user")
        
        # Attempt to create duplicate should fail
        with pytest.raises(ValueError, match="Mapping already exists"):
            service.create_mapping(mapping_data, "test_user")
    
    def test_update_mapping(self, test_db: Session):
        """Test updating an existing mapping."""
        service = FrameworkMappingService(test_db)
        
        # Create mapping
        mapping_data = FrameworkMappingCreate(
            source_framework="ISF",
            source_control_id="ISF.05.01",
            target_framework="NIST_CSF",
            target_control_id="PR.AC-1",
            confidence_score=0.7
        )
        
        mapping = service.create_mapping(mapping_data, "test_user")
        
        # Update mapping
        from api.schemas.framework_mapping import FrameworkMappingUpdate
        update_data = FrameworkMappingUpdate(
            confidence_score=0.9,
            description="Updated mapping description"
        )
        
        updated_mapping = service.update_mapping(mapping.id, update_data, "test_user")
        
        assert updated_mapping.confidence_score == 0.9
        assert updated_mapping.description == "Updated mapping description"
    
    def test_delete_mapping(self, test_db: Session):
        """Test soft deleting a mapping."""
        service = FrameworkMappingService(test_db)
        
        # Create mapping
        mapping_data = FrameworkMappingCreate(
            source_framework="ISF",
            source_control_id="ISF.05.01",
            target_framework="NIST_CSF",
            target_control_id="PR.AC-1"
        )
        
        mapping = service.create_mapping(mapping_data, "test_user")
        
        # Delete mapping
        result = service.delete_mapping(mapping.id, "test_user")
        assert result is True
        
        # Verify mapping is soft deleted
        deleted_mapping = test_db.query(FrameworkMapping).filter(
            FrameworkMapping.id == mapping.id
        ).first()
        assert deleted_mapping.deleted_at is not None
    
    def test_search_mappings(self, test_db: Session):
        """Test searching mappings with filters."""
        service = FrameworkMappingService(test_db)
        
        # Create test mappings
        mappings_data = [
            FrameworkMappingCreate(
                source_framework="ISF",
                source_control_id="ISF.05.01",
                target_framework="NIST_CSF",
                target_control_id="PR.AC-1",
                mapping_type="direct"
            ),
            FrameworkMappingCreate(
                source_framework="ISF",
                source_control_id="ISF.05.02",
                target_framework="NIST_CSF",
                target_control_id="PR.AC-2",
                mapping_type="partial"
            ),
            FrameworkMappingCreate(
                source_framework="MITRE_ATTACK",
                source_control_id="T1078",
                target_framework="ISF",
                target_control_id="ISF.05.01",
                mapping_type="related"
            )
        ]
        
        for mapping_data in mappings_data:
            service.create_mapping(mapping_data, "test_user")
        
        # Search by source framework
        search_request = MappingSearchRequest(source_framework="ISF")
        mappings, total = service.search_mappings(search_request)
        
        assert total == 2
        assert all(m.source_framework == "ISF" for m in mappings)
        
        # Search by mapping type
        search_request = MappingSearchRequest(mapping_type="direct")
        mappings, total = service.search_mappings(search_request)
        
        assert total == 1
        assert mappings[0].mapping_type == "direct"
    
    def test_validate_mapping(self, test_db: Session):
        """Test validating a mapping."""
        service = FrameworkMappingService(test_db)
        
        # Create mapping
        mapping_data = FrameworkMappingCreate(
            source_framework="ISF",
            source_control_id="ISF.05.01",
            target_framework="NIST_CSF",
            target_control_id="PR.AC-1"
        )
        
        mapping = service.create_mapping(mapping_data, "test_user")
        
        # Validate mapping
        validation_data = MappingValidationCreate(
            mapping_id=mapping.id,
            validation_status="approved",
            comments="Mapping is accurate and complete",
            accuracy_score=0.9,
            completeness_score=0.8,
            relevance_score=0.9
        )
        
        validation = service.validate_mapping(validation_data, "reviewer_user")
        
        assert validation.validation_status == "approved"
        assert validation.accuracy_score == 0.9
        
        # Check that mapping is marked as validated
        test_db.refresh(mapping)
        assert mapping.is_validated is True
        assert mapping.validated_by == "reviewer_user"
    
    def test_create_mapping_set(self, test_db: Session):
        """Test creating a mapping set."""
        service = FrameworkMappingService(test_db)
        
        set_data = MappingSetCreate(
            name="ISF to NIST CSF Mappings",
            description="Official mappings between ISF and NIST CSF",
            source_framework="ISF",
            target_framework="NIST_CSF",
            is_official=True
        )
        
        mapping_set = service.create_mapping_set(set_data, "test_user")
        
        assert mapping_set.id is not None
        assert mapping_set.name == "ISF to NIST CSF Mappings"
        assert mapping_set.source_framework == "ISF"
        assert mapping_set.target_framework == "NIST_CSF"
        assert mapping_set.is_official is True
    
    def test_get_mapping_analytics(self, test_db: Session):
        """Test getting mapping analytics."""
        service = FrameworkMappingService(test_db)
        
        # Create test mappings
        mappings_data = [
            FrameworkMappingCreate(
                source_framework="ISF",
                source_control_id="ISF.05.01",
                target_framework="NIST_CSF",
                target_control_id="PR.AC-1",
                mapping_type="direct",
                confidence_score=0.9
            ),
            FrameworkMappingCreate(
                source_framework="ISF",
                source_control_id="ISF.05.02",
                target_framework="NIST_CSF",
                target_control_id="PR.AC-2",
                mapping_type="partial",
                confidence_score=0.7
            )
        ]
        
        for mapping_data in mappings_data:
            service.create_mapping(mapping_data, "test_user")
        
        analytics = service.get_mapping_analytics()
        
        assert analytics["total_mappings"] == 2
        assert "ISF->NIST_CSF" in analytics["mappings_by_framework"]
        assert analytics["mappings_by_framework"]["ISF->NIST_CSF"] == 2
        assert analytics["mappings_by_type"]["direct"] == 1
        assert analytics["mappings_by_type"]["partial"] == 1
        assert analytics["average_confidence_score"] == 0.8


class TestFrameworkMappingAPI:
    """Test suite for framework mapping API endpoints."""
    
    def test_create_mapping_endpoint(self):
        """Test creating mapping via API endpoint."""
        mapping_data = {
            "source_framework": "ISF",
            "source_control_id": "ISF.05.01",
            "target_framework": "NIST_CSF",
            "target_control_id": "PR.AC-1",
            "mapping_type": "direct",
            "confidence_level": "high",
            "confidence_score": 0.9,
            "description": "Access control policy mapping"
        }
        
        # Note: This would require proper authentication setup
        # For now, testing the service directly
        # response = client.post("/api/v1/framework-mappings/mappings", json=mapping_data)
        # assert response.status_code == 201
        
        # Test service directly instead
        from api.database import SessionLocal
        db = SessionLocal()
        try:
            service = FrameworkMappingService(db)
            mapping_create = FrameworkMappingCreate(**mapping_data)
            mapping = service.create_mapping(mapping_create, "test_user")
            assert mapping.source_framework == "ISF"
        finally:
            db.close()
    
    def test_search_mappings_endpoint(self):
        """Test searching mappings via API endpoint."""
        # Note: This would require proper authentication setup
        # For now, testing the service directly
        
        from api.database import SessionLocal
        db = SessionLocal()
        try:
            service = FrameworkMappingService(db)
            
            # Create test mapping
            mapping_data = FrameworkMappingCreate(
                source_framework="ISF",
                source_control_id="ISF.05.01",
                target_framework="NIST_CSF",
                target_control_id="PR.AC-1"
            )
            service.create_mapping(mapping_data, "test_user")
            
            # Search mappings
            search_request = MappingSearchRequest(source_framework="ISF")
            mappings, total = service.search_mappings(search_request)
            
            assert total >= 1
            assert any(m.source_framework == "ISF" for m in mappings)
        finally:
            db.close()


class TestFrameworkMappingValidation:
    """Test suite for framework mapping validation and error handling."""
    
    def test_invalid_confidence_score(self, test_db: Session):
        """Test that invalid confidence scores are rejected."""
        service = FrameworkMappingService(test_db)
        
        # Test confidence score > 1.0
        with pytest.raises(ValueError):
            mapping_data = FrameworkMappingCreate(
                source_framework="ISF",
                source_control_id="ISF.05.01",
                target_framework="NIST_CSF",
                target_control_id="PR.AC-1",
                confidence_score=1.5  # Invalid: > 1.0
            )
        
        # Test confidence score < 0.0
        with pytest.raises(ValueError):
            mapping_data = FrameworkMappingCreate(
                source_framework="ISF",
                source_control_id="ISF.05.01",
                target_framework="NIST_CSF",
                target_control_id="PR.AC-1",
                confidence_score=-0.1  # Invalid: < 0.0
            )
    
    def test_update_nonexistent_mapping(self, test_db: Session):
        """Test updating a non-existent mapping."""
        service = FrameworkMappingService(test_db)
        
        from api.schemas.framework_mapping import FrameworkMappingUpdate
        update_data = FrameworkMappingUpdate(confidence_score=0.9)
        
        with pytest.raises(ValueError, match="Mapping not found"):
            service.update_mapping(99999, update_data, "test_user")
    
    def test_validate_nonexistent_mapping(self, test_db: Session):
        """Test validating a non-existent mapping."""
        service = FrameworkMappingService(test_db)
        
        validation_data = MappingValidationCreate(
            mapping_id=99999,
            validation_status="approved"
        )
        
        with pytest.raises(ValueError, match="Mapping not found"):
            service.validate_mapping(validation_data, "reviewer_user")


class TestFrameworkMappingIntegration:
    """Integration tests for framework mapping with other system components."""
    
    def test_mapping_with_isf_controls(self, test_db: Session):
        """Test creating mappings that reference ISF controls."""
        service = FrameworkMappingService(test_db)
        
        # Create mapping referencing ISF control
        mapping_data = FrameworkMappingCreate(
            source_framework="ISF",
            source_control_id="ISF.05.01",  # This should reference actual ISF control
            target_framework="NIST_CSF",
            target_control_id="PR.AC-1",
            description="Maps ISF access control policy to NIST CSF"
        )
        
        mapping = service.create_mapping(mapping_data, "test_user")
        assert mapping.source_control_id == "ISF.05.01"
    
    def test_mapping_audit_trail(self, test_db: Session):
        """Test that mapping changes create audit trail entries."""
        service = FrameworkMappingService(test_db)
        
        # Create mapping
        mapping_data = FrameworkMappingCreate(
            source_framework="ISF",
            source_control_id="ISF.05.01",
            target_framework="NIST_CSF",
            target_control_id="PR.AC-1"
        )
        
        mapping = service.create_mapping(mapping_data, "test_user")
        
        # Check audit trail
        from api.models.framework_mapping import MappingAudit
        audit_entries = test_db.query(MappingAudit).filter(
            MappingAudit.mapping_id == mapping.id
        ).all()
        
        assert len(audit_entries) >= 1
        assert any(entry.action == "created" for entry in audit_entries)
