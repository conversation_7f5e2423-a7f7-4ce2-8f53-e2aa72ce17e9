"""
Tests for ISF Data Import functionality.

This module contains comprehensive tests for the ISF data import service,
including ISF 2022 framework data import and validation.
"""

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.orm import Session
from datetime import datetime

from api.main import app
from api.database import get_db
from api.models.isf import ISFVersion, ISFSecurityArea, ISFControl
from api.services.isf_import import ISFImportService, import_isf_2022_data
from tests.conftest import test_db


client = TestClient(app)


class TestISFImportService:
    """Test suite for ISF import service functionality."""
    
    def test_import_isf_2022_creates_version(self, test_db: Session):
        """Test that ISF 2022 import creates the correct version."""
        service = ISFImportService(test_db)
        result = service.import_isf_2022()
        
        assert result["success"] is True
        assert result["version"] == "2022"
        
        # Verify version was created
        version = test_db.query(ISFVersion).filter(
            ISFVersion.version == "2022"
        ).first()
        
        assert version is not None
        assert version.is_current is True
        assert version.description is not None
    
    def test_import_isf_2022_creates_security_areas(self, test_db: Session):
        """Test that ISF 2022 import creates all security areas."""
        service = ISFImportService(test_db)
        result = service.import_isf_2022()
        
        assert result["success"] is True
        
        # Verify security areas were created
        areas = test_db.query(ISFSecurityArea).all()
        assert len(areas) == 14  # ISF 2022 has 14 security areas
        
        # Check specific areas
        area_ids = [area.area_id for area in areas]
        assert "ISF.01" in area_ids
        assert "ISF.05" in area_ids
        assert "ISF.14" in area_ids
    
    def test_import_isf_2022_creates_controls(self, test_db: Session):
        """Test that ISF 2022 import creates controls."""
        service = ISFImportService(test_db)
        result = service.import_isf_2022()
        
        assert result["success"] is True
        
        # Verify controls were created
        controls = test_db.query(ISFControl).all()
        assert len(controls) > 0
        
        # Check specific controls
        control_ids = [control.control_id for control in controls]
        assert "ISF.01.01" in control_ids
        assert "ISF.05.01" in control_ids
    
    def test_import_isf_2022_idempotent(self, test_db: Session):
        """Test that running import twice doesn't create duplicates."""
        service = ISFImportService(test_db)
        
        # First import
        result1 = service.import_isf_2022()
        assert result1["success"] is True
        
        # Count records
        version_count_1 = test_db.query(ISFVersion).count()
        area_count_1 = test_db.query(ISFSecurityArea).count()
        control_count_1 = test_db.query(ISFControl).count()
        
        # Second import
        result2 = service.import_isf_2022()
        assert result2["success"] is True
        
        # Count records again
        version_count_2 = test_db.query(ISFVersion).count()
        area_count_2 = test_db.query(ISFSecurityArea).count()
        control_count_2 = test_db.query(ISFControl).count()
        
        # Should be the same (no duplicates)
        assert version_count_1 == version_count_2
        assert area_count_1 == area_count_2
        assert control_count_1 == control_count_2
    
    def test_import_isf_2022_sets_current_version(self, test_db: Session):
        """Test that ISF 2022 import sets the version as current."""
        # Create an existing version
        old_version = ISFVersion(
            version="2021",
            release_date="2021-01-01",
            description="Old version",
            is_current=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        test_db.add(old_version)
        test_db.commit()
        
        # Import ISF 2022
        service = ISFImportService(test_db)
        result = service.import_isf_2022()
        
        assert result["success"] is True
        
        # Check that old version is no longer current
        test_db.refresh(old_version)
        assert old_version.is_current is False
        
        # Check that new version is current
        new_version = test_db.query(ISFVersion).filter(
            ISFVersion.version == "2022"
        ).first()
        assert new_version.is_current is True
    
    def test_import_isf_2022_statistics(self, test_db: Session):
        """Test that import returns correct statistics."""
        service = ISFImportService(test_db)
        result = service.import_isf_2022()
        
        assert result["success"] is True
        assert "statistics" in result
        
        stats = result["statistics"]
        assert stats["security_areas"] == 14
        assert stats["controls"] > 0
        assert stats["total_records"] > 14  # version + areas + controls


class TestISFImportEndpoints:
    """Test suite for ISF import API endpoints."""
    
    def test_import_isf_2022_endpoint_requires_admin(self):
        """Test that ISF 2022 import endpoint requires admin privileges."""
        response = client.post("/api/v1/isf/import/isf-2022")
        
        # Should require authentication
        assert response.status_code in [401, 403]
    
    def test_import_isf_2022_endpoint_success(self, test_db: Session):
        """Test successful ISF 2022 import via API endpoint."""
        # Mock admin user authentication
        # In a real test, you would set up proper authentication
        
        # For now, test the service directly
        result = import_isf_2022_data(test_db)
        
        assert result["success"] is True
        assert result["version"] == "2022"
        assert "statistics" in result


class TestISFImportValidation:
    """Test suite for ISF import validation and error handling."""
    
    def test_import_handles_database_errors(self, test_db: Session):
        """Test that import handles database errors gracefully."""
        service = ISFImportService(test_db)
        
        # Close the database connection to simulate an error
        test_db.close()
        
        with pytest.raises(Exception):
            service.import_isf_2022()
    
    def test_import_validates_data_integrity(self, test_db: Session):
        """Test that import validates data integrity."""
        service = ISFImportService(test_db)
        result = service.import_isf_2022()
        
        assert result["success"] is True
        
        # Verify relationships are correct
        controls = test_db.query(ISFControl).all()
        for control in controls:
            # Each control should have a valid security area
            assert control.security_area_id is not None
            
            # Security area should exist
            area = test_db.query(ISFSecurityArea).filter(
                ISFSecurityArea.id == control.security_area_id
            ).first()
            assert area is not None
            
            # Version should exist
            version = test_db.query(ISFVersion).filter(
                ISFVersion.id == control.version_id
            ).first()
            assert version is not None


class TestISFImportPerformance:
    """Test suite for ISF import performance and scalability."""
    
    def test_import_performance(self, test_db: Session):
        """Test that import completes within reasonable time."""
        import time
        
        service = ISFImportService(test_db)
        
        start_time = time.time()
        result = service.import_isf_2022()
        end_time = time.time()
        
        assert result["success"] is True
        
        # Import should complete within 10 seconds
        execution_time = end_time - start_time
        assert execution_time < 10.0
    
    def test_import_memory_usage(self, test_db: Session):
        """Test that import doesn't consume excessive memory."""
        import psutil
        import os
        
        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss
        
        service = ISFImportService(test_db)
        result = service.import_isf_2022()
        
        final_memory = process.memory_info().rss
        memory_increase = final_memory - initial_memory
        
        assert result["success"] is True
        
        # Memory increase should be reasonable (less than 100MB)
        assert memory_increase < 100 * 1024 * 1024


class TestISFImportIntegration:
    """Integration tests for ISF import with other system components."""
    
    def test_import_integrates_with_search(self, test_db: Session):
        """Test that imported data is searchable."""
        # Import data
        service = ISFImportService(test_db)
        result = service.import_isf_2022()
        assert result["success"] is True
        
        # Test search functionality
        controls = test_db.query(ISFControl).filter(
            ISFControl.name.ilike("%policy%")
        ).all()
        
        assert len(controls) > 0
    
    def test_import_integrates_with_export(self, test_db: Session):
        """Test that imported data can be exported."""
        # Import data
        service = ISFImportService(test_db)
        result = service.import_isf_2022()
        assert result["success"] is True
        
        # Test that data can be queried for export
        version = test_db.query(ISFVersion).filter(
            ISFVersion.version == "2022"
        ).first()
        
        areas = test_db.query(ISFSecurityArea).filter(
            ISFSecurityArea.version_id == version.id
        ).all()
        
        controls = test_db.query(ISFControl).filter(
            ISFControl.version_id == version.id
        ).all()
        
        assert len(areas) > 0
        assert len(controls) > 0
