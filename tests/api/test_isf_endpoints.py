"""
TDD Tests for ISF API Endpoints.

This module contains comprehensive failing tests for ISF framework API endpoints
following Test-Driven Development methodology. Tests cover CRUD operations,
filtering, pagination, authentication, and error handling.
"""

import pytest
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any
from unittest.mock import Mock, patch

from fastapi.testclient import TestClient
from sqlalchemy.orm import Session

from api.main import app
from api.core.database import get_db
from api.models.isf import ISFVersion, ISFSecurityArea, ISFControl
from api.models.user import User
from api.auth.dependencies import get_current_user
from tests.conftest import TestDatabase


class TestISFVersionEndpoints:
    """Test ISF version management endpoints."""
    
    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()
        
        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user
    
    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()
    
    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.email = "<EMAIL>"
        user.is_active = True
        user.roles = ["admin"]
        return user
    
    def test_get_isf_versions_empty_list(self):
        """Test GET /api/v1/isf/versions returns empty list when no versions exist."""
        response = self.client.get("/api/v1/isf/versions")
        
        assert response.status_code == 200
        data = response.json()
        assert data["versions"] == []
        assert data["total"] == 0
        assert data["page"] == 1
        assert data["page_size"] == 50
    
    def test_get_isf_versions_with_data(self):
        """Test GET /api/v1/isf/versions returns paginated version list."""
        # This test will fail until we implement the endpoint
        response = self.client.get("/api/v1/isf/versions?page=1&page_size=10")
        
        assert response.status_code == 200
        data = response.json()
        assert "versions" in data
        assert "total" in data
        assert "page" in data
        assert "page_size" in data
        assert isinstance(data["versions"], list)
    
    def test_get_isf_version_by_id_not_found(self):
        """Test GET /api/v1/isf/versions/{id} returns 404 for non-existent version."""
        response = self.client.get("/api/v1/isf/versions/999")
        
        assert response.status_code == 404
        data = response.json()
        assert "detail" in data
        assert "not found" in data["detail"].lower()
    
    def test_get_isf_version_by_id_success(self):
        """Test GET /api/v1/isf/versions/{id} returns version details."""
        # This test will fail until we implement the endpoint
        response = self.client.get("/api/v1/isf/versions/1")
        
        # Should return 200 when version exists
        expected_fields = [
            "id", "version", "release_date", "description", 
            "is_current", "import_date", "created_at"
        ]
        
        if response.status_code == 200:
            data = response.json()
            for field in expected_fields:
                assert field in data
    
    def test_create_isf_version_invalid_data(self):
        """Test POST /api/v1/isf/versions with invalid data returns 422."""
        invalid_data = {
            "version": "",  # Empty version should be invalid
            "description": "Test version"
        }
        
        response = self.client.post(
            "/api/v1/isf/versions",
            json=invalid_data
        )
        
        assert response.status_code == 422
        data = response.json()
        assert "detail" in data
    
    def test_create_isf_version_success(self):
        """Test POST /api/v1/isf/versions creates new version."""
        valid_data = {
            "version": "2024.1",
            "release_date": "2024-01-15",
            "description": "Test ISF version",
            "is_current": True
        }
        
        response = self.client.post(
            "/api/v1/isf/versions",
            json=valid_data
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 201
        data = response.json()
        assert data["version"] == valid_data["version"]
        assert data["description"] == valid_data["description"]
        assert "id" in data
        assert "created_at" in data
    
    def test_create_isf_version_duplicate(self):
        """Test POST /api/v1/isf/versions with duplicate version returns 409."""
        version_data = {
            "version": "2024.1",
            "description": "Test version"
        }
        
        # First creation should succeed
        response1 = self.client.post("/api/v1/isf/versions", json=version_data)
        
        # Second creation should fail with conflict
        response2 = self.client.post("/api/v1/isf/versions", json=version_data)
        
        assert response2.status_code == 409
        data = response2.json()
        assert "already exists" in data["detail"].lower()
    
    def test_update_isf_version_not_found(self):
        """Test PUT /api/v1/isf/versions/{id} returns 404 for non-existent version."""
        update_data = {
            "description": "Updated description"
        }
        
        response = self.client.put(
            "/api/v1/isf/versions/999",
            json=update_data
        )
        
        assert response.status_code == 404
    
    def test_update_isf_version_success(self):
        """Test PUT /api/v1/isf/versions/{id} updates version."""
        update_data = {
            "description": "Updated description",
            "is_current": False
        }
        
        response = self.client.put(
            "/api/v1/isf/versions/1",
            json=update_data
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert data["description"] == update_data["description"]
        assert data["is_current"] == update_data["is_current"]
    
    def test_delete_isf_version_not_found(self):
        """Test DELETE /api/v1/isf/versions/{id} returns 404 for non-existent version."""
        response = self.client.delete("/api/v1/isf/versions/999")
        
        assert response.status_code == 404
    
    def test_delete_isf_version_success(self):
        """Test DELETE /api/v1/isf/versions/{id} soft deletes version."""
        response = self.client.delete("/api/v1/isf/versions/1")
        
        # This will fail until endpoint is implemented
        assert response.status_code == 204
    
    def test_set_current_version_success(self):
        """Test POST /api/v1/isf/versions/{id}/set-current sets version as current."""
        response = self.client.post("/api/v1/isf/versions/1/set-current")
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert data["is_current"] is True
        assert "message" in data


class TestISFSecurityAreaEndpoints:
    """Test ISF security area endpoints."""
    
    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()
        
        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user
    
    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()
    
    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user
    
    def test_get_security_areas_by_version(self):
        """Test GET /api/v1/isf/versions/{version_id}/security-areas returns areas."""
        response = self.client.get("/api/v1/isf/versions/1/security-areas")
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "security_areas" in data
        assert isinstance(data["security_areas"], list)
    
    def test_get_security_areas_with_filtering(self):
        """Test GET security areas with filtering parameters."""
        response = self.client.get(
            "/api/v1/isf/versions/1/security-areas?search=access&order_by=name"
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "security_areas" in data
        assert "total" in data
    
    def test_get_security_area_by_id(self):
        """Test GET /api/v1/isf/security-areas/{id} returns area details."""
        response = self.client.get("/api/v1/isf/security-areas/1")
        
        # This will fail until endpoint is implemented
        expected_fields = [
            "id", "area_id", "name", "description", 
            "version_id", "order_index", "controls_count"
        ]
        
        if response.status_code == 200:
            data = response.json()
            for field in expected_fields:
                assert field in data
    
    def test_get_security_area_not_found(self):
        """Test GET /api/v1/isf/security-areas/{id} returns 404 for non-existent area."""
        response = self.client.get("/api/v1/isf/security-areas/999")
        
        assert response.status_code == 404
    
    def test_create_security_area_success(self):
        """Test POST /api/v1/isf/security-areas creates new area."""
        area_data = {
            "area_id": "SG1",
            "name": "Security Governance",
            "description": "Security governance and management",
            "version_id": 1,
            "order_index": 1
        }
        
        response = self.client.post(
            "/api/v1/isf/security-areas",
            json=area_data
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 201
        data = response.json()
        assert data["area_id"] == area_data["area_id"]
        assert data["name"] == area_data["name"]
    
    def test_create_security_area_invalid_data(self):
        """Test POST /api/v1/isf/security-areas with invalid data returns 422."""
        invalid_data = {
            "area_id": "",  # Empty area_id should be invalid
            "name": "Test Area"
        }
        
        response = self.client.post(
            "/api/v1/isf/security-areas",
            json=invalid_data
        )
        
        assert response.status_code == 422
    
    def test_update_security_area_success(self):
        """Test PUT /api/v1/isf/security-areas/{id} updates area."""
        update_data = {
            "name": "Updated Security Governance",
            "description": "Updated description"
        }
        
        response = self.client.put(
            "/api/v1/isf/security-areas/1",
            json=update_data
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == update_data["name"]
    
    def test_delete_security_area_success(self):
        """Test DELETE /api/v1/isf/security-areas/{id} soft deletes area."""
        response = self.client.delete("/api/v1/isf/security-areas/1")
        
        # This will fail until endpoint is implemented
        assert response.status_code == 204


class TestISFControlEndpoints:
    """Test ISF control endpoints."""
    
    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()
        
        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user
    
    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()
    
    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user
    
    def test_get_controls_by_security_area(self):
        """Test GET /api/v1/isf/security-areas/{area_id}/controls returns controls."""
        response = self.client.get("/api/v1/isf/security-areas/1/controls")
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "controls" in data
        assert isinstance(data["controls"], list)
    
    def test_get_controls_with_pagination(self):
        """Test GET controls with pagination parameters."""
        response = self.client.get(
            "/api/v1/isf/security-areas/1/controls?page=1&page_size=10"
        )
        
        assert response.status_code == 200
        data = response.json()
        assert "controls" in data
        assert "total" in data
        assert "page" in data
        assert "page_size" in data
    
    def test_get_control_by_id(self):
        """Test GET /api/v1/isf/controls/{id} returns control details."""
        response = self.client.get("/api/v1/isf/controls/1")
        
        # This will fail until endpoint is implemented
        expected_fields = [
            "id", "control_id", "name", "description", "objective",
            "guidance", "security_area_id", "version_id", "control_type",
            "maturity_level", "mappings_count"
        ]
        
        if response.status_code == 200:
            data = response.json()
            for field in expected_fields:
                assert field in data
    
    def test_search_controls(self):
        """Test GET /api/v1/isf/controls/search with search parameters."""
        response = self.client.get(
            "/api/v1/isf/controls/search?q=access&control_type=preventive&version_id=1"
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "controls" in data
        assert "total" in data
        assert "search_query" in data
    
    def test_create_control_success(self):
        """Test POST /api/v1/isf/controls creates new control."""
        control_data = {
            "control_id": "SG1.1",
            "name": "Security Policy",
            "description": "Establish security policy",
            "objective": "Define security objectives",
            "guidance": "Implementation guidance",
            "security_area_id": 1,
            "version_id": 1,
            "control_type": "preventive",
            "maturity_level": 2,
            "order_index": 1
        }
        
        response = self.client.post(
            "/api/v1/isf/controls",
            json=control_data
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 201
        data = response.json()
        assert data["control_id"] == control_data["control_id"]
        assert data["name"] == control_data["name"]
    
    def test_bulk_create_controls(self):
        """Test POST /api/v1/isf/controls/bulk creates multiple controls."""
        controls_data = {
            "controls": [
                {
                    "control_id": "SG1.1",
                    "name": "Security Policy",
                    "security_area_id": 1,
                    "version_id": 1
                },
                {
                    "control_id": "SG1.2", 
                    "name": "Risk Management",
                    "security_area_id": 1,
                    "version_id": 1
                }
            ]
        }
        
        response = self.client.post(
            "/api/v1/isf/controls/bulk",
            json=controls_data
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 201
        data = response.json()
        assert "created_count" in data
        assert "controls" in data
        assert data["created_count"] == 2
    
    def test_update_control_success(self):
        """Test PUT /api/v1/isf/controls/{id} updates control."""
        update_data = {
            "name": "Updated Security Policy",
            "description": "Updated description",
            "maturity_level": 3
        }
        
        response = self.client.put(
            "/api/v1/isf/controls/1",
            json=update_data
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert data["name"] == update_data["name"]
        assert data["maturity_level"] == update_data["maturity_level"]
    
    def test_get_control_mappings(self):
        """Test GET /api/v1/isf/controls/{id}/mappings returns control mappings."""
        response = self.client.get("/api/v1/isf/controls/1/mappings")
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "mappings" in data
        assert isinstance(data["mappings"], list)


class TestISFImportEndpoints:
    """Test ISF import/export endpoints."""
    
    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()
        
        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user
    
    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()
    
    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user
    
    def test_import_isf_json_success(self):
        """Test POST /api/v1/isf/import/json imports ISF data."""
        import_data = {
            "version": "2024.1",
            "release_date": "2024-01-15",
            "description": "Test ISF version",
            "security_areas": [
                {
                    "area_id": "SG",
                    "name": "Security Governance",
                    "description": "Security governance area",
                    "controls": [
                        {
                            "control_id": "SG1",
                            "name": "Security Policy",
                            "description": "Establish security policy"
                        }
                    ]
                }
            ]
        }
        
        response = self.client.post(
            "/api/v1/isf/import/json",
            json=import_data
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 201
        data = response.json()
        assert "import_id" in data
        assert "status" in data
        assert data["status"] == "completed"
    
    def test_import_isf_file_upload(self):
        """Test POST /api/v1/isf/import/file uploads and imports ISF file."""
        # Mock file upload
        files = {
            "file": ("isf_data.json", '{"version": "2024.1"}', "application/json")
        }
        
        response = self.client.post(
            "/api/v1/isf/import/file",
            files=files
        )
        
        # This will fail until endpoint is implemented
        assert response.status_code == 201
        data = response.json()
        assert "import_id" in data
    
    def test_get_import_status(self):
        """Test GET /api/v1/isf/import/{import_id}/status returns import status."""
        response = self.client.get("/api/v1/isf/import/123/status")
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "status" in data
        assert "progress" in data
    
    def test_export_isf_json(self):
        """Test GET /api/v1/isf/export/json exports ISF data."""
        response = self.client.get("/api/v1/isf/export/json?version_id=1")
        
        # This will fail until endpoint is implemented
        assert response.status_code == 200
        assert response.headers["content-type"] == "application/json"


class TestISFAuthenticationAndAuthorization:
    """Test authentication and authorization for ISF endpoints."""
    
    def setup_method(self):
        """Set up test client."""
        self.client = TestClient(app)
    
    def test_unauthenticated_access_denied(self):
        """Test that unauthenticated requests are denied."""
        # Clear any existing auth overrides
        app.dependency_overrides.clear()
        
        response = self.client.get("/api/v1/isf/versions")
        
        # Should return 401 Unauthorized
        assert response.status_code == 401
    
    def test_insufficient_permissions_denied(self):
        """Test that users without proper permissions are denied."""
        def mock_user_no_permissions():
            user = Mock(spec=User)
            user.id = 1
            user.username = "limited_user"
            user.is_active = True
            user.roles = ["viewer"]  # No admin permissions
            return user
        
        app.dependency_overrides[get_current_user] = mock_user_no_permissions
        
        # Try to create a version (should require admin role)
        response = self.client.post(
            "/api/v1/isf/versions",
            json={"version": "2024.1", "description": "Test"}
        )
        
        # Should return 403 Forbidden
        assert response.status_code == 403
    
    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()


class TestISFAdvancedFiltering:
    """Test advanced filtering and search capabilities for ISF endpoints."""

    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()

        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user

    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()

    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user

    def test_filter_controls_by_maturity_level(self):
        """Test filtering controls by maturity level."""
        response = self.client.get(
            "/api/v1/isf/controls/search?maturity_level=2&maturity_level=3"
        )

        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "controls" in data
        # All returned controls should have maturity level 2 or 3
        for control in data["controls"]:
            assert control["maturity_level"] in [2, 3]

    def test_filter_controls_by_control_type(self):
        """Test filtering controls by control type."""
        response = self.client.get(
            "/api/v1/isf/controls/search?control_type=preventive"
        )

        assert response.status_code == 200
        data = response.json()
        for control in data["controls"]:
            assert control["control_type"] == "preventive"

    def test_complex_search_query(self):
        """Test complex search with multiple parameters."""
        response = self.client.get(
            "/api/v1/isf/controls/search?"
            "q=access control&"
            "control_type=preventive&"
            "maturity_level=2&"
            "security_area_id=1&"
            "has_mappings=true&"
            "order_by=name&"
            "order_direction=asc&"
            "page=1&"
            "page_size=20"
        )

        assert response.status_code == 200
        data = response.json()
        assert "controls" in data
        assert "total" in data
        assert "filters_applied" in data
        assert data["page"] == 1
        assert data["page_size"] == 20

    def test_date_range_filtering(self):
        """Test filtering by date ranges."""
        response = self.client.get(
            "/api/v1/isf/versions?"
            "created_after=2024-01-01&"
            "created_before=2024-12-31"
        )

        assert response.status_code == 200
        data = response.json()
        assert "versions" in data
        # Verify date filtering is applied
        for version in data["versions"]:
            created_date = datetime.fromisoformat(version["created_at"].replace("Z", "+00:00"))
            assert created_date >= datetime(2024, 1, 1)
            assert created_date <= datetime(2024, 12, 31)

    def test_full_text_search(self):
        """Test full-text search across multiple fields."""
        response = self.client.get(
            "/api/v1/isf/controls/search?q=security policy governance&search_fields=name,description,objective"
        )

        assert response.status_code == 200
        data = response.json()
        assert "controls" in data
        assert "search_metadata" in data
        assert data["search_metadata"]["query"] == "security policy governance"
        assert data["search_metadata"]["fields_searched"] == ["name", "description", "objective"]


class TestISFBulkOperations:
    """Test bulk operations for ISF endpoints."""

    def setup_method(self):
        """Set up test client and database."""
        self.client = TestClient(app)
        self.test_db = TestDatabase()
        self.db = self.test_db.get_session()

        # Override dependencies
        app.dependency_overrides[get_db] = lambda: self.db
        app.dependency_overrides[get_current_user] = self._mock_current_user

    def teardown_method(self):
        """Clean up after tests."""
        app.dependency_overrides.clear()
        self.test_db.cleanup()

    def _mock_current_user(self) -> User:
        """Mock authenticated user."""
        user = Mock(spec=User)
        user.id = 1
        user.username = "test_user"
        user.is_active = True
        user.roles = ["admin"]
        return user

    def test_bulk_update_controls(self):
        """Test bulk update of multiple controls."""
        bulk_update_data = {
            "updates": [
                {
                    "id": 1,
                    "maturity_level": 3,
                    "control_type": "detective"
                },
                {
                    "id": 2,
                    "maturity_level": 2,
                    "description": "Updated description"
                }
            ]
        }

        response = self.client.put(
            "/api/v1/isf/controls/bulk",
            json=bulk_update_data
        )

        # This will fail until endpoint is implemented
        assert response.status_code == 200
        data = response.json()
        assert "updated_count" in data
        assert "failed_updates" in data
        assert data["updated_count"] == 2

    def test_bulk_delete_controls(self):
        """Test bulk deletion of multiple controls."""
        bulk_delete_data = {
            "control_ids": [1, 2, 3, 4, 5]
        }

        response = self.client.delete(
            "/api/v1/isf/controls/bulk",
            json=bulk_delete_data
        )

        assert response.status_code == 200
        data = response.json()
        assert "deleted_count" in data
        assert "failed_deletions" in data

    def test_bulk_export_controls(self):
        """Test bulk export of controls with filtering."""
        export_request = {
            "filters": {
                "security_area_ids": [1, 2, 3],
                "control_types": ["preventive", "detective"],
                "maturity_levels": [2, 3]
            },
            "format": "csv",
            "include_mappings": True
        }

        response = self.client.post(
            "/api/v1/isf/controls/export",
            json=export_request
        )

        assert response.status_code == 200
        assert response.headers["content-type"] == "text/csv"
