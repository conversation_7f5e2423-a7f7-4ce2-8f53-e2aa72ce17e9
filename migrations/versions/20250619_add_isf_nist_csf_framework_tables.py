"""Add ISF and NIST CSF framework tables

Revision ID: 20250619_add_isf_nist_csf_framework_tables
Revises: 20250427_add_performance_indexes
Create Date: 2025-06-19 10:00:00.000000

This migration adds comprehensive support for:
- ISF (Information Security Forum) Standard of Good Practice
- NIST Cybersecurity Framework (CSF) 2.0
- Cross-framework mappings between MITRE ATT&CK, ISF, and NIST CSF
- Audit trails and validation workflows
"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime

# revision identifiers, used by Alembic.
revision = '20250619_add_isf_nist_csf_framework_tables'
down_revision = '20250427_add_performance_indexes'
branch_labels = None
depends_on = None

def upgrade() -> None:
    # ========================================
    # ISF (Information Security Forum) Tables
    # ========================================
    
    # ISF Versions table
    op.create_table(
        'isf_versions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('version', sa.String(length=50), nullable=False),
        sa.Column('release_date', sa.DateTime(), nullable=True),
        sa.Column('import_date', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('is_current', sa.Boolean(), nullable=False, default=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('url', sa.String(length=500), nullable=True),
        sa.Column('deprecated', sa.Boolean(), nullable=False, default=False),
        sa.Column('deprecation_date', sa.DateTime(), nullable=True),
        # Soft deletion and audit fields
        sa.Column('created_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('deleted_time', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('version', name='uq_isf_versions_version')
    )
    
    # ISF Security Areas table
    op.create_table(
        'isf_security_areas',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('area_id', sa.String(length=10), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('version_id', sa.Integer(), nullable=False),
        sa.Column('order_index', sa.Integer(), nullable=False, default=0),
        # Soft deletion and audit fields
        sa.Column('created_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('deleted_time', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['version_id'], ['isf_versions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('area_id', 'version_id', name='uq_isf_security_areas_area_version')
    )
    
    # ISF Controls table
    op.create_table(
        'isf_controls',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('control_id', sa.String(length=20), nullable=False),
        sa.Column('name', sa.String(length=500), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('objective', sa.Text(), nullable=True),
        sa.Column('guidance', sa.Text(), nullable=True),
        sa.Column('security_area_id', sa.Integer(), nullable=False),
        sa.Column('version_id', sa.Integer(), nullable=False),
        sa.Column('order_index', sa.Integer(), nullable=False, default=0),
        sa.Column('control_type', sa.String(length=50), nullable=True),  # policy, technical, administrative
        sa.Column('maturity_level', sa.String(length=20), nullable=True),  # basic, intermediate, advanced
        # Soft deletion and audit fields
        sa.Column('created_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('deleted_time', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['security_area_id'], ['isf_security_areas.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['version_id'], ['isf_versions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('control_id', 'version_id', name='uq_isf_controls_control_version')
    )
    
    # ========================================
    # NIST CSF (Cybersecurity Framework) Tables
    # ========================================
    
    # NIST CSF Versions table
    op.create_table(
        'nist_csf_versions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('version', sa.String(length=50), nullable=False),
        sa.Column('release_date', sa.DateTime(), nullable=True),
        sa.Column('import_date', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('is_current', sa.Boolean(), nullable=False, default=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('url', sa.String(length=500), nullable=True),
        sa.Column('deprecated', sa.Boolean(), nullable=False, default=False),
        sa.Column('deprecation_date', sa.DateTime(), nullable=True),
        sa.Column('supersedes_version_id', sa.Integer(), nullable=True),
        # Soft deletion and audit fields
        sa.Column('created_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('deleted_time', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['supersedes_version_id'], ['nist_csf_versions.id'], ondelete='SET NULL'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('version', name='uq_nist_csf_versions_version')
    )
    
    # NIST CSF Functions table
    op.create_table(
        'nist_csf_functions',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('function_id', sa.String(length=10), nullable=False),
        sa.Column('name', sa.String(length=100), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('version_id', sa.Integer(), nullable=False),
        sa.Column('order_index', sa.Integer(), nullable=False, default=0),
        # Soft deletion and audit fields
        sa.Column('created_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('deleted_time', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['version_id'], ['nist_csf_versions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('function_id', 'version_id', name='uq_nist_csf_functions_function_version')
    )
    
    # NIST CSF Categories table
    op.create_table(
        'nist_csf_categories',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('category_id', sa.String(length=20), nullable=False),
        sa.Column('name', sa.String(length=255), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('function_id', sa.Integer(), nullable=False),
        sa.Column('version_id', sa.Integer(), nullable=False),
        sa.Column('order_index', sa.Integer(), nullable=False, default=0),
        # Soft deletion and audit fields
        sa.Column('created_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('deleted_time', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['function_id'], ['nist_csf_functions.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['version_id'], ['nist_csf_versions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('category_id', 'version_id', name='uq_nist_csf_categories_category_version')
    )
    
    # NIST CSF Subcategories table
    op.create_table(
        'nist_csf_subcategories',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('subcategory_id', sa.String(length=30), nullable=False),
        sa.Column('name', sa.String(length=500), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('category_id', sa.Integer(), nullable=False),
        sa.Column('function_id', sa.Integer(), nullable=False),
        sa.Column('version_id', sa.Integer(), nullable=False),
        sa.Column('order_index', sa.Integer(), nullable=False, default=0),
        # Soft deletion and audit fields
        sa.Column('created_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('deleted_time', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['category_id'], ['nist_csf_categories.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['function_id'], ['nist_csf_functions.id'], ondelete='CASCADE'),
        sa.ForeignKeyConstraint(['version_id'], ['nist_csf_versions.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('subcategory_id', 'version_id', name='uq_nist_csf_subcategories_subcategory_version')
    )
    
    # NIST CSF Implementation Examples table
    op.create_table(
        'nist_csf_implementation_examples',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('subcategory_id', sa.Integer(), nullable=False),
        sa.Column('example_text', sa.Text(), nullable=False),
        sa.Column('example_type', sa.String(length=50), nullable=True),
        sa.Column('order_index', sa.Integer(), nullable=False, default=0),
        # Soft deletion and audit fields
        sa.Column('created_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('deleted_time', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['subcategory_id'], ['nist_csf_subcategories.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # NIST CSF Informative References table
    op.create_table(
        'nist_csf_informative_references',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('subcategory_id', sa.Integer(), nullable=False),
        sa.Column('framework_name', sa.String(length=100), nullable=False),
        sa.Column('reference_id', sa.String(length=50), nullable=False),
        sa.Column('description', sa.Text(), nullable=True),
        sa.Column('url', sa.String(length=500), nullable=True),
        # Soft deletion and audit fields
        sa.Column('created_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('deleted_time', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['subcategory_id'], ['nist_csf_subcategories.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id')
    )
    
    # ========================================
    # Cross-Framework Mapping Tables
    # ========================================
    
    # MITRE ATT&CK to ISF Mappings
    op.create_table(
        'mitre_to_isf_mappings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('mitre_technique_id', sa.String(length=50), nullable=False),
        sa.Column('isf_control_id', sa.Integer(), nullable=False),
        sa.Column('mapping_type', sa.String(length=50), nullable=False),  # mitigates, detects, prevents, responds, recovers
        sa.Column('effectiveness_score', sa.Numeric(precision=3, scale=2), nullable=True),  # 0.00 to 1.00
        sa.Column('confidence_level', sa.String(length=20), nullable=True),  # low, medium, high
        sa.Column('mapping_rationale', sa.Text(), nullable=True),
        sa.Column('evidence_sources', sa.JSON(), nullable=True),
        sa.Column('created_by', sa.String(length=100), nullable=False),
        sa.Column('updated_by', sa.String(length=100), nullable=True),
        sa.Column('validated', sa.Boolean(), nullable=False, default=False),
        sa.Column('validation_required', sa.Boolean(), nullable=False, default=True),
        sa.Column('validation_date', sa.DateTime(), nullable=True),
        sa.Column('validated_by', sa.String(length=100), nullable=True),
        # Soft deletion and audit fields
        sa.Column('created_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('deleted_time', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['isf_control_id'], ['isf_controls.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('mitre_technique_id', 'isf_control_id', 'mapping_type', name='uq_mitre_isf_mapping')
    )
    
    # MITRE ATT&CK to NIST CSF Mappings
    op.create_table(
        'mitre_to_nist_csf_mappings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('mitre_technique_id', sa.String(length=50), nullable=False),
        sa.Column('nist_csf_subcategory_id', sa.String(length=30), nullable=False),
        sa.Column('mapping_type', sa.String(length=50), nullable=False),  # addressed_by, detected_by, mitigated_by
        sa.Column('effectiveness_score', sa.Numeric(precision=3, scale=2), nullable=True),
        sa.Column('confidence_level', sa.String(length=20), nullable=True),
        sa.Column('mapping_rationale', sa.Text(), nullable=True),
        sa.Column('implementation_guidance', sa.Text(), nullable=True),
        sa.Column('created_by', sa.String(length=100), nullable=False),
        sa.Column('updated_by', sa.String(length=100), nullable=True),
        sa.Column('validated', sa.Boolean(), nullable=False, default=False),
        sa.Column('validation_date', sa.DateTime(), nullable=True),
        sa.Column('validated_by', sa.String(length=100), nullable=True),
        # Soft deletion and audit fields
        sa.Column('created_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('deleted_time', sa.DateTime(), nullable=True),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('mitre_technique_id', 'nist_csf_subcategory_id', 'mapping_type', name='uq_mitre_nist_csf_mapping')
    )
    
    # ISF to NIST CSF Mappings
    op.create_table(
        'isf_to_nist_csf_mappings',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('isf_control_id', sa.Integer(), nullable=False),
        sa.Column('nist_csf_subcategory_id', sa.String(length=30), nullable=False),
        sa.Column('mapping_type', sa.String(length=50), nullable=False),  # equivalent, overlapping, complementary, related, divergent
        sa.Column('alignment_score', sa.Numeric(precision=3, scale=2), nullable=True),
        sa.Column('confidence_level', sa.String(length=20), nullable=True),
        sa.Column('mapping_rationale', sa.Text(), nullable=True),
        sa.Column('implementation_notes', sa.Text(), nullable=True),
        sa.Column('created_by', sa.String(length=100), nullable=False),
        sa.Column('updated_by', sa.String(length=100), nullable=True),
        sa.Column('validated', sa.Boolean(), nullable=False, default=False),
        sa.Column('validation_date', sa.DateTime(), nullable=True),
        sa.Column('validated_by', sa.String(length=100), nullable=True),
        # Soft deletion and audit fields
        sa.Column('created_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_time', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('deleted_time', sa.DateTime(), nullable=True),
        sa.ForeignKeyConstraint(['isf_control_id'], ['isf_controls.id'], ondelete='CASCADE'),
        sa.PrimaryKeyConstraint('id'),
        sa.UniqueConstraint('isf_control_id', 'nist_csf_subcategory_id', 'mapping_type', name='uq_isf_nist_csf_mapping')
    )
    
    # ========================================
    # Indexes for Performance
    # ========================================
    
    # ISF indexes
    op.create_index('idx_isf_versions_current', 'isf_versions', ['is_current'])
    op.create_index('idx_isf_security_areas_version', 'isf_security_areas', ['version_id'])
    op.create_index('idx_isf_controls_security_area', 'isf_controls', ['security_area_id'])
    op.create_index('idx_isf_controls_version', 'isf_controls', ['version_id'])
    op.create_index('idx_isf_controls_type', 'isf_controls', ['control_type'])
    
    # NIST CSF indexes
    op.create_index('idx_nist_csf_versions_current', 'nist_csf_versions', ['is_current'])
    op.create_index('idx_nist_csf_functions_version', 'nist_csf_functions', ['version_id'])
    op.create_index('idx_nist_csf_categories_function', 'nist_csf_categories', ['function_id'])
    op.create_index('idx_nist_csf_categories_version', 'nist_csf_categories', ['version_id'])
    op.create_index('idx_nist_csf_subcategories_category', 'nist_csf_subcategories', ['category_id'])
    op.create_index('idx_nist_csf_subcategories_function', 'nist_csf_subcategories', ['function_id'])
    op.create_index('idx_nist_csf_subcategories_version', 'nist_csf_subcategories', ['version_id'])
    
    # Cross-framework mapping indexes
    op.create_index('idx_mitre_isf_technique', 'mitre_to_isf_mappings', ['mitre_technique_id'])
    op.create_index('idx_mitre_isf_control', 'mitre_to_isf_mappings', ['isf_control_id'])
    op.create_index('idx_mitre_isf_validated', 'mitre_to_isf_mappings', ['validated'])
    op.create_index('idx_mitre_nist_csf_technique', 'mitre_to_nist_csf_mappings', ['mitre_technique_id'])
    op.create_index('idx_mitre_nist_csf_subcategory', 'mitre_to_nist_csf_mappings', ['nist_csf_subcategory_id'])
    op.create_index('idx_mitre_nist_csf_validated', 'mitre_to_nist_csf_mappings', ['validated'])
    op.create_index('idx_isf_nist_csf_control', 'isf_to_nist_csf_mappings', ['isf_control_id'])
    op.create_index('idx_isf_nist_csf_subcategory', 'isf_to_nist_csf_mappings', ['nist_csf_subcategory_id'])
    op.create_index('idx_isf_nist_csf_validated', 'isf_to_nist_csf_mappings', ['validated'])
    
    # Soft deletion indexes
    op.create_index('idx_isf_versions_not_deleted', 'isf_versions', ['deleted_time'])
    op.create_index('idx_isf_security_areas_not_deleted', 'isf_security_areas', ['deleted_time'])
    op.create_index('idx_isf_controls_not_deleted', 'isf_controls', ['deleted_time'])
    op.create_index('idx_nist_csf_versions_not_deleted', 'nist_csf_versions', ['deleted_time'])
    op.create_index('idx_nist_csf_functions_not_deleted', 'nist_csf_functions', ['deleted_time'])
    op.create_index('idx_nist_csf_categories_not_deleted', 'nist_csf_categories', ['deleted_time'])
    op.create_index('idx_nist_csf_subcategories_not_deleted', 'nist_csf_subcategories', ['deleted_time'])


def downgrade() -> None:
    # Drop indexes first
    op.drop_index('idx_nist_csf_subcategories_not_deleted')
    op.drop_index('idx_nist_csf_categories_not_deleted')
    op.drop_index('idx_nist_csf_functions_not_deleted')
    op.drop_index('idx_nist_csf_versions_not_deleted')
    op.drop_index('idx_isf_controls_not_deleted')
    op.drop_index('idx_isf_security_areas_not_deleted')
    op.drop_index('idx_isf_versions_not_deleted')
    
    op.drop_index('idx_isf_nist_csf_validated')
    op.drop_index('idx_isf_nist_csf_subcategory')
    op.drop_index('idx_isf_nist_csf_control')
    op.drop_index('idx_mitre_nist_csf_validated')
    op.drop_index('idx_mitre_nist_csf_subcategory')
    op.drop_index('idx_mitre_nist_csf_technique')
    op.drop_index('idx_mitre_isf_validated')
    op.drop_index('idx_mitre_isf_control')
    op.drop_index('idx_mitre_isf_technique')
    
    op.drop_index('idx_nist_csf_subcategories_version')
    op.drop_index('idx_nist_csf_subcategories_function')
    op.drop_index('idx_nist_csf_subcategories_category')
    op.drop_index('idx_nist_csf_categories_version')
    op.drop_index('idx_nist_csf_categories_function')
    op.drop_index('idx_nist_csf_functions_version')
    op.drop_index('idx_nist_csf_versions_current')
    
    op.drop_index('idx_isf_controls_type')
    op.drop_index('idx_isf_controls_version')
    op.drop_index('idx_isf_controls_security_area')
    op.drop_index('idx_isf_security_areas_version')
    op.drop_index('idx_isf_versions_current')
    
    # Drop tables in reverse order of creation
    op.drop_table('isf_to_nist_csf_mappings')
    op.drop_table('mitre_to_nist_csf_mappings')
    op.drop_table('mitre_to_isf_mappings')
    op.drop_table('nist_csf_informative_references')
    op.drop_table('nist_csf_implementation_examples')
    op.drop_table('nist_csf_subcategories')
    op.drop_table('nist_csf_categories')
    op.drop_table('nist_csf_functions')
    op.drop_table('nist_csf_versions')
    op.drop_table('isf_controls')
    op.drop_table('isf_security_areas')
    op.drop_table('isf_versions')
