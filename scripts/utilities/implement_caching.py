#!/usr/bin/env python3
"""
Implement caching for performance optimization.

This script analyzes Python files and adds caching decorators to functions
that would benefit from caching.

Usage:
    python scripts/utilities/implement_caching.py [--path PATH] [--dry-run]

Options:
    --path PATH    Path to the directory or file to analyze (default: current directory)
    --dry-run      Show what would be done without actually doing it
"""

import argparse
import ast
import logging
import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Implement caching for performance optimization"
    )
    parser.add_argument(
        "--path",
        type=str,
        default=".",
        help="Path to the directory or file to analyze (default: current directory)",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without actually doing it",
    )
    return parser.parse_args()


def find_python_files(path: str) -> List[Path]:
    """Find all Python files in the given path."""
    path_obj = Path(path)
    if path_obj.is_file() and path_obj.suffix == ".py":
        return [path_obj]
    
    python_files = []
    for root, _, files in os.walk(path):
        for file in files:
            if file.endswith(".py"):
                python_files.append(Path(root) / file)
    
    return sorted(python_files)


class CacheAnalyzer(ast.NodeVisitor):
    """AST visitor to analyze functions for caching potential."""
    
    def __init__(self):
        """Initialize the analyzer."""
        self.cacheable_functions = []
        self.current_function = None
        self.has_db_access = False
        self.has_side_effects = False
    
    def visit_FunctionDef(self, node):
        """Visit a function definition node."""
        # Skip methods with special names
        if node.name.startswith("__") and node.name.endswith("__"):
            return
        
        # Save the current function
        old_function = self.current_function
        old_db_access = self.has_db_access
        old_side_effects = self.has_side_effects
        
        # Reset for the new function
        self.current_function = node
        self.has_db_access = False
        self.has_side_effects = False
        
        # Visit the function body
        self.generic_visit(node)
        
        # Check if the function is cacheable
        if (
            self.current_function
            and not self.has_side_effects
            and not self.has_db_access
            and node.args.args  # Has at least one argument
            and not any(
                decorator.id == "lru_cache"
                for decorator in node.decorator_list
                if isinstance(decorator, ast.Name)
            )
            and not any(
                decorator.attr == "lru_cache"
                for decorator in node.decorator_list
                if isinstance(decorator, ast.Attribute)
            )
        ):
            self.cacheable_functions.append(node)
        
        # Restore the previous function
        self.current_function = old_function
        self.has_db_access = old_db_access
        self.has_side_effects = old_side_effects
    
    def visit_Call(self, node):
        """Visit a function call node."""
        # Check for database access
        if isinstance(node.func, ast.Attribute):
            if node.func.attr in ["query", "execute", "commit", "rollback"]:
                self.has_db_access = True
        
        # Visit the function call arguments
        self.generic_visit(node)
    
    def visit_Assign(self, node):
        """Visit an assignment node."""
        # Check for side effects (modifying global variables)
        if self.current_function:
            # This is a simplification; a more thorough analysis would check
            # if the assignment is to a global variable
            pass
        
        # Visit the assignment target and value
        self.generic_visit(node)


def analyze_file_for_caching(file_path: Path) -> List[ast.FunctionDef]:
    """Analyze a file for functions that could benefit from caching."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Parse the file
        tree = ast.parse(content)
        
        # Analyze the file
        analyzer = CacheAnalyzer()
        analyzer.visit(tree)
        
        return analyzer.cacheable_functions
    except Exception as e:
        logger.error(f"Error analyzing {file_path}: {e}")
        return []


def add_caching_to_file(
    file_path: Path, cacheable_functions: List[ast.FunctionDef], dry_run: bool = False
) -> bool:
    """Add caching decorators to functions in a file."""
    if not cacheable_functions:
        return False
    
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Check if functools is already imported
        has_functools_import = re.search(
            r"from\s+functools\s+import\s+.*lru_cache", content
        ) or re.search(r"import\s+functools", content)
        
        # Add the import if needed
        if not has_functools_import:
            import_statement = "from functools import lru_cache\n"
            # Find the last import statement
            last_import = re.search(r"^(import|from)\s+.*$", content, re.MULTILINE)
            if last_import:
                # Insert after the last import
                last_import_end = last_import.end()
                content = (
                    content[:last_import_end]
                    + "\n"
                    + import_statement
                    + content[last_import_end:]
                )
            else:
                # Insert at the beginning of the file
                content = import_statement + content
        
        # Add caching decorators to functions
        for function in cacheable_functions:
            # Find the function definition
            function_pattern = re.compile(
                r"^(\s*)def\s+" + function.name + r"\s*\(", re.MULTILINE
            )
            match = function_pattern.search(content)
            if match:
                # Add the decorator
                indent = match.group(1)
                decorator = f"{indent}@lru_cache(maxsize=128)\n"
                content = content[: match.start()] + decorator + content[match.start() :]
        
        if dry_run:
            logger.info(
                f"Would add caching to {len(cacheable_functions)} functions in {file_path}"
            )
            for function in cacheable_functions:
                logger.info(f"  - {function.name}")
        else:
            # Write the modified content back to the file
            with open(file_path, "w", encoding="utf-8") as f:
                f.write(content)
            
            logger.info(
                f"Added caching to {len(cacheable_functions)} functions in {file_path}"
            )
            for function in cacheable_functions:
                logger.info(f"  - {function.name}")
        
        return True
    except Exception as e:
        logger.error(f"Error adding caching to {file_path}: {e}")
        return False


def main() -> int:
    """Main function."""
    args = parse_args()
    
    # Find Python files
    python_files = find_python_files(args.path)
    logger.info(f"Found {len(python_files)} Python files to analyze")
    
    # Analyze files for caching potential
    modified_files = 0
    for file_path in python_files:
        cacheable_functions = analyze_file_for_caching(file_path)
        if cacheable_functions:
            if add_caching_to_file(file_path, cacheable_functions, args.dry_run):
                modified_files += 1
    
    if args.dry_run:
        logger.info(f"Would modify {modified_files} files to add caching")
    else:
        logger.info(f"Modified {modified_files} files to add caching")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
