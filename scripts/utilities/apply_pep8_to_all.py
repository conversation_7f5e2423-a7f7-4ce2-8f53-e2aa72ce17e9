#!/usr/bin/env python3
"""
Apply PEP 8 style guidelines to all Python files in the project.

This script uses the apply_pep8.py script to format all Python files in the project
according to PEP 8 style guidelines.

Usage:
    python scripts/utilities/apply_pep8_to_all.py [--skip-dirs SKIP_DIRS]

Options:
    --skip-dirs SKIP_DIRS    Comma-separated list of directories to skip (default: venv,env,.venv,.env,__pycache__,migrations,3rdparty,vendor)
"""

import argparse
import logging
import os
import subprocess
import sys
from pathlib import Path
from typing import List, Set

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Apply PEP 8 style guidelines to all Python files in the project"
    )
    parser.add_argument(
        "--skip-dirs",
        type=str,
        default="venv,env,.venv,.env,__pycache__,migrations,3rdparty,vendor",
        help="Comma-separated list of directories to skip",
    )
    return parser.parse_args()


def get_directories_to_format(skip_dirs: Set[str]) -> List[str]:
    """Get a list of directories to format."""
    # Start with the root directory
    root_dir = Path(__file__).parent.parent.parent
    
    # Get all directories in the root directory
    directories = [
        str(d) for d in root_dir.iterdir() if d.is_dir() and d.name not in skip_dirs
    ]
    
    # Add the root directory itself for Python files in the root
    directories.append(str(root_dir))
    
    return directories


def format_directory(directory: str) -> bool:
    """Format all Python files in a directory."""
    logger.info(f"Formatting directory: {directory}")
    
    try:
        result = subprocess.run(
            [
                sys.executable,
                "scripts/utilities/apply_pep8.py",
                "--path",
                directory,
            ],
            check=True,
            capture_output=True,
            text=True,
        )
        logger.info(f"Successfully formatted directory: {directory}")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Error formatting directory {directory}: {e}")
        logger.error(f"Command output: {e.stdout}")
        logger.error(f"Command error: {e.stderr}")
        return False


def main() -> int:
    """Main function."""
    args = parse_args()
    
    # Get directories to skip
    skip_dirs = set(args.skip_dirs.split(","))
    logger.info(f"Skipping directories: {skip_dirs}")
    
    # Get directories to format
    directories = get_directories_to_format(skip_dirs)
    logger.info(f"Found {len(directories)} directories to format")
    
    # Format each directory
    success = True
    for directory in directories:
        if not format_directory(directory):
            success = False
    
    if success:
        logger.info("Successfully applied PEP 8 style guidelines to all Python files")
    else:
        logger.error("Errors occurred while applying PEP 8 style guidelines")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
