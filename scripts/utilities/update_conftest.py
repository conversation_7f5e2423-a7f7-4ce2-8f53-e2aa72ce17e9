#!/usr/bin/env python3
"""
Update conftest.py files to include fixtures for all test directories.

This script creates or updates conftest.py files in all test directories to
include fixtures for testing the corresponding modules.

Usage:
    python scripts/utilities/update_conftest.py [--dry-run]

Options:
    --dry-run    Show what would be done without actually doing it
"""

import argparse
import logging
import os
import sys
from pathlib import Path
from typing import Dict, List, Set

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Update conftest.py files to include fixtures for all test directories"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without actually doing it",
    )
    return parser.parse_args()


def get_test_directories() -> List[str]:
    """Get a list of test directories."""
    test_dirs = []
    
    for root, dirs, _ in os.walk("tests"):
        for dir_name in dirs:
            if dir_name.startswith("__") or dir_name.startswith("."):
                continue
            test_dirs.append(os.path.join(root, dir_name))
    
    # Add the tests directory itself
    test_dirs.append("tests")
    
    return sorted(test_dirs)


def generate_conftest_content(test_dir: str) -> str:
    """Generate content for a conftest.py file."""
    # Extract the module name from the test directory
    # e.g., tests/api/auth -> api.auth
    if test_dir == "tests":
        module_path = ""
    else:
        module_path = test_dir[6:].replace("/", ".")  # Remove "tests/" prefix
    
    # Generate the conftest content
    content = f'''"""
Pytest fixtures for {module_path} tests.
"""

import os
import pytest
from unittest.mock import MagicMock, patch

'''
    
    # Add database fixture if this is a database-related test
    if "models" in test_dir or "database" in test_dir:
        content += '''
@pytest.fixture
def db_session():
    """Provide a database session for testing."""
    from sqlalchemy import create_engine
    from sqlalchemy.orm import sessionmaker, Session
    from api.database import Base
    
    # Create an in-memory SQLite database for testing
    engine = create_engine("sqlite:///:memory:")
    Base.metadata.create_all(engine)
    
    # Create a session
    TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    session = TestingSessionLocal()
    
    try:
        yield session
    finally:
        session.close()


@pytest.fixture
def db_models():
    """Provide database models for testing."""
    from api.models import Base
    return Base


'''
    
    # Add API client fixture if this is an API-related test
    if "api" in test_dir or "routes" in test_dir or "endpoints" in test_dir:
        content += '''
@pytest.fixture
def client():
    """Provide a test client for the API."""
    from fastapi.testclient import TestClient
    from api.main import app
    
    client = TestClient(app)
    return client


@pytest.fixture
def auth_headers():
    """Provide authentication headers for testing."""
    return {"Authorization": "Bearer test_token"}


'''
    
    # Add specific fixtures based on the module
    if "auth" in test_dir:
        content += '''
@pytest.fixture
def mock_user():
    """Provide a mock user for testing."""
    from api.models.user import User, UserRole
    
    user = MagicMock(spec=User)
    user.id = 1
    user.username = "testuser"
    user.email = "<EMAIL>"
    user.role = UserRole.ADMIN
    user.is_active = True
    
    return user


@pytest.fixture
def mock_token():
    """Provide a mock token for testing."""
    return "test_token"


'''
    
    # Add environment fixture
    content += '''
@pytest.fixture
def env_vars():
    """Set up environment variables for testing."""
    original_environ = os.environ.copy()
    
    # Set test environment variables
    os.environ["DATABASE_URL"] = "sqlite:///:memory:"
    os.environ["SECRET_KEY"] = "test_secret_key"
    os.environ["ALGORITHM"] = "HS256"
    os.environ["ACCESS_TOKEN_EXPIRE_MINUTES"] = "30"
    
    yield
    
    # Restore original environment variables
    os.environ.clear()
    os.environ.update(original_environ)


'''
    
    return content


def update_conftest_files(test_dirs: List[str], dry_run: bool = False) -> None:
    """Update conftest.py files in all test directories."""
    for test_dir in test_dirs:
        conftest_path = os.path.join(test_dir, "conftest.py")
        
        # Check if conftest.py already exists
        if os.path.exists(conftest_path):
            logger.info(f"Conftest file already exists at {conftest_path}")
            continue
        
        # Generate conftest content
        content = generate_conftest_content(test_dir)
        
        if dry_run:
            logger.info(f"Would create conftest.py at {conftest_path}")
        else:
            # Create the directory if it doesn't exist
            os.makedirs(test_dir, exist_ok=True)
            
            # Write the conftest file
            with open(conftest_path, "w", encoding="utf-8") as f:
                f.write(content)
            
            logger.info(f"Created conftest.py at {conftest_path}")


def main() -> int:
    """Main function."""
    args = parse_args()
    
    logger.info("Getting test directories...")
    test_dirs = get_test_directories()
    logger.info(f"Found {len(test_dirs)} test directories")
    
    logger.info("Updating conftest files...")
    update_conftest_files(test_dirs, args.dry_run)
    
    if args.dry_run:
        logger.info("Dry run completed. No changes were made.")
    else:
        logger.info("Conftest file update completed.")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
