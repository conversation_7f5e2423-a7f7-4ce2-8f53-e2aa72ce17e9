#!/usr/bin/env python3
"""
Apply PEP 8 style guidelines to Python files.

This script uses the black formatter to automatically format Python files
according to PEP 8 style guidelines, and isort to organize imports.

Usage:
    python scripts/utilities/apply_pep8.py [--path PATH]

Options:
    --path PATH    Path to the directory or file to format (default: current directory)
"""

import argparse
import logging
import os
import subprocess
import sys
from pathlib import Path
from typing import List, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Apply PEP 8 style guidelines to Python files")
    parser.add_argument(
        "--path",
        type=str,
        default=".",
        help="Path to the directory or file to format (default: current directory)",
    )
    return parser.parse_args()


def find_python_files(path: str) -> List[Path]:
    """Find all Python files in the given path."""
    path_obj = Path(path)
    if path_obj.is_file() and path_obj.suffix == ".py":
        return [path_obj]
    
    python_files = []
    for root, _, files in os.walk(path):
        for file in files:
            if file.endswith(".py"):
                python_files.append(Path(root) / file)
    
    return python_files


def run_command(command: List[str], description: str) -> Tuple[bool, str]:
    """Run a command and return whether it succeeded and its output."""
    try:
        logger.info(f"Running {description}...")
        result = subprocess.run(
            command,
            check=True,
            capture_output=True,
            text=True,
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        logger.error(f"Error running {description}: {e}")
        logger.error(f"Command output: {e.stdout}")
        logger.error(f"Command error: {e.stderr}")
        return False, e.stderr


def check_dependencies() -> bool:
    """Check if required dependencies are installed."""
    dependencies = ["black", "isort"]
    missing_deps = []
    
    for dep in dependencies:
        try:
            subprocess.run(
                [dep, "--version"],
                check=True,
                capture_output=True,
                text=True,
            )
        except (subprocess.CalledProcessError, FileNotFoundError):
            missing_deps.append(dep)
    
    if missing_deps:
        logger.error(f"Missing dependencies: {', '.join(missing_deps)}")
        logger.error("Please install them using: pip install black isort")
        return False
    
    return True


def format_with_black(files: List[Path]) -> bool:
    """Format Python files using black."""
    if not files:
        logger.info("No Python files found to format with black")
        return True
    
    file_paths = [str(file) for file in files]
    success, output = run_command(
        ["black", "--line-length", "79"] + file_paths,
        "black formatter",
    )
    
    if success:
        logger.info("Successfully formatted files with black")
    
    return success


def organize_imports_with_isort(files: List[Path]) -> bool:
    """Organize imports using isort."""
    if not files:
        logger.info("No Python files found to organize imports")
        return True
    
    file_paths = [str(file) for file in files]
    success, output = run_command(
        [
            "isort",
            "--profile", "black",
            "--line-length", "79",
        ] + file_paths,
        "isort",
    )
    
    if success:
        logger.info("Successfully organized imports with isort")
    
    return success


def main() -> int:
    """Main function."""
    args = parse_args()
    
    if not check_dependencies():
        return 1
    
    python_files = find_python_files(args.path)
    logger.info(f"Found {len(python_files)} Python files to process")
    
    if not format_with_black(python_files):
        return 1
    
    if not organize_imports_with_isort(python_files):
        return 1
    
    logger.info("Successfully applied PEP 8 style guidelines to Python files")
    return 0


if __name__ == "__main__":
    sys.exit(main())
