#!/usr/bin/env python3
"""
Standardize docstrings according to PEP 257.

This script analyzes Python files and standardizes docstrings to follow
PEP 257 conventions.

Usage:
    python scripts/utilities/standardize_docstrings_pep257.py [--path PATH]

Options:
    --path PATH    Path to the directory or file to standardize docstrings in (default: current directory)
"""

import argparse
import ast
import logging
import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Standardize docstrings according to PEP 257"
    )
    parser.add_argument(
        "--path",
        type=str,
        default=".",
        help="Path to the directory or file to standardize docstrings in (default: current directory)",
    )
    return parser.parse_args()


def find_python_files(path: str) -> List[Path]:
    """Find all Python files in the given path."""
    path_obj = Path(path)
    if path_obj.is_file() and path_obj.suffix == ".py":
        return [path_obj]
    
    python_files = []
    for root, _, files in os.walk(path):
        for file in files:
            if file.endswith(".py"):
                python_files.append(Path(root) / file)
    
    return python_files


class DocstringVisitor(ast.NodeVisitor):
    """AST visitor to find and analyze docstrings."""
    
    def __init__(self):
        """Initialize the visitor."""
        self.docstrings = {}
        self.issues = []
    
    def visit_Module(self, node):
        """Visit a module node."""
        # Check for module docstring
        if (
            node.body
            and isinstance(node.body[0], ast.Expr)
            and isinstance(node.body[0].value, ast.Str)
        ):
            docstring = node.body[0].value.s
            self.docstrings["module"] = {
                "docstring": docstring,
                "lineno": node.body[0].lineno,
                "col_offset": node.body[0].col_offset,
                "end_lineno": node.body[0].end_lineno,
                "end_col_offset": node.body[0].end_col_offset,
            }
            self._check_docstring("module", docstring, node.body[0].lineno)
        else:
            self.issues.append(("module", "Missing module docstring", 1))
        
        self.generic_visit(node)
    
    def visit_ClassDef(self, node):
        """Visit a class definition node."""
        # Check for class docstring
        if (
            node.body
            and isinstance(node.body[0], ast.Expr)
            and isinstance(node.body[0].value, ast.Str)
        ):
            docstring = node.body[0].value.s
            self.docstrings[f"class:{node.name}"] = {
                "docstring": docstring,
                "lineno": node.body[0].lineno,
                "col_offset": node.body[0].col_offset,
                "end_lineno": node.body[0].end_lineno,
                "end_col_offset": node.body[0].end_col_offset,
            }
            self._check_docstring(f"class:{node.name}", docstring, node.body[0].lineno)
        else:
            self.issues.append(
                (f"class:{node.name}", "Missing class docstring", node.lineno)
            )
        
        self.generic_visit(node)
    
    def visit_FunctionDef(self, node):
        """Visit a function definition node."""
        # Skip special methods like __init__, __str__, etc.
        if not (node.name.startswith("__") and node.name.endswith("__")):
            # Check for function docstring
            if (
                node.body
                and isinstance(node.body[0], ast.Expr)
                and isinstance(node.body[0].value, ast.Str)
            ):
                docstring = node.body[0].value.s
                self.docstrings[f"function:{node.name}"] = {
                    "docstring": docstring,
                    "lineno": node.body[0].lineno,
                    "col_offset": node.body[0].col_offset,
                    "end_lineno": node.body[0].end_lineno,
                    "end_col_offset": node.body[0].end_col_offset,
                }
                self._check_docstring(
                    f"function:{node.name}", docstring, node.body[0].lineno
                )
            else:
                self.issues.append(
                    (f"function:{node.name}", "Missing function docstring", node.lineno)
                )
        
        self.generic_visit(node)
    
    def _check_docstring(self, name: str, docstring: str, lineno: int) -> None:
        """Check if a docstring follows PEP 257 conventions."""
        # Check if docstring is empty
        if not docstring.strip():
            self.issues.append((name, "Empty docstring", lineno))
            return
        
        # Check if docstring starts with a capital letter
        if not docstring.strip().startswith(tuple("ABCDEFGHIJKLMNOPQRSTUVWXYZ")):
            self.issues.append(
                (name, "Docstring should start with a capital letter", lineno)
            )
        
        # Check if docstring ends with a period
        if not docstring.strip().endswith("."):
            self.issues.append((name, "Docstring should end with a period", lineno))
        
        # Check if docstring is a one-liner but uses triple quotes
        lines = docstring.strip().split("\n")
        if len(lines) == 1 and '"""' in docstring:
            self.issues.append(
                (
                    name,
                    "One-line docstring should use single quotes, not triple quotes",
                    lineno,
                )
            )
        
        # Check if multi-line docstring has a summary line
        if len(lines) > 1:
            if lines[1].strip():
                self.issues.append(
                    (
                        name,
                        "Multi-line docstring should have a blank line after the summary",
                        lineno,
                    )
                )


def standardize_docstrings(file_path: Path) -> Tuple[bool, List[str]]:
    """Standardize docstrings in a Python file."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Parse the file
        tree = ast.parse(content)
        visitor = DocstringVisitor()
        visitor.visit(tree)
        
        # Report issues
        if visitor.issues:
            logger.info(f"Found {len(visitor.issues)} docstring issues in {file_path}")
            for name, issue, lineno in visitor.issues:
                logger.info(f"  Line {lineno}: {name} - {issue}")
        
        # Fix docstrings
        lines = content.split("\n")
        modified = False
        
        for name, info in visitor.docstrings.items():
            docstring = info["docstring"]
            
            # Standardize docstring
            new_docstring = standardize_docstring(docstring)
            
            if new_docstring != docstring:
                # Replace the docstring in the file
                start_line = info["lineno"] - 1
                end_line = info["end_lineno"] - 1
                
                # Handle multi-line docstrings
                if start_line == end_line:
                    # Single-line docstring
                    lines[start_line] = lines[start_line].replace(
                        repr(docstring), repr(new_docstring)
                    )
                else:
                    # Multi-line docstring
                    # This is a simplification; in a real implementation,
                    # you would need to handle the indentation and quotes properly
                    docstring_lines = []
                    for i in range(start_line, end_line + 1):
                        docstring_lines.append(lines[i])
                    
                    old_docstring_text = "\n".join(docstring_lines)
                    new_docstring_lines = [
                        f'    """{new_docstring.split("\n")[0]}',
                        "",
                    ] + [
                        f"    {line}" for line in new_docstring.split("\n")[1:-1]
                    ] + [f'    """']
                    
                    new_docstring_text = "\n".join(new_docstring_lines)
                    
                    for i in range(start_line, end_line + 1):
                        if i - start_line < len(new_docstring_lines):
                            lines[i] = new_docstring_lines[i - start_line]
                        else:
                            lines[i] = ""
                
                modified = True
        
        if modified:
            # Write the modified content back to the file
            with open(file_path, "w", encoding="utf-8") as f:
                f.write("\n".join(lines))
            
            logger.info(f"Standardized docstrings in {file_path}")
        
        return True, visitor.issues
    except Exception as e:
        logger.error(f"Error standardizing docstrings in {file_path}: {e}")
        return False, []


def standardize_docstring(docstring: str) -> str:
    """Standardize a docstring according to PEP 257."""
    # Remove leading/trailing whitespace
    docstring = docstring.strip()
    
    # Ensure the first letter is capitalized
    if docstring and not docstring[0].isupper():
        docstring = docstring[0].upper() + docstring[1:]
    
    # Ensure the docstring ends with a period
    if docstring and not docstring.endswith("."):
        docstring += "."
    
    # Handle multi-line docstrings
    lines = docstring.split("\n")
    if len(lines) > 1:
        # Ensure there's a blank line after the summary
        if len(lines) > 1 and lines[1].strip():
            lines.insert(1, "")
        
        # Ensure consistent indentation for all lines after the first
        if len(lines) > 2:
            indent = len(lines[2]) - len(lines[2].lstrip())
            for i in range(2, len(lines)):
                if lines[i].strip():
                    lines[i] = " " * indent + lines[i].lstrip()
        
        docstring = "\n".join(lines)
    
    return docstring


def main() -> int:
    """Main function."""
    args = parse_args()
    
    python_files = find_python_files(args.path)
    logger.info(f"Found {len(python_files)} Python files to process")
    
    all_issues = []
    for file_path in python_files:
        success, issues = standardize_docstrings(file_path)
        if not success:
            return 1
        all_issues.extend([(file_path, *issue) for issue in issues])
    
    if all_issues:
        logger.info(f"Found {len(all_issues)} docstring issues in total")
        logger.info("Please review the changes manually")
    else:
        logger.info("No docstring issues found")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
