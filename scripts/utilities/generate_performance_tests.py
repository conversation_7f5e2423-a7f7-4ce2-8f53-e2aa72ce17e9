#!/usr/bin/env python3
"""
Generate performance tests for Python code.

This script generates performance tests for Python code using pytest-benchmark.

Usage:
    python scripts/utilities/generate_performance_tests.py [--module MODULE] [--output OUTPUT] [--dry-run]

Options:
    --module MODULE      Module to generate tests for (e.g., api.routes.test_case)
    --output OUTPUT      Output directory for test files (default: tests/performance)
    --dry-run           Show what would be done without actually doing it
"""

import argparse
import ast
import importlib
import inspect
import logging
import os
import sys
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Generate performance tests for Python code"
    )
    parser.add_argument(
        "--module",
        type=str,
        required=True,
        help="Module to generate tests for (e.g., api.routes.test_case)",
    )
    parser.add_argument(
        "--output",
        type=str,
        default="tests/performance",
        help="Output directory for test files (default: tests/performance)",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without actually doing it",
    )
    return parser.parse_args()


def import_module(module_name: str) -> Any:
    """Import a module."""
    try:
        module = importlib.import_module(module_name)
        return module
    except ImportError:
        logger.error(f"Could not import module {module_name}")
        sys.exit(1)


class FunctionVisitor(ast.NodeVisitor):
    """AST visitor to find functions in a module."""
    
    def __init__(self):
        """Initialize the visitor."""
        self.functions = []
    
    def visit_FunctionDef(self, node):
        """Visit a function definition node."""
        # Skip private functions and special methods
        if not node.name.startswith("_"):
            self.functions.append(node.name)
        
        # Visit the function body
        self.generic_visit(node)


def find_functions_in_module(module_name: str) -> List[str]:
    """Find functions in a module."""
    try:
        # Import the module
        module = import_module(module_name)
        
        # Get the source code
        source = inspect.getsource(module)
        
        # Parse the source code
        tree = ast.parse(source)
        
        # Find functions
        visitor = FunctionVisitor()
        visitor.visit(tree)
        
        return visitor.functions
    except Exception as e:
        logger.error(f"Error finding functions in module {module_name}: {e}")
        return []


def generate_performance_test(
    module_name: str, function_name: str, output_dir: str, dry_run: bool = False
) -> None:
    """Generate a performance test for a function."""
    # Create the output directory
    os.makedirs(output_dir, exist_ok=True)
    
    # Create the test file path
    test_file_path = os.path.join(
        output_dir, f"test_perf_{module_name.replace('.', '_')}.py"
    )
    
    # Generate the test code
    test_code = f'''"""
Performance tests for {module_name}.
"""

import pytest
from {module_name} import {function_name}


def test_{function_name}_performance(benchmark):
    """Test the performance of {function_name}."""
    # TODO: Set up test data and parameters
    
    # Run the benchmark
    result = benchmark(
        {function_name},
        # TODO: Add parameters here
    )
    
    # TODO: Add assertions if needed
    assert result is not None
'''
    
    if dry_run:
        logger.info(f"Would create performance test for {module_name}.{function_name}")
        logger.info(f"Test file: {test_file_path}")
        logger.info("Test code:")
        logger.info(test_code)
    else:
        # Write the test code to the file
        with open(test_file_path, "a") as f:
            f.write(test_code)
        
        logger.info(f"Created performance test for {module_name}.{function_name}")
        logger.info(f"Test file: {test_file_path}")


def main() -> int:
    """Main function."""
    args = parse_args()
    
    # Find functions in the module
    functions = find_functions_in_module(args.module)
    logger.info(f"Found {len(functions)} functions in module {args.module}")
    
    # Create the output directory
    if not args.dry_run:
        os.makedirs(args.output, exist_ok=True)
        
        # Create an __init__.py file
        init_file = os.path.join(args.output, "__init__.py")
        if not os.path.exists(init_file):
            with open(init_file, "w") as f:
                f.write('"""Performance tests."""\n')
    
    # Generate performance tests
    for function_name in functions:
        generate_performance_test(
            args.module, function_name, args.output, args.dry_run
        )
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
