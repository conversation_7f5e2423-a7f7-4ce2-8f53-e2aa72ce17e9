#!/usr/bin/env python3
"""
Optimize database queries in Python code.

This script analyzes Python code for database queries and suggests optimizations.

Usage:
    python scripts/utilities/optimize_database_queries.py [--path PATH] [--dry-run]

Options:
    --path PATH    Path to the directory or file to analyze (default: current directory)
    --dry-run      Show what would be done without actually doing it
"""

import argparse
import ast
import logging
import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Optional, Set, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Optimize database queries in Python code"
    )
    parser.add_argument(
        "--path",
        type=str,
        default=".",
        help="Path to the directory or file to analyze (default: current directory)",
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without actually doing it",
    )
    return parser.parse_args()


def find_python_files(path: str) -> List[Path]:
    """Find all Python files in the given path."""
    path_obj = Path(path)
    if path_obj.is_file() and path_obj.suffix == ".py":
        return [path_obj]
    
    python_files = []
    for root, _, files in os.walk(path):
        for file in files:
            if file.endswith(".py"):
                python_files.append(Path(root) / file)
    
    return sorted(python_files)


class QueryAnalyzer(ast.NodeVisitor):
    """AST visitor to analyze database queries."""
    
    def __init__(self):
        """Initialize the analyzer."""
        self.queries = []
        self.current_function = None
        self.n_plus_one_candidates = []
        self.missing_indexes = []
        self.eager_loading_candidates = []
    
    def visit_FunctionDef(self, node):
        """Visit a function definition node."""
        # Save the current function
        old_function = self.current_function
        self.current_function = node
        
        # Visit the function body
        self.generic_visit(node)
        
        # Restore the previous function
        self.current_function = old_function
    
    def visit_Call(self, node):
        """Visit a function call node."""
        # Check for database queries
        if isinstance(node.func, ast.Attribute):
            # SQLAlchemy query
            if node.func.attr in ["query", "execute", "all", "first", "one"]:
                self.queries.append((self.current_function, node))
                
                # Check for potential N+1 query problems
                if self.is_in_loop(node):
                    self.n_plus_one_candidates.append((self.current_function, node))
            
            # Check for missing eager loading
            if node.func.attr in ["all", "first", "one"]:
                if not self.has_eager_loading(node):
                    self.eager_loading_candidates.append((self.current_function, node))
        
        # Visit the function call arguments
        self.generic_visit(node)
    
    def is_in_loop(self, node):
        """Check if a node is inside a loop."""
        parent = node
        while hasattr(parent, "parent"):
            parent = parent.parent
            if isinstance(parent, (ast.For, ast.While, ast.comprehension)):
                return True
        return False
    
    def has_eager_loading(self, node):
        """Check if a query has eager loading."""
        # This is a simplification; a more thorough analysis would check
        # for joinedload, subqueryload, etc.
        if isinstance(node.func, ast.Attribute) and isinstance(node.func.value, ast.Call):
            if hasattr(node.func.value.func, "attr"):
                return node.func.value.func.attr in ["options", "join"]
        return False


def analyze_file_for_queries(file_path: Path) -> Tuple[List, List, List]:
    """Analyze a file for database queries."""
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Parse the file
        tree = ast.parse(content)
        
        # Add parent references to nodes
        for node in ast.walk(tree):
            for child in ast.iter_child_nodes(node):
                child.parent = node
        
        # Analyze the file
        analyzer = QueryAnalyzer()
        analyzer.visit(tree)
        
        return (
            analyzer.n_plus_one_candidates,
            analyzer.missing_indexes,
            analyzer.eager_loading_candidates,
        )
    except Exception as e:
        logger.error(f"Error analyzing {file_path}: {e}")
        return [], [], []


def optimize_queries_in_file(
    file_path: Path,
    n_plus_one_candidates: List,
    missing_indexes: List,
    eager_loading_candidates: List,
    dry_run: bool = False,
) -> bool:
    """Optimize database queries in a file."""
    if not n_plus_one_candidates and not eager_loading_candidates:
        return False
    
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        # Generate optimization suggestions
        suggestions = []
        
        # N+1 query suggestions
        for function, node in n_plus_one_candidates:
            line_no = node.lineno
            suggestions.append(
                f"Potential N+1 query problem at line {line_no} in function {function.name}. "
                "Consider using eager loading or a join."
            )
        
        # Eager loading suggestions
        for function, node in eager_loading_candidates:
            line_no = node.lineno
            suggestions.append(
                f"Missing eager loading at line {line_no} in function {function.name}. "
                "Consider using joinedload() or subqueryload()."
            )
        
        if dry_run:
            logger.info(f"Would optimize queries in {file_path}")
            for suggestion in suggestions:
                logger.info(f"  - {suggestion}")
        else:
            # Add suggestions as comments to the file
            lines = content.split("\n")
            for suggestion in suggestions:
                match = re.search(r"at line (\d+)", suggestion)
                if match:
                    line_no = int(match.group(1))
                    if 0 <= line_no - 1 < len(lines):
                        lines[line_no - 1] += f"  # TODO: {suggestion}"
            
            # Write the modified content back to the file
            with open(file_path, "w", encoding="utf-8") as f:
                f.write("\n".join(lines))
            
            logger.info(f"Optimized queries in {file_path}")
            for suggestion in suggestions:
                logger.info(f"  - {suggestion}")
        
        return True
    except Exception as e:
        logger.error(f"Error optimizing queries in {file_path}: {e}")
        return False


def main() -> int:
    """Main function."""
    args = parse_args()
    
    # Find Python files
    python_files = find_python_files(args.path)
    logger.info(f"Found {len(python_files)} Python files to analyze")
    
    # Analyze files for database queries
    modified_files = 0
    for file_path in python_files:
        n_plus_one_candidates, missing_indexes, eager_loading_candidates = (
            analyze_file_for_queries(file_path)
        )
        if n_plus_one_candidates or eager_loading_candidates:
            if optimize_queries_in_file(
                file_path,
                n_plus_one_candidates,
                missing_indexes,
                eager_loading_candidates,
                args.dry_run,
            ):
                modified_files += 1
    
    if args.dry_run:
        logger.info(f"Would modify {modified_files} files to optimize queries")
    else:
        logger.info(f"Modified {modified_files} files to optimize queries")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
