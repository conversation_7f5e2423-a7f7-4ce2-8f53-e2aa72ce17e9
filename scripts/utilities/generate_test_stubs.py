#!/usr/bin/env python3
"""
Generate test stubs for modules that don't have tests.

This script analyzes the source code and test directories to identify modules
that don't have corresponding test files, and generates test stubs for them.

Usage:
    python scripts/utilities/generate_test_stubs.py [--dry-run]

Options:
    --dry-run    Show what would be done without actually doing it
"""

import argparse
import ast
import importlib
import inspect
import logging
import os
import re
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Generate test stubs for modules that don't have tests"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without actually doing it",
    )
    return parser.parse_args()


def get_python_modules() -> List[str]:
    """Get a list of Python modules in the source code."""
    modules = []
    
    # API modules
    for root, _, files in os.walk("api"):
        for file_name in files:
            if file_name.endswith(".py") and not file_name.startswith("__"):
                modules.append(os.path.join(root, file_name))
    
    # SRC modules
    for root, _, files in os.walk("src"):
        for file_name in files:
            if file_name.endswith(".py") and not file_name.startswith("__"):
                modules.append(os.path.join(root, file_name))
    
    return sorted(modules)


def get_test_files() -> List[str]:
    """Get a list of test files."""
    test_files = []
    
    for root, _, files in os.walk("tests"):
        for file_name in files:
            if file_name.endswith(".py") and file_name.startswith("test_"):
                test_files.append(os.path.join(root, file_name))
    
    return sorted(test_files)


def map_modules_to_tests(
    modules: List[str], test_files: List[str]
) -> Dict[str, List[str]]:
    """Map source modules to their corresponding test files."""
    module_to_tests = {}
    
    for module in modules:
        module_name = os.path.basename(module)[:-3]  # Remove .py extension
        module_to_tests[module] = []
        
        # Find test files for this module
        for test_file in test_files:
            test_file_name = os.path.basename(test_file)
            if test_file_name == f"test_{module_name}.py":
                module_to_tests[module].append(test_file)
    
    return module_to_tests


def extract_classes_and_functions(module_path: str) -> Tuple[List[str], List[str]]:
    """Extract class and function names from a Python module."""
    try:
        with open(module_path, "r", encoding="utf-8") as f:
            content = f.read()
        
        tree = ast.parse(content)
        
        classes = []
        functions = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                classes.append(node.name)
            elif isinstance(node, ast.FunctionDef):
                # Skip private functions and special methods
                if not node.name.startswith("_"):
                    functions.append(node.name)
        
        return classes, functions
    except Exception as e:
        logger.error(f"Error extracting classes and functions from {module_path}: {e}")
        return [], []


def generate_test_stub(
    module_path: str, classes: List[str], functions: List[str]
) -> str:
    """Generate a test stub for a module."""
    module_name = os.path.basename(module_path)[:-3]  # Remove .py extension
    module_import_path = module_path[:-3].replace("/", ".")  # Convert path to import path
    
    # Generate test stub
    stub = f'''"""
Tests for {module_import_path} module.
"""

import unittest
from unittest.mock import MagicMock, patch

import pytest

from {module_import_path} import *


'''
    
    # Add class tests
    for class_name in classes:
        stub += f'''class Test{class_name}(unittest.TestCase):
    """Tests for the {class_name} class."""
    
    def setUp(self):
        """Set up test fixtures."""
        pass
    
    def tearDown(self):
        """Tear down test fixtures."""
        pass
    
    def test_initialization(self):
        """Test initialization of {class_name}."""
        # TODO: Implement test
        pass
    
    # TODO: Add more tests for {class_name} methods
    
    
'''
    
    # Add function tests
    if functions:
        stub += f'''class Test{module_name.capitalize()}Functions(unittest.TestCase):
    """Tests for functions in the {module_name} module."""
    
'''
        
        for function_name in functions:
            stub += f'''    def test_{function_name}(self):
        """Test {function_name} function."""
        # TODO: Implement test
        pass
    
'''
    
    # Add pytest fixtures and tests
    stub += f'''
# Pytest style tests
@pytest.fixture
def setup_{module_name}():
    """Set up test fixtures for pytest style tests."""
    # TODO: Implement fixture
    yield
    # TODO: Implement teardown


'''
    
    # Add pytest tests for classes
    for class_name in classes:
        stub += f'''def test_{class_name.lower()}_with_pytest(setup_{module_name}):
    """Test {class_name} using pytest style."""
    # TODO: Implement test
    pass


'''
    
    # Add pytest tests for functions
    for function_name in functions:
        stub += f'''def test_{function_name}_with_pytest(setup_{module_name}):
    """Test {function_name} function using pytest style."""
    # TODO: Implement test
    pass


'''
    
    return stub


def generate_test_stubs(
    module_to_tests: Dict[str, List[str]], dry_run: bool = False
) -> None:
    """Generate test stubs for modules that don't have tests."""
    for module, tests in module_to_tests.items():
        if not tests:
            # Extract classes and functions from the module
            classes, functions = extract_classes_and_functions(module)
            
            if not classes and not functions:
                logger.info(f"Skipping {module}: No classes or functions found")
                continue
            
            # Generate test stub
            test_stub = generate_test_stub(module, classes, functions)
            
            # Determine the test file path
            module_name = os.path.basename(module)[:-3]  # Remove .py extension
            module_dir = os.path.dirname(module)
            test_file_path = f"tests/{module_dir}/test_{module_name}.py"
            
            if dry_run:
                logger.info(f"Would create test stub for {module} at {test_file_path}")
            else:
                # Create the directory if it doesn't exist
                os.makedirs(os.path.dirname(test_file_path), exist_ok=True)
                
                # Write the test stub
                with open(test_file_path, "w", encoding="utf-8") as f:
                    f.write(test_stub)
                
                logger.info(f"Created test stub for {module} at {test_file_path}")


def main() -> int:
    """Main function."""
    args = parse_args()
    
    logger.info("Getting Python modules...")
    modules = get_python_modules()
    logger.info(f"Found {len(modules)} Python modules")
    
    logger.info("Getting test files...")
    test_files = get_test_files()
    logger.info(f"Found {len(test_files)} test files")
    
    logger.info("Mapping modules to tests...")
    module_to_tests = map_modules_to_tests(modules, test_files)
    
    # Count modules without tests
    modules_without_tests = sum(1 for tests in module_to_tests.values() if not tests)
    logger.info(f"Found {modules_without_tests} modules without tests")
    
    logger.info("Generating test stubs...")
    generate_test_stubs(module_to_tests, args.dry_run)
    
    if args.dry_run:
        logger.info("Dry run completed. No changes were made.")
    else:
        logger.info("Test stub generation completed.")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
