#!/usr/bin/env python3
"""
Reorganize test directory structure to mirror the source code structure.

This script reorganizes the test directory structure to mirror the source code
structure, making it easier to find tests for specific modules.

Usage:
    python scripts/utilities/reorganize_tests.py [--dry-run]

Options:
    --dry-run    Show what would be done without actually doing it
"""

import argparse
import logging
import os
import re
import shutil
import sys
from pathlib import Path
from typing import Dict, List, Set, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Reorganize test directory structure to mirror the source code structure"
    )
    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be done without actually doing it",
    )
    return parser.parse_args()


def get_source_directories() -> List[str]:
    """Get a list of source directories."""
    source_dirs = []
    
    # API directories
    for root, dirs, _ in os.walk("api"):
        for dir_name in dirs:
            if dir_name.startswith("__") or dir_name.startswith("."):
                continue
            source_dirs.append(os.path.join(root, dir_name))
    
    # SRC directories
    for root, dirs, _ in os.walk("src"):
        for dir_name in dirs:
            if dir_name.startswith("__") or dir_name.startswith("."):
                continue
            source_dirs.append(os.path.join(root, dir_name))
    
    return sorted(source_dirs)


def get_test_files() -> List[str]:
    """Get a list of test files."""
    test_files = []
    
    for root, _, files in os.walk("tests"):
        for file_name in files:
            if file_name.endswith(".py") and file_name.startswith("test_"):
                test_files.append(os.path.join(root, file_name))
    
    return sorted(test_files)


def categorize_test_files(
    test_files: List[str], source_dirs: List[str]
) -> Dict[str, List[str]]:
    """Categorize test files based on their content and naming."""
    categorized_files = {}
    
    # Initialize categories
    for source_dir in source_dirs:
        categorized_files[source_dir] = []
    
    # Special categories
    categorized_files["unit"] = []
    categorized_files["integration"] = []
    categorized_files["uncategorized"] = []
    
    # Regular expressions to match imports
    import_patterns = {
        source_dir: re.compile(
            r"from\s+{0}\.|\s+import\s+{0}\.".format(
                source_dir.replace("/", ".")
            )
        )
        for source_dir in source_dirs
    }
    
    for test_file in test_files:
        # Skip __init__.py and conftest.py files
        if test_file.endswith("__init__.py") or test_file.endswith("conftest.py"):
            continue
        
        # Check if the file is already in a specific test category
        if "/unit/" in test_file:
            categorized_files["unit"].append(test_file)
            continue
        
        if "/integration/" in test_file:
            categorized_files["integration"].append(test_file)
            continue
        
        # Read the file content
        try:
            with open(test_file, "r", encoding="utf-8") as f:
                content = f.read()
        except Exception as e:
            logger.error(f"Error reading {test_file}: {e}")
            continue
        
        # Check for imports matching source directories
        matched = False
        for source_dir, pattern in import_patterns.items():
            if pattern.search(content):
                categorized_files[source_dir].append(test_file)
                matched = True
                break
        
        # If no match found, try to categorize based on filename
        if not matched:
            file_name = os.path.basename(test_file)
            
            # Extract the module name from the test file name
            # e.g., test_auth.py -> auth
            module_name = file_name[5:].split(".")[0]  # Remove "test_" prefix
            
            # Check if the module name matches any source directory
            for source_dir in source_dirs:
                dir_name = os.path.basename(source_dir)
                if module_name == dir_name:
                    categorized_files[source_dir].append(test_file)
                    matched = True
                    break
            
            # If still no match, put in uncategorized
            if not matched:
                categorized_files["uncategorized"].append(test_file)
    
    return categorized_files


def create_new_test_structure(
    categorized_files: Dict[str, List[str]], dry_run: bool = False
) -> None:
    """Create a new test directory structure."""
    # Create new test directories
    new_test_dirs = {
        "unit": "tests/unit",
        "integration": "tests/integration",
        "uncategorized": "tests/uncategorized",
    }
    
    for source_dir, files in categorized_files.items():
        if source_dir in ["unit", "integration", "uncategorized"]:
            continue
        
        # Create a corresponding test directory
        test_dir = f"tests/{source_dir}"
        new_test_dirs[source_dir] = test_dir
        
        if not dry_run and files:
            os.makedirs(test_dir, exist_ok=True)
            logger.info(f"Created directory: {test_dir}")
    
    # Create __init__.py files in all test directories
    if not dry_run:
        for test_dir in new_test_dirs.values():
            init_file = os.path.join(test_dir, "__init__.py")
            if not os.path.exists(init_file):
                with open(init_file, "w") as f:
                    f.write('"""Tests for this module."""\n')
                logger.info(f"Created file: {init_file}")
    
    # Move test files to their new locations
    for source_dir, files in categorized_files.items():
        if not files:
            continue
        
        test_dir = new_test_dirs[source_dir]
        
        for file_path in files:
            file_name = os.path.basename(file_path)
            new_file_path = os.path.join(test_dir, file_name)
            
            if dry_run:
                logger.info(f"Would move {file_path} to {new_file_path}")
            else:
                # Create the directory if it doesn't exist
                os.makedirs(os.path.dirname(new_file_path), exist_ok=True)
                
                # Copy the file to the new location
                shutil.copy2(file_path, new_file_path)
                logger.info(f"Copied {file_path} to {new_file_path}")


def main() -> int:
    """Main function."""
    args = parse_args()
    
    logger.info("Getting source directories...")
    source_dirs = get_source_directories()
    logger.info(f"Found {len(source_dirs)} source directories")
    
    logger.info("Getting test files...")
    test_files = get_test_files()
    logger.info(f"Found {len(test_files)} test files")
    
    logger.info("Categorizing test files...")
    categorized_files = categorize_test_files(test_files, source_dirs)
    
    # Print categorization summary
    for category, files in categorized_files.items():
        if files:
            logger.info(f"Category '{category}': {len(files)} files")
    
    logger.info("Creating new test structure...")
    create_new_test_structure(categorized_files, args.dry_run)
    
    if args.dry_run:
        logger.info("Dry run completed. No changes were made.")
    else:
        logger.info("Test reorganization completed.")
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
