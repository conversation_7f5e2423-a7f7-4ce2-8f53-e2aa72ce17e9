#!/usr/bin/env python3
"""
Add type hints to Python files according to PEP 484.

This script uses the pytype tool to infer type hints for Python files
and adds them to the files.

Usage:
    python scripts/utilities/add_type_hints.py [--path PATH]

Options:
    --path PATH    Path to the directory or file to add type hints to (default: current directory)
"""

import argparse
import logging
import os
import subprocess
import sys
from pathlib import Path
from typing import List, Optional, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Add type hints to Python files according to PEP 484"
    )
    parser.add_argument(
        "--path",
        type=str,
        default=".",
        help="Path to the directory or file to add type hints to (default: current directory)",
    )
    return parser.parse_args()


def find_python_files(path: str) -> List[Path]:
    """Find all Python files in the given path."""
    path_obj = Path(path)
    if path_obj.is_file() and path_obj.suffix == ".py":
        return [path_obj]
    
    python_files = []
    for root, _, files in os.walk(path):
        for file in files:
            if file.endswith(".py"):
                python_files.append(Path(root) / file)
    
    return python_files


def run_command(command: List[str], description: str) -> Tuple[bool, str]:
    """Run a command and return whether it succeeded and its output."""
    try:
        logger.info(f"Running {description}...")
        result = subprocess.run(
            command,
            check=True,
            capture_output=True,
            text=True,
        )
        return True, result.stdout
    except subprocess.CalledProcessError as e:
        logger.error(f"Error running {description}: {e}")
        logger.error(f"Command output: {e.stdout}")
        logger.error(f"Command error: {e.stderr}")
        return False, e.stderr


def check_dependencies() -> bool:
    """Check if required dependencies are installed."""
    try:
        # Check if pytype is installed
        subprocess.run(
            ["pip", "show", "pytype"],
            check=True,
            capture_output=True,
            text=True,
        )
        return True
    except (subprocess.CalledProcessError, FileNotFoundError):
        logger.error("pytype is not installed")
        logger.error("Please install it using: pip install pytype")
        return False


def add_type_hints(files: List[Path]) -> bool:
    """Add type hints to Python files using pytype."""
    if not files:
        logger.info("No Python files found to add type hints to")
        return True
    
    # First, run pytype to infer types
    file_paths = [str(file) for file in files]
    success, output = run_command(
        ["pytype", "--output-errors-csv", "pytype_errors.csv"] + file_paths,
        "pytype type inference",
    )
    
    if not success:
        return False
    
    # Then, run merge-pyi to add type annotations to the files
    for file in files:
        pyi_file = file.with_suffix(".pyi")
        if pyi_file.exists():
            success, output = run_command(
                ["merge-pyi", str(file), str(pyi_file)],
                f"merge-pyi for {file}",
            )
            if not success:
                return False
    
    logger.info("Successfully added type hints to Python files")
    return True


def main() -> int:
    """Main function."""
    args = parse_args()
    
    if not check_dependencies():
        logger.info("Installing pytype...")
        success, _ = run_command(
            ["pip", "install", "pytype"],
            "pip install pytype",
        )
        if not success:
            return 1
    
    python_files = find_python_files(args.path)
    logger.info(f"Found {len(python_files)} Python files to process")
    
    if not add_type_hints(python_files):
        return 1
    
    logger.info("Successfully added type hints to Python files")
    return 0


if __name__ == "__main__":
    sys.exit(main())
