#!/usr/bin/env python3
"""
Analyze memory usage of Python code.

This script analyzes the memory usage of Python code using the memory_profiler
module and identifies memory-intensive functions.

Usage:
    python scripts/analysis/analyze_memory_usage.py [--module MODULE] [--function FUNCTION] [--output OUTPUT]

Options:
    --module MODULE      Module to analyze (e.g., api.routes.test_case)
    --function FUNCTION  Function to analyze (e.g., get_test_cases)
    --output OUTPUT      Output file for memory profile results (default: memory_profile_results.txt)
"""

import argparse
import importlib
import logging
import os
import sys
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Analyze memory usage of Python code")
    parser.add_argument(
        "--module",
        type=str,
        required=True,
        help="Module to analyze (e.g., api.routes.test_case)",
    )
    parser.add_argument(
        "--function",
        type=str,
        required=True,
        help="Function to analyze (e.g., get_test_cases)",
    )
    parser.add_argument(
        "--output",
        type=str,
        default="memory_profile_results.txt",
        help="Output file for memory profile results (default: memory_profile_results.txt)",
    )
    return parser.parse_args()


def import_module_function(module_name: str, function_name: str) -> Callable:
    """Import a function from a module."""
    try:
        module = importlib.import_module(module_name)
        function = getattr(module, function_name)
        return function
    except ImportError:
        logger.error(f"Could not import module {module_name}")
        sys.exit(1)
    except AttributeError:
        logger.error(f"Could not find function {function_name} in module {module_name}")
        sys.exit(1)


def analyze_memory_usage(
    function: Callable, output_file: str
) -> None:
    """Analyze memory usage of a function."""
    try:
        import memory_profiler
        
        # Redirect output to a file
        with open(output_file, "w") as f:
            # Profile the function
            logger.info(f"Analyzing memory usage of function {function.__name__}...")
            memory_profiler.profile(
                function,
                stream=f,
            )
        
        logger.info(f"Memory profile results written to {output_file}")
        
        # Also print a summary to the console
        with open(output_file, "r") as f:
            lines = f.readlines()
            # Print the header and the top 10 lines
            for line in lines[:20]:
                print(line.strip())
    except ImportError:
        logger.error(
            "memory_profiler not installed. Install with: pip install memory_profiler"
        )
        sys.exit(1)


def main() -> int:
    """Main function."""
    args = parse_args()
    
    # Import the function to analyze
    function = import_module_function(args.module, args.function)
    
    # Analyze memory usage
    analyze_memory_usage(function, args.output)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
