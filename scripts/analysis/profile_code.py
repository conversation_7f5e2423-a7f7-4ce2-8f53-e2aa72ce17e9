#!/usr/bin/env python3
"""
Profile code performance.

This script profiles the performance of Python code using cProfile and visualizes
the results using snakeviz or as a text report.

Usage:
    python scripts/analysis/profile_code.py [--module MODULE] [--function FUNCTION] [--output OUTPUT] [--format FORMAT]

Options:
    --module MODULE      Module to profile (e.g., api.routes.test_case)
    --function FUNCTION  Function to profile (e.g., get_test_cases)
    --output OUTPUT      Output file for profile results (default: profile_results)
    --format FORMAT      Output format (text, snakeviz, callgraph) (default: text)
"""

import argparse
import cProfile
import importlib
import logging
import os
import pstats
import sys
from io import StringIO
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Profile code performance")
    parser.add_argument(
        "--module",
        type=str,
        required=True,
        help="Module to profile (e.g., api.routes.test_case)",
    )
    parser.add_argument(
        "--function",
        type=str,
        required=True,
        help="Function to profile (e.g., get_test_cases)",
    )
    parser.add_argument(
        "--output",
        type=str,
        default="profile_results",
        help="Output file for profile results (default: profile_results)",
    )
    parser.add_argument(
        "--format",
        type=str,
        choices=["text", "snakeviz", "callgraph"],
        default="text",
        help="Output format (text, snakeviz, callgraph) (default: text)",
    )
    return parser.parse_args()


def import_module_function(module_name: str, function_name: str) -> Callable:
    """Import a function from a module."""
    try:
        module = importlib.import_module(module_name)
        function = getattr(module, function_name)
        return function
    except ImportError:
        logger.error(f"Could not import module {module_name}")
        sys.exit(1)
    except AttributeError:
        logger.error(f"Could not find function {function_name} in module {module_name}")
        sys.exit(1)


def profile_function(
    function: Callable, output_file: str, format_type: str
) -> None:
    """Profile a function and output the results."""
    # Create the profile
    profiler = cProfile.Profile()
    
    # Run the function with the profiler
    logger.info(f"Profiling function {function.__name__}...")
    profiler.enable()
    try:
        function()
    except Exception as e:
        logger.error(f"Error running function: {e}")
    finally:
        profiler.disable()
    
    # Output the results
    if format_type == "text":
        output_text_profile(profiler, output_file)
    elif format_type == "snakeviz":
        output_snakeviz_profile(profiler, output_file)
    elif format_type == "callgraph":
        output_callgraph_profile(profiler, output_file)


def output_text_profile(profiler: cProfile.Profile, output_file: str) -> None:
    """Output profile results as text."""
    # Create a Stats object
    stats = pstats.Stats(profiler)
    
    # Sort the statistics by the cumulative time
    stats.sort_stats("cumulative")
    
    # Write to a file
    with open(f"{output_file}.txt", "w") as f:
        stats_stream = StringIO()
        stats = pstats.Stats(profiler, stream=stats_stream)
        stats.sort_stats("cumulative")
        stats.print_stats()
        f.write(stats_stream.getvalue())
    
    logger.info(f"Profile results written to {output_file}.txt")
    
    # Also print to console
    stats = pstats.Stats(profiler)
    stats.sort_stats("cumulative")
    stats.print_stats(30)  # Print top 30 functions


def output_snakeviz_profile(profiler: cProfile.Profile, output_file: str) -> None:
    """Output profile results for snakeviz."""
    # Save the profile to a file
    profiler.dump_stats(f"{output_file}.prof")
    logger.info(f"Profile results written to {output_file}.prof")
    logger.info("To view the results, run: snakeviz {output_file}.prof")


def output_callgraph_profile(profiler: cProfile.Profile, output_file: str) -> None:
    """Output profile results as a call graph."""
    try:
        import gprof2dot
        import subprocess
        
        # Save the profile to a file
        profiler.dump_stats(f"{output_file}.prof")
        
        # Convert to DOT format
        with open(f"{output_file}.dot", "w") as f:
            subprocess.run(
                [
                    "gprof2dot",
                    "-f",
                    "pstats",
                    f"{output_file}.prof",
                ],
                stdout=f,
                check=True,
            )
        
        # Convert to PNG
        subprocess.run(
            [
                "dot",
                "-Tpng",
                "-o",
                f"{output_file}.png",
                f"{output_file}.dot",
            ],
            check=True,
        )
        
        logger.info(f"Call graph written to {output_file}.png")
    except ImportError:
        logger.error("gprof2dot not installed. Install with: pip install gprof2dot")
    except subprocess.CalledProcessError as e:
        logger.error(f"Error generating call graph: {e}")


def main() -> int:
    """Main function."""
    args = parse_args()
    
    # Import the function to profile
    function = import_module_function(args.module, args.function)
    
    # Profile the function
    profile_function(function, args.output, args.format)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
