#!/usr/bin/env python3
"""
Check for circular imports in Python files.

This script analyzes Python files to detect circular imports, which can cause
issues with module loading and should be avoided.

Usage:
    python scripts/analysis/check_circular_imports.py [--path PATH] [--output OUTPUT]

Options:
    --path PATH        Path to the directory to analyze (default: current directory)
    --output OUTPUT    Path to the output file (default: circular_imports_report.txt)
"""

import argparse
import logging
import os
import re
import sys
from collections import defaultdict
from pathlib import Path
from typing import Dict, List, Set, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Check for circular imports in Python files")
    parser.add_argument(
        "--path",
        type=str,
        default=".",
        help="Path to the directory to analyze (default: current directory)",
    )
    parser.add_argument(
        "--output",
        type=str,
        default="circular_imports_report.txt",
        help="Path to the output file (default: circular_imports_report.txt)",
    )
    return parser.parse_args()


def find_python_files(path: str) -> List[Path]:
    """Find all Python files in the given path."""
    path_obj = Path(path)
    if path_obj.is_file() and path_obj.suffix == ".py":
        return [path_obj]
    
    python_files = []
    for root, _, files in os.walk(path):
        for file in files:
            if file.endswith(".py"):
                python_files.append(Path(root) / file)
    
    return python_files


def extract_imports(file_path: Path) -> List[str]:
    """Extract import statements from a Python file."""
    imports = []
    import_pattern = re.compile(r"^\s*(import|from)\s+([^\s]+)")
    
    try:
        with open(file_path, "r", encoding="utf-8") as f:
            for line in f:
                match = import_pattern.match(line)
                if match:
                    import_type, module = match.groups()
                    if import_type == "from":
                        # Extract the module part from "from module import ..."
                        module = module.split(" ")[0]
                    
                    # Remove relative import dots
                    module = module.lstrip(".")
                    
                    # Skip standard library and third-party imports
                    if "." in module and not module.startswith(("api.", "src.", "tests.")):
                        continue
                    
                    imports.append(module)
    except Exception as e:
        logger.error(f"Error extracting imports from {file_path}: {e}")
    
    return imports


def build_import_graph(python_files: List[Path]) -> Dict[str, Set[str]]:
    """Build a graph of imports between modules."""
    import_graph = defaultdict(set)
    
    for file_path in python_files:
        # Convert file path to module path
        module_path = str(file_path).replace("/", ".").replace("\\", ".")
        if module_path.endswith(".py"):
            module_path = module_path[:-3]
        
        # Extract imports
        imports = extract_imports(file_path)
        
        # Add to graph
        for imported_module in imports:
            import_graph[module_path].add(imported_module)
    
    return import_graph


def find_cycles(import_graph: Dict[str, Set[str]]) -> List[List[str]]:
    """Find cycles in the import graph using DFS."""
    cycles = []
    visited = set()
    path = []
    
    def dfs(node: str) -> None:
        if node in path:
            # Found a cycle
            cycle_start = path.index(node)
            cycles.append(path[cycle_start:] + [node])
            return
        
        if node in visited:
            return
        
        visited.add(node)
        path.append(node)
        
        for neighbor in import_graph.get(node, set()):
            dfs(neighbor)
        
        path.pop()
    
    for node in import_graph:
        if node not in visited:
            dfs(node)
    
    return cycles


def write_report(cycles: List[List[str]], output_path: str) -> None:
    """Write a report of circular imports to a file."""
    with open(output_path, "w", encoding="utf-8") as f:
        f.write("Circular Import Analysis Report\n")
        f.write("==============================\n\n")
        
        if not cycles:
            f.write("No circular imports found.\n")
            return
        
        f.write(f"Found {len(cycles)} circular import chains:\n\n")
        
        for i, cycle in enumerate(cycles, 1):
            f.write(f"Cycle {i}:\n")
            for j, module in enumerate(cycle):
                if j < len(cycle) - 1:
                    f.write(f"  {module} imports {cycle[j+1]}\n")
                else:
                    f.write(f"  {module} imports {cycle[0]}\n")
            f.write("\n")


def main() -> int:
    """Main function."""
    args = parse_args()
    
    python_files = find_python_files(args.path)
    logger.info(f"Found {len(python_files)} Python files to analyze")
    
    import_graph = build_import_graph(python_files)
    logger.info(f"Built import graph with {len(import_graph)} modules")
    
    cycles = find_cycles(import_graph)
    logger.info(f"Found {len(cycles)} circular import chains")
    
    write_report(cycles, args.output)
    logger.info(f"Wrote report to {args.output}")
    
    return 0 if not cycles else 1


if __name__ == "__main__":
    sys.exit(main())
