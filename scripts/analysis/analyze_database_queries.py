#!/usr/bin/env python3
"""
Analyze database queries to identify slow queries.

This script analyzes database queries by enabling SQLAlchemy query logging
and identifying slow queries.

Usage:
    python scripts/analysis/analyze_database_queries.py [--module MODULE] [--function FUNCTION] [--threshold THRESHOLD]

Options:
    --module MODULE          Module to analyze (e.g., api.routes.test_case)
    --function FUNCTION      Function to analyze (e.g., get_test_cases)
    --threshold THRESHOLD    Threshold in seconds for slow queries (default: 0.1)
"""

import argparse
import importlib
import logging
import os
import sys
import time
from pathlib import Path
from typing import Any, Callable, Dict, List, Optional, Tuple, Union

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)

# Create a logger for SQL queries
sql_logger = logging.getLogger("sqlalchemy.engine")
sql_logger.setLevel(logging.INFO)


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(
        description="Analyze database queries to identify slow queries"
    )
    parser.add_argument(
        "--module",
        type=str,
        required=True,
        help="Module to analyze (e.g., api.routes.test_case)",
    )
    parser.add_argument(
        "--function",
        type=str,
        required=True,
        help="Function to analyze (e.g., get_test_cases)",
    )
    parser.add_argument(
        "--threshold",
        type=float,
        default=0.1,
        help="Threshold in seconds for slow queries (default: 0.1)",
    )
    return parser.parse_args()


def import_module_function(module_name: str, function_name: str) -> Callable:
    """Import a function from a module."""
    try:
        module = importlib.import_module(module_name)
        function = getattr(module, function_name)
        return function
    except ImportError:
        logger.error(f"Could not import module {module_name}")
        sys.exit(1)
    except AttributeError:
        logger.error(f"Could not find function {function_name} in module {module_name}")
        sys.exit(1)


def setup_query_logging(threshold: float) -> None:
    """Set up query logging for SQLAlchemy."""
    try:
        from sqlalchemy import event
        from sqlalchemy.engine import Engine
        import time
        
        # Dictionary to store query start times
        query_start_times = {}
        
        @event.listens_for(Engine, "before_cursor_execute")
        def before_cursor_execute(
            conn, cursor, statement, parameters, context, executemany
        ):
            conn.info.setdefault("query_start_time", []).append(time.time())
        
        @event.listens_for(Engine, "after_cursor_execute")
        def after_cursor_execute(
            conn, cursor, statement, parameters, context, executemany
        ):
            total_time = time.time() - conn.info["query_start_time"].pop()
            if total_time > threshold:
                logger.warning(
                    f"Slow query detected ({total_time:.4f}s): {statement}"
                )
            else:
                logger.info(f"Query executed in {total_time:.4f}s: {statement}")
        
        logger.info(f"Query logging set up with threshold {threshold}s")
    except ImportError:
        logger.error("SQLAlchemy not installed. Install with: pip install sqlalchemy")
        sys.exit(1)


def analyze_function(function: Callable, threshold: float) -> None:
    """Analyze a function for database queries."""
    # Set up query logging
    setup_query_logging(threshold)
    
    # Run the function
    logger.info(f"Analyzing function {function.__name__}...")
    try:
        function()
    except Exception as e:
        logger.error(f"Error running function: {e}")


def main() -> int:
    """Main function."""
    args = parse_args()
    
    # Import the function to analyze
    function = import_module_function(args.module, args.function)
    
    # Analyze the function
    analyze_function(function, args.threshold)
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
