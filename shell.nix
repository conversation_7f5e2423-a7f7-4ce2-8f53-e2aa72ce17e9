{ pkgs ? import <nixpkgs> {} }:

pkgs.mkShell {
  buildInputs = with pkgs; [
    # Python environment with pip
    python311
    python311Packages.pip
    python311Packages.setuptools
    python311Packages.wheel
    (python311.withPackages (ps: with ps; [
      # Testing
      pytest
      pytest-cov
      pytest-asyncio
      pytest-mock

      # Web frameworks
      fastapi
      uvicorn
      flask
      httpx
      starlette
      jinja2

      # Database
      sqlalchemy
      alembic
      psycopg2
      redis

      # Authentication & Security
      pyjwt
      python-jose
      bcrypt
      passlib
      pyotp
      flask-login

      # Data validation
      pydantic
      email-validator
      python-multipart

      # Security frameworks
      stix2

      # Utilities
      requests
      python-dotenv
      click
      rich
      typer
      babel

      # Data processing
      numpy
      pandas

      # Visualization
      matplotlib
      seaborn

      # Code quality
      black
      isort
      mypy
      flake8

      # Additional packages (if available)
      # Note: Some packages might not be available in nixpkgs
      # We'll handle missing ones gracefully
    ]))
    
    # System dependencies
    postgresql
    redis
    docker
    docker-compose
  ];
  
  shellHook = ''
    echo "🚀 Entering Regression Rigor development environment..."
    echo "======================================================="
    export PYTHONPATH="$PWD:$PYTHONPATH"
    export DATABASE_URL="sqlite:///./test.db"

    # Create virtual environment for additional packages
    if [ ! -d "venv" ]; then
      echo "📦 Creating virtual environment..."
      python -m venv venv
    fi

    # Activate virtual environment
    if [ -f "venv/bin/activate" ]; then
      source venv/bin/activate

      # Install additional dependencies not available in nixpkgs
      echo "📥 Installing additional dependencies..."
      pip install --quiet --upgrade \
        fastapi-mail \
        fastapi-limiter \
        mitreattack-python \
        celery \
        flower \
        prometheus-client \
        loguru \
        structlog \
        sentry-sdk \
        playwright \
        selenium \
        requests-mock \
        factory-boy \
        faker \
        freezegun \
        responses
    else
      echo "⚠️  Virtual environment not created properly, skipping additional packages"
    fi

    # Function to run ISF tests
    run_isf_tests() {
      echo "🧪 Running ISF test suite..."
      python run_isf_tests.py
    }

    # Function to run all tests
    run_tests() {
      echo "🔬 Running full test suite..."
      python -m pytest "$@"
    }

    # Function to run ISF pytest tests
    run_isf_pytest() {
      echo "🧪 Running ISF pytest tests..."
      python -m pytest tests/api/test_isf_endpoints.py -v "$@"
    }

    # Function to start development server
    start_dev() {
      echo "🚀 Starting development server..."
      uvicorn api.main:app --reload --host 0.0.0.0 --port 8000
    }

    # Function to start services
    start_services() {
      echo "🐳 Starting services..."
      docker-compose up -d
    }

    # Function to stop services
    stop_services() {
      echo "🛑 Stopping services..."
      docker-compose down
    }

    # Function to format code
    format_code() {
      echo "🎨 Formatting code..."
      black . --exclude venv
      isort . --skip venv
    }

    # Function to lint code
    lint_code() {
      echo "🔍 Linting code..."
      flake8 . --exclude venv
      mypy . --exclude venv
    }

    echo "✅ Environment ready!"
    echo ""
    echo "🧪 ISF Testing Commands:"
    echo "  run_isf_tests    - Run standalone ISF test suite"
    echo "  run_isf_pytest   - Run ISF pytest tests"
    echo ""
    echo "🔬 General Testing Commands:"
    echo "  run_tests        - Run full test suite"
    echo ""
    echo "🚀 Development Commands:"
    echo "  start_dev        - Start development server"
    echo "  start_services   - Start application services"
    echo "  stop_services    - Stop application services"
    echo ""
    echo "🛠️  Code Quality Commands:"
    echo "  format_code      - Format code with black & isort"
    echo "  lint_code        - Lint code with flake8 & mypy"
    echo ""
  '';
}
