"""Main FastAPI application module."""
from contextlib import asynccontextmanager
from fastapi import FastAPI, Request, Depends
from fastapi.staticfiles import StaticFiles
from fastapi.responses import JSONResponse
from fastapi import HTTPException, status
from fastapi.openapi.docs import get_swagger_ui_html, get_redoc_html
from fastapi.openapi.utils import get_openapi
import logging
import os
import sys
from datetime import datetime
from sqlalchemy.exc import SQLAlchemyError
import traceback
from fastapi.middleware.cors import CORSMiddleware
from fastapi.exceptions import RequestValidationError
from pydantic import ValidationError

from api.utils.logging_config import setup_logging, get_logger
from api.routes.user import router as user_router
from api.auth.router import router as auth_router  # Original auth router
from api.auth.enhanced_router import router as enhanced_auth_router  # Enhanced auth router
from api.routes.v1 import router as v1_router  # Import consolidated v1 router
from api.routes.v2 import router as v2_router  # Import v2 router for enhanced automation
from api.routes.v3 import router as v3_router  # Import v3 router for advanced analytics
from api.routes.v4 import router as v4_router  # Import v4 router for enterprise integration
from api.routes.monitoring import router as monitoring_router  # Import monitoring router
from api.routes.sessions import router as session_router  # Import session router
from api.routes.v1.two_factor import router as two_factor_router  # Import 2FA router
from api.routes.campaign import router as campaign_router  # Import campaign router
from api.routes.testcase import router as test_case_router  # Import testcase router
from api.routes.assessment import router as assessment_router  # Import assessment router
# Import new cybersecurity framework routers
from api.routers.isf import router as isf_router  # Import ISF router
from api.routers.nist_csf import router as nist_csf_router  # Import NIST CSF router
from api.routers.framework_mapping import router as framework_mapping_router  # Import framework mapping router
from api.routers.export import router as export_router  # Import export router
from api.routers.analytics import router as analytics_router  # Import analytics router
from api.tasks.scheduler import scheduler, start_scheduler  # Import task scheduler
from api.tasks.report_tasks import check_scheduled_reports_task, cleanup_old_reports_task  # Import report tasks
from api.tasks.test_schedule_tasks import check_scheduled_test_executions_task  # Import test schedule tasks
from api.routes.environment import router as environment_router  # Import environment router
from api.database import init_db, get_db
from api.utils.rate_limiter import init_rate_limiter  # Import rate limiter
from api.middleware.validation import get_validation_middleware, ValidationMiddleware  # Import validation middleware
from api.middleware.enhanced_validation import get_enhanced_validation_middleware, EnhancedValidationMiddleware  # Import enhanced validation middleware
from api.middleware.error_handler import ErrorHandlerMiddleware, register_error_handlers  # Import error handler middleware
from api.middleware.rate_limit import default_rate_limiter  # Import enhanced rate limiter
from api.middleware.security_headers import SecurityHeadersMiddleware, add_security_headers_middleware  # Import security headers middleware
from api.middleware.content_security import add_csp_middleware, ContentSecurityPolicyMiddleware  # Import CSP middleware
from api.endpoints.error_handling import router as error_handling_router  # Import error handling router
from api.endpoints.admin_interface import router as admin_interface_router  # Import admin interface router
from api.middleware.logging import RequestLoggingMiddleware
from api.routes.v1.error_handlers import exception_handlers

# Configure logging
logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

@asynccontextmanager
async def lifespan(app: FastAPI):
    """Async context manager for FastAPI lifespan."""
    try:
        # Initialize centralized logging
        setup_logging(app_name="api")
        logger.info("Starting FastAPI application initialization")

        # Verify database URL
        database_url = os.environ.get("DATABASE_URL")
        if not database_url:
            raise ValueError("DATABASE_URL environment variable is not set")
        logger.info("Database URL configured: %s", database_url.split("@")[-1])  # Log only the host part

        # Initialize database
        try:
            logger.debug("Initializing database...")
            init_db()
            logger.info("Database initialization completed successfully")
        except SQLAlchemyError as e:
            logger.error(f"Database initialization error: {str(e)}")
            logger.error("Traceback: %s", traceback.format_exc())
            raise

        # Initialize rate limiter
        try:
            logger.debug("Initializing rate limiter...")
            await init_rate_limiter()
            logger.info("Rate limiter initialization completed successfully")
        except Exception as e:
            logger.error(f"Rate limiter initialization error: {str(e)}")
            logger.error("Traceback: %s", traceback.format_exc())
            logger.warning("Continuing without rate limiting")

        # Start task scheduler
        try:
            logger.debug("Starting task scheduler...")
            scheduler.add_task(check_scheduled_reports_task, 60, "check_scheduled_reports")
            scheduler.add_task(check_scheduled_test_executions_task, 60, "check_scheduled_test_executions")
            scheduler.add_task(cleanup_old_reports_task, 86400, "cleanup_old_reports")  # Run once a day
            start_scheduler()
            logger.info("Task scheduler started successfully")
        except Exception as e:
            logger.error(f"Task scheduler initialization error: {str(e)}")
            logger.error("Traceback: %s", traceback.format_exc())
            logger.warning("Continuing without task scheduler")

        yield
    except Exception as e:
        logger.error(f"Application startup failed: {str(e)}")
        logger.error("Traceback: %s", traceback.format_exc())
        raise
    finally:
        # Stop task scheduler
        try:
            logger.debug("Stopping task scheduler...")
            scheduler.stop()
            logger.info("Task scheduler stopped successfully")
        except Exception as e:
            logger.error(f"Error stopping task scheduler: {str(e)}")
            logger.error("Traceback: %s", traceback.format_exc())

        logger.info("Application shutdown")

def create_app() -> FastAPI:
    """Create and configure the FastAPI application."""
    # Create the FastAPI app with lifespan support
    app = FastAPI(
        title="Regression Rigor API",
        description="""
        # Regression Rigor Cybersecurity Data Platform

        This API provides a comprehensive set of endpoints for managing cybersecurity data,
        including user management, authentication, and integration with the MITRE ATT&CK framework.

        ## Key Features

        * **Secure Authentication**: JWT-based authentication with two-factor authentication support
        * **User Management**: Complete user lifecycle management with role-based access control
        * **MITRE ATT&CK Integration**: Access to MITRE ATT&CK techniques, tactics, and groups
        * **Rate Limiting**: Protection against abuse with tiered rate limiting
        * **Comprehensive Documentation**: Detailed API documentation with examples
        * **Error Handling**: Robust error handling with detailed error tracking

        ## Authentication

        Most endpoints require authentication. To authenticate, obtain a JWT token by using the `/api/v1/auth/token/` endpoint.
        If two-factor authentication is enabled, you'll need to complete the 2FA verification process.

        ## Rate Limiting

        This API implements rate limiting to protect against abuse. Different endpoints have different rate limits:

        * Standard endpoints: 100 requests per minute
        * Strict endpoints: 20 requests per minute
        * Authentication endpoints: 5 requests per minute
        * User-specific endpoints: 200 requests per minute per user

        When a rate limit is exceeded, the API wil return a 429 Too Many Requests response.

        ## Input Validation

        All API endpoints implement strict input validation. Invalid inputs wil result in a 422 Unprocessable Entity response
        with detailed error messages. Make sure to follow the schema definitions for all requests.

        ## Error Handling

        The API implements comprehensive error handling with detailed error tracking. All errors are logged and can be
        viewed through the error handling endpoints. Different types of errors (validation, authentication, database, etc.)
        are handled appropriately with meaningful error messages.
        """,
        version="1.0.0",
        docs_url="/api/docs",
        redoc_url="/api/redoc",
        openapi_url="/api/openapi.json",
        exception_handlers=exception_handlers,
        lifespan=lifespan,
        contact={
            "name": "Regression Rigor Support",
            "url": "https://example.com/support",
            "email": "<EMAIL>",
        },
        license_info={
            "name": "Proprietary",
            "url": "https://example.com/license",
        },
    )

    # Configure CORS
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],  # TODO: Configure this for production
        allow_credentials=True,
        allow_methods=["GET", "POST", "PUT", "DELETE", "OPTIONS", "PATCH"],
        allow_headers=[
            "Authorization",
            "Content-Type",
            "Accept",
            "Origin",
            "User-Agent",
            "X-Requested-With",
            "X-CSRF-Token",
        ],
        expose_headers=[
            "X-RateLimit-Limit",
            "X-RateLimit-Remaining",
            "X-RateLimit-Reset",
        ],
        max_age=600,  # 10 minutes
    )

    # Add rate limiting middleware
    try:
        app.middleware("http")(default_rate_limiter)
        logger.info("Rate limiting middleware added")
    except Exception as e:
        logger.error(f"Error adding rate limiting middleware: {str(e)}")
        logger.error("Traceback: %s", traceback.format_exc())
        logger.warning("Continuing without rate limiting middleware")

    # Add error handling middleware
    try:
        app.add_middleware(ErrorHandlerMiddleware)
        register_error_handlers(app)
        logger.info("Error handling middleware added")
    except Exception as e:
        logger.error(f"Error adding error handling middleware: {str(e)}")
        logger.error("Traceback: %s", traceback.format_exc())
        logger.warning("Continuing without error handling middleware")

    # Add enhanced validation middleware
    try:
        app.add_middleware(EnhancedValidationMiddleware,
                          sanitize=True,
                          log_validation_errors=True,
                          return_sanitized_data=True)
        logger.info("Enhanced validation middleware added")
    except Exception as e:
        logger.error(f"Error adding enhanced validation middleware: {str(e)}")
        logger.error("Traceback: %s", traceback.format_exc())
        logger.warning("Continuing without enhanced validation middleware")

        # Fall back to basic validation middleware
        try:
            app.add_middleware(ValidationMiddleware)
            logger.info("Basic validation middleware added as fallback")
        except Exception as e:
            logger.error(f"Error adding basic validation middleware: {str(e)}")
            logger.error("Traceback: %s", traceback.format_exc())
            logger.warning("Continuing without validation middleware")

    # Add GZip compression
    app.add_middleware(GZipMiddleware, minimum_size=1000)

    # Add security headers middleware
    try:
        app.add_middleware(SecurityHeadersMiddleware)
        logger.info("Security headers middleware added")
    except Exception as e:
        logger.error(f"Error adding security headers middleware: {str(e)}")
        logger.error("Traceback: %s", traceback.format_exc())
        logger.warning("Continuing without security headers middleware")

    # Add Content Security Policy middleware
    try:
        # Define CSP policy
        csp_policy = {
            "default-src": ["'self'"],
            "script-src": ["'self'", "'unsafe-inline'"],
            "style-src": ["'self'", "'unsafe-inline'"],
            "img-src": ["'self'", "data:"],
            "font-src": ["'self'"],
            "connect-src": ["'self'"],
            "frame-src": ["'none'"],
            "object-src": ["'none'"],
            "base-uri": ["'self'"],
            "form-action": ["'self'"],
            "frame-ancestors": ["'none'"],
            "upgrade-insecure-requests": [],
        }

        # Exclude paths for static files and docs
        exclude_paths = ["/static/", "/docs/", "/redoc/", "/openapi.json"]

        # Add CSP middleware
        add_csp_middleware(
            app,
            policy=csp_policy,
            report_only=False,  # Enforce CSP
            exclude_paths=exclude_paths,
            strict=False,  # Use standard CSP, not strict
        )
    except Exception as e:
        logger.error(f"Error adding Content Security Policy middleware: {str(e)}")
        logger.error("Traceback: %s", traceback.format_exc())
        logger.warning("Continuing without Content Security Policy middleware")

    # Add logging middleware as Starlette middleware
    class LoggingMiddleware(BaseHTTPMiddleware):
        """Middleware for logging HTTP requests and responses.

        This middleware wraps the logging_middleware function as a Starlette
        middleware class, allowing it to be added to the FastAPI application
        middleware stack. It logs request details, timing information, and
        response status codes.
        """
        async def dispatch(self, request, call_next):
            """Process the request and log details.

            Args:
                request: The incoming HTTP request
                call_next: The next middleware or route handler in the chain

            Returns:
                The HTTP response from downstream handlers
            """
            return await logging_middleware(request, call_next)

    app.add_middleware(LoggingMiddleware)

    # Add request logging middleware
    app.add_middleware(RequestLoggingMiddleware)

    # Include routers
    try:
        # Enhanced Authentication router (v2)
        app.include_router(
            enhanced_auth_router,
            prefix="/api/v2",
            tags=["Authentication V2"]
        )

        # Original Authentication router (v1)
        app.include_router(
            auth_router,
            prefix="/api",
            tags=["Authentication V1"]
        )

        # User management router
        app.include_router(
            user_router,
            prefix="/api",
            tags=["Users"]
        )

        # Session management router
        app.include_router(
            session_router,
            prefix="/api",
            tags=["Sessions"]
        )

        # Two-factor authentication router
        app.include_router(
            two_factor_router,
            prefix="/api",
            tags=["Two-Factor Authentication"]
        )

        # Assessment management router
        app.include_router(
            assessment_router,
            prefix="/api/v1",
            tags=["Assessments"]
        )

        # Campaign management router
        app.include_router(
            campaign_router,
            prefix="/api/v1",
            tags=["Campaigns"]
        )

        # Environment management router
        # Commented out until environment router is implemented
        # app.include_router(
        #     environment_router,
        #     prefix="/api/v1",
        #     tags=["Environments"]
        # )
        # Error handling router
        app.include_router(
            error_handling_router,
            prefix="/api",
            tags=["Error Handling"]
        )

        # Admin interface router
        app.include_router(
            admin_interface_router,
            prefix="/api",
            tags=["Admin Interface"]
        )

        # Dashboard router
        app.include_router(
            dashboard_router,
            prefix="/api",
            tags=["Dashboard"]
        )

        # Include test case router
        app.include_router(
            test_case_router,
            prefix="/api/v1",
            tags=["Test Cases"]
        )

        # Include advanced test case router
        app.include_router(
            test_case_advanced_router,
            prefix="/api/v1",
            tags=["Test Cases Advanced"]
        )

        # Include test case template router
        app.include_router(
            test_case_template_router,
            prefix="/api/v1",
            tags=["Test Case Templates"]
        )

        # Include test case dependency router
        app.include_router(
            test_case_dependency_router,
            prefix="/api/v1",
            tags=["Test Case Dependencies"]
        )

        # Include bulk execution router
        app.include_router(
            bulk_execution_router,
            prefix="/api/v1",
            tags=["Bulk Execution"]
        )

        # Include test schedule router
        app.include_router(
            test_schedule_router,
            prefix="/api/v1",
            tags=["Test Schedules"]
        )

        # Include test analytics router
        app.include_router(
            test_analytics_router,
            prefix="/api/v1",
            tags=["Test Analytics"]
        )

        # Include report router
        app.include_router(
            report_router,
            prefix="/api/v1",
            tags=["Reports"]
        )

        # Include scheduler router
        app.include_router(
            scheduler_router,
            prefix="/api/v1",
            tags=["Scheduler"]
        )

        # Include cron builder router
        app.include_router(
            cron_router,
            prefix="/api/v1",
            tags=["Cron Builder"]
        )

        # Include execution framework router
        app.include_router(
            execution_framework_router,
            prefix="/api/v1",
            tags=["Execution Framework"]
        )

        # Include enhanced MITRE Navigator router
        app.include_router(
            enhanced_navigator_router,
            prefix="/api/v1/execution-framework",
            tags=["Enhanced MITRE Navigator"]
        )

        # Include workspace router
        app.include_router(
            workspace_router,
            prefix="/api/v1",
            tags=["Purple Team Workspaces"]
        )

        # Include navigator websocket router
        app.include_router(
            navigator_websocket_router,
            prefix="/api/v1",
            tags=["Navigator WebSocket"]
        )

        # Include BAS router
        app.include_router(
            bas_router,
            prefix="/api/v1",
            tags=["Breach & Attack Simulation"]
        )

        # Include all v1 routes
        app.include_router(v1_router, prefix="/api")

        # Include v2 routes for enhanced automation
        app.include_router(v2_router, prefix="/api/v2")

        # Include v3 routes for advanced analytics
        app.include_router(v3_router, prefix="/api/v3")

        # Include v4 routes for enterprise integration
        app.include_router(v4_router, prefix="/api/v4")

        # Include monitoring routes
        app.include_router(monitoring_router)

        # Add campaign, testcase, and assessment routers
        app.include_router(campaign_router, tags=["Campaigns"])
        app.include_router(test_case_router, tags=["Test Cases"])
        app.include_router(assessment_router, tags=["Assessments"])

        # Include logging router
        app.include_router(logging_endpoints.router, prefix="/api")

        # Include testing router
        app.include_router(testing_endpoints.router, prefix="/api")

        # Add rate limit router
        app.include_router(rate_limit_router, prefix="/api/rate-limit", tags=["rate-limit"])

        # Include cybersecurity framework routers
        app.include_router(
            isf_router,
            prefix="/api/v1/isf",
            tags=["ISF Framework"]
        )

        app.include_router(
            nist_csf_router,
            prefix="/api/v1/nist-csf",
            tags=["NIST CSF Framework"]
        )

        app.include_router(
            framework_mapping_router,
            prefix="/api/v1/framework-mappings",
            tags=["Framework Mappings"]
        )

        app.include_router(
            export_router,
            prefix="/api/v1",
            tags=["Framework Export"]
        )

        app.include_router(
            analytics_router,
            prefix="/api/v1",
            tags=["Framework Analytics"]
        )

        logger.info("All routers included successfully")
    except Exception as e:
        logger.error(f"Error including routers: {str(e)}")
        logger.error("Traceback: %s", traceback.format_exc())
        raise

    # Custom OpenAPI schema
    def custom_openapi():
        """Generate a customised OpenAPI schema for the application.

        This function enhances the standard FastAPI OpenAPI schema with:
        - Custom security schemes (OAuth2 and Bearer authentication)
        - Global security requirements
        - Additional documentation for rate limiting, validation, and error handling
        - Custom examples and descriptions for endpoints

        Returns:
            dict: The customised OpenAPI schema
        """
        if app.openapi_schema:
            return app.openapi_schema

        openapi_schema = get_openapi(
            title=app.title,
            version=app.version,
            description=app.description,
            routes=app.routes,
            contact=app.contact,
            license_info=app.license_info,
        )

        # Add security schemes
        openapi_schema["components"]["securitySchemes"] = {
            "OAuth2PasswordBearer": {
                "type": "oauth2",
                "flows": {
                    "password": {
                        "tokenUrl": "/api/v1/auth/token",
                        "scopes": {
                            "admin": "Admin access",
                            "analyst": "Analyst access",
                            "viewer": "Viewer access"
                        }
                    }
                }
            },
            "BearerAuth": {
                "type": "http",
                "scheme": "bearer",
                "bearerFormat": "JWT"
            }
        }

        # Add global security requirement
        openapi_schema["security"] = [{"BearerAuth": []}]

        # Add custom examples and descriptions
        for path in openapi_schema["paths"]:
            for method in openapi_schema["paths"][path]:
                if method.lower() in ["get", "post", "put", "delete", "patch"]:
                    # Add rate limiting information
                    if "description" in openapi_schema["paths"][path][method]:
                        openapi_schema["paths"][path][method]["description"] += "\n\n**Rate Limiting**: This endpoint is subject to rate limiting."
                    else:
                        openapi_schema["paths"][path][method]["description"] = "**Rate Limiting**: This endpoint is subject to rate limiting."

                    # Add validation information
                    if "description" in openapi_schema["paths"][path][method]:
                        openapi_schema["paths"][path][method]["description"] += "\n\n**Input Validation**: All inputs are validated. Invalid inputs will result in a 422 response."
                    else:
                        openapi_schema["paths"][path][method]["description"] = "**Input Validation**: All inputs are validated. Invalid inputs will result in a 422 response."

                    # Add error handling information
                    if "description" in openapi_schema["paths"][path][method]:
                        openapi_schema["paths"][path][method]["description"] += "\n\n**Error Handling**: All errors are logged and can be viewed through the error handling endpoints."
                    else:
                        openapi_schema["paths"][path][method]["description"] = "**Error Handling**: All errors are logged and can be viewed through the error handling endpoints."

        app.openapi_schema = openapi_schema
        return app.openapi_schema

    app.openapi = custom_openapi

    # Custom documentation endpoints
    @app.get("/api/docs", include_in_schema=False)  # summary="Get docs"  # description="Retrieve docs information"  # responses={200: {"description": "Successful response"}}
    async def custom_swagger_ui_html():
        return get_swagger_ui_html(
            openapi_url="/openapi.json",
            title=f"{app.title} - Swagger UI",
            oauth2_redirect_url="/api/docs/oauth2-redirect",
            swagger_js_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui-bundle.js",
            swagger_css_url="https://cdn.jsdelivr.net/npm/swagger-ui-dist@5/swagger-ui.css"
        )  # summary="Get redoc"  # description="Retrieve redoc information"  # responses={200: {"description": "Successful response"}}

    @app.get("/api/redoc", include_in_schema=False)
    async def redoc_html():
        return get_redoc_html(
            openapi_url="/openapi.json",
            title=f"{app.title} - ReDoc",
            redoc_js_url="https://cdn.jsdelivr.net/npm/redoc@next/bundles/redoc.standalone.js",
            redoc_favicon_url="https://fastapi.tiangolo.com/img/favicon.png"
        )

    # Note: We don't need to add custom exception handlers here as they are handled by the error handling middleware

    # Mount static files
    try:
        # Mount static files for the application
        if os.path.exists("static"):
            app.mount("/static", StaticFiles(directory="static"), name="static")
            logger.info("Static files mounted successfully")

        # Mount MITRE Navigator static files
        navigator_dist_path = "vendor/attack-navigator/nav-app/dist"
        if os.path.exists(navigator_dist_path):
            app.mount("/vendor/attack-navigator/nav-app", StaticFiles(directory=navigator_dist_path, html=True), name="navigator")
            logger.info("MITRE Navigator static files mounted successfully")
        else:
            logger.warning(f"MITRE Navigator dist directory not found at {navigator_dist_path}")
    except Exception as e:
        logger.error(f"Failed to mount static files: {str(e)}")
        # Continue without static files if mounting fails

    return app

# Create the application instance
try:
    logger.info("Creating FastAPI application instance")
    app = create_app()
    logger.info("FastAPI application instance created successfully")
except Exception as e:
    logger.error(f"Failed to create FastAPI application: {str(e)}")
    logger.error("Traceback: %s", traceback.format_exc())
    raise

@app.on_event("startup")
async def startup_event():
    """Initialize application on startup."""
    try:
        logger.info("Starting application...")
        init_db()
        logger.info("Application started successfully")
    except Exception as e:
        logger.error(
            "Application startup failed",
            extra={
                "error": str(e)
            },
            exc_info=True
        )
        raise

@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {"status": "healthy"}

if __name__ == "__main__":
    import uvicorn
    port = int(os.environ.get("PORT", 5001))
    logger.info(f"Starting server on port {port}")
    uvicorn.run("api.main:app", host="0.0.0.0", port=port, reload=True)