"""
Pydantic schemas for search and linking functionality.

This module contains all the request/response schemas for the advanced
search and linking API endpoints.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Dict, Any, Optional
from datetime import datetime
from enum import Enum


class SearchType(str, Enum):
    """Search type enumeration."""
    TEXT = "text"
    SEMANTIC = "semantic"
    HYBRID = "hybrid"


class SortBy(str, Enum):
    """Sort criteria enumeration."""
    RELEVANCE = "relevance"
    TITLE = "title"
    DATE = "date"
    POPULARITY = "popularity"
    QUALITY = "quality"


class LinkType(str, Enum):
    """Link type enumeration."""
    DIRECT = "direct"
    RELATED = "related"
    MAPPED = "mapped"
    CUSTOM = "custom"


class RelationshipType(str, Enum):
    """Cross-framework relationship type enumeration."""
    EQUIVALENT = "equivalent"
    RELATED = "related"
    IMPLEMENTS = "implements"
    SUPPORTS = "supports"


class ColumnType(str, Enum):
    """Custom column type enumeration."""
    CATEGORY = "category"
    PRIORITY = "priority"
    STATUS = "status"
    CUSTOM = "custom"
    SECURITY_AREA = "security_area"
    CONTROL_TYPE = "control_type"
    FUNCTION = "function"
    TIER = "tier"
    PROFILE = "profile"
    DOMAIN = "domain"
    CERTIFICATION = "certification"
    AUDIT = "audit"
    IMPLEMENTATION_GROUP = "implementation_group"
    ASSET_TYPE = "asset_type"
    SAFEGUARD_TYPE = "safeguard_type"


class DataType(str, Enum):
    """Data type enumeration for custom columns."""
    TEXT = "text"
    NUMBER = "number"
    BOOLEAN = "boolean"
    DATE = "date"
    JSON = "json"


class Scope(str, Enum):
    """Scope enumeration for custom columns."""
    GLOBAL = "global"
    ORGANIZATION = "organization"
    USER = "user"
    PROJECT = "project"


# Search Schemas
class SearchRequest(BaseModel):
    """Request schema for advanced search."""
    query: str = Field(..., description="Search query string")
    frameworks: Optional[List[str]] = Field(None, description="Frameworks to search in")
    element_types: Optional[List[str]] = Field(None, description="Element types to include")
    filters: Optional[Dict[str, Any]] = Field(None, description="Additional filters")
    sort_by: SortBy = Field(SortBy.RELEVANCE, description="Sort criteria")
    limit: int = Field(20, ge=1, le=100, description="Maximum number of results")
    offset: int = Field(0, ge=0, description="Result offset for pagination")
    user_id: Optional[str] = Field(None, description="User ID for personalization")
    include_facets: bool = Field(True, description="Include facet data")
    search_type: SearchType = Field(SearchType.HYBRID, description="Type of search")


class SearchResultItem(BaseModel):
    """Individual search result item."""
    id: int
    framework: str
    element_type: str
    element_id: str
    title: str
    description: Optional[str]
    category: Optional[str]
    subcategory: Optional[str]
    tags: List[str]
    snippet: Optional[str]
    relevance_score: float
    quality_score: float
    popularity_score: float
    related_elements: List[Dict[str, Any]]
    mapped_elements: List[Dict[str, Any]]
    rank: int


class SearchFacetValue(BaseModel):
    """Search facet value."""
    value: str
    count: int
    label: str


class SearchFacets(BaseModel):
    """Search facets for filtering."""
    frameworks: List[SearchFacetValue]
    element_types: List[SearchFacetValue]
    categories: List[SearchFacetValue]
    quality_ranges: List[SearchFacetValue]


class SearchMetadata(BaseModel):
    """Search metadata."""
    search_type: str
    frameworks_searched: Any
    element_types: Any
    sort_by: str
    query_id: int


class SearchPagination(BaseModel):
    """Search pagination information."""
    limit: int
    offset: int
    has_more: bool


class SearchResponse(BaseModel):
    """Response schema for advanced search."""
    query: str
    total_results: int
    results: List[SearchResultItem]
    facets: SearchFacets
    suggestions: List[str]
    search_metadata: SearchMetadata
    pagination: SearchPagination


class SearchSuggestionResponse(BaseModel):
    """Search suggestion response."""
    suggestion: str
    confidence: float


# Custom Column Schemas
class CustomColumnCreate(BaseModel):
    """Schema for creating custom columns."""
    name: str = Field(..., max_length=255, description="Column name")
    description: Optional[str] = Field(None, description="Column description")
    column_type: ColumnType = Field(..., description="Type of column")
    data_type: DataType = Field(..., description="Data type")
    allowed_values: Optional[List[str]] = Field(None, description="Predefined values")
    validation_rules: Optional[Dict[str, Any]] = Field(None, description="Validation rules")
    display_order: int = Field(0, description="Display order")
    is_visible: bool = Field(True, description="Is column visible")
    color_scheme: Optional[Dict[str, str]] = Field(None, description="Color coding rules")
    icon: Optional[str] = Field(None, max_length=100, description="Icon identifier")
    scope: Scope = Field(..., description="Column scope")
    owner_id: Optional[str] = Field(None, max_length=100, description="Owner ID")
    organization_id: Optional[str] = Field(None, max_length=100, description="Organization ID")
    is_required: bool = Field(False, description="Is column required")
    is_searchable: bool = Field(True, description="Is column searchable")
    created_by: Optional[str] = Field(None, max_length=100, description="Creator ID")


class CustomColumnResponse(BaseModel):
    """Response schema for custom columns."""
    id: int
    name: str
    description: Optional[str]
    column_type: str
    data_type: str
    allowed_values: Optional[List[str]]
    validation_rules: Optional[Dict[str, Any]]
    display_order: int
    is_visible: bool
    color_scheme: Optional[Dict[str, str]]
    icon: Optional[str]
    scope: str
    owner_id: Optional[str]
    organization_id: Optional[str]
    is_system_column: bool
    is_required: bool
    is_searchable: bool
    usage_count: int
    last_used: Optional[datetime]
    created_by: Optional[str]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Column Link Schemas
class ColumnLinkCreate(BaseModel):
    """Schema for creating column links."""
    search_item_id: int = Field(..., description="Search item ID")
    column_id: int = Field(..., description="Custom column ID")
    link_type: LinkType = Field(LinkType.DIRECT, description="Type of link")
    link_strength: float = Field(1.0, ge=0.0, le=1.0, description="Link strength")
    description: Optional[str] = Field(None, description="Link description")
    rationale: Optional[str] = Field(None, description="Link rationale")
    tags: Optional[List[str]] = Field(None, description="Link tags")
    created_by: Optional[str] = Field(None, max_length=100, description="Creator ID")
    organization_context: Optional[Dict[str, Any]] = Field(None, description="Organizational context")


class ColumnLinkResponse(BaseModel):
    """Response schema for column links."""
    id: int
    search_item_id: int
    column_id: int
    link_type: str
    link_strength: float
    description: Optional[str]
    rationale: Optional[str]
    tags: Optional[List[str]]
    is_validated: bool
    validated_by: Optional[str]
    validated_at: Optional[datetime]
    confidence_score: float
    created_by: Optional[str]
    organization_context: Optional[Dict[str, Any]]
    usage_count: int
    last_accessed: Optional[datetime]
    effectiveness_rating: Optional[float]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Cross-Framework Relationship Schemas
class CrossFrameworkRelationshipCreate(BaseModel):
    """Schema for creating cross-framework relationships."""
    source_item_id: int = Field(..., description="Source search item ID")
    target_item_id: int = Field(..., description="Target search item ID")
    relationship_type: RelationshipType = Field(RelationshipType.RELATED, description="Relationship type")
    relationship_strength: float = Field(0.5, ge=0.0, le=1.0, description="Relationship strength")
    bidirectional: bool = Field(False, description="Is relationship bidirectional")
    description: Optional[str] = Field(None, description="Relationship description")
    rationale: Optional[str] = Field(None, description="Relationship rationale")
    evidence: Optional[Dict[str, Any]] = Field(None, description="Supporting evidence")
    created_by: Optional[str] = Field(None, max_length=100, description="Creator ID")


class CrossFrameworkRelationshipResponse(BaseModel):
    """Response schema for cross-framework relationships."""
    id: int
    source_item_id: int
    target_item_id: int
    relationship_type: str
    relationship_strength: float
    bidirectional: bool
    description: Optional[str]
    rationale: Optional[str]
    evidence: Optional[Dict[str, Any]]
    semantic_similarity: Optional[float]
    context_similarity: Optional[float]
    structural_similarity: Optional[float]
    confidence_score: float
    is_validated: bool
    validated_by: Optional[str]
    validated_at: Optional[datetime]
    discovery_method: Optional[str]
    created_by: Optional[str]
    usage_count: int
    effectiveness_rating: Optional[float]
    last_accessed: Optional[datetime]
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


# Analytics Schemas
class SearchAnalyticsResponse(BaseModel):
    """Response schema for search analytics."""
    period_days: int
    total_searches: int
    unique_users: int
    popular_terms: List[Dict[str, Any]]
    framework_usage: Dict[str, float]
    search_success_rate: float
    average_results_per_search: float


class LinkSuggestion(BaseModel):
    """Link suggestion schema."""
    column_id: int
    column_name: str
    column_type: str
    confidence_score: float
    suggested_link_type: str
    rationale: str


class RelationshipSuggestion(BaseModel):
    """Cross-framework relationship suggestion schema."""
    source_item: Dict[str, Any]
    target_item: Dict[str, Any]
    confidence_score: float
    semantic_similarity: float
    context_similarity: float
    structural_similarity: float
    suggested_type: str
    rationale: str
