"""
CIS Critical Security Controls v8 Schemas.

This module contains Pydantic schemas for CIS Controls v8 API requests and responses.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum


class OrganizationSizeEnum(str, Enum):
    """Organization size options."""
    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"
    ENTERPRISE = "enterprise"


class ImplementationGroupEnum(str, Enum):
    """Implementation group options."""
    IG1 = "IG1"
    IG2 = "IG2"
    IG3 = "IG3"


class ImplementationDifficultyEnum(str, Enum):
    """Implementation difficulty levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class ControlTypeEnum(str, Enum):
    """Control type options."""
    BASIC = "basic"
    FOUNDATIONAL = "foundational"
    ORGANIZATIONAL = "organizational"


class AssessmentTypeEnum(str, Enum):
    """Assessment type options."""
    SELF = "self"
    THIRD_PARTY = "third_party"
    CERTIFICATION = "certification"


class SecurityMaturityEnum(str, Enum):
    """Security maturity levels."""
    BASIC = "basic"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"


# Base schemas
class CISControlsVersionBase(BaseModel):
    """Base schema for CIS Controls versions."""
    version: str = Field(..., description="CIS Controls version")
    release_date: str = Field(..., description="Release date")
    description: Optional[str] = Field(None, description="Version description")
    standard_url: Optional[str] = Field(None, description="Official standard URL")
    documentation_url: Optional[str] = Field(None, description="Documentation URL")
    organization: Optional[str] = Field(None, description="Publishing organization")
    total_controls: int = Field(0, description="Total number of controls")
    total_safeguards: int = Field(0, description="Total number of safeguards")
    implementation_groups: Optional[Dict[str, Any]] = Field(None, description="Implementation groups info")


class CISControlsVersionCreate(CISControlsVersionBase):
    """Schema for creating CIS Controls versions."""
    is_current: bool = Field(False, description="Is this the current version")


class CISControlsVersionUpdate(BaseModel):
    """Schema for updating CIS Controls versions."""
    description: Optional[str] = None
    standard_url: Optional[str] = None
    documentation_url: Optional[str] = None
    organization: Optional[str] = None
    is_current: Optional[bool] = None
    implementation_groups: Optional[Dict[str, Any]] = None


class CISControlsVersionResponse(CISControlsVersionBase):
    """Schema for CIS Controls version responses."""
    id: int
    is_current: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class CISControlsVersionListResponse(BaseModel):
    """Schema for CIS Controls version list responses."""
    versions: List[CISControlsVersionResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


# Control schemas
class CISControlBase(BaseModel):
    """Base schema for CIS Controls."""
    control_id: str = Field(..., description="Control identifier")
    title: str = Field(..., description="Control title")
    description: Optional[str] = Field(None, description="Control description")
    order_index: int = Field(..., description="Display order")
    control_type: Optional[ControlTypeEnum] = Field(None, description="Control type")
    asset_types: Optional[List[str]] = Field(None, description="Applicable asset types")
    security_functions: Optional[List[str]] = Field(None, description="Security functions")
    why_is_this_important: Optional[str] = Field(None, description="Rationale for the control")
    implementation_guidance: Optional[str] = Field(None, description="Implementation guidance")
    total_safeguards: int = Field(0, description="Total number of safeguards")
    ig1_safeguards: int = Field(0, description="IG1 safeguards count")
    ig2_safeguards: int = Field(0, description="IG2 safeguards count")
    ig3_safeguards: int = Field(0, description="IG3 safeguards count")
    related_controls: Optional[List[str]] = Field(None, description="Related control IDs")
    external_references: Optional[Dict[str, Any]] = Field(None, description="External references")
    regulatory_mappings: Optional[Dict[str, Any]] = Field(None, description="Regulatory mappings")


class CISControlCreate(CISControlBase):
    """Schema for creating CIS Controls."""
    version_id: int = Field(..., description="Framework version ID")


class CISControlUpdate(BaseModel):
    """Schema for updating CIS Controls."""
    title: Optional[str] = None
    description: Optional[str] = None
    control_type: Optional[ControlTypeEnum] = None
    asset_types: Optional[List[str]] = None
    security_functions: Optional[List[str]] = None
    why_is_this_important: Optional[str] = None
    implementation_guidance: Optional[str] = None
    related_controls: Optional[List[str]] = None
    external_references: Optional[Dict[str, Any]] = None
    regulatory_mappings: Optional[Dict[str, Any]] = None


class CISControlResponse(CISControlBase):
    """Schema for CIS Control responses."""
    id: int
    version_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class CISControlListResponse(BaseModel):
    """Schema for CIS Control list responses."""
    controls: List[CISControlResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


# Safeguard schemas
class CISSafeguardBase(BaseModel):
    """Base schema for CIS Safeguards."""
    safeguard_id: str = Field(..., description="Safeguard identifier")
    title: str = Field(..., description="Safeguard title")
    description: Optional[str] = Field(None, description="Safeguard description")
    order_index: int = Field(..., description="Display order")
    safeguard_type: Optional[str] = Field(None, description="Safeguard type")
    implementation_group_1: bool = Field(False, description="IG1 requirement")
    implementation_group_2: bool = Field(False, description="IG2 requirement")
    implementation_group_3: bool = Field(False, description="IG3 requirement")
    asset_types: Optional[List[str]] = Field(None, description="Applicable asset types")
    security_functions: Optional[List[str]] = Field(None, description="Security functions")
    implementation_guidance: Optional[str] = Field(None, description="Implementation guidance")
    measurement_specification: Optional[str] = Field(None, description="Measurement specification")
    procedure_review: Optional[str] = Field(None, description="Procedure review guidance")
    automation_support: Optional[str] = Field(None, description="Automation possibilities")
    dependencies: Optional[List[str]] = Field(None, description="Dependencies")
    prerequisites: Optional[List[str]] = Field(None, description="Prerequisites")
    assessment_criteria: Optional[Dict[str, Any]] = Field(None, description="Assessment criteria")
    validation_methods: Optional[List[str]] = Field(None, description="Validation methods")
    evidence_examples: Optional[List[str]] = Field(None, description="Evidence examples")
    related_safeguards: Optional[List[str]] = Field(None, description="Related safeguards")
    external_references: Optional[Dict[str, Any]] = Field(None, description="External references")
    regulatory_mappings: Optional[Dict[str, Any]] = Field(None, description="Regulatory mappings")


class CISSafeguardCreate(CISSafeguardBase):
    """Schema for creating CIS Safeguards."""
    control_id: int = Field(..., description="Control ID")
    version_id: int = Field(..., description="Framework version ID")


class CISSafeguardUpdate(BaseModel):
    """Schema for updating CIS Safeguards."""
    title: Optional[str] = None
    description: Optional[str] = None
    safeguard_type: Optional[str] = None
    implementation_group_1: Optional[bool] = None
    implementation_group_2: Optional[bool] = None
    implementation_group_3: Optional[bool] = None
    asset_types: Optional[List[str]] = None
    security_functions: Optional[List[str]] = None
    implementation_guidance: Optional[str] = None
    measurement_specification: Optional[str] = None
    procedure_review: Optional[str] = None
    automation_support: Optional[str] = None
    dependencies: Optional[List[str]] = None
    prerequisites: Optional[List[str]] = None
    assessment_criteria: Optional[Dict[str, Any]] = None
    validation_methods: Optional[List[str]] = None
    evidence_examples: Optional[List[str]] = None
    related_safeguards: Optional[List[str]] = None
    external_references: Optional[Dict[str, Any]] = None
    regulatory_mappings: Optional[Dict[str, Any]] = None


class CISSafeguardResponse(CISSafeguardBase):
    """Schema for CIS Safeguard responses."""
    id: int
    control_id: int
    version_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class CISSafeguardListResponse(BaseModel):
    """Schema for CIS Safeguard list responses."""
    safeguards: List[CISSafeguardResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


# Implementation Example schemas
class CISImplementationExampleBase(BaseModel):
    """Base schema for CIS Implementation examples."""
    title: str = Field(..., description="Example title")
    description: str = Field(..., description="Example description")
    implementation_approach: Optional[str] = Field(None, description="Implementation approach")
    implementation_group: Optional[ImplementationGroupEnum] = Field(None, description="Implementation group")
    organization_size: Optional[OrganizationSizeEnum] = Field(None, description="Organization size")
    industry_sector: Optional[str] = Field(None, description="Industry sector")
    technology_context: Optional[str] = Field(None, description="Technology context")
    asset_types: Optional[List[str]] = Field(None, description="Applicable asset types")
    implementation_steps: Optional[List[str]] = Field(None, description="Implementation steps")
    tools_and_technologies: Optional[List[str]] = Field(None, description="Tools and technologies")
    automation_opportunities: Optional[List[str]] = Field(None, description="Automation opportunities")
    implementation_difficulty: Optional[ImplementationDifficultyEnum] = Field(None, description="Implementation difficulty")
    estimated_effort: Optional[str] = Field(None, description="Estimated effort")
    budget_considerations: Optional[str] = Field(None, description="Budget considerations")
    skill_requirements: Optional[List[str]] = Field(None, description="Required skills")
    expected_outcomes: Optional[List[str]] = Field(None, description="Expected outcomes")
    success_metrics: Optional[List[str]] = Field(None, description="Success metrics")
    measurement_procedures: Optional[List[str]] = Field(None, description="Measurement procedures")
    common_pitfalls: Optional[List[str]] = Field(None, description="Common pitfalls")
    lessons_learned: Optional[List[str]] = Field(None, description="Lessons learned")
    best_practices: Optional[List[str]] = Field(None, description="Best practices")


class CISImplementationExampleCreate(CISImplementationExampleBase):
    """Schema for creating CIS Implementation examples."""
    safeguard_id: int = Field(..., description="Safeguard ID")


class CISImplementationExampleResponse(CISImplementationExampleBase):
    """Schema for CIS Implementation example responses."""
    id: int
    safeguard_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Assessment schemas
class CISAssessmentBase(BaseModel):
    """Base schema for CIS assessments."""
    name: str = Field(..., description="Assessment name")
    description: Optional[str] = Field(None, description="Assessment description")
    assessment_date: datetime = Field(default_factory=datetime.utcnow, description="Assessment date")
    assessor: Optional[str] = Field(None, description="Assessor name")
    assessment_type: Optional[AssessmentTypeEnum] = Field(None, description="Assessment type")
    assessment_scope: Optional[str] = Field(None, description="Assessment scope")
    target_implementation_group: Optional[ImplementationGroupEnum] = Field(None, description="Target IG")
    control_scores: Optional[Dict[str, float]] = Field(None, description="Control scores")
    safeguard_scores: Optional[Dict[str, float]] = Field(None, description="Safeguard scores")
    implementation_status: Optional[Dict[str, str]] = Field(None, description="Implementation status")
    overall_score: Optional[float] = Field(None, ge=0.0, le=100.0, description="Overall score")
    implementation_percentage: Optional[float] = Field(None, ge=0.0, le=100.0, description="Implementation percentage")
    ig1_compliance: Optional[float] = Field(None, ge=0.0, le=100.0, description="IG1 compliance")
    ig2_compliance: Optional[float] = Field(None, ge=0.0, le=100.0, description="IG2 compliance")
    ig3_compliance: Optional[float] = Field(None, ge=0.0, le=100.0, description="IG3 compliance")
    findings: Optional[List[Dict[str, Any]]] = Field(None, description="Assessment findings")
    gaps: Optional[List[Dict[str, Any]]] = Field(None, description="Implementation gaps")
    recommendations: Optional[List[str]] = Field(None, description="Recommendations")
    priority_actions: Optional[List[Dict[str, Any]]] = Field(None, description="Priority actions")
    next_assessment_date: Optional[datetime] = Field(None, description="Next assessment date")
    follow_up_required: bool = Field(False, description="Follow-up required")
    improvement_target_date: Optional[datetime] = Field(None, description="Improvement target date")


class CISAssessmentCreate(CISAssessmentBase):
    """Schema for creating CIS assessments."""
    version_id: int = Field(..., description="Framework version ID")


class CISAssessmentUpdate(BaseModel):
    """Schema for updating CIS assessments."""
    name: Optional[str] = None
    description: Optional[str] = None
    assessor: Optional[str] = None
    assessment_type: Optional[AssessmentTypeEnum] = None
    assessment_scope: Optional[str] = None
    target_implementation_group: Optional[ImplementationGroupEnum] = None
    control_scores: Optional[Dict[str, float]] = None
    safeguard_scores: Optional[Dict[str, float]] = None
    implementation_status: Optional[Dict[str, str]] = None
    overall_score: Optional[float] = Field(None, ge=0.0, le=100.0)
    implementation_percentage: Optional[float] = Field(None, ge=0.0, le=100.0)
    ig1_compliance: Optional[float] = Field(None, ge=0.0, le=100.0)
    ig2_compliance: Optional[float] = Field(None, ge=0.0, le=100.0)
    ig3_compliance: Optional[float] = Field(None, ge=0.0, le=100.0)
    findings: Optional[List[Dict[str, Any]]] = None
    gaps: Optional[List[Dict[str, Any]]] = None
    recommendations: Optional[List[str]] = None
    priority_actions: Optional[List[Dict[str, Any]]] = None
    next_assessment_date: Optional[datetime] = None
    follow_up_required: Optional[bool] = None
    improvement_target_date: Optional[datetime] = None


class CISAssessmentResponse(CISAssessmentBase):
    """Schema for CIS assessment responses."""
    id: int
    version_id: int
    created_by: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class CISAssessmentListResponse(BaseModel):
    """Schema for CIS assessment list responses."""
    assessments: List[CISAssessmentResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


# Search and filter schemas
class CISSearchRequest(BaseModel):
    """Schema for CIS Controls search requests."""
    query: Optional[str] = Field(None, description="Search query")
    control_id: Optional[str] = Field(None, description="Filter by control")
    implementation_group: Optional[ImplementationGroupEnum] = Field(None, description="Filter by IG")
    asset_type: Optional[str] = Field(None, description="Filter by asset type")
    security_function: Optional[str] = Field(None, description="Filter by security function")
    version: Optional[str] = Field(None, description="Filter by version")
    include_examples: bool = Field(False, description="Include implementation examples")
    include_references: bool = Field(False, description="Include external references")


class CISSearchResponse(BaseModel):
    """Schema for CIS Controls search responses."""
    results: List[CISSafeguardResponse]
    total: int
    page: int
    page_size: int
    total_pages: int
    search_metadata: Dict[str, Any]


# Export schemas
class CISExportRequest(BaseModel):
    """Schema for CIS Controls export requests."""
    format: str = Field("json", description="Export format")
    version: Optional[str] = Field(None, description="Framework version")
    implementation_group: Optional[ImplementationGroupEnum] = Field(None, description="Implementation group")
    controls: Optional[List[str]] = Field(None, description="Controls to include")
    safeguards: Optional[List[str]] = Field(None, description="Safeguards to include")
    include_examples: bool = Field(False, description="Include implementation examples")
    include_assessments: bool = Field(False, description="Include assessments")


class CISExportResponse(BaseModel):
    """Schema for CIS Controls export responses."""
    success: bool
    format: str
    data: str
    file_size: int
    record_count: int
    export_metadata: Dict[str, Any]
    processing_time: float
