"""
Framework Mapping Schemas.

This module contains Pydantic schemas for framework mapping API requests and responses.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum


class MappingTypeEnum(str, Enum):
    """Types of framework mappings."""
    DIRECT = "direct"
    PARTIAL = "partial"
    RELATED = "related"
    COMPLEMENTARY = "complementary"
    HIERARCHICAL = "hierarchical"


class MappingConfidenceEnum(str, Enum):
    """Confidence levels for mappings."""
    HIGH = "high"
    MEDIUM = "medium"
    LOW = "low"
    EXPERIMENTAL = "experimental"


class ValidationStatusEnum(str, Enum):
    """Validation status options."""
    PENDING = "pending"
    APPROVED = "approved"
    REJECTED = "rejected"
    NEEDS_REVIEW = "needs_review"


# Base schemas
class FrameworkMappingBase(BaseModel):
    """Base schema for framework mappings."""
    source_framework: str = Field(..., description="Source framework name")
    source_control_id: str = Field(..., description="Source control identifier")
    source_version: Optional[str] = Field(None, description="Source framework version")
    target_framework: str = Field(..., description="Target framework name")
    target_control_id: str = Field(..., description="Target control identifier")
    target_version: Optional[str] = Field(None, description="Target framework version")
    mapping_type: MappingTypeEnum = Field(MappingTypeEnum.DIRECT, description="Type of mapping")
    confidence_level: MappingConfidenceEnum = Field(MappingConfidenceEnum.MEDIUM, description="Confidence level")
    confidence_score: float = Field(0.75, ge=0.0, le=1.0, description="Confidence score (0.0-1.0)")
    effectiveness_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Effectiveness score")
    coverage_percentage: Optional[float] = Field(None, ge=0.0, le=100.0, description="Coverage percentage")
    description: Optional[str] = Field(None, description="Mapping description")
    rationale: Optional[str] = Field(None, description="Mapping rationale")
    notes: Optional[str] = Field(None, description="Additional notes")
    mapping_data: Optional[Dict[str, Any]] = Field(None, description="Additional mapping data")


class FrameworkMappingCreate(FrameworkMappingBase):
    """Schema for creating framework mappings."""
    mapping_set_id: Optional[int] = Field(None, description="Associated mapping set ID")


class FrameworkMappingUpdate(BaseModel):
    """Schema for updating framework mappings."""
    mapping_type: Optional[MappingTypeEnum] = None
    confidence_level: Optional[MappingConfidenceEnum] = None
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    effectiveness_score: Optional[float] = Field(None, ge=0.0, le=1.0)
    coverage_percentage: Optional[float] = Field(None, ge=0.0, le=100.0)
    description: Optional[str] = None
    rationale: Optional[str] = None
    notes: Optional[str] = None
    mapping_data: Optional[Dict[str, Any]] = None
    mapping_set_id: Optional[int] = None


class FrameworkMappingResponse(FrameworkMappingBase):
    """Schema for framework mapping responses."""
    id: int
    is_validated: bool
    validated_by: Optional[str] = None
    validated_at: Optional[datetime] = None
    mapping_set_id: Optional[int] = None
    created_by: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class FrameworkMappingListResponse(BaseModel):
    """Schema for framework mapping list responses."""
    mappings: List[FrameworkMappingResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


# Mapping Set schemas
class MappingSetBase(BaseModel):
    """Base schema for mapping sets."""
    name: str = Field(..., description="Mapping set name")
    description: Optional[str] = Field(None, description="Mapping set description")
    source_framework: str = Field(..., description="Source framework")
    target_framework: str = Field(..., description="Target framework")
    version: str = Field("1.0", description="Mapping set version")
    is_official: bool = Field(False, description="Is this an official mapping set")


class MappingSetCreate(MappingSetBase):
    """Schema for creating mapping sets."""
    pass


class MappingSetUpdate(BaseModel):
    """Schema for updating mapping sets."""
    name: Optional[str] = None
    description: Optional[str] = None
    version: Optional[str] = None
    is_official: Optional[bool] = None
    is_published: Optional[bool] = None


class MappingSetResponse(MappingSetBase):
    """Schema for mapping set responses."""
    id: int
    is_published: bool
    total_mappings: int
    validated_mappings: int
    average_confidence: Optional[float] = None
    created_by: str
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class MappingSetListResponse(BaseModel):
    """Schema for mapping set list responses."""
    mapping_sets: List[MappingSetResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


# Validation schemas
class MappingValidationBase(BaseModel):
    """Base schema for mapping validations."""
    validation_status: ValidationStatusEnum = Field(..., description="Validation status")
    comments: Optional[str] = Field(None, description="Reviewer comments")
    suggested_changes: Optional[Dict[str, Any]] = Field(None, description="Suggested changes")
    confidence_assessment: Optional[float] = Field(None, ge=0.0, le=1.0, description="Confidence assessment")
    accuracy_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Accuracy score")
    completeness_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Completeness score")
    relevance_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Relevance score")


class MappingValidationCreate(MappingValidationBase):
    """Schema for creating mapping validations."""
    mapping_id: int = Field(..., description="Mapping ID to validate")


class MappingValidationResponse(MappingValidationBase):
    """Schema for mapping validation responses."""
    id: int
    mapping_id: int
    reviewer_id: str
    review_date: datetime
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Search and filter schemas
class MappingSearchRequest(BaseModel):
    """Schema for mapping search requests."""
    query: Optional[str] = Field(None, description="Search query")
    source_framework: Optional[str] = Field(None, description="Filter by source framework")
    target_framework: Optional[str] = Field(None, description="Filter by target framework")
    mapping_type: Optional[MappingTypeEnum] = Field(None, description="Filter by mapping type")
    confidence_level: Optional[MappingConfidenceEnum] = Field(None, description="Filter by confidence level")
    is_validated: Optional[bool] = Field(None, description="Filter by validation status")
    min_confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Minimum confidence score")
    max_confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Maximum confidence score")
    created_after: Optional[datetime] = Field(None, description="Filter by creation date")
    created_before: Optional[datetime] = Field(None, description="Filter by creation date")


class MappingSearchResponse(BaseModel):
    """Schema for mapping search responses."""
    results: List[FrameworkMappingResponse]
    total: int
    page: int
    page_size: int
    total_pages: int
    search_metadata: Dict[str, Any]


# Bulk operations schemas
class BulkMappingCreate(BaseModel):
    """Schema for bulk mapping creation."""
    mappings: List[FrameworkMappingCreate] = Field(..., description="List of mappings to create")
    mapping_set_id: Optional[int] = Field(None, description="Associate all mappings with this set")
    validate_immediately: bool = Field(False, description="Validate mappings immediately after creation")


class BulkMappingResponse(BaseModel):
    """Schema for bulk mapping operation responses."""
    success: bool
    created_count: int
    failed_count: int
    created_mappings: List[FrameworkMappingResponse]
    errors: List[Dict[str, Any]]
    processing_time: float


# Analytics schemas
class MappingAnalytics(BaseModel):
    """Schema for mapping analytics."""
    total_mappings: int
    mappings_by_framework: Dict[str, int]
    mappings_by_type: Dict[str, int]
    mappings_by_confidence: Dict[str, int]
    validation_statistics: Dict[str, int]
    average_confidence_score: float
    coverage_statistics: Dict[str, Any]
    recent_activity: List[Dict[str, Any]]


class FrameworkCoverage(BaseModel):
    """Schema for framework coverage analysis."""
    framework_name: str
    total_controls: int
    mapped_controls: int
    coverage_percentage: float
    unmapped_controls: List[str]
    mapping_quality_score: float


class CrossFrameworkAnalysis(BaseModel):
    """Schema for cross-framework analysis."""
    framework_pair: str
    total_possible_mappings: int
    actual_mappings: int
    coverage_percentage: float
    quality_metrics: Dict[str, float]
    gap_analysis: List[Dict[str, Any]]
    recommendations: List[str]


# Export schemas
class MappingExportRequest(BaseModel):
    """Schema for mapping export requests."""
    format: str = Field("json", description="Export format (json, csv, xlsx)")
    source_framework: Optional[str] = Field(None, description="Filter by source framework")
    target_framework: Optional[str] = Field(None, description="Filter by target framework")
    mapping_set_id: Optional[int] = Field(None, description="Export specific mapping set")
    include_validations: bool = Field(False, description="Include validation data")
    include_audit_trail: bool = Field(False, description="Include audit trail")
    filters: Dict[str, Any] = Field(default_factory=dict, description="Additional filters")


class MappingExportResponse(BaseModel):
    """Schema for mapping export responses."""
    success: bool
    format: str
    data: str  # Base64 encoded data or JSON string
    file_size: int
    record_count: int
    export_metadata: Dict[str, Any]
    processing_time: float
