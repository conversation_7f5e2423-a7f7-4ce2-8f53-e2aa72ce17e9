"""
Pydantic schemas for ISF (Information Security Forum) API endpoints.

This module defines the request and response models for ISF framework operations
including version management, import/export, and search functionality.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum


class ISFControlType(str, Enum):
    """ISF control types."""
    POLICY = "policy"
    ADMINISTRATIVE = "administrative"
    TECHNICAL = "technical"


class ISFMaturityLevel(str, Enum):
    """ISF maturity levels."""
    BASIC = "basic"
    INTERMEDIATE = "intermediate"
    ADVANCED = "advanced"


class ISFImportFormat(str, Enum):
    """Supported import formats."""
    JSON = "json"
    CSV = "csv"


class ISFExportFormat(str, Enum):
    """Supported export formats."""
    JSON = "json"
    CSV = "csv"


# Base schemas
class ISFControlBase(BaseModel):
    """Base schema for ISF control."""
    control_id: str = Field(..., description="Unique control identifier")
    name: str = Field(..., description="Control name")
    description: Optional[str] = Field(None, description="Control description")
    objective: Optional[str] = Field(None, description="Control objective")
    guidance: Optional[str] = Field(None, description="Implementation guidance")
    control_type: ISFControlType = Field(..., description="Type of control")
    maturity_level: ISFMaturityLevel = Field(..., description="Maturity level")
    keywords: Optional[List[str]] = Field(default_factory=list, description="Associated keywords")


class ISFSecurityAreaBase(BaseModel):
    """Base schema for ISF security area."""
    area_id: str = Field(..., description="Unique area identifier")
    name: str = Field(..., description="Security area name")
    description: Optional[str] = Field(None, description="Area description")


class ISFVersionBase(BaseModel):
    """Base schema for ISF version."""
    version: str = Field(..., description="Version identifier")
    release_date: Optional[datetime] = Field(None, description="Release date")
    description: Optional[str] = Field(None, description="Version description")
    url: Optional[str] = Field(None, description="Official URL")
    is_current: bool = Field(False, description="Whether this is the current version")


# Response schemas
class ISFControlResponse(ISFControlBase):
    """Response schema for ISF control."""
    id: int = Field(..., description="Database ID")
    security_area_id: int = Field(..., description="Security area ID")
    version_id: int = Field(..., description="Version ID")
    order_index: int = Field(..., description="Display order")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    
    class Config:
        from_attributes = True


class ISFSecurityAreaResponse(ISFSecurityAreaBase):
    """Response schema for ISF security area."""
    id: int = Field(..., description="Database ID")
    version_id: int = Field(..., description="Version ID")
    order_index: int = Field(..., description="Display order")
    controls_count: int = Field(..., description="Number of controls in this area")
    controls: Optional[List[ISFControlResponse]] = Field(None, description="Controls in this area")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    
    class Config:
        from_attributes = True


class ISFVersionResponse(ISFVersionBase):
    """Response schema for ISF version."""
    id: int = Field(..., description="Database ID")
    import_date: datetime = Field(..., description="Import timestamp")
    areas_count: int = Field(..., description="Number of security areas")
    controls_count: int = Field(..., description="Total number of controls")
    security_areas: Optional[List[ISFSecurityAreaResponse]] = Field(None, description="Security areas")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    
    class Config:
        from_attributes = True


class ISFVersionListResponse(BaseModel):
    """Response schema for ISF version list."""
    versions: List[ISFVersionResponse] = Field(..., description="List of versions")
    total: int = Field(..., description="Total number of versions")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Items per page")
    total_pages: int = Field(..., description="Total number of pages")


class ISFControlListResponse(BaseModel):
    """Response schema for ISF control list."""
    controls: List[ISFControlResponse] = Field(..., description="List of controls")
    pagination: Dict[str, Any] = Field(..., description="Pagination information")


# Import/Export schemas
class ISFImportRequest(BaseModel):
    """Request schema for ISF import."""
    format: ISFImportFormat = Field(..., description="Import format")
    data: str = Field(..., description="Data to import")
    version: Optional[str] = Field(None, description="Version identifier (required for CSV)")
    replace_existing: bool = Field(False, description="Whether to replace existing version")
    async_import: bool = Field(False, description="Whether to perform async import")
    
    @validator('data')
    def validate_data(cls, v):
        if not v or not v.strip():
            raise ValueError("Data cannot be empty")
        return v


class ISFImportResponse(BaseModel):
    """Response schema for ISF import."""
    success: bool = Field(..., description="Whether import was successful")
    version_id: Optional[int] = Field(None, description="ID of imported version")
    imported_security_areas: int = Field(0, description="Number of imported security areas")
    imported_controls: int = Field(0, description="Number of imported controls")
    processing_time: float = Field(0.0, description="Processing time in seconds")
    errors: List[str] = Field(default_factory=list, description="Import errors")
    warnings: List[str] = Field(default_factory=list, description="Import warnings")
    replaced_existing: bool = Field(False, description="Whether existing version was replaced")
    file_name: Optional[str] = Field(None, description="Uploaded file name")
    file_size: Optional[int] = Field(None, description="File size in bytes")
    task_id: Optional[str] = Field(None, description="Async task ID")
    status: Optional[str] = Field(None, description="Import status")
    message: Optional[str] = Field(None, description="Status message")


class ISFExportRequest(BaseModel):
    """Request schema for ISF export."""
    format: ISFExportFormat = Field(..., description="Export format")
    version: Optional[str] = Field(None, description="Version to export")
    include_mappings: bool = Field(False, description="Include cross-framework mappings")
    filters: Optional[Dict[str, Any]] = Field(None, description="Export filters")
    
    class Config:
        schema_extra = {
            "example": {
                "format": "json",
                "version": "2020.1",
                "include_mappings": True,
                "filters": {
                    "security_areas": ["SG", "RM"],
                    "control_types": ["policy", "technical"],
                    "maturity_levels": ["basic", "intermediate"]
                }
            }
        }


class ISFExportResponse(BaseModel):
    """Response schema for ISF export."""
    success: bool = Field(..., description="Whether export was successful")
    format: str = Field(..., description="Export format")
    data: str = Field(..., description="Exported data")
    file_size: int = Field(..., description="Data size in bytes")
    record_count: int = Field(..., description="Number of exported records")
    processing_time: float = Field(..., description="Processing time in seconds")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Export metadata")


# Search schemas
class ISFSearchFilters(BaseModel):
    """Filters for ISF search."""
    control_types: Optional[List[ISFControlType]] = Field(None, description="Filter by control types")
    maturity_levels: Optional[List[ISFMaturityLevel]] = Field(None, description="Filter by maturity levels")
    security_areas: Optional[List[str]] = Field(None, description="Filter by security area IDs")
    version: Optional[str] = Field(None, description="Filter by version")


class ISFSearchRequest(BaseModel):
    """Request schema for ISF search."""
    query: Optional[str] = Field(None, description="Search query")
    filters: Optional[ISFSearchFilters] = Field(None, description="Search filters")
    page: int = Field(1, ge=1, description="Page number")
    page_size: int = Field(10, ge=1, le=100, description="Items per page")
    sort_by: Optional[str] = Field("relevance", description="Sort field")
    sort_order: Optional[str] = Field("desc", regex="^(asc|desc)$", description="Sort order")
    
    class Config:
        schema_extra = {
            "example": {
                "query": "security policy",
                "filters": {
                    "control_types": ["policy"],
                    "maturity_levels": ["basic", "intermediate"],
                    "security_areas": ["SG"]
                },
                "page": 1,
                "page_size": 10
            }
        }


class ISFSearchResult(BaseModel):
    """Individual search result."""
    control_id: str = Field(..., description="Control identifier")
    name: str = Field(..., description="Control name")
    description: str = Field(..., description="Control description")
    control_type: str = Field(..., description="Control type")
    maturity_level: str = Field(..., description="Maturity level")
    security_area: str = Field(..., description="Security area ID")
    relevance_score: float = Field(..., ge=0.0, le=1.0, description="Relevance score")


class ISFSearchResponse(BaseModel):
    """Response schema for ISF search."""
    results: List[ISFSearchResult] = Field(..., description="Search results")
    total: int = Field(..., description="Total number of results")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Items per page")
    total_pages: int = Field(..., description="Total number of pages")
    search_metadata: Optional[Dict[str, Any]] = Field(None, description="Search metadata")


# Progress tracking schemas
class ISFImportProgress(BaseModel):
    """Progress tracking for ISF import."""
    task_id: str = Field(..., description="Task identifier")
    status: str = Field(..., description="Current status")
    progress_percentage: float = Field(..., ge=0.0, le=100.0, description="Progress percentage")
    current_stage: str = Field(..., description="Current processing stage")
    processed_items: int = Field(0, description="Number of processed items")
    total_items: int = Field(0, description="Total number of items")
    estimated_completion: Optional[datetime] = Field(None, description="Estimated completion time")
    errors: List[str] = Field(default_factory=list, description="Processing errors")


# Validation schemas
class ISFDataValidation(BaseModel):
    """ISF data validation result."""
    is_valid: bool = Field(..., description="Whether data is valid")
    errors: List[str] = Field(default_factory=list, description="Validation errors")
    warnings: List[str] = Field(default_factory=list, description="Validation warnings")
    validated_controls: int = Field(0, description="Number of validated controls")
    validated_areas: int = Field(0, description="Number of validated areas")


# Statistics schemas
class ISFStatistics(BaseModel):
    """ISF framework statistics."""
    total_versions: int = Field(..., description="Total number of versions")
    current_version: str = Field(..., description="Current version identifier")
    total_security_areas: int = Field(..., description="Total security areas")
    total_controls: int = Field(..., description="Total controls")
    controls_by_type: Dict[str, int] = Field(..., description="Controls grouped by type")
    controls_by_maturity: Dict[str, int] = Field(..., description="Controls grouped by maturity level")
    last_import_date: Optional[datetime] = Field(None, description="Last import timestamp")
    framework_coverage: float = Field(..., ge=0.0, le=100.0, description="Framework coverage percentage")
