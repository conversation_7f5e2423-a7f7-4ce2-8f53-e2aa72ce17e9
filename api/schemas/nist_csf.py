"""
Pydantic schemas for NIST CSF (Cybersecurity Framework) API endpoints.

This module defines the request and response models for NIST CSF framework operations
including hierarchical navigation, version management, and import/export functionality.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum


class NISTCSFImportFormat(str, Enum):
    """Supported import formats."""
    JSON = "json"


class NISTCSFExportFormat(str, Enum):
    """Supported export formats."""
    JSON = "json"
    CSV = "csv"


# Base schemas
class NISTCSFImplementationExampleBase(BaseModel):
    """Base schema for implementation example."""
    example_text: str = Field(..., description="Example text")
    example_type: str = Field(..., description="Type of example")
    applicability: Optional[str] = Field(None, description="Applicability context")


class NISTCSFInformativeReferenceBase(BaseModel):
    """Base schema for informative reference."""
    framework_name: str = Field(..., description="Referenced framework name")
    reference_id: str = Field(..., description="Reference identifier")
    description: Optional[str] = Field(None, description="Reference description")


class NISTCSFSubcategoryBase(BaseModel):
    """Base schema for NIST CSF subcategory."""
    subcategory_id: str = Field(..., description="Unique subcategory identifier")
    name: str = Field(..., description="Subcategory name")
    description: Optional[str] = Field(None, description="Subcategory description")


class NISTCSFCategoryBase(BaseModel):
    """Base schema for NIST CSF category."""
    category_id: str = Field(..., description="Unique category identifier")
    name: str = Field(..., description="Category name")
    description: Optional[str] = Field(None, description="Category description")


class NISTCSFFunctionBase(BaseModel):
    """Base schema for NIST CSF function."""
    function_id: str = Field(..., description="Unique function identifier")
    name: str = Field(..., description="Function name")
    description: Optional[str] = Field(None, description="Function description")


class NISTCSFVersionBase(BaseModel):
    """Base schema for NIST CSF version."""
    version: str = Field(..., description="Version identifier")
    release_date: Optional[datetime] = Field(None, description="Release date")
    description: Optional[str] = Field(None, description="Version description")
    url: Optional[str] = Field(None, description="Official URL")
    is_current: bool = Field(False, description="Whether this is the current version")


# Response schemas
class NISTCSFImplementationExampleResponse(NISTCSFImplementationExampleBase):
    """Response schema for implementation example."""
    id: int = Field(..., description="Database ID")
    subcategory_id: int = Field(..., description="Subcategory ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    
    class Config:
        from_attributes = True


class NISTCSFInformativeReferenceResponse(NISTCSFInformativeReferenceBase):
    """Response schema for informative reference."""
    id: int = Field(..., description="Database ID")
    subcategory_id: int = Field(..., description="Subcategory ID")
    created_at: datetime = Field(..., description="Creation timestamp")
    
    class Config:
        from_attributes = True


class NISTCSFSubcategoryResponse(NISTCSFSubcategoryBase):
    """Response schema for NIST CSF subcategory."""
    id: int = Field(..., description="Database ID")
    category_id: int = Field(..., description="Category ID")
    function_id: int = Field(..., description="Function ID")
    order_index: int = Field(..., description="Display order")
    implementation_examples: Optional[List[NISTCSFImplementationExampleResponse]] = Field(
        None, description="Implementation examples"
    )
    informative_references: Optional[List[NISTCSFInformativeReferenceResponse]] = Field(
        None, description="Informative references"
    )
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    
    class Config:
        from_attributes = True


class NISTCSFCategoryResponse(NISTCSFCategoryBase):
    """Response schema for NIST CSF category."""
    id: int = Field(..., description="Database ID")
    function_id: int = Field(..., description="Function ID")
    order_index: int = Field(..., description="Display order")
    subcategories_count: int = Field(..., description="Number of subcategories")
    subcategories: Optional[List[NISTCSFSubcategoryResponse]] = Field(
        None, description="Subcategories in this category"
    )
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    
    class Config:
        from_attributes = True


class NISTCSFFunctionResponse(NISTCSFFunctionBase):
    """Response schema for NIST CSF function."""
    id: int = Field(..., description="Database ID")
    version_id: int = Field(..., description="Version ID")
    order_index: int = Field(..., description="Display order")
    categories_count: int = Field(..., description="Number of categories")
    subcategories_count: int = Field(..., description="Total number of subcategories")
    categories: Optional[List[NISTCSFCategoryResponse]] = Field(
        None, description="Categories in this function"
    )
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    
    class Config:
        from_attributes = True


class NISTCSFVersionResponse(NISTCSFVersionBase):
    """Response schema for NIST CSF version."""
    id: int = Field(..., description="Database ID")
    import_date: datetime = Field(..., description="Import timestamp")
    functions_count: int = Field(..., description="Number of functions")
    categories_count: int = Field(..., description="Total number of categories")
    subcategories_count: int = Field(..., description="Total number of subcategories")
    supersedes_version_id: Optional[int] = Field(None, description="ID of superseded version")
    superseded_by_version: Optional[str] = Field(None, description="Version that supersedes this one")
    functions: Optional[List[NISTCSFFunctionResponse]] = Field(None, description="Functions")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    
    class Config:
        from_attributes = True


class NISTCSFVersionListResponse(BaseModel):
    """Response schema for NIST CSF version list."""
    versions: List[NISTCSFVersionResponse] = Field(..., description="List of versions")
    total: int = Field(..., description="Total number of versions")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Items per page")


# Import/Export schemas
class NISTCSFImportRequest(BaseModel):
    """Request schema for NIST CSF import."""
    format: NISTCSFImportFormat = Field(..., description="Import format")
    data: str = Field(..., description="Data to import")
    preserve_hierarchy: bool = Field(True, description="Preserve hierarchical structure")
    handle_supersession: bool = Field(False, description="Handle version supersession")
    
    @validator('data')
    def validate_data(cls, v):
        if not v or not v.strip():
            raise ValueError("Data cannot be empty")
        return v


class NISTCSFImportResponse(BaseModel):
    """Response schema for NIST CSF import."""
    success: bool = Field(..., description="Whether import was successful")
    version_id: Optional[int] = Field(None, description="ID of imported version")
    imported_functions: int = Field(0, description="Number of imported functions")
    imported_categories: int = Field(0, description="Number of imported categories")
    imported_subcategories: int = Field(0, description="Number of imported subcategories")
    imported_implementation_examples: int = Field(0, description="Number of imported examples")
    imported_informative_references: int = Field(0, description="Number of imported references")
    processing_time: float = Field(0.0, description="Processing time in seconds")
    superseded_version_id: Optional[int] = Field(None, description="ID of superseded version")
    errors: List[str] = Field(default_factory=list, description="Import errors")
    warnings: List[str] = Field(default_factory=list, description="Import warnings")


class NISTCSFExportRequest(BaseModel):
    """Request schema for NIST CSF export."""
    format: NISTCSFExportFormat = Field(..., description="Export format")
    version: Optional[str] = Field(None, description="Version to export")
    preserve_hierarchy: bool = Field(True, description="Preserve hierarchical structure")
    flatten_hierarchy: bool = Field(False, description="Flatten hierarchy for CSV")
    include_examples: bool = Field(True, description="Include implementation examples")
    include_references: bool = Field(True, description="Include informative references")
    export_type: Optional[str] = Field(None, description="Special export type")
    source_version: Optional[str] = Field(None, description="Source version for comparison")
    target_version: Optional[str] = Field(None, description="Target version for comparison")


class NISTCSFExportResponse(BaseModel):
    """Response schema for NIST CSF export."""
    success: bool = Field(..., description="Whether export was successful")
    format: str = Field(..., description="Export format")
    data: str = Field(..., description="Exported data")
    file_size: int = Field(..., description="Data size in bytes")
    record_count: int = Field(..., description="Number of exported records")
    processing_time: float = Field(..., description="Processing time in seconds")
    metadata: Optional[Dict[str, Any]] = Field(None, description="Export metadata")


# Search schemas
class NISTCSFSearchFilters(BaseModel):
    """Filters for NIST CSF search."""
    functions: Optional[List[str]] = Field(None, description="Filter by function IDs")
    categories: Optional[List[str]] = Field(None, description="Filter by category IDs")
    version: Optional[str] = Field(None, description="Filter by version")
    include_examples: bool = Field(False, description="Include implementation examples")


class NISTCSFSearchRequest(BaseModel):
    """Request schema for NIST CSF search."""
    query: Optional[str] = Field(None, description="Search query")
    search_scope: str = Field("subcategories", description="Search scope")
    filters: Optional[NISTCSFSearchFilters] = Field(None, description="Search filters")
    page: int = Field(1, ge=1, description="Page number")
    page_size: int = Field(10, ge=1, le=100, description="Items per page")


class NISTCSFSearchResult(BaseModel):
    """Individual search result."""
    subcategory_id: str = Field(..., description="Subcategory identifier")
    name: str = Field(..., description="Subcategory name")
    function_id: str = Field(..., description="Function identifier")
    category_id: str = Field(..., description="Category identifier")
    relevance_score: float = Field(..., ge=0.0, le=1.0, description="Relevance score")


class NISTCSFSearchResponse(BaseModel):
    """Response schema for NIST CSF search."""
    results: List[NISTCSFSearchResult] = Field(..., description="Search results")
    total: int = Field(..., description="Total number of results")
    search_metadata: Optional[Dict[str, Any]] = Field(None, description="Search metadata")


# Version comparison schemas
class NISTCSFVersionComparisonResponse(BaseModel):
    """Response schema for version comparison."""
    comparison_metadata: Dict[str, Any] = Field(..., description="Comparison metadata")
    added_items: Dict[str, List[Dict[str, Any]]] = Field(..., description="Added items")
    removed_items: Dict[str, List[Dict[str, Any]]] = Field(..., description="Removed items")
    modified_items: Dict[str, List[Dict[str, Any]]] = Field(..., description="Modified items")
    unchanged_items: Dict[str, List[str]] = Field(..., description="Unchanged items")


# Migration schemas
class NISTCSFMigrationRequest(BaseModel):
    """Request schema for NIST CSF migration."""
    source_version: str = Field(..., description="Source version")
    target_version: str = Field(..., description="Target version")
    migration_options: Optional[Dict[str, Any]] = Field(None, description="Migration options")


class NISTCSFMigrationResponse(BaseModel):
    """Response schema for NIST CSF migration."""
    success: bool = Field(..., description="Whether migration was successful")
    source_version: str = Field(..., description="Source version")
    target_version: str = Field(..., description="Target version")
    migrated_functions: int = Field(..., description="Number of migrated functions")
    migration_notes: List[str] = Field(..., description="Migration notes")


# Navigation schemas
class NISTCSFNavigationResponse(BaseModel):
    """Response schema for subcategory navigation."""
    current: Dict[str, str] = Field(..., description="Current subcategory")
    parent_category: Dict[str, str] = Field(..., description="Parent category")
    parent_function: Dict[str, str] = Field(..., description="Parent function")
    siblings: List[Dict[str, str]] = Field(..., description="Sibling subcategories")
    related_subcategories: List[Dict[str, str]] = Field(..., description="Related subcategories")


# Guidance schemas
class NISTCSFGuidanceResponse(BaseModel):
    """Response schema for implementation guidance."""
    subcategory: Dict[str, str] = Field(..., description="Subcategory information")
    implementation_examples: List[Dict[str, str]] = Field(..., description="Implementation examples")
    informative_references: List[Dict[str, str]] = Field(..., description="Informative references")
    related_controls: List[Dict[str, str]] = Field(..., description="Related controls")
    implementation_tips: List[str] = Field(..., description="Implementation tips")


# Performance schemas
class NISTCSFBulkRequest(BaseModel):
    """Request schema for bulk operations."""
    operations: List[Dict[str, Any]] = Field(..., description="List of operations to perform")


class NISTCSFBulkResponse(BaseModel):
    """Response schema for bulk operations."""
    results: List[Dict[str, Any]] = Field(..., description="Results of bulk operations")
    success_count: int = Field(..., description="Number of successful operations")
    error_count: int = Field(..., description="Number of failed operations")
    processing_time: float = Field(..., description="Total processing time")
