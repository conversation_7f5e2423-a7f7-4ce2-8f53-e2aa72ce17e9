"""
Pydantic schemas for Cross-Framework Mapping API endpoints.

This module defines the request and response models for cross-framework mapping
operations including suggestions, validation, CRUD operations, and analysis.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any, Union
from datetime import datetime
from enum import Enum


class MappingType(str, Enum):
    """Types of mappings between frameworks."""
    MITIGATES = "mitigates"
    DETECTS = "detects"
    PREVENTS = "prevents"
    RESPONDS = "responds"
    RECOVERS = "recovers"
    EQUIVALENT = "equivalent"
    OVERLAPPING = "overlapping"
    COMPLEMENTARY = "complementary"
    RELATED = "related"


class ValidationStatus(str, Enum):
    """Validation status for mappings."""
    PENDING = "pending"
    VALIDATED = "validated"
    REJECTED = "rejected"
    NEEDS_REVIEW = "needs_review"


class FrameworkType(str, Enum):
    """Supported framework types."""
    MITRE_ATTACK = "mitre_attack"
    ISF = "isf"
    NIST_CSF = "nist_csf"


# Base schemas
class MappingSuggestionBase(BaseModel):
    """Base schema for mapping suggestion."""
    control_id: Optional[str] = Field(None, description="Control identifier")
    subcategory_id: Optional[str] = Field(None, description="Subcategory identifier")
    control_name: Optional[str] = Field(None, description="Control name")
    subcategory_name: Optional[str] = Field(None, description="Subcategory name")
    mapping_type: MappingType = Field(..., description="Type of mapping")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence score")
    effectiveness_score: float = Field(..., ge=0.0, le=1.0, description="Effectiveness score")
    rationale: str = Field(..., description="Mapping rationale")


class MappingBase(BaseModel):
    """Base schema for mapping."""
    source_framework: FrameworkType = Field(..., description="Source framework")
    target_framework: FrameworkType = Field(..., description="Target framework")
    source_id: str = Field(..., description="Source item identifier")
    target_id: str = Field(..., description="Target item identifier")
    mapping_type: MappingType = Field(..., description="Type of mapping")
    effectiveness_score: float = Field(..., ge=0.0, le=1.0, description="Effectiveness score")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence score")
    rationale: str = Field(..., description="Mapping rationale")


# Response schemas
class MappingSuggestionResponse(BaseModel):
    """Response schema for mapping suggestions."""
    technique_id: str = Field(..., description="MITRE technique identifier")
    technique_name: str = Field(..., description="MITRE technique name")
    suggestions: List[Dict[str, Any]] = Field(..., description="List of mapping suggestions")
    metadata: Dict[str, Any] = Field(..., description="Suggestion metadata")


class MappingValidationResponse(BaseModel):
    """Response schema for mapping validation."""
    validation_result: str = Field(..., description="Validation result")
    is_valid: bool = Field(..., description="Whether mapping is valid")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Validation confidence")
    effectiveness_score: float = Field(..., ge=0.0, le=1.0, description="Effectiveness score")
    validation_details: Dict[str, Any] = Field(..., description="Detailed validation results")
    recommendations: List[str] = Field(..., description="Improvement recommendations")


class MappingResponse(BaseModel):
    """Response schema for mapping."""
    id: int = Field(..., description="Mapping ID")
    source_framework: str = Field(..., description="Source framework")
    target_framework: str = Field(..., description="Target framework")
    source_id: str = Field(..., description="Source item identifier")
    target_id: str = Field(..., description="Target item identifier")
    mapping_type: str = Field(..., description="Type of mapping")
    effectiveness_score: float = Field(..., description="Effectiveness score")
    confidence_score: float = Field(..., description="Confidence score")
    rationale: str = Field(..., description="Mapping rationale")
    evidence: Optional[List[str]] = Field(None, description="Supporting evidence")
    validation_status: str = Field(..., description="Validation status")
    created_at: datetime = Field(..., description="Creation timestamp")
    updated_at: Optional[datetime] = Field(None, description="Last update timestamp")
    created_by: str = Field(..., description="Creator username")
    updated_by: Optional[str] = Field(None, description="Last updater username")
    validation_history: List[Dict[str, Any]] = Field(..., description="Validation history")


class MappingListResponse(BaseModel):
    """Response schema for mapping list."""
    mappings: List[MappingResponse] = Field(..., description="List of mappings")
    total: int = Field(..., description="Total number of mappings")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Items per page")
    total_pages: int = Field(..., description="Total number of pages")


# Request schemas
class MappingCreateRequest(BaseModel):
    """Request schema for creating mapping."""
    source_framework: FrameworkType = Field(..., description="Source framework")
    target_framework: FrameworkType = Field(..., description="Target framework")
    source_id: str = Field(..., description="Source item identifier")
    target_id: str = Field(..., description="Target item identifier")
    mapping_type: MappingType = Field(..., description="Type of mapping")
    effectiveness_score: float = Field(..., ge=0.0, le=1.0, description="Effectiveness score")
    confidence_score: float = Field(..., ge=0.0, le=1.0, description="Confidence score")
    rationale: str = Field(..., description="Mapping rationale")
    evidence: Optional[List[str]] = Field(None, description="Supporting evidence")
    validation_status: ValidationStatus = Field(ValidationStatus.PENDING, description="Initial validation status")
    
    @validator('source_id', 'target_id')
    def validate_ids(cls, v):
        if not v or not v.strip():
            raise ValueError("IDs cannot be empty")
        return v.strip()


class MappingUpdateRequest(BaseModel):
    """Request schema for updating mapping."""
    effectiveness_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Effectiveness score")
    confidence_score: Optional[float] = Field(None, ge=0.0, le=1.0, description="Confidence score")
    rationale: Optional[str] = Field(None, description="Mapping rationale")
    evidence: Optional[List[str]] = Field(None, description="Supporting evidence")
    validation_status: Optional[ValidationStatus] = Field(None, description="Validation status")


class MappingSearchFilters(BaseModel):
    """Filters for mapping search."""
    source_framework: Optional[FrameworkType] = Field(None, description="Source framework filter")
    target_framework: Optional[FrameworkType] = Field(None, description="Target framework filter")
    mapping_types: Optional[List[MappingType]] = Field(None, description="Mapping type filters")
    min_effectiveness: Optional[float] = Field(None, ge=0.0, le=1.0, description="Minimum effectiveness")
    min_confidence: Optional[float] = Field(None, ge=0.0, le=1.0, description="Minimum confidence")
    validation_status: Optional[List[ValidationStatus]] = Field(None, description="Validation status filters")


class MappingSearchRequest(BaseModel):
    """Request schema for mapping search."""
    query: Optional[str] = Field(None, description="Search query")
    filters: Optional[MappingSearchFilters] = Field(None, description="Search filters")
    sort_by: str = Field("effectiveness_score", description="Sort field")
    sort_order: str = Field("desc", regex="^(asc|desc)$", description="Sort order")
    page: int = Field(1, ge=1, description="Page number")
    page_size: int = Field(20, ge=1, le=100, description="Items per page")


class MappingSearchResult(BaseModel):
    """Individual mapping search result."""
    id: int = Field(..., description="Mapping ID")
    source_id: str = Field(..., description="Source identifier")
    target_id: str = Field(..., description="Target identifier")
    mapping_type: str = Field(..., description="Mapping type")
    effectiveness_score: float = Field(..., description="Effectiveness score")
    confidence_score: float = Field(..., description="Confidence score")
    relevance_score: float = Field(..., ge=0.0, le=1.0, description="Search relevance score")


class MappingSearchResponse(BaseModel):
    """Response schema for mapping search."""
    results: List[MappingSearchResult] = Field(..., description="Search results")
    total: int = Field(..., description="Total number of results")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Items per page")
    search_metadata: Dict[str, Any] = Field(..., description="Search metadata")


# Bulk operations schemas
class BulkSuggestionOptions(BaseModel):
    """Options for bulk mapping suggestions."""
    min_confidence: Optional[float] = Field(None, ge=0.0, le=1.0, description="Minimum confidence threshold")
    include_effectiveness: bool = Field(True, description="Include effectiveness scores")
    max_suggestions_per_technique: Optional[int] = Field(None, ge=1, description="Max suggestions per technique")


class BulkMappingSuggestionsRequest(BaseModel):
    """Request schema for bulk mapping suggestions."""
    technique_ids: List[str] = Field(..., description="List of technique IDs")
    target_framework: FrameworkType = Field(..., description="Target framework")
    suggestion_options: BulkSuggestionOptions = Field(..., description="Suggestion options")
    
    @validator('technique_ids')
    def validate_technique_ids(cls, v):
        if not v:
            raise ValueError("At least one technique ID required")
        return v


class BulkMappingSuggestionsResponse(BaseModel):
    """Response schema for bulk mapping suggestions."""
    results: List[Dict[str, Any]] = Field(..., description="Bulk suggestion results")
    summary: Dict[str, Any] = Field(..., description="Summary statistics")


# Analysis schemas
class EffectivenessAnalysisResponse(BaseModel):
    """Response schema for effectiveness analysis."""
    analysis_metadata: Dict[str, Any] = Field(..., description="Analysis metadata")
    effectiveness_summary: Dict[str, Any] = Field(..., description="Effectiveness summary")
    tactic_analysis: Dict[str, Any] = Field(..., description="Tactic-based analysis")
    recommendations: List[str] = Field(..., description="Improvement recommendations")


class CrossFrameworkAnalysisResponse(BaseModel):
    """Response schema for cross-framework analysis."""
    coverage_summary: Dict[str, Any] = Field(..., description="Coverage summary")
    framework_breakdown: Dict[str, Any] = Field(..., description="Framework breakdown")
    gap_analysis: Dict[str, Any] = Field(..., description="Gap analysis")
    recommendations: List[str] = Field(..., description="Recommendations")


class MappingAnalysisRequest(BaseModel):
    """Request schema for mapping analysis."""
    framework_pair: str = Field(..., description="Framework pair (e.g., 'mitre_attack-isf')")
    analysis_type: str = Field("detailed", description="Type of analysis")
    group_by: Optional[str] = Field(None, description="Grouping criteria")
    filters: Optional[Dict[str, Any]] = Field(None, description="Analysis filters")


class MappingAnalysisResponse(BaseModel):
    """Response schema for mapping analysis."""
    analysis_type: str = Field(..., description="Type of analysis performed")
    results: Dict[str, Any] = Field(..., description="Analysis results")
    metadata: Dict[str, Any] = Field(..., description="Analysis metadata")


# Export schemas
class MappingExportRequest(BaseModel):
    """Request schema for mapping export."""
    format: str = Field(..., regex="^(json|csv|stix)$", description="Export format")
    framework_pair: Optional[str] = Field(None, description="Framework pair to export")
    filters: Optional[Dict[str, Any]] = Field(None, description="Export filters")
    include_metadata: bool = Field(True, description="Include metadata")
    include_analysis: bool = Field(False, description="Include analysis")
    stix_options: Optional[Dict[str, Any]] = Field(None, description="STIX-specific options")


class MappingExportResponse(BaseModel):
    """Response schema for mapping export."""
    success: bool = Field(..., description="Whether export was successful")
    format: str = Field(..., description="Export format")
    data: str = Field(..., description="Exported data")
    file_size: int = Field(..., description="Data size in bytes")
    record_count: int = Field(..., description="Number of exported records")
    export_metadata: Dict[str, Any] = Field(..., description="Export metadata")


# Technique/Control specific schemas
class TechniqueMappingsResponse(BaseModel):
    """Response schema for technique mappings."""
    technique_id: str = Field(..., description="Technique identifier")
    technique_name: str = Field(..., description="Technique name")
    mappings: List[MappingResponse] = Field(..., description="Existing mappings")
    suggestions: List[Dict[str, Any]] = Field(..., description="Mapping suggestions")
    coverage_analysis: Dict[str, Any] = Field(..., description="Coverage analysis")


class ControlMappingsResponse(BaseModel):
    """Response schema for control mappings."""
    control_id: str = Field(..., description="Control identifier")
    control_name: str = Field(..., description="Control name")
    framework: str = Field(..., description="Framework name")
    mappings: List[MappingResponse] = Field(..., description="Existing mappings")
    effectiveness_summary: Dict[str, Any] = Field(..., description="Effectiveness summary")


# Quality assessment schemas
class QualityAssessmentResponse(BaseModel):
    """Response schema for mapping quality assessment."""
    quality_summary: Dict[str, Any] = Field(..., description="Quality summary")
    quality_distribution: Dict[str, Any] = Field(..., description="Quality distribution")
    quality_issues: List[Dict[str, Any]] = Field(..., description="Identified quality issues")
    improvement_recommendations: List[str] = Field(..., description="Improvement recommendations")


# Coverage analysis schemas
class CoverageAnalysisResponse(BaseModel):
    """Response schema for coverage analysis."""
    coverage_summary: Dict[str, Any] = Field(..., description="Coverage summary")
    framework_breakdown: Dict[str, Any] = Field(..., description="Framework breakdown")
    gap_analysis: Dict[str, Any] = Field(..., description="Gap analysis")
    recommendations: List[str] = Field(..., description="Coverage recommendations")


# Statistics schemas
class MappingStatistics(BaseModel):
    """Mapping statistics."""
    total_mappings: int = Field(..., description="Total number of mappings")
    mappings_by_framework: Dict[str, int] = Field(..., description="Mappings by framework pair")
    mappings_by_type: Dict[str, int] = Field(..., description="Mappings by type")
    average_effectiveness: float = Field(..., description="Average effectiveness score")
    average_confidence: float = Field(..., description="Average confidence score")
    validation_status_distribution: Dict[str, int] = Field(..., description="Validation status distribution")
    recent_activity: Dict[str, Any] = Field(..., description="Recent mapping activity")
