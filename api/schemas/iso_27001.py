"""
ISO/IEC 27001 Schemas.

This module contains Pydantic schemas for ISO/IEC 27001 Information Security
Management System API requests and responses.
"""

from pydantic import BaseModel, Field, validator
from typing import List, Optional, Dict, Any
from datetime import datetime
from enum import Enum


class OrganizationSizeEnum(str, Enum):
    """Organization size options."""
    SMALL = "small"
    MEDIUM = "medium"
    LARGE = "large"
    ENTERPRISE = "enterprise"


class ImplementationDifficultyEnum(str, Enum):
    """Implementation difficulty levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"


class ControlTypeEnum(str, Enum):
    """Control type options."""
    PREVENTIVE = "preventive"
    DETECTIVE = "detective"
    CORRECTIVE = "corrective"


class AssessmentTypeEnum(str, Enum):
    """Assessment type options."""
    INTERNAL = "internal"
    EXTERNAL = "external"
    CERTIFICATION = "certification"


class CertificationReadinessEnum(str, Enum):
    """Certification readiness levels."""
    READY = "ready"
    NEEDS_WORK = "needs_work"
    NOT_READY = "not_ready"


class ImplementationStatusEnum(str, Enum):
    """Implementation status options."""
    PLANNING = "planning"
    IMPLEMENTING = "implementing"
    OPERATIONAL = "operational"


class CertificationStatusEnum(str, Enum):
    """Certification status options."""
    NOT_CERTIFIED = "not_certified"
    IN_PROGRESS = "in_progress"
    CERTIFIED = "certified"


# Base schemas
class ISO27001VersionBase(BaseModel):
    """Base schema for ISO/IEC 27001 versions."""
    version: str = Field(..., description="Standard version")
    release_date: str = Field(..., description="Release date")
    description: Optional[str] = Field(None, description="Version description")
    standard_url: Optional[str] = Field(None, description="Official standard URL")
    documentation_url: Optional[str] = Field(None, description="Documentation URL")
    certification_body: Optional[str] = Field(None, description="Certification body")


class ISO27001VersionCreate(ISO27001VersionBase):
    """Schema for creating ISO/IEC 27001 versions."""
    is_current: bool = Field(False, description="Is this the current version")


class ISO27001VersionUpdate(BaseModel):
    """Schema for updating ISO/IEC 27001 versions."""
    description: Optional[str] = None
    standard_url: Optional[str] = None
    documentation_url: Optional[str] = None
    certification_body: Optional[str] = None
    is_current: Optional[bool] = None


class ISO27001VersionResponse(ISO27001VersionBase):
    """Schema for ISO/IEC 27001 version responses."""
    id: int
    is_current: bool
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ISO27001VersionListResponse(BaseModel):
    """Schema for ISO/IEC 27001 version list responses."""
    versions: List[ISO27001VersionResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


# Domain schemas
class ISO27001DomainBase(BaseModel):
    """Base schema for ISO/IEC 27001 domains."""
    domain_id: str = Field(..., description="Domain identifier")
    name: str = Field(..., description="Domain name")
    description: Optional[str] = Field(None, description="Domain description")
    order_index: int = Field(..., description="Display order")
    implementation_guidance: Optional[str] = Field(None, description="Implementation guidance")
    best_practices: Optional[List[str]] = Field(None, description="Best practices")
    common_challenges: Optional[List[str]] = Field(None, description="Common challenges")


class ISO27001DomainCreate(ISO27001DomainBase):
    """Schema for creating ISO/IEC 27001 domains."""
    version_id: int = Field(..., description="Framework version ID")


class ISO27001DomainResponse(ISO27001DomainBase):
    """Schema for ISO/IEC 27001 domain responses."""
    id: int
    version_id: int
    control_count: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ISO27001DomainListResponse(BaseModel):
    """Schema for ISO/IEC 27001 domain list responses."""
    domains: List[ISO27001DomainResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


# Control schemas
class ISO27001ControlBase(BaseModel):
    """Base schema for ISO/IEC 27001 controls."""
    control_id: str = Field(..., description="Control identifier")
    name: str = Field(..., description="Control name")
    description: Optional[str] = Field(None, description="Control description")
    order_index: int = Field(..., description="Display order")
    control_type: Optional[ControlTypeEnum] = Field(None, description="Control type")
    objective: Optional[str] = Field(None, description="Control objective")
    implementation_guidance: Optional[str] = Field(None, description="Implementation guidance")
    other_information: Optional[str] = Field(None, description="Additional information")
    requirements: Optional[List[str]] = Field(None, description="Specific requirements")
    evidence_examples: Optional[List[str]] = Field(None, description="Evidence examples")
    documentation_requirements: Optional[List[str]] = Field(None, description="Documentation requirements")
    assessment_criteria: Optional[Dict[str, Any]] = Field(None, description="Assessment criteria")
    maturity_levels: Optional[Dict[str, Any]] = Field(None, description="Maturity levels")
    compliance_indicators: Optional[List[str]] = Field(None, description="Compliance indicators")
    risk_categories: Optional[List[str]] = Field(None, description="Risk categories")
    impact_areas: Optional[List[str]] = Field(None, description="Impact areas")
    threat_mitigation: Optional[List[str]] = Field(None, description="Threats mitigated")
    related_controls: Optional[List[str]] = Field(None, description="Related controls")
    external_references: Optional[Dict[str, Any]] = Field(None, description="External references")
    regulatory_mappings: Optional[Dict[str, Any]] = Field(None, description="Regulatory mappings")


class ISO27001ControlCreate(ISO27001ControlBase):
    """Schema for creating ISO/IEC 27001 controls."""
    domain_id: int = Field(..., description="Domain ID")
    version_id: int = Field(..., description="Framework version ID")


class ISO27001ControlUpdate(BaseModel):
    """Schema for updating ISO/IEC 27001 controls."""
    name: Optional[str] = None
    description: Optional[str] = None
    control_type: Optional[ControlTypeEnum] = None
    objective: Optional[str] = None
    implementation_guidance: Optional[str] = None
    other_information: Optional[str] = None
    requirements: Optional[List[str]] = None
    evidence_examples: Optional[List[str]] = None
    documentation_requirements: Optional[List[str]] = None
    assessment_criteria: Optional[Dict[str, Any]] = None
    maturity_levels: Optional[Dict[str, Any]] = None
    compliance_indicators: Optional[List[str]] = None
    risk_categories: Optional[List[str]] = None
    impact_areas: Optional[List[str]] = None
    threat_mitigation: Optional[List[str]] = None
    related_controls: Optional[List[str]] = None
    external_references: Optional[Dict[str, Any]] = None
    regulatory_mappings: Optional[Dict[str, Any]] = None


class ISO27001ControlResponse(ISO27001ControlBase):
    """Schema for ISO/IEC 27001 control responses."""
    id: int
    domain_id: int
    version_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ISO27001ControlListResponse(BaseModel):
    """Schema for ISO/IEC 27001 control list responses."""
    controls: List[ISO27001ControlResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


# Implementation Example schemas
class ISO27001ImplementationExampleBase(BaseModel):
    """Base schema for ISO/IEC 27001 implementation examples."""
    title: str = Field(..., description="Example title")
    description: str = Field(..., description="Example description")
    implementation_approach: Optional[str] = Field(None, description="Implementation approach")
    organization_size: Optional[OrganizationSizeEnum] = Field(None, description="Organization size")
    industry_sector: Optional[str] = Field(None, description="Industry sector")
    technology_context: Optional[str] = Field(None, description="Technology context")
    geographic_region: Optional[str] = Field(None, description="Geographic region")
    implementation_steps: Optional[List[str]] = Field(None, description="Implementation steps")
    tools_and_technologies: Optional[List[str]] = Field(None, description="Tools and technologies")
    roles_and_responsibilities: Optional[Dict[str, str]] = Field(None, description="Roles and responsibilities")
    policies_and_procedures: Optional[List[str]] = Field(None, description="Required policies/procedures")
    implementation_difficulty: Optional[ImplementationDifficultyEnum] = Field(None, description="Implementation difficulty")
    estimated_effort: Optional[str] = Field(None, description="Estimated effort")
    budget_considerations: Optional[str] = Field(None, description="Budget considerations")
    prerequisites: Optional[List[str]] = Field(None, description="Prerequisites")
    expected_outcomes: Optional[List[str]] = Field(None, description="Expected outcomes")
    success_metrics: Optional[List[str]] = Field(None, description="Success metrics")
    business_benefits: Optional[List[str]] = Field(None, description="Business benefits")
    common_pitfalls: Optional[List[str]] = Field(None, description="Common pitfalls")
    lessons_learned: Optional[List[str]] = Field(None, description="Lessons learned")
    mitigation_strategies: Optional[List[str]] = Field(None, description="Mitigation strategies")


class ISO27001ImplementationExampleCreate(ISO27001ImplementationExampleBase):
    """Schema for creating ISO/IEC 27001 implementation examples."""
    control_id: int = Field(..., description="Control ID")


class ISO27001ImplementationExampleResponse(ISO27001ImplementationExampleBase):
    """Schema for ISO/IEC 27001 implementation example responses."""
    id: int
    control_id: int
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


# Assessment schemas
class ISO27001AssessmentBase(BaseModel):
    """Base schema for ISO/IEC 27001 assessments."""
    name: str = Field(..., description="Assessment name")
    description: Optional[str] = Field(None, description="Assessment description")
    assessment_date: datetime = Field(default_factory=datetime.utcnow, description="Assessment date")
    assessor: Optional[str] = Field(None, description="Assessor name")
    assessment_type: Optional[AssessmentTypeEnum] = Field(None, description="Assessment type")
    assessment_scope: Optional[str] = Field(None, description="Assessment scope")
    control_scores: Optional[Dict[str, float]] = Field(None, description="Control scores")
    compliance_status: Optional[Dict[str, str]] = Field(None, description="Compliance status")
    implementation_status: Optional[Dict[str, str]] = Field(None, description="Implementation status")
    evidence_quality: Optional[Dict[str, str]] = Field(None, description="Evidence quality")
    overall_score: Optional[float] = Field(None, ge=0.0, le=100.0, description="Overall score")
    compliance_percentage: Optional[float] = Field(None, ge=0.0, le=100.0, description="Compliance percentage")
    certification_readiness: Optional[CertificationReadinessEnum] = Field(None, description="Certification readiness")
    findings: Optional[List[Dict[str, Any]]] = Field(None, description="Assessment findings")
    non_conformities: Optional[List[Dict[str, Any]]] = Field(None, description="Non-conformities")
    recommendations: Optional[List[str]] = Field(None, description="Recommendations")
    action_items: Optional[List[Dict[str, Any]]] = Field(None, description="Action items")
    next_assessment_date: Optional[datetime] = Field(None, description="Next assessment date")
    follow_up_required: bool = Field(False, description="Follow-up required")
    certification_target_date: Optional[datetime] = Field(None, description="Certification target date")


class ISO27001AssessmentCreate(ISO27001AssessmentBase):
    """Schema for creating ISO/IEC 27001 assessments."""
    version_id: int = Field(..., description="Framework version ID")


class ISO27001AssessmentUpdate(BaseModel):
    """Schema for updating ISO/IEC 27001 assessments."""
    name: Optional[str] = None
    description: Optional[str] = None
    assessor: Optional[str] = None
    assessment_type: Optional[AssessmentTypeEnum] = None
    assessment_scope: Optional[str] = None
    control_scores: Optional[Dict[str, float]] = None
    compliance_status: Optional[Dict[str, str]] = None
    implementation_status: Optional[Dict[str, str]] = None
    evidence_quality: Optional[Dict[str, str]] = None
    overall_score: Optional[float] = Field(None, ge=0.0, le=100.0)
    compliance_percentage: Optional[float] = Field(None, ge=0.0, le=100.0)
    certification_readiness: Optional[CertificationReadinessEnum] = None
    findings: Optional[List[Dict[str, Any]]] = None
    non_conformities: Optional[List[Dict[str, Any]]] = None
    recommendations: Optional[List[str]] = None
    action_items: Optional[List[Dict[str, Any]]] = None
    next_assessment_date: Optional[datetime] = None
    follow_up_required: Optional[bool] = None
    certification_target_date: Optional[datetime] = None


class ISO27001AssessmentResponse(ISO27001AssessmentBase):
    """Schema for ISO/IEC 27001 assessment responses."""
    id: int
    version_id: int
    created_by: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class ISO27001AssessmentListResponse(BaseModel):
    """Schema for ISO/IEC 27001 assessment list responses."""
    assessments: List[ISO27001AssessmentResponse]
    total: int
    page: int
    page_size: int
    total_pages: int


# Search and filter schemas
class ISO27001SearchRequest(BaseModel):
    """Schema for ISO/IEC 27001 search requests."""
    query: Optional[str] = Field(None, description="Search query")
    domain_id: Optional[str] = Field(None, description="Filter by domain")
    control_type: Optional[ControlTypeEnum] = Field(None, description="Filter by control type")
    version: Optional[str] = Field(None, description="Filter by version")
    include_examples: bool = Field(False, description="Include implementation examples")
    include_references: bool = Field(False, description="Include external references")


class ISO27001SearchResponse(BaseModel):
    """Schema for ISO/IEC 27001 search responses."""
    results: List[ISO27001ControlResponse]
    total: int
    page: int
    page_size: int
    total_pages: int
    search_metadata: Dict[str, Any]


# Export schemas
class ISO27001ExportRequest(BaseModel):
    """Schema for ISO/IEC 27001 export requests."""
    format: str = Field("json", description="Export format")
    version: Optional[str] = Field(None, description="Framework version")
    domains: Optional[List[str]] = Field(None, description="Domains to include")
    controls: Optional[List[str]] = Field(None, description="Controls to include")
    include_examples: bool = Field(False, description="Include implementation examples")
    include_assessments: bool = Field(False, description="Include assessments")
    include_isms: bool = Field(False, description="Include ISMS information")


class ISO27001ExportResponse(BaseModel):
    """Schema for ISO/IEC 27001 export responses."""
    success: bool
    format: str
    data: str
    file_size: int
    record_count: int
    export_metadata: Dict[str, Any]
    processing_time: float
