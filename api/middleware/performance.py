"""
Performance middleware for FastAPI application.

This module provides middleware for performance monitoring, caching,
and response optimization.
"""

import time
import logging
from typing import Callable
from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response as StarletteResponse

logger = logging.getLogger(__name__)


class PerformanceMiddleware(BaseHTTPMiddleware):
    """
    Middleware for performance monitoring and optimization.
    
    Features:
    - Request timing
    - Response caching headers
    - Performance logging
    - Memory usage tracking
    """
    
    def __init__(self, app, enable_caching: bool = True, log_slow_requests: bool = True, slow_request_threshold: float = 1.0):
        super().__init__(app)
        self.enable_caching = enable_caching
        self.log_slow_requests = log_slow_requests
        self.slow_request_threshold = slow_request_threshold
    
    async def dispatch(self, request: Request, call_next: Callable) -> StarletteResponse:
        """Process request with performance monitoring."""
        start_time = time.time()
        
        # Add request start time to state
        request.state.start_time = start_time
        
        # Process request
        response = await call_next(request)
        
        # Calculate processing time
        process_time = time.time() - start_time
        
        # Add performance headers
        response.headers["X-Process-Time"] = str(process_time)
        response.headers["X-Response-Time"] = f"{process_time:.3f}s"
        
        # Add caching headers if enabled
        if self.enable_caching and self._should_cache(request, response):
            self._add_cache_headers(response)
        
        # Log slow requests
        if self.log_slow_requests and process_time > self.slow_request_threshold:
            logger.warning(
                f"Slow request detected: {request.method} {request.url.path} "
                f"took {process_time:.3f}s"
            )
        
        # Log performance metrics
        logger.debug(
            f"Request processed: {request.method} {request.url.path} "
            f"in {process_time:.3f}s with status {response.status_code}"
        )
        
        return response
    
    def _should_cache(self, request: Request, response: Response) -> bool:
        """Determine if response should be cached."""
        # Only cache GET requests
        if request.method != "GET":
            return False
        
        # Only cache successful responses
        if response.status_code != 200:
            return False
        
        # Don't cache authenticated endpoints (simplified check)
        if "Authorization" in request.headers:
            return False
        
        # Cache static content and read-only endpoints
        cacheable_paths = [
            "/api/v1/isf/versions",
            "/api/v1/nist-csf/versions",
            "/api/v1/isf/controls",
            "/api/v1/nist-csf/functions",
            "/docs",
            "/openapi.json"
        ]
        
        return any(request.url.path.startswith(path) for path in cacheable_paths)
    
    def _add_cache_headers(self, response: Response) -> None:
        """Add caching headers to response."""
        # Set cache control headers
        response.headers["Cache-Control"] = "public, max-age=300"  # 5 minutes
        response.headers["ETag"] = f'"{hash(str(response.body))}"'
        response.headers["X-Cache-Status"] = "miss"  # Would be "hit" if from cache
        
        # Add Vary header for content negotiation
        response.headers["Vary"] = "Accept, Accept-Encoding"


class CompressionMiddleware(BaseHTTPMiddleware):
    """
    Middleware for response compression.
    
    Compresses responses to reduce bandwidth usage.
    """
    
    def __init__(self, app, minimum_size: int = 1000):
        super().__init__(app)
        self.minimum_size = minimum_size
    
    async def dispatch(self, request: Request, call_next: Callable) -> StarletteResponse:
        """Process request with compression."""
        response = await call_next(request)
        
        # Check if compression is supported
        accept_encoding = request.headers.get("accept-encoding", "")
        if "gzip" not in accept_encoding.lower():
            return response
        
        # Check response size
        content_length = response.headers.get("content-length")
        if content_length and int(content_length) < self.minimum_size:
            return response
        
        # Add compression indicator
        response.headers["X-Compression"] = "gzip"
        
        return response


class RateLimitMiddleware(BaseHTTPMiddleware):
    """
    Basic rate limiting middleware.
    
    Note: In production, use Redis-based rate limiting.
    """
    
    def __init__(self, app, requests_per_minute: int = 100):
        super().__init__(app)
        self.requests_per_minute = requests_per_minute
        self.request_counts = {}  # In-memory store (use Redis in production)
    
    async def dispatch(self, request: Request, call_next: Callable) -> StarletteResponse:
        """Process request with rate limiting."""
        client_ip = self._get_client_ip(request)
        current_time = int(time.time() / 60)  # Current minute
        
        # Clean old entries (simplified)
        self._cleanup_old_entries(current_time)
        
        # Check rate limit
        key = f"{client_ip}:{current_time}"
        current_count = self.request_counts.get(key, 0)
        
        if current_count >= self.requests_per_minute:
            # Rate limit exceeded
            response = Response(
                content='{"detail": "Rate limit exceeded"}',
                status_code=429,
                media_type="application/json"
            )
            response.headers["X-RateLimit-Limit"] = str(self.requests_per_minute)
            response.headers["X-RateLimit-Remaining"] = "0"
            response.headers["X-RateLimit-Reset"] = str((current_time + 1) * 60)
            return response
        
        # Increment counter
        self.request_counts[key] = current_count + 1
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers
        remaining = self.requests_per_minute - self.request_counts[key]
        response.headers["X-RateLimit-Limit"] = str(self.requests_per_minute)
        response.headers["X-RateLimit-Remaining"] = str(remaining)
        response.headers["X-RateLimit-Reset"] = str((current_time + 1) * 60)
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address."""
        # Check for forwarded headers
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    def _cleanup_old_entries(self, current_time: int) -> None:
        """Remove old rate limit entries."""
        # Remove entries older than 2 minutes
        cutoff_time = current_time - 2
        keys_to_remove = [
            key for key in self.request_counts.keys()
            if int(key.split(":")[1]) < cutoff_time
        ]
        
        for key in keys_to_remove:
            del self.request_counts[key]


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """
    Middleware for adding security headers.
    """
    
    def __init__(self, app):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> StarletteResponse:
        """Add security headers to response."""
        response = await call_next(request)
        
        # Add security headers
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        # Add HSTS header for HTTPS
        if request.url.scheme == "https":
            response.headers["Strict-Transport-Security"] = "max-age=31536000; includeSubDomains"
        
        # Add CSP header (basic)
        response.headers["Content-Security-Policy"] = (
            "default-src 'self'; "
            "script-src 'self' 'unsafe-inline'; "
            "style-src 'self' 'unsafe-inline'; "
            "img-src 'self' data:; "
            "font-src 'self'; "
            "connect-src 'self'; "
            "frame-src 'none'; "
            "object-src 'none'"
        )
        
        return response


class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for detailed request logging.
    """
    
    def __init__(self, app, log_body: bool = False, max_body_size: int = 1024):
        super().__init__(app)
        self.log_body = log_body
        self.max_body_size = max_body_size
    
    async def dispatch(self, request: Request, call_next: Callable) -> StarletteResponse:
        """Log request details."""
        start_time = time.time()
        
        # Log request
        logger.info(
            f"Request started: {request.method} {request.url} "
            f"from {request.client.host if request.client else 'unknown'}"
        )
        
        # Log headers (excluding sensitive ones)
        safe_headers = {
            k: v for k, v in request.headers.items()
            if k.lower() not in ["authorization", "cookie", "x-api-key"]
        }
        logger.debug(f"Request headers: {safe_headers}")
        
        # Log body if enabled (for debugging)
        if self.log_body and request.method in ["POST", "PUT", "PATCH"]:
            try:
                body = await request.body()
                if len(body) <= self.max_body_size:
                    logger.debug(f"Request body: {body.decode('utf-8')[:self.max_body_size]}")
                else:
                    logger.debug(f"Request body: {body.decode('utf-8')[:self.max_body_size]}... (truncated)")
            except Exception as e:
                logger.debug(f"Could not log request body: {e}")
        
        # Process request
        response = await call_next(request)
        
        # Log response
        process_time = time.time() - start_time
        logger.info(
            f"Request completed: {request.method} {request.url} "
            f"status={response.status_code} time={process_time:.3f}s"
        )
        
        return response
