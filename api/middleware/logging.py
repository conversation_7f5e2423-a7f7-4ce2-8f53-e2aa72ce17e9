"""
Logging middleware for FastAPI application.

This module provides middleware for comprehensive request/response logging
and audit trail functionality.
"""

import time
import logging
import json
from typing import Callable
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response

logger = logging.getLogger(__name__)


class LoggingMiddleware(BaseHTTPMiddleware):
    """
    Comprehensive logging middleware for API requests and responses.
    
    Features:
    - Request/response logging
    - Performance metrics
    - Error tracking
    - Audit trail
    """
    
    def __init__(
        self,
        app,
        log_requests: bool = True,
        log_responses: bool = True,
        log_body: bool = False,
        max_body_size: int = 1024,
        exclude_paths: list = None
    ):
        super().__init__(app)
        self.log_requests = log_requests
        self.log_responses = log_responses
        self.log_body = log_body
        self.max_body_size = max_body_size
        self.exclude_paths = exclude_paths or ["/health", "/metrics", "/docs", "/openapi.json"]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Process request with comprehensive logging."""
        start_time = time.time()
        
        # Skip logging for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)
        
        # Generate request ID for tracking
        request_id = f"{int(start_time * 1000000)}"
        request.state.request_id = request_id
        
        # Log request
        if self.log_requests:
            await self._log_request(request, request_id)
        
        # Process request
        try:
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log response
            if self.log_responses:
                await self._log_response(request, response, request_id, process_time)
            
            # Add request ID to response headers
            response.headers["X-Request-ID"] = request_id
            
            return response
            
        except Exception as e:
            # Log error
            process_time = time.time() - start_time
            await self._log_error(request, e, request_id, process_time)
            raise
    
    async def _log_request(self, request: Request, request_id: str) -> None:
        """Log incoming request details."""
        try:
            # Basic request info
            log_data = {
                "event": "request_started",
                "request_id": request_id,
                "method": request.method,
                "url": str(request.url),
                "path": request.url.path,
                "query_params": dict(request.query_params),
                "client_ip": self._get_client_ip(request),
                "user_agent": request.headers.get("user-agent", ""),
                "timestamp": time.time()
            }
            
            # Add headers (excluding sensitive ones)
            safe_headers = self._filter_sensitive_headers(dict(request.headers))
            log_data["headers"] = safe_headers
            
            # Add body if enabled
            if self.log_body and request.method in ["POST", "PUT", "PATCH"]:
                body = await self._get_request_body(request)
                if body:
                    log_data["body"] = body
            
            logger.info(f"Request: {json.dumps(log_data)}")
            
        except Exception as e:
            logger.error(f"Error logging request: {e}")
    
    async def _log_response(self, request: Request, response: Response, request_id: str, process_time: float) -> None:
        """Log response details."""
        try:
            log_data = {
                "event": "request_completed",
                "request_id": request_id,
                "method": request.method,
                "path": request.url.path,
                "status_code": response.status_code,
                "process_time": round(process_time, 3),
                "response_size": len(response.body) if hasattr(response, 'body') else 0,
                "timestamp": time.time()
            }
            
            # Add response headers (excluding sensitive ones)
            safe_headers = self._filter_sensitive_headers(dict(response.headers))
            log_data["response_headers"] = safe_headers
            
            # Determine log level based on status code
            if response.status_code >= 500:
                logger.error(f"Response: {json.dumps(log_data)}")
            elif response.status_code >= 400:
                logger.warning(f"Response: {json.dumps(log_data)}")
            else:
                logger.info(f"Response: {json.dumps(log_data)}")
                
        except Exception as e:
            logger.error(f"Error logging response: {e}")
    
    async def _log_error(self, request: Request, error: Exception, request_id: str, process_time: float) -> None:
        """Log error details."""
        try:
            log_data = {
                "event": "request_error",
                "request_id": request_id,
                "method": request.method,
                "path": request.url.path,
                "error_type": type(error).__name__,
                "error_message": str(error),
                "process_time": round(process_time, 3),
                "timestamp": time.time()
            }
            
            logger.error(f"Error: {json.dumps(log_data)}")
            
        except Exception as e:
            logger.error(f"Error logging error: {e}")
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address from request."""
        # Check for forwarded headers
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"
    
    def _filter_sensitive_headers(self, headers: dict) -> dict:
        """Filter out sensitive headers from logging."""
        sensitive_headers = {
            "authorization",
            "cookie",
            "x-api-key",
            "x-auth-token",
            "x-csrf-token"
        }
        
        return {
            k: v if k.lower() not in sensitive_headers else "[REDACTED]"
            for k, v in headers.items()
        }
    
    async def _get_request_body(self, request: Request) -> str:
        """Get request body for logging."""
        try:
            body = await request.body()
            if not body:
                return ""
            
            # Limit body size
            body_str = body.decode('utf-8')
            if len(body_str) > self.max_body_size:
                body_str = body_str[:self.max_body_size] + "... [TRUNCATED]"
            
            # Try to parse as JSON for better formatting
            try:
                parsed = json.loads(body_str)
                # Filter sensitive fields
                filtered = self._filter_sensitive_body(parsed)
                return json.dumps(filtered)
            except json.JSONDecodeError:
                return body_str
                
        except Exception as e:
            logger.debug(f"Could not read request body: {e}")
            return ""
    
    def _filter_sensitive_body(self, data) -> dict:
        """Filter sensitive data from request body."""
        if not isinstance(data, dict):
            return data
        
        sensitive_fields = {
            "password",
            "token",
            "secret",
            "key",
            "credential",
            "auth"
        }
        
        filtered = {}
        for k, v in data.items():
            if any(sensitive in k.lower() for sensitive in sensitive_fields):
                filtered[k] = "[REDACTED]"
            elif isinstance(v, dict):
                filtered[k] = self._filter_sensitive_body(v)
            else:
                filtered[k] = v
        
        return filtered


class AuditMiddleware(BaseHTTPMiddleware):
    """
    Audit trail middleware for tracking user actions.
    """
    
    def __init__(self, app, audit_actions: list = None):
        super().__init__(app)
        self.audit_actions = audit_actions or ["POST", "PUT", "PATCH", "DELETE"]
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Track user actions for audit trail."""
        # Only audit specific actions
        if request.method not in self.audit_actions:
            return await call_next(request)
        
        start_time = time.time()
        
        # Extract user info (if available)
        user_id = getattr(request.state, 'user_id', None)
        username = getattr(request.state, 'username', None)
        
        # Process request
        response = await call_next(request)
        
        # Log audit event
        audit_data = {
            "event": "user_action",
            "user_id": user_id,
            "username": username,
            "action": request.method,
            "resource": request.url.path,
            "status_code": response.status_code,
            "timestamp": time.time(),
            "ip_address": self._get_client_ip(request),
            "user_agent": request.headers.get("user-agent", "")
        }
        
        # Log successful actions as info, failures as warning
        if response.status_code < 400:
            logger.info(f"Audit: {json.dumps(audit_data)}")
        else:
            logger.warning(f"Audit: {json.dumps(audit_data)}")
        
        return response
    
    def _get_client_ip(self, request: Request) -> str:
        """Get client IP address."""
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        return request.client.host if request.client else "unknown"


class MetricsMiddleware(BaseHTTPMiddleware):
    """
    Middleware for collecting API metrics.
    """
    
    def __init__(self, app):
        super().__init__(app)
        self.request_count = 0
        self.error_count = 0
        self.total_response_time = 0.0
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        """Collect metrics for monitoring."""
        start_time = time.time()
        
        # Increment request counter
        self.request_count += 1
        
        try:
            response = await call_next(request)
            
            # Calculate response time
            response_time = time.time() - start_time
            self.total_response_time += response_time
            
            # Count errors
            if response.status_code >= 400:
                self.error_count += 1
            
            # Add metrics headers
            response.headers["X-Request-Count"] = str(self.request_count)
            response.headers["X-Error-Rate"] = str(self.error_count / self.request_count)
            response.headers["X-Avg-Response-Time"] = str(self.total_response_time / self.request_count)
            
            return response
            
        except Exception as e:
            self.error_count += 1
            raise
