"""
CIS Critical Security Controls v8 Models.

This module contains SQLAlchemy models for the CIS Critical Security Controls v8
framework, including controls, implementation groups, safeguards, and asset types.
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Float, JSON
from sqlalchemy.orm import relationship
from datetime import datetime

from api.database import Base


class CISControlsVersion(Base):
    """
    CIS Controls version model.
    
    Manages different versions of the CIS Critical Security Controls
    with release information and current version tracking.
    """
    __tablename__ = "cis_controls_versions"
    
    id = Column(Integer, primary_key=True, index=True)
    version = Column(String(20), nullable=False, unique=True, index=True)  # e.g., "v8", "v7.1"
    release_date = Column(String(50), nullable=False)  # Release date
    description = Column(Text, nullable=True)
    is_current = Column(Boolean, default=False)  # Is this the current version
    
    # Standard metadata
    standard_url = Column(String(500), nullable=True)  # Official standard URL
    documentation_url = Column(String(500), nullable=True)  # Documentation URL
    organization = Column(String(255), nullable=True)  # Center for Internet Security
    
    # Version-specific metadata
    total_controls = Column(Integer, default=0)  # Total number of controls
    total_safeguards = Column(Integer, default=0)  # Total number of safeguards
    implementation_groups = Column(JSON, nullable=True)  # IG1, IG2, IG3 descriptions
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    controls = relationship("CISControl", back_populates="version", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class CISControl(Base):
    """
    CIS Control model.
    
    Represents individual CIS Controls with their safeguards,
    implementation groups, and asset types.
    """
    __tablename__ = "cis_controls"
    
    id = Column(Integer, primary_key=True, index=True)
    control_id = Column(String(10), nullable=False, index=True)  # e.g., "1", "2", "3"
    title = Column(String(500), nullable=False)  # Control title
    description = Column(Text, nullable=True)  # Control description
    
    # Framework version
    version_id = Column(Integer, ForeignKey("cis_controls_versions.id"), nullable=False)
    
    # Control metadata
    order_index = Column(Integer, nullable=False)  # Display order
    control_type = Column(String(50), nullable=True)  # basic, foundational, organizational
    
    # Asset types this control applies to
    asset_types = Column(JSON, nullable=True)  # List of asset types
    security_functions = Column(JSON, nullable=True)  # Identify, Protect, Detect, Respond, Recover
    
    # Implementation guidance
    why_is_this_important = Column(Text, nullable=True)  # Rationale for the control
    implementation_guidance = Column(Text, nullable=True)  # How to implement
    
    # Safeguards count
    total_safeguards = Column(Integer, default=0)  # Number of safeguards
    ig1_safeguards = Column(Integer, default=0)  # IG1 safeguards count
    ig2_safeguards = Column(Integer, default=0)  # IG2 safeguards count
    ig3_safeguards = Column(Integer, default=0)  # IG3 safeguards count
    
    # References and mappings
    related_controls = Column(JSON, nullable=True)  # Related CIS control IDs
    external_references = Column(JSON, nullable=True)  # References to other frameworks
    regulatory_mappings = Column(JSON, nullable=True)  # Regulatory requirement mappings
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    version = relationship("CISControlsVersion", back_populates="controls")
    safeguards = relationship("CISSafeguard", back_populates="control", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class CISSafeguard(Base):
    """
    CIS Safeguard model.
    
    Represents individual safeguards within each CIS Control
    with implementation group assignments and asset types.
    """
    __tablename__ = "cis_safeguards"
    
    id = Column(Integer, primary_key=True, index=True)
    safeguard_id = Column(String(20), nullable=False, index=True)  # e.g., "1.1", "1.2", "2.1"
    title = Column(String(500), nullable=False)  # Safeguard title
    description = Column(Text, nullable=True)  # Safeguard description
    
    # Framework relationships
    control_id = Column(Integer, ForeignKey("cis_controls.id"), nullable=False)
    version_id = Column(Integer, ForeignKey("cis_controls_versions.id"), nullable=False)
    
    # Safeguard metadata
    order_index = Column(Integer, nullable=False)  # Display order within control
    safeguard_type = Column(String(50), nullable=True)  # basic, foundational, organizational
    
    # Implementation Groups
    implementation_group_1 = Column(Boolean, default=False)  # IG1 requirement
    implementation_group_2 = Column(Boolean, default=False)  # IG2 requirement
    implementation_group_3 = Column(Boolean, default=False)  # IG3 requirement
    
    # Asset types this safeguard applies to
    asset_types = Column(JSON, nullable=True)  # List of applicable asset types
    security_functions = Column(JSON, nullable=True)  # Security functions addressed
    
    # Implementation details
    implementation_guidance = Column(Text, nullable=True)  # How to implement
    measurement_specification = Column(Text, nullable=True)  # How to measure
    
    # Procedures and automation
    procedure_review = Column(Text, nullable=True)  # Procedure review guidance
    automation_support = Column(Text, nullable=True)  # Automation possibilities
    
    # Dependencies and prerequisites
    dependencies = Column(JSON, nullable=True)  # Dependencies on other safeguards
    prerequisites = Column(JSON, nullable=True)  # Prerequisites for implementation
    
    # Assessment and validation
    assessment_criteria = Column(JSON, nullable=True)  # How to assess compliance
    validation_methods = Column(JSON, nullable=True)  # Methods to validate implementation
    evidence_examples = Column(JSON, nullable=True)  # Examples of evidence
    
    # References and mappings
    related_safeguards = Column(JSON, nullable=True)  # Related safeguard IDs
    external_references = Column(JSON, nullable=True)  # References to other standards
    regulatory_mappings = Column(JSON, nullable=True)  # Regulatory requirement mappings
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    control = relationship("CISControl", back_populates="safeguards")
    version = relationship("CISControlsVersion")
    implementation_examples = relationship("CISImplementationExample", back_populates="safeguard", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class CISImplementationExample(Base):
    """
    CIS Implementation example model.
    
    Provides specific examples of how to implement safeguards
    across different organizational contexts and implementation groups.
    """
    __tablename__ = "cis_implementation_examples"
    
    id = Column(Integer, primary_key=True, index=True)
    safeguard_id = Column(Integer, ForeignKey("cis_safeguards.id"), nullable=False)
    
    # Example details
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    implementation_approach = Column(Text, nullable=True)
    
    # Context and applicability
    implementation_group = Column(String(10), nullable=True)  # IG1, IG2, IG3
    organization_size = Column(String(50), nullable=True)  # small, medium, large, enterprise
    industry_sector = Column(String(100), nullable=True)  # healthcare, finance, manufacturing, etc.
    technology_context = Column(String(100), nullable=True)  # cloud, on-premise, hybrid, etc.
    asset_types = Column(JSON, nullable=True)  # Applicable asset types
    
    # Implementation details
    implementation_steps = Column(JSON, nullable=True)  # Step-by-step implementation
    tools_and_technologies = Column(JSON, nullable=True)  # Tools/technologies used
    automation_opportunities = Column(JSON, nullable=True)  # Automation possibilities
    
    # Resources and effort
    implementation_difficulty = Column(String(20), nullable=True)  # low, medium, high
    estimated_effort = Column(String(50), nullable=True)  # Time/resource estimate
    budget_considerations = Column(Text, nullable=True)  # Budget and cost considerations
    skill_requirements = Column(JSON, nullable=True)  # Required skills and expertise
    
    # Outcomes and measurement
    expected_outcomes = Column(JSON, nullable=True)  # Expected outcomes
    success_metrics = Column(JSON, nullable=True)  # How to measure success
    measurement_procedures = Column(JSON, nullable=True)  # Measurement procedures
    
    # Challenges and lessons learned
    common_pitfalls = Column(JSON, nullable=True)  # Common implementation pitfalls
    lessons_learned = Column(JSON, nullable=True)  # Lessons learned from implementation
    best_practices = Column(JSON, nullable=True)  # Best practices and recommendations
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    safeguard = relationship("CISSafeguard", back_populates="implementation_examples")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class CISAssessment(Base):
    """
    CIS Controls assessment model.
    
    Tracks assessment results and implementation status for
    CIS Controls within an organizational context.
    """
    __tablename__ = "cis_assessments"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Assessment metadata
    assessment_date = Column(DateTime, default=datetime.utcnow)
    assessor = Column(String(255), nullable=True)
    assessment_type = Column(String(50), nullable=True)  # self, third_party, certification
    assessment_scope = Column(Text, nullable=True)
    
    # Framework version and implementation group
    version_id = Column(Integer, ForeignKey("cis_controls_versions.id"), nullable=False)
    target_implementation_group = Column(String(10), nullable=True)  # IG1, IG2, IG3
    
    # Assessment results
    control_scores = Column(JSON, nullable=True)  # Scores for each control
    safeguard_scores = Column(JSON, nullable=True)  # Scores for each safeguard
    implementation_status = Column(JSON, nullable=True)  # Implementation status
    
    # Overall assessment
    overall_score = Column(Float, nullable=True)  # Overall assessment score
    implementation_percentage = Column(Float, nullable=True)  # Overall implementation percentage
    ig1_compliance = Column(Float, nullable=True)  # IG1 compliance percentage
    ig2_compliance = Column(Float, nullable=True)  # IG2 compliance percentage
    ig3_compliance = Column(Float, nullable=True)  # IG3 compliance percentage
    
    # Findings and recommendations
    findings = Column(JSON, nullable=True)  # Assessment findings
    gaps = Column(JSON, nullable=True)  # Implementation gaps identified
    recommendations = Column(JSON, nullable=True)  # Recommendations for improvement
    priority_actions = Column(JSON, nullable=True)  # Priority action items
    
    # Timeline and follow-up
    next_assessment_date = Column(DateTime, nullable=True)
    follow_up_required = Column(Boolean, default=False)
    improvement_target_date = Column(DateTime, nullable=True)
    
    # Metadata
    created_by = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    version = relationship("CISControlsVersion")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class CISImplementationGroup(Base):
    """
    CIS Implementation Group model.
    
    Defines the three implementation groups (IG1, IG2, IG3) with
    their characteristics and target organizations.
    """
    __tablename__ = "cis_implementation_groups"
    
    id = Column(Integer, primary_key=True, index=True)
    group_id = Column(String(10), nullable=False, index=True)  # IG1, IG2, IG3
    name = Column(String(255), nullable=False)  # Implementation Group name
    description = Column(Text, nullable=True)  # Group description
    
    # Framework version
    version_id = Column(Integer, ForeignKey("cis_controls_versions.id"), nullable=False)
    
    # Group characteristics
    target_organizations = Column(JSON, nullable=True)  # Target organization types
    security_maturity_level = Column(String(50), nullable=True)  # basic, intermediate, advanced
    resource_requirements = Column(Text, nullable=True)  # Resource requirements
    
    # Safeguards and controls
    total_safeguards = Column(Integer, default=0)  # Total safeguards in this IG
    controls_covered = Column(JSON, nullable=True)  # Controls covered by this IG
    
    # Implementation guidance
    implementation_approach = Column(Text, nullable=True)  # How to approach implementation
    prioritization_guidance = Column(Text, nullable=True)  # How to prioritize safeguards
    success_criteria = Column(JSON, nullable=True)  # Success criteria for this IG
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    version = relationship("CISControlsVersion")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )
