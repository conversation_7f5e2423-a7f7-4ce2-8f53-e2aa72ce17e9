"""
ISO/IEC 27001 Models.

This module contains SQLAlchemy models for the ISO/IEC 27001 Information Security
Management System standard, including controls, domains, and implementation guidance.
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, Foreign<PERSON>ey, Float, JSON
from sqlalchemy.orm import relationship
from datetime import datetime

from api.database import Base


class ISO27001Version(Base):
    """
    ISO/IEC 27001 version model.
    
    Manages different versions of the ISO/IEC 27001 standard
    with release information and current version tracking.
    """
    __tablename__ = "iso_27001_versions"
    
    id = Column(Integer, primary_key=True, index=True)
    version = Column(String(20), nullable=False, unique=True, index=True)  # e.g., "2022", "2013"
    release_date = Column(String(50), nullable=False)  # Release date
    description = Column(Text, nullable=True)
    is_current = Column(Boolean, default=False)  # Is this the current version
    
    # Standard metadata
    standard_url = Column(String(500), nullable=True)  # Official standard URL
    documentation_url = Column(String(500), nullable=True)  # Documentation URL
    certification_body = Column(String(255), nullable=True)  # ISO/IEC
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    domains = relationship("ISO27001Domain", back_populates="version", cascade="all, delete-orphan")
    controls = relationship("ISO27001Control", back_populates="version", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class ISO27001Domain(Base):
    """
    ISO/IEC 27001 domain model.
    
    Represents the control domains (categories) in ISO/IEC 27001
    that group related security controls.
    """
    __tablename__ = "iso_27001_domains"
    
    id = Column(Integer, primary_key=True, index=True)
    domain_id = Column(String(10), nullable=False, index=True)  # e.g., "A.5", "A.6", "A.7"
    name = Column(String(255), nullable=False)  # e.g., "Information security policies"
    description = Column(Text, nullable=True)
    
    # Framework version
    version_id = Column(Integer, ForeignKey("iso_27001_versions.id"), nullable=False)
    
    # Domain metadata
    order_index = Column(Integer, nullable=False)  # Display order
    control_count = Column(Integer, default=0)  # Number of controls in this domain
    
    # Implementation guidance
    implementation_guidance = Column(Text, nullable=True)
    best_practices = Column(JSON, nullable=True)  # List of best practices
    common_challenges = Column(JSON, nullable=True)  # Common implementation challenges
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    version = relationship("ISO27001Version", back_populates="domains")
    controls = relationship("ISO27001Control", back_populates="domain", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class ISO27001Control(Base):
    """
    ISO/IEC 27001 control model.
    
    Represents individual security controls within each domain
    with implementation requirements and guidance.
    """
    __tablename__ = "iso_27001_controls"
    
    id = Column(Integer, primary_key=True, index=True)
    control_id = Column(String(20), nullable=False, index=True)  # e.g., "A.5.1", "A.6.1"
    name = Column(String(500), nullable=False)  # Control title
    description = Column(Text, nullable=True)  # Control description
    
    # Framework relationships
    domain_id = Column(Integer, ForeignKey("iso_27001_domains.id"), nullable=False)
    version_id = Column(Integer, ForeignKey("iso_27001_versions.id"), nullable=False)
    
    # Control metadata
    order_index = Column(Integer, nullable=False)  # Display order within domain
    control_type = Column(String(50), nullable=True)  # preventive, detective, corrective
    
    # Implementation details
    objective = Column(Text, nullable=True)  # Control objective
    implementation_guidance = Column(Text, nullable=True)  # How to implement
    other_information = Column(Text, nullable=True)  # Additional information
    
    # Requirements and evidence
    requirements = Column(JSON, nullable=True)  # List of specific requirements
    evidence_examples = Column(JSON, nullable=True)  # Examples of evidence
    documentation_requirements = Column(JSON, nullable=True)  # Required documentation
    
    # Assessment and compliance
    assessment_criteria = Column(JSON, nullable=True)  # How to assess compliance
    maturity_levels = Column(JSON, nullable=True)  # Maturity level descriptions
    compliance_indicators = Column(JSON, nullable=True)  # Indicators of compliance
    
    # Risk and impact
    risk_categories = Column(JSON, nullable=True)  # Risk categories addressed
    impact_areas = Column(JSON, nullable=True)  # Business impact areas
    threat_mitigation = Column(JSON, nullable=True)  # Threats mitigated
    
    # References and mappings
    related_controls = Column(JSON, nullable=True)  # Related control IDs
    external_references = Column(JSON, nullable=True)  # References to other standards
    regulatory_mappings = Column(JSON, nullable=True)  # Regulatory requirement mappings
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    domain = relationship("ISO27001Domain", back_populates="controls")
    version = relationship("ISO27001Version", back_populates="controls")
    implementation_examples = relationship("ISO27001ImplementationExample", back_populates="control", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class ISO27001ImplementationExample(Base):
    """
    ISO/IEC 27001 implementation example model.
    
    Provides specific examples of how to implement controls
    across different organizational contexts and industries.
    """
    __tablename__ = "iso_27001_implementation_examples"
    
    id = Column(Integer, primary_key=True, index=True)
    control_id = Column(Integer, ForeignKey("iso_27001_controls.id"), nullable=False)
    
    # Example details
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    implementation_approach = Column(Text, nullable=True)
    
    # Context and applicability
    organization_size = Column(String(50), nullable=True)  # small, medium, large, enterprise
    industry_sector = Column(String(100), nullable=True)  # healthcare, finance, manufacturing, etc.
    technology_context = Column(String(100), nullable=True)  # cloud, on-premise, hybrid, etc.
    geographic_region = Column(String(100), nullable=True)  # EU, US, APAC, etc.
    
    # Implementation details
    implementation_steps = Column(JSON, nullable=True)  # Step-by-step implementation
    tools_and_technologies = Column(JSON, nullable=True)  # Tools/technologies used
    roles_and_responsibilities = Column(JSON, nullable=True)  # Who is responsible
    policies_and_procedures = Column(JSON, nullable=True)  # Required policies/procedures
    
    # Resources and effort
    implementation_difficulty = Column(String(20), nullable=True)  # low, medium, high
    estimated_effort = Column(String(50), nullable=True)  # Time/resource estimate
    budget_considerations = Column(Text, nullable=True)  # Budget and cost considerations
    prerequisites = Column(JSON, nullable=True)  # What's needed before implementation
    
    # Outcomes and benefits
    expected_outcomes = Column(JSON, nullable=True)  # Expected outcomes
    success_metrics = Column(JSON, nullable=True)  # How to measure success
    business_benefits = Column(JSON, nullable=True)  # Business benefits achieved
    
    # Challenges and lessons learned
    common_pitfalls = Column(JSON, nullable=True)  # Common implementation pitfalls
    lessons_learned = Column(JSON, nullable=True)  # Lessons learned from implementation
    mitigation_strategies = Column(JSON, nullable=True)  # Risk mitigation strategies
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    control = relationship("ISO27001Control", back_populates="implementation_examples")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class ISO27001Assessment(Base):
    """
    ISO/IEC 27001 assessment model.
    
    Tracks assessment results and compliance status for
    controls within an organizational context.
    """
    __tablename__ = "iso_27001_assessments"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Assessment metadata
    assessment_date = Column(DateTime, default=datetime.utcnow)
    assessor = Column(String(255), nullable=True)
    assessment_type = Column(String(50), nullable=True)  # internal, external, certification
    assessment_scope = Column(Text, nullable=True)
    
    # Framework version
    version_id = Column(Integer, ForeignKey("iso_27001_versions.id"), nullable=False)
    
    # Assessment results
    control_scores = Column(JSON, nullable=True)  # Scores for each control
    compliance_status = Column(JSON, nullable=True)  # Compliance status for each control
    implementation_status = Column(JSON, nullable=True)  # Implementation status
    evidence_quality = Column(JSON, nullable=True)  # Quality of evidence provided
    
    # Overall assessment
    overall_score = Column(Float, nullable=True)  # Overall assessment score
    compliance_percentage = Column(Float, nullable=True)  # Overall compliance percentage
    certification_readiness = Column(String(20), nullable=True)  # ready, needs_work, not_ready
    
    # Findings and recommendations
    findings = Column(JSON, nullable=True)  # Assessment findings
    non_conformities = Column(JSON, nullable=True)  # Non-conformities identified
    recommendations = Column(JSON, nullable=True)  # Recommendations for improvement
    action_items = Column(JSON, nullable=True)  # Specific action items
    
    # Timeline and follow-up
    next_assessment_date = Column(DateTime, nullable=True)
    follow_up_required = Column(Boolean, default=False)
    certification_target_date = Column(DateTime, nullable=True)
    
    # Metadata
    created_by = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    version = relationship("ISO27001Version")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class ISO27001ISMS(Base):
    """
    ISO/IEC 27001 ISMS (Information Security Management System) model.
    
    Represents an organization's ISMS implementation including
    scope, policies, and management system components.
    """
    __tablename__ = "iso_27001_isms"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # ISMS scope and context
    scope_description = Column(Text, nullable=True)  # ISMS scope
    organizational_context = Column(JSON, nullable=True)  # Organizational context
    interested_parties = Column(JSON, nullable=True)  # Interested parties
    
    # Framework version
    version_id = Column(Integer, ForeignKey("iso_27001_versions.id"), nullable=False)
    
    # ISMS components
    information_security_policy = Column(Text, nullable=True)  # Top-level policy
    risk_management_approach = Column(Text, nullable=True)  # Risk management approach
    treatment_plan = Column(JSON, nullable=True)  # Risk treatment plan
    
    # Management system
    roles_and_responsibilities = Column(JSON, nullable=True)  # Roles and responsibilities
    competence_requirements = Column(JSON, nullable=True)  # Competence requirements
    awareness_program = Column(JSON, nullable=True)  # Awareness program details
    communication_plan = Column(JSON, nullable=True)  # Communication plan
    
    # Documentation
    documented_information = Column(JSON, nullable=True)  # Required documented information
    procedures = Column(JSON, nullable=True)  # ISMS procedures
    work_instructions = Column(JSON, nullable=True)  # Work instructions
    
    # Performance and improvement
    monitoring_program = Column(JSON, nullable=True)  # Monitoring and measurement program
    internal_audit_program = Column(JSON, nullable=True)  # Internal audit program
    management_review_process = Column(JSON, nullable=True)  # Management review process
    improvement_actions = Column(JSON, nullable=True)  # Continual improvement actions
    
    # Status and certification
    implementation_status = Column(String(50), nullable=True)  # planning, implementing, operational
    certification_status = Column(String(50), nullable=True)  # not_certified, in_progress, certified
    certification_date = Column(DateTime, nullable=True)
    certification_body = Column(String(255), nullable=True)
    certificate_number = Column(String(100), nullable=True)
    certificate_expiry_date = Column(DateTime, nullable=True)
    
    # Metadata
    created_by = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    version = relationship("ISO27001Version")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )
