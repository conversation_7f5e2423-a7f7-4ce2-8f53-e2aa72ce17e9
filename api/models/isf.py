"""
ISF (Information Security Forum) Standard of Good Practice models.

This module implements SQLAlchemy models for the ISF Standard of Good Practice
framework, including versions, security areas, controls, and cross-framework mappings.

The models follow the established patterns in the codebase:
- Soft deletion support via VersionMixin
- Proper relationships with cascade deletion
- Comprehensive validation and constraints
- Search and filtering capabilities
"""

from datetime import datetime
from typing import List, Optional, Dict, Any
from decimal import Decimal

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    Text,
    Numeric,
    JSON,
    UniqueConstraint,
    CheckConstraint,
    Index
)
from sqlalchemy.orm import relationship, Session, Mapped, mapped_column
from sqlalchemy.sql import func

from api.database import Base
from api.models.base import VersionMixin


class ISFVersion(Base, VersionMixin):
    """
    ISF Standard of Good Practice version management.
    
    Tracks different versions of the ISF Standard with proper
    version control, deprecation support, and current version management.
    """
    
    __tablename__ = "isf_versions"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    version: Mapped[str] = mapped_column(String(50), nullable=False, unique=True)
    release_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    import_date: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=datetime.utcnow)
    is_current: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    url: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    deprecated: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    deprecation_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # Relationships
    security_areas: Mapped[List["ISFSecurityArea"]] = relationship(
        "ISFSecurityArea", 
        back_populates="version",
        cascade="all, delete-orphan",
        order_by="ISFSecurityArea.order_index"
    )
    controls: Mapped[List["ISFControl"]] = relationship(
        "ISFControl",
        back_populates="version", 
        cascade="all, delete-orphan",
        order_by="ISFControl.order_index"
    )
    
    __table_args__ = (
        Index('idx_isf_versions_current', 'is_current'),
        Index('idx_isf_versions_not_deleted', 'deleted_time'),
    )
    
    def __repr__(self) -> str:
        return f"<ISFVersion(version='{self.version}', is_current={self.is_current})>"
    
    @classmethod
    def get_current_version(cls, db: Session) -> Optional["ISFVersion"]:
        """Get the current ISF version."""
        return db.query(cls).filter(
            cls.is_current == True,
            cls.not_deleted()
        ).first()
    
    def set_as_current(self, db: Session) -> None:
        """Set this version as current and unset all others."""
        # Unset all other current versions
        db.query(ISFVersion).filter(
            ISFVersion.is_current == True,
            ISFVersion.id != self.id
        ).update({"is_current": False})
        
        # Set this version as current
        self.is_current = True
        db.commit()


class ISFSecurityArea(Base, VersionMixin):
    """
    ISF Security Areas.
    
    Represents the main security areas in the ISF Standard of Good Practice,
    such as Security Governance, Risk Management, etc.
    """
    
    __tablename__ = "isf_security_areas"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    area_id: Mapped[str] = mapped_column(String(10), nullable=False)
    name: Mapped[str] = mapped_column(String(255), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    version_id: Mapped[int] = mapped_column(Integer, ForeignKey("isf_versions.id", ondelete="CASCADE"), nullable=False)
    order_index: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    
    # Relationships
    version: Mapped["ISFVersion"] = relationship("ISFVersion", back_populates="security_areas")
    controls: Mapped[List["ISFControl"]] = relationship(
        "ISFControl",
        back_populates="security_area",
        cascade="all, delete-orphan",
        order_by="ISFControl.order_index"
    )
    
    __table_args__ = (
        UniqueConstraint('area_id', 'version_id', name='uq_isf_security_areas_area_version'),
        Index('idx_isf_security_areas_version', 'version_id'),
        Index('idx_isf_security_areas_not_deleted', 'deleted_time'),
    )
    
    def __repr__(self) -> str:
        return f"<ISFSecurityArea(area_id='{self.area_id}', name='{self.name}')>"


class ISFControl(Base, VersionMixin):
    """
    ISF Controls.
    
    Individual security controls within the ISF Standard of Good Practice,
    with detailed descriptions, objectives, guidance, and metadata.
    """
    
    __tablename__ = "isf_controls"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    control_id: Mapped[str] = mapped_column(String(20), nullable=False)
    name: Mapped[str] = mapped_column(String(500), nullable=False)
    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    objective: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    guidance: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    security_area_id: Mapped[int] = mapped_column(Integer, ForeignKey("isf_security_areas.id", ondelete="CASCADE"), nullable=False)
    version_id: Mapped[int] = mapped_column(Integer, ForeignKey("isf_versions.id", ondelete="CASCADE"), nullable=False)
    order_index: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    control_type: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)  # policy, technical, administrative
    maturity_level: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)  # basic, intermediate, advanced
    
    # Relationships
    security_area: Mapped["ISFSecurityArea"] = relationship("ISFSecurityArea", back_populates="controls")
    version: Mapped["ISFVersion"] = relationship("ISFVersion", back_populates="controls")
    
    __table_args__ = (
        UniqueConstraint('control_id', 'version_id', name='uq_isf_controls_control_version'),
        Index('idx_isf_controls_security_area', 'security_area_id'),
        Index('idx_isf_controls_version', 'version_id'),
        Index('idx_isf_controls_type', 'control_type'),
        Index('idx_isf_controls_not_deleted', 'deleted_time'),
        CheckConstraint(
            "control_type IN ('policy', 'technical', 'administrative') OR control_type IS NULL",
            name='ck_isf_controls_control_type'
        ),
        CheckConstraint(
            "maturity_level IN ('basic', 'intermediate', 'advanced') OR maturity_level IS NULL",
            name='ck_isf_controls_maturity_level'
        ),
    )
    
    def __repr__(self) -> str:
        return f"<ISFControl(control_id='{self.control_id}', name='{self.name[:50]}...')>"
    
    @classmethod
    def search_by_type(cls, db: Session, control_type: str) -> List["ISFControl"]:
        """Search ISF controls by control type."""
        return db.query(cls).filter(
            cls.control_type == control_type,
            cls.not_deleted()
        ).order_by(cls.order_index).all()
    
    @classmethod
    def search_by_maturity(cls, db: Session, maturity_level: str) -> List["ISFControl"]:
        """Search ISF controls by maturity level."""
        return db.query(cls).filter(
            cls.maturity_level == maturity_level,
            cls.not_deleted()
        ).order_by(cls.order_index).all()
    
    @classmethod
    def full_text_search(cls, db: Session, search_term: str) -> List["ISFControl"]:
        """Perform full-text search across ISF controls."""
        search_pattern = f"%{search_term}%"
        return db.query(cls).filter(
            (cls.name.ilike(search_pattern)) |
            (cls.description.ilike(search_pattern)) |
            (cls.objective.ilike(search_pattern)) |
            (cls.guidance.ilike(search_pattern)),
            cls.not_deleted()
        ).order_by(cls.order_index).all()
    
    @classmethod
    def get_by_security_area(cls, db: Session, security_area_id: int) -> List["ISFControl"]:
        """Get all controls for a specific security area."""
        return db.query(cls).filter(
            cls.security_area_id == security_area_id,
            cls.not_deleted()
        ).order_by(cls.order_index).all()
    
    @classmethod
    def get_by_version(cls, db: Session, version_id: int) -> List["ISFControl"]:
        """Get all controls for a specific ISF version."""
        return db.query(cls).filter(
            cls.version_id == version_id,
            cls.not_deleted()
        ).order_by(cls.security_area_id, cls.order_index).all()


class ISFFrameworkMapping(Base, VersionMixin):
    """
    Cross-framework mappings for ISF controls.
    
    This model supports mapping ISF controls to other frameworks
    like MITRE ATT&CK techniques with effectiveness scoring,
    confidence levels, and audit trails.
    """
    
    __tablename__ = "isf_framework_mappings"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    isf_control_id: Mapped[int] = mapped_column(Integer, ForeignKey("isf_controls.id", ondelete="CASCADE"), nullable=False)
    mitre_technique_id: Mapped[str] = mapped_column(String(50), nullable=False)
    mapping_type: Mapped[str] = mapped_column(String(50), nullable=False, default="mitigates")
    effectiveness_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(precision=3, scale=2), nullable=True)
    confidence_level: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    mapping_rationale: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_by: Mapped[str] = mapped_column(String(100), nullable=False)
    updated_by: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    update_reason: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    validated: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    validation_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # Relationships
    isf_control: Mapped["ISFControl"] = relationship("ISFControl")
    
    __table_args__ = (
        UniqueConstraint('isf_control_id', 'mitre_technique_id', 'mapping_type', name='uq_isf_framework_mapping'),
        Index('idx_isf_framework_mappings_control', 'isf_control_id'),
        Index('idx_isf_framework_mappings_technique', 'mitre_technique_id'),
        Index('idx_isf_framework_mappings_validated', 'validated'),
        Index('idx_isf_framework_mappings_not_deleted', 'deleted_time'),
        CheckConstraint(
            "effectiveness_score >= 0.0 AND effectiveness_score <= 1.0",
            name='ck_isf_framework_mappings_effectiveness_score'
        ),
        CheckConstraint(
            "confidence_level IN ('low', 'medium', 'high') OR confidence_level IS NULL",
            name='ck_isf_framework_mappings_confidence_level'
        ),
        CheckConstraint(
            "mapping_type IN ('mitigates', 'detects', 'prevents', 'responds', 'recovers')",
            name='ck_isf_framework_mappings_mapping_type'
        ),
    )
    
    def __repr__(self) -> str:
        return f"<ISFFrameworkMapping(isf_control_id={self.isf_control_id}, mitre_technique_id='{self.mitre_technique_id}', mapping_type='{self.mapping_type}')>"
    
    def validate_effectiveness_score(self) -> None:
        """Validate effectiveness score is between 0.0 and 1.0."""
        if self.effectiveness_score is not None:
            if self.effectiveness_score < 0.0 or self.effectiveness_score > 1.0:
                raise ValueError("Effectiveness score must be between 0.0 and 1.0")
    
    @classmethod
    def get_mappings_by_technique(cls, db: Session, mitre_technique_id: str) -> List["ISFFrameworkMapping"]:
        """Get all ISF mappings for a specific MITRE technique."""
        return db.query(cls).filter(
            cls.mitre_technique_id == mitre_technique_id,
            cls.not_deleted()
        ).all()
    
    @classmethod
    def get_mappings_by_control(cls, db: Session, isf_control_id: int) -> List["ISFFrameworkMapping"]:
        """Get all mappings for a specific ISF control."""
        return db.query(cls).filter(
            cls.isf_control_id == isf_control_id,
            cls.not_deleted()
        ).all()
    
    @classmethod
    def get_validated_mappings(cls, db: Session) -> List["ISFFrameworkMapping"]:
        """Get all validated framework mappings."""
        return db.query(cls).filter(
            cls.validated == True,
            cls.not_deleted()
        ).all()
    
    @classmethod
    def calculate_coverage_score(cls, db: Session, mitre_technique_id: str) -> Optional[float]:
        """Calculate overall coverage score for a MITRE technique across ISF controls."""
        mappings = cls.get_mappings_by_technique(db, mitre_technique_id)
        if not mappings:
            return None
        
        # Calculate weighted average effectiveness score
        total_score = 0.0
        total_weight = 0.0
        
        for mapping in mappings:
            if mapping.effectiveness_score is not None:
                weight = 1.0
                if mapping.confidence_level == "high":
                    weight = 1.0
                elif mapping.confidence_level == "medium":
                    weight = 0.7
                elif mapping.confidence_level == "low":
                    weight = 0.4
                
                total_score += float(mapping.effectiveness_score) * weight
                total_weight += weight
        
        return total_score / total_weight if total_weight > 0 else None
