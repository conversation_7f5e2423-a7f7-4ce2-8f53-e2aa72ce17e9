"""
NIST CSF 2.0 Models.

This module contains SQLAlchemy models for the NIST Cybersecurity Framework 2.0,
including functions, categories, subcategories, and implementation guidance.
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Float, JSON
from sqlalchemy.orm import relationship
from datetime import datetime

from api.database import Base


class NISTCSFVersion(Base):
    """
    NIST CSF version model.
    
    Manages different versions of the NIST Cybersecurity Framework
    with release information and current version tracking.
    """
    __tablename__ = "nist_csf_versions"
    
    id = Column(Integer, primary_key=True, index=True)
    version = Column(String(20), nullable=False, unique=True, index=True)  # e.g., "2.0", "1.1"
    release_date = Column(String(50), nullable=False)  # Release date
    description = Column(Text, nullable=True)
    is_current = Column(Boolean, default=False)  # Is this the current version
    
    # Framework metadata
    framework_url = Column(String(500), nullable=True)  # Official framework URL
    documentation_url = Column(String(500), nullable=True)  # Documentation URL
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    functions = relationship("NISTCSFFunction", back_populates="version", cascade="all, delete-orphan")
    categories = relationship("NISTCSFCategory", back_populates="version", cascade="all, delete-orphan")
    subcategories = relationship("NISTCSFSubcategory", back_populates="version", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class NISTCSFFunction(Base):
    """
    NIST CSF function model.
    
    Represents the six core functions of the NIST CSF 2.0:
    Govern, Identify, Protect, Detect, Respond, Recover
    """
    __tablename__ = "nist_csf_functions"
    
    id = Column(Integer, primary_key=True, index=True)
    function_id = Column(String(10), nullable=False, index=True)  # e.g., "GV", "ID", "PR", "DE", "RS", "RC"
    name = Column(String(255), nullable=False)  # e.g., "Govern", "Identify"
    description = Column(Text, nullable=True)
    
    # Framework version
    version_id = Column(Integer, ForeignKey("nist_csf_versions.id"), nullable=False)
    
    # Function metadata
    order_index = Column(Integer, nullable=False)  # Display order
    color_code = Column(String(7), nullable=True)  # Hex color for UI
    icon = Column(String(50), nullable=True)  # Icon identifier
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    version = relationship("NISTCSFVersion", back_populates="functions")
    categories = relationship("NISTCSFCategory", back_populates="function", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class NISTCSFCategory(Base):
    """
    NIST CSF category model.
    
    Represents categories within each function that group related
    cybersecurity outcomes.
    """
    __tablename__ = "nist_csf_categories"
    
    id = Column(Integer, primary_key=True, index=True)
    category_id = Column(String(20), nullable=False, index=True)  # e.g., "GV.GV", "ID.AM", "PR.AC"
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Framework relationships
    function_id = Column(Integer, ForeignKey("nist_csf_functions.id"), nullable=False)
    version_id = Column(Integer, ForeignKey("nist_csf_versions.id"), nullable=False)
    
    # Category metadata
    order_index = Column(Integer, nullable=False)  # Display order within function
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    function = relationship("NISTCSFFunction", back_populates="categories")
    version = relationship("NISTCSFVersion", back_populates="categories")
    subcategories = relationship("NISTCSFSubcategory", back_populates="category", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class NISTCSFSubcategory(Base):
    """
    NIST CSF subcategory model.
    
    Represents specific cybersecurity outcomes that support
    the achievement of each category.
    """
    __tablename__ = "nist_csf_subcategories"
    
    id = Column(Integer, primary_key=True, index=True)
    subcategory_id = Column(String(30), nullable=False, index=True)  # e.g., "GV.GV-01", "ID.AM-01"
    name = Column(String(500), nullable=False)  # Full subcategory text
    description = Column(Text, nullable=True)
    
    # Framework relationships
    category_id = Column(Integer, ForeignKey("nist_csf_categories.id"), nullable=False)
    function_id = Column(Integer, ForeignKey("nist_csf_functions.id"), nullable=False)
    version_id = Column(Integer, ForeignKey("nist_csf_versions.id"), nullable=False)
    
    # Subcategory metadata
    order_index = Column(Integer, nullable=False)  # Display order within category
    
    # Implementation guidance
    implementation_guidance = Column(Text, nullable=True)  # How to implement this subcategory
    example_implementations = Column(JSON, nullable=True)  # List of example implementations
    
    # Assessment and maturity
    maturity_levels = Column(JSON, nullable=True)  # Maturity level descriptions
    assessment_criteria = Column(JSON, nullable=True)  # Assessment criteria
    
    # References and mappings
    informative_references = Column(JSON, nullable=True)  # References to other frameworks
    related_subcategories = Column(JSON, nullable=True)  # Related subcategory IDs
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    category = relationship("NISTCSFCategory", back_populates="subcategories")
    function = relationship("NISTCSFFunction")
    version = relationship("NISTCSFVersion", back_populates="subcategories")
    implementation_examples = relationship("NISTCSFImplementationExample", back_populates="subcategory", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class NISTCSFImplementationExample(Base):
    """
    NIST CSF implementation example model.
    
    Provides specific examples of how to implement subcategories
    across different organizational contexts and technologies.
    """
    __tablename__ = "nist_csf_implementation_examples"
    
    id = Column(Integer, primary_key=True, index=True)
    subcategory_id = Column(Integer, ForeignKey("nist_csf_subcategories.id"), nullable=False)
    
    # Example details
    title = Column(String(255), nullable=False)
    description = Column(Text, nullable=False)
    implementation_approach = Column(Text, nullable=True)
    
    # Context and applicability
    organization_size = Column(String(50), nullable=True)  # small, medium, large, enterprise
    industry_sector = Column(String(100), nullable=True)  # healthcare, finance, manufacturing, etc.
    technology_context = Column(String(100), nullable=True)  # cloud, on-premise, hybrid, etc.
    
    # Implementation details
    tools_and_technologies = Column(JSON, nullable=True)  # List of tools/technologies
    roles_and_responsibilities = Column(JSON, nullable=True)  # Who is responsible
    success_metrics = Column(JSON, nullable=True)  # How to measure success
    
    # Difficulty and resources
    implementation_difficulty = Column(String(20), nullable=True)  # low, medium, high
    estimated_effort = Column(String(50), nullable=True)  # Time/resource estimate
    prerequisites = Column(JSON, nullable=True)  # What's needed before implementation
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    subcategory = relationship("NISTCSFSubcategory", back_populates="implementation_examples")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class NISTCSFProfile(Base):
    """
    NIST CSF profile model.
    
    Represents organizational profiles that align Framework subcategories
    with business requirements, risk tolerance, and resources.
    """
    __tablename__ = "nist_csf_profiles"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Profile metadata
    profile_type = Column(String(50), nullable=False)  # current, target, custom
    organization_type = Column(String(100), nullable=True)  # Industry or org type
    risk_tolerance = Column(String(20), nullable=True)  # low, medium, high
    
    # Framework version
    version_id = Column(Integer, ForeignKey("nist_csf_versions.id"), nullable=False)
    
    # Profile configuration
    selected_subcategories = Column(JSON, nullable=True)  # List of subcategory IDs
    priority_levels = Column(JSON, nullable=True)  # Priority for each subcategory
    implementation_status = Column(JSON, nullable=True)  # Status for each subcategory
    
    # Metadata
    created_by = Column(String, nullable=True)  # User who created the profile
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    version = relationship("NISTCSFVersion")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class NISTCSFAssessment(Base):
    """
    NIST CSF assessment model.
    
    Tracks assessment results and maturity levels for
    subcategories within an organizational context.
    """
    __tablename__ = "nist_csf_assessments"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Assessment metadata
    assessment_date = Column(DateTime, default=datetime.utcnow)
    assessor = Column(String(255), nullable=True)
    assessment_scope = Column(Text, nullable=True)
    
    # Framework version
    version_id = Column(Integer, ForeignKey("nist_csf_versions.id"), nullable=False)
    
    # Assessment results
    subcategory_scores = Column(JSON, nullable=True)  # Scores for each subcategory
    maturity_levels = Column(JSON, nullable=True)  # Maturity level for each subcategory
    implementation_status = Column(JSON, nullable=True)  # Implementation status
    
    # Overall assessment
    overall_score = Column(Float, nullable=True)  # Overall assessment score
    risk_level = Column(String(20), nullable=True)  # Overall risk level
    
    # Recommendations
    recommendations = Column(JSON, nullable=True)  # List of recommendations
    action_items = Column(JSON, nullable=True)  # Specific action items
    
    # Metadata
    created_by = Column(String, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    version = relationship("NISTCSFVersion")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )
