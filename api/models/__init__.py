"""Models package for the cybersecurity data platform.
Contains all database models used in the application.
"""

# Import base models first
from api.models.admin_notification import AdminNotification
from api.models.assessment import Assessment, TestExecution

# Import ATLAS models last since they don't have circular dependencies
from api.models.atlas import (
    AtlasMatrix,
    AtlasMatrixItem,
    AtlasTactic,
    AtlasTechnique,
    AtlasVersion,
)
from api.models.audit_log import AuditLog
from api.models.campaign import Campaign
from api.models.database.test_case import TestCase

# Import framework models
from api.models.framework_mappings import (
    BulkMappingOperation,
    FrameworkMappingAudit,
    ISFToNISTCSFMapping,
    MappingValidation,
    MitreToISFMapping,
    MitreToNISTCSFMapping,
)

# Import hierarchical data models
from api.models.environment import Environment

# Import ISF models
from api.models.isf import (
    ISFControl,
    ISFFrameworkMapping,
    ISFSecurityArea,
    ISFVersion,
)

# Import logging models
from api.models.error_log import ErrorLog

# Import MITRE models
from api.models.mitre import (
    MitreGroup,
    MitreMitigation,
    MitreSoftware,
    MitreTactic,
    MitreTechnique,
    MitreVersion,
    TechnologyDomain,
)

# Import NIST CSF models
from api.models.nist_csf import (
    NISTCSFCategory,
    NISTCSFFunction,
    NISTCSFImplementationExample,
    NISTCSFInformativeReference,
    NISTCSFSubcategory,
    NISTCSFVersion,
)
from api.models.session import UserSession
from api.models.testcase_chain import (
    ChainExecution,
    NodeExecution,
    TestcaseChain,
    TestcaseChainEdge,
    TestcaseChainNode,
    TestcaseCondition,
)
from api.models.user import User
from api.models.user_preferences import UserPreference

# Export all models that are currently in use
__all__ = [
    "AdminNotification",
    "Assessment",
    "AtlasMatrix",
    "AtlasMatrixItem",
    "AtlasTactic",
    "AtlasTechnique",
    "AtlasVersion",
    "AuditLog",
    "BulkMappingOperation",
    "Campaign",
    "ChainExecution",
    "Environment",
    "ErrorLog",
    "FrameworkMappingAudit",
    "ISFControl",
    "ISFFrameworkMapping",
    "ISFSecurityArea",
    "ISFToNISTCSFMapping",
    "ISFVersion",
    "MappingValidation",
    "MitreGroup",
    "MitreMitigation",
    "MitreSoftware",
    "MitreTactic",
    "MitreTechnique",
    "MitreToISFMapping",
    "MitreToNISTCSFMapping",
    "MitreVersion",
    "NISTCSFCategory",
    "NISTCSFFunction",
    "NISTCSFImplementationExample",
    "NISTCSFInformativeReference",
    "NISTCSFSubcategory",
    "NISTCSFVersion",
    "NodeExecution",
    "TechnologyDomain",
    "TestCase",
    "TestcaseChain",
    "TestcaseChainEdge",
    "TestcaseChainNode",
    "TestcaseCondition",
    "TestExecution",
    "User",
    "UserPreference",
    "UserSession",
]
