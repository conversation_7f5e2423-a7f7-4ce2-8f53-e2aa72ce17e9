"""Models package for the cybersecurity data platform.
Contains all database models used in the application.
"""

# Base models
from api.models.user import User, UserRole
from api.models.user_preferences import UserPreference

# Session models
from api.models.session import UserSession, DeviceInfo

# Logging models
from api.models.audit import AuditLog

# Hierarchical data models
from api.models.base import AssessmentDB
from api.models.assessment import Assessment, TestExecution
from api.models.campaign import CampaignDB
from api.models.organization import OrganizationDB
from api.models.database.test_case import TestCaseDB
from api.models.database.admin_interface import AdminAuditLog, AdminNotification

# Import ATLAS models
from api.models.atlas import (
    AtlasMatrix,
    AtlasMatrixItem,
    AtlasTactic,
    AtlasTechnique,
    AtlasVersion,
    AtlasRelationship,
)

# Import framework models
from api.models.framework_mappings import (
    BulkMappingOperation,
    FrameworkMappingAudit,
    ISFToNISTCSFMapping,
    MappingValidation,
    MitreToISFMapping,
    MitreToNISTCSFMapping,
)

# Import hierarchical data models
from api.models.environment import Environment

# Import ISF models
from api.models.isf import (
    ISFControl,
    ISFFrameworkMapping,
    ISFSecurityArea,
    ISFVersion,
)

# Import STIX models
from api.models.stix import StixDB, StixObject, StixBundle

# Import soft deletion models
from api.models.soft_deletion import (
    SoftDeletionAudit,
    SoftDeletionNotification,
    SoftDeletionPolicy,
    SoftDeletionSchedule,
)

# Import framework mapping models
from api.models.framework_mapping import (
    FrameworkMapping,
    MappingSet,
    MappingValidation,
    MappingTemplate,
    MappingAudit,
)

# Import NIST CSF 2.0 models
from api.models.nist_csf_2 import (
    NISTCSFVersion,
    NISTCSFFunction,
    NISTCSFCategory,
    NISTCSFSubcategory,
    NISTCSFImplementationExample,
    NISTCSFProfile,
    NISTCSFAssessment,
)
from api.models.error_log import ErrorLog

# MITRE models
# from mitreattack.stix20 import MitreAttackData  # Commented out - missing dependency
from api.models.mitre import (
    MitreGroup,
    MitreMitigation,
    MitreSoftware,
    MitreTactic,
    MitreTechnique,
    MitreVersion,
    TechniqueScore,
    TechnologyDomain,
)
from api.models.relationships import MitreRelationship

# Import NIST CSF models
from api.models.nist_csf import (
    NISTCSFCategory,
    NISTCSFFunction,
    NISTCSFImplementationExample,
    NISTCSFInformativeReference,
    NISTCSFSubcategory,
    NISTCSFVersion,
)

# Testcase chain models
from api.models.testcase_chain import (
    ChainExecution,
    NodeExecution,
    TestcaseChain,
    TestcaseChainEdge,
    TestcaseChainNode,
    TestcaseCondition,
)

# Export all models that are currently in use
__all__ = [
    # Base models
    "User",
    "UserRole",
    "UserPreference",

    # Session models
    "UserSession",
    "DeviceInfo",

    # Logging models
    "AuditLog",
    "ErrorLog",

    # Hierarchical data models
    "AssessmentDB",
    "Assessment",
    "TestExecution",
    "CampaignDB",
    "OrganizationDB",
    "TestCaseDB",

    # Admin models
    "AdminAuditLog",
    "AdminNotification",

    # MITRE models
    # "MitreAttackData",  # Commented out - missing dependency
    "MitreGroup",
    "MitreMitigation",
    "MitreSoftware",
    "MitreTactic",
    "MitreTechnique",
    "MitreVersion",
    "TechniqueScore",
    "TechnologyDomain",
    "MitreRelationship",

    # ATLAS models
    "AtlasMatrix",
    "AtlasMatrixItem",
    "AtlasTactic",
    "AtlasTechnique",
    "AtlasVersion",
    "AtlasRelationship",

    # STIX models
    "StixDB",
    "StixObject",
    "StixBundle",

    # Framework models
    "BulkMappingOperation",
    "FrameworkMappingAudit",
    "ISFToNISTCSFMapping",
    "MappingValidation",
    "MitreToISFMapping",
    "MitreToNISTCSFMapping",

    # Environment models
    "Environment",

    # ISF models
    "ISFControl",
    "ISFFrameworkMapping",
    "ISFSecurityArea",
    "ISFVersion",

    # NIST CSF models
    "NISTCSFCategory",
    "NISTCSFFunction",
    "NISTCSFImplementationExample",
    "NISTCSFInformativeReference",
    "NISTCSFSubcategory",
    "NISTCSFVersion",

    # Soft deletion models
    "SoftDeletionAudit",
    "SoftDeletionNotification",
    "SoftDeletionPolicy",
    "SoftDeletionSchedule",

    # Framework mapping models
    "FrameworkMapping",
    "MappingSet",
    "MappingValidation",
    "MappingTemplate",
    "MappingAudit",

    # NIST CSF 2.0 models
    "NISTCSFVersion",
    "NISTCSFFunction",
    "NISTCSFCategory",
    "NISTCSFSubcategory",
    "NISTCSFImplementationExample",
    "NISTCSFProfile",
    "NISTCSFAssessment",

    # Testcase chain models
    "ChainExecution",
    "NodeExecution",
    "TestcaseChain",
    "TestcaseChainEdge",
    "TestcaseChainNode",
    "TestcaseCondition",
]
