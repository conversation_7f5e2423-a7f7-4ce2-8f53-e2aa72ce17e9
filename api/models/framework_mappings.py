"""
Cross-Framework Mapping models.

This module implements SQLAlchemy models for mapping between different
cybersecurity frameworks including MITRE ATT&CK, ISF, and NIST CSF.

The models support:
- MITRE ATT&CK to ISF control mappings
- MITRE ATT&CK to NIST CSF subcategory mappings
- ISF to NIST CSF control mappings
- Effectiveness scoring and confidence levels
- Audit trails and validation workflows
- Bulk mapping operations
"""

from datetime import datetime, timedelta
from typing import List, Optional, Dict, Any, TYPE_CHECKING
from decimal import Decimal

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    ForeignKey,
    Integer,
    String,
    Text,
    Numeric,
    JSON,
    UniqueConstraint,
    CheckConstraint,
    Index
)
from sqlalchemy.orm import relationship, Session, Mapped, mapped_column
from sqlalchemy.sql import func

from api.database import Base
from api.models.base import VersionMixin

# Forward references to avoid circular imports
if TYPE_CHECKING:
    from api.models.isf import ISFControl


class MitreToISFMapping(Base, VersionMixin):
    """
    MITRE ATT&CK to ISF control mappings.
    
    Maps MITRE ATT&CK techniques to ISF controls with effectiveness
    scoring, confidence levels, and validation workflows.
    """
    
    __tablename__ = "mitre_to_isf_mappings"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    mitre_technique_id: Mapped[str] = mapped_column(String(50), nullable=False)
    isf_control_id: Mapped[int] = mapped_column(Integer, ForeignKey("isf_controls.id", ondelete="CASCADE"), nullable=False)
    mapping_type: Mapped[str] = mapped_column(String(50), nullable=False, default="mitigates")
    effectiveness_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(precision=3, scale=2), nullable=True)
    confidence_level: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    mapping_rationale: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    evidence_sources: Mapped[Optional[List[str]]] = mapped_column(JSON, nullable=True)
    created_by: Mapped[str] = mapped_column(String(100), nullable=False)
    updated_by: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    validated: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    validation_required: Mapped[bool] = mapped_column(Boolean, nullable=False, default=True)
    validation_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    validated_by: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    
    # Relationships
    isf_control: Mapped["ISFControl"] = relationship("ISFControl")
    
    __table_args__ = (
        UniqueConstraint('mitre_technique_id', 'isf_control_id', 'mapping_type', name='uq_mitre_isf_mapping'),
        Index('idx_mitre_isf_technique', 'mitre_technique_id'),
        Index('idx_mitre_isf_control', 'isf_control_id'),
        Index('idx_mitre_isf_validated', 'validated'),
        Index('idx_mitre_isf_not_deleted', 'deleted_time'),
        CheckConstraint(
            "effectiveness_score >= 0.0 AND effectiveness_score <= 1.0",
            name='ck_mitre_isf_effectiveness_score'
        ),
        CheckConstraint(
            "confidence_level IN ('low', 'medium', 'high') OR confidence_level IS NULL",
            name='ck_mitre_isf_confidence_level'
        ),
        CheckConstraint(
            "mapping_type IN ('mitigates', 'detects', 'prevents', 'responds', 'recovers')",
            name='ck_mitre_isf_mapping_type'
        ),
    )
    
    def __repr__(self) -> str:
        return f"<MitreToISFMapping(mitre_technique_id='{self.mitre_technique_id}', isf_control_id={self.isf_control_id}, mapping_type='{self.mapping_type}')>"
    
    def validate_effectiveness_score(self) -> None:
        """Validate effectiveness score is between 0.0 and 1.0."""
        if self.effectiveness_score is not None:
            if self.effectiveness_score < 0.0 or self.effectiveness_score > 1.0:
                raise ValueError("Effectiveness score must be between 0.0 and 1.0")
    
    @property
    def mitre_technique(self):
        """Get the MITRE technique (placeholder for relationship)."""
        # This would be implemented when MITRE models are available
        return None
    
    @classmethod
    def get_mappings_by_technique(cls, db: Session, mitre_technique_id: str) -> List["MitreToISFMapping"]:
        """Get all ISF mappings for a specific MITRE technique."""
        return db.query(cls).filter(
            cls.mitre_technique_id == mitre_technique_id,
            cls.not_deleted()
        ).all()
    
    @classmethod
    def get_mappings_by_control(cls, db: Session, isf_control_id: int) -> List["MitreToISFMapping"]:
        """Get all mappings for a specific ISF control."""
        return db.query(cls).filter(
            cls.isf_control_id == isf_control_id,
            cls.not_deleted()
        ).all()


class MitreToNISTCSFMapping(Base, VersionMixin):
    """
    MITRE ATT&CK to NIST CSF subcategory mappings.
    
    Maps MITRE ATT&CK techniques to NIST CSF subcategories with
    effectiveness scoring and implementation guidance.
    """
    
    __tablename__ = "mitre_to_nist_csf_mappings"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    mitre_technique_id: Mapped[str] = mapped_column(String(50), nullable=False)
    nist_csf_subcategory_id: Mapped[str] = mapped_column(String(30), nullable=False)
    mapping_type: Mapped[str] = mapped_column(String(50), nullable=False, default="addressed_by")
    effectiveness_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(precision=3, scale=2), nullable=True)
    confidence_level: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    mapping_rationale: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    implementation_guidance: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_by: Mapped[str] = mapped_column(String(100), nullable=False)
    updated_by: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    validated: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    validation_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    validated_by: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    
    __table_args__ = (
        UniqueConstraint('mitre_technique_id', 'nist_csf_subcategory_id', 'mapping_type', name='uq_mitre_nist_csf_mapping'),
        Index('idx_mitre_nist_csf_technique', 'mitre_technique_id'),
        Index('idx_mitre_nist_csf_subcategory', 'nist_csf_subcategory_id'),
        Index('idx_mitre_nist_csf_validated', 'validated'),
        Index('idx_mitre_nist_csf_not_deleted', 'deleted_time'),
        CheckConstraint(
            "effectiveness_score >= 0.0 AND effectiveness_score <= 1.0",
            name='ck_mitre_nist_csf_effectiveness_score'
        ),
        CheckConstraint(
            "confidence_level IN ('low', 'medium', 'high') OR confidence_level IS NULL",
            name='ck_mitre_nist_csf_confidence_level'
        ),
        CheckConstraint(
            "mapping_type IN ('addressed_by', 'detected_by', 'mitigated_by')",
            name='ck_mitre_nist_csf_mapping_type'
        ),
    )
    
    def __repr__(self) -> str:
        return f"<MitreToNISTCSFMapping(mitre_technique_id='{self.mitre_technique_id}', nist_csf_subcategory_id='{self.nist_csf_subcategory_id}')>"
    
    @classmethod
    def get_function_coverage(cls, db: Session, mitre_technique_id: str) -> List[Dict[str, Any]]:
        """Get NIST CSF function coverage for a MITRE technique."""
        # This would join with NIST CSF models to get function information
        mappings = db.query(cls).filter(
            cls.mitre_technique_id == mitre_technique_id,
            cls.not_deleted()
        ).all()
        
        # Placeholder implementation - would be enhanced with actual joins
        return [{"subcategory_id": m.nist_csf_subcategory_id, "mapping_type": m.mapping_type} for m in mappings]
    
    @classmethod
    def calculate_coverage_completeness(cls, db: Session, mitre_technique_id: str) -> float:
        """Calculate coverage completeness for a MITRE technique across NIST CSF functions."""
        mappings = cls.get_function_coverage(db, mitre_technique_id)
        
        # Simplified calculation - would be enhanced with actual function analysis
        if not mappings:
            return 0.0
        
        # Assume 6 NIST CSF functions, calculate coverage percentage
        unique_functions = len(set(m["subcategory_id"][:2] for m in mappings))  # Extract function prefix
        return min(unique_functions / 6.0, 1.0)


class ISFToNISTCSFMapping(Base, VersionMixin):
    """
    ISF to NIST CSF control mappings.
    
    Maps ISF controls to NIST CSF subcategories to show alignment
    between the two defensive frameworks.
    """
    
    __tablename__ = "isf_to_nist_csf_mappings"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    isf_control_id: Mapped[int] = mapped_column(Integer, ForeignKey("isf_controls.id", ondelete="CASCADE"), nullable=False)
    nist_csf_subcategory_id: Mapped[str] = mapped_column(String(30), nullable=False)
    mapping_type: Mapped[str] = mapped_column(String(50), nullable=False, default="equivalent")
    alignment_score: Mapped[Optional[Decimal]] = mapped_column(Numeric(precision=3, scale=2), nullable=True)
    confidence_level: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    mapping_rationale: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    implementation_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_by: Mapped[str] = mapped_column(String(100), nullable=False)
    updated_by: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    validated: Mapped[bool] = mapped_column(Boolean, nullable=False, default=False)
    validation_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    validated_by: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    
    # Relationships
    isf_control: Mapped["ISFControl"] = relationship("ISFControl")
    
    __table_args__ = (
        UniqueConstraint('isf_control_id', 'nist_csf_subcategory_id', 'mapping_type', name='uq_isf_nist_csf_mapping'),
        Index('idx_isf_nist_csf_control', 'isf_control_id'),
        Index('idx_isf_nist_csf_subcategory', 'nist_csf_subcategory_id'),
        Index('idx_isf_nist_csf_validated', 'validated'),
        Index('idx_isf_nist_csf_not_deleted', 'deleted_time'),
        CheckConstraint(
            "alignment_score >= 0.0 AND alignment_score <= 1.0",
            name='ck_isf_nist_csf_alignment_score'
        ),
        CheckConstraint(
            "confidence_level IN ('low', 'medium', 'high') OR confidence_level IS NULL",
            name='ck_isf_nist_csf_confidence_level'
        ),
        CheckConstraint(
            "mapping_type IN ('equivalent', 'overlapping', 'complementary', 'related', 'divergent')",
            name='ck_isf_nist_csf_mapping_type'
        ),
    )
    
    def __repr__(self) -> str:
        return f"<ISFToNISTCSFMapping(isf_control_id={self.isf_control_id}, nist_csf_subcategory_id='{self.nist_csf_subcategory_id}')>"


class FrameworkMappingAudit(Base, VersionMixin):
    """
    Framework mapping audit trail.
    
    Tracks all changes to framework mappings for compliance
    and quality assurance purposes.
    """
    
    __tablename__ = "framework_mapping_audits"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    mapping_type: Mapped[str] = mapped_column(String(50), nullable=False)  # mitre_to_isf, mitre_to_nist_csf, isf_to_nist_csf
    mapping_id: Mapped[int] = mapped_column(Integer, nullable=False)
    action: Mapped[str] = mapped_column(String(20), nullable=False)  # created, updated, deleted, validated
    old_values: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    new_values: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    changed_by: Mapped[str] = mapped_column(String(100), nullable=False)
    change_reason: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    change_timestamp: Mapped[datetime] = mapped_column(DateTime, nullable=False, default=datetime.utcnow)
    
    __table_args__ = (
        Index('idx_framework_mapping_audits_mapping', 'mapping_type', 'mapping_id'),
        Index('idx_framework_mapping_audits_timestamp', 'change_timestamp'),
        Index('idx_framework_mapping_audits_user', 'changed_by'),
        Index('idx_framework_mapping_audits_not_deleted', 'deleted_time'),
        CheckConstraint(
            "action IN ('created', 'updated', 'deleted', 'validated')",
            name='ck_framework_mapping_audits_action'
        ),
        CheckConstraint(
            "mapping_type IN ('mitre_to_isf', 'mitre_to_nist_csf', 'isf_to_nist_csf')",
            name='ck_framework_mapping_audits_mapping_type'
        ),
    )
    
    def __repr__(self) -> str:
        return f"<FrameworkMappingAudit(mapping_type='{self.mapping_type}', mapping_id={self.mapping_id}, action='{self.action}')>"


class MappingValidation(Base, VersionMixin):
    """
    Mapping validation workflow.
    
    Manages the validation process for framework mappings including
    assignment, review, and approval workflows.
    """
    
    __tablename__ = "mapping_validations"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    mapping_type: Mapped[str] = mapped_column(String(50), nullable=False)
    mapping_id: Mapped[int] = mapped_column(Integer, nullable=False)
    validation_status: Mapped[str] = mapped_column(String(20), nullable=False, default="pending")
    assigned_to: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)
    validation_criteria: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    validation_notes: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    created_by: Mapped[str] = mapped_column(String(100), nullable=False)
    due_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    started_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    completed_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    validation_result: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    validator_comments: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    __table_args__ = (
        Index('idx_mapping_validations_mapping', 'mapping_type', 'mapping_id'),
        Index('idx_mapping_validations_status', 'validation_status'),
        Index('idx_mapping_validations_assigned', 'assigned_to'),
        Index('idx_mapping_validations_due_date', 'due_date'),
        Index('idx_mapping_validations_not_deleted', 'deleted_time'),
        CheckConstraint(
            "validation_status IN ('pending', 'in_progress', 'approved', 'rejected', 'cancelled')",
            name='ck_mapping_validations_status'
        ),
        CheckConstraint(
            "mapping_type IN ('mitre_to_isf', 'mitre_to_nist_csf', 'isf_to_nist_csf')",
            name='ck_mapping_validations_mapping_type'
        ),
    )
    
    def __repr__(self) -> str:
        return f"<MappingValidation(mapping_type='{self.mapping_type}', mapping_id={self.mapping_id}, status='{self.validation_status}')>"


class BulkMappingOperation(Base, VersionMixin):
    """
    Bulk mapping operations.
    
    Manages large-scale import, export, and update operations
    for framework mappings with progress tracking.
    """
    
    __tablename__ = "bulk_mapping_operations"
    
    id: Mapped[int] = mapped_column(Integer, primary_key=True, index=True)
    operation_type: Mapped[str] = mapped_column(String(20), nullable=False)  # import, export, update, delete
    source_framework: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    target_framework: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    operation_status: Mapped[str] = mapped_column(String(20), nullable=False, default="pending")
    total_mappings: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    processed_mappings: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    successful_mappings: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    failed_mappings: Mapped[int] = mapped_column(Integer, nullable=False, default=0)
    progress_percentage: Mapped[float] = mapped_column(Numeric(precision=5, scale=2), nullable=False, default=0.0)
    operation_config: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    created_by: Mapped[str] = mapped_column(String(100), nullable=False)
    scheduled_start: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    started_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    completed_date: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    error_log: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    __table_args__ = (
        Index('idx_bulk_mapping_ops_status', 'operation_status'),
        Index('idx_bulk_mapping_ops_type', 'operation_type'),
        Index('idx_bulk_mapping_ops_created_by', 'created_by'),
        Index('idx_bulk_mapping_ops_not_deleted', 'deleted_time'),
        CheckConstraint(
            "operation_type IN ('import', 'export', 'update', 'delete')",
            name='ck_bulk_mapping_ops_operation_type'
        ),
        CheckConstraint(
            "operation_status IN ('pending', 'running', 'completed', 'failed', 'cancelled')",
            name='ck_bulk_mapping_ops_status'
        ),
        CheckConstraint(
            "progress_percentage >= 0.0 AND progress_percentage <= 100.0",
            name='ck_bulk_mapping_ops_progress'
        ),
    )
    
    def __repr__(self) -> str:
        return f"<BulkMappingOperation(operation_type='{self.operation_type}', status='{self.operation_status}', progress={self.progress_percentage}%)>"
    
    @property
    def success_rate(self) -> float:
        """Calculate success rate of the operation."""
        if self.processed_mappings == 0:
            return 0.0
        return self.successful_mappings / self.processed_mappings
