"""
Advanced Search and Linking Models.

This module contains SQLAlchemy models for advanced search capabilities,
cross-framework linking, and intelligent relationship management across
all cybersecurity frameworks.
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Float, JSON, Index
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import TSVECTOR
from datetime import datetime

from api.database import Base


class SearchIndex(Base):
    """
    Unified search index for all framework elements.
    
    Provides full-text search capabilities across all frameworks
    with semantic understanding and relevance scoring.
    """
    __tablename__ = "search_index"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Content identification
    framework = Column(String(50), nullable=False, index=True)  # ISF, NIST_CSF_2, ISO_27001, CIS_CONTROLS
    element_type = Column(String(50), nullable=False, index=True)  # control, safeguard, category, etc.
    element_id = Column(String(100), nullable=False, index=True)  # Framework-specific ID
    internal_id = Column(Integer, nullable=False, index=True)  # Internal database ID
    
    # Searchable content
    title = Column(String(1000), nullable=False)
    description = Column(Text, nullable=True)
    content = Column(Text, nullable=True)  # Combined searchable content
    keywords = Column(JSON, nullable=True)  # Extracted keywords
    
    # Search vectors for full-text search
    search_vector = Column(TSVECTOR, nullable=True)
    
    # Categorization and metadata
    category = Column(String(100), nullable=True, index=True)  # Security domain/category
    subcategory = Column(String(100), nullable=True, index=True)
    tags = Column(JSON, nullable=True)  # Searchable tags
    
    # Semantic and contextual information
    semantic_embedding = Column(JSON, nullable=True)  # Vector embedding for semantic search
    context_data = Column(JSON, nullable=True)  # Additional context information
    
    # Relevance and quality metrics
    search_weight = Column(Float, default=1.0)  # Search result weighting
    quality_score = Column(Float, default=0.0)  # Content quality score
    popularity_score = Column(Float, default=0.0)  # Usage-based popularity
    
    # Linking and relationship data
    related_elements = Column(JSON, nullable=True)  # Related framework elements
    mapped_elements = Column(JSON, nullable=True)  # Cross-framework mappings
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    search_results = relationship("SearchResult", back_populates="search_item", cascade="all, delete-orphan")
    column_links = relationship("ColumnLink", back_populates="search_item", cascade="all, delete-orphan")
    
    # Indexes for performance
    __table_args__ = (
        Index('idx_search_framework_type', 'framework', 'element_type'),
        Index('idx_search_category', 'category', 'subcategory'),
        Index('idx_search_content', 'search_vector', postgresql_using='gin'),
        Index('idx_search_quality', 'quality_score', 'popularity_score'),
        {"extend_existing": True}
    )


class SearchQuery(Base):
    """
    Search query tracking and analytics.
    
    Tracks user search queries for analytics, personalization,
    and search result optimization.
    """
    __tablename__ = "search_queries"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Query details
    query_text = Column(Text, nullable=False)
    query_type = Column(String(50), nullable=False, index=True)  # text, semantic, faceted, advanced
    
    # Search parameters
    frameworks = Column(JSON, nullable=True)  # Frameworks searched
    element_types = Column(JSON, nullable=True)  # Element types included
    filters = Column(JSON, nullable=True)  # Applied filters
    sort_criteria = Column(JSON, nullable=True)  # Sort parameters
    
    # Search context
    user_id = Column(String(100), nullable=True, index=True)
    session_id = Column(String(100), nullable=True, index=True)
    search_context = Column(JSON, nullable=True)  # Additional context
    
    # Results and performance
    results_count = Column(Integer, default=0)
    execution_time = Column(Float, nullable=True)  # Query execution time in seconds
    clicked_results = Column(JSON, nullable=True)  # Results that were clicked
    
    # Analytics data
    search_intent = Column(String(100), nullable=True)  # Inferred search intent
    success_indicators = Column(JSON, nullable=True)  # Success metrics
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    ip_address = Column(String(45), nullable=True)
    user_agent = Column(String(500), nullable=True)
    
    # Relationships
    search_results = relationship("SearchResult", back_populates="query", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        Index('idx_query_text', 'query_text', postgresql_using='gin'),
        Index('idx_query_user_time', 'user_id', 'created_at'),
        {"extend_existing": True}
    )


class SearchResult(Base):
    """
    Individual search result with relevance scoring.
    
    Represents a single search result with relevance metrics,
    ranking information, and user interaction tracking.
    """
    __tablename__ = "search_results"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Query and result relationship
    query_id = Column(Integer, ForeignKey("search_queries.id"), nullable=False)
    search_item_id = Column(Integer, ForeignKey("search_index.id"), nullable=False)
    
    # Ranking and relevance
    rank_position = Column(Integer, nullable=False)  # Position in search results
    relevance_score = Column(Float, nullable=False)  # Calculated relevance score
    
    # Scoring components
    text_match_score = Column(Float, default=0.0)  # Text matching score
    semantic_score = Column(Float, default=0.0)  # Semantic similarity score
    popularity_score = Column(Float, default=0.0)  # Popularity-based score
    quality_score = Column(Float, default=0.0)  # Content quality score
    recency_score = Column(Float, default=0.0)  # Recency boost score
    
    # User interaction tracking
    was_clicked = Column(Boolean, default=False)
    click_timestamp = Column(DateTime, nullable=True)
    dwell_time = Column(Float, nullable=True)  # Time spent on result
    
    # Result metadata
    result_snippet = Column(Text, nullable=True)  # Search result snippet
    highlighted_terms = Column(JSON, nullable=True)  # Highlighted search terms
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    query = relationship("SearchQuery", back_populates="search_results")
    search_item = relationship("SearchIndex", back_populates="search_results")
    
    # Constraints
    __table_args__ = (
        Index('idx_result_query_rank', 'query_id', 'rank_position'),
        Index('idx_result_relevance', 'relevance_score'),
        {"extend_existing": True}
    )


class ColumnLink(Base):
    """
    Links between search items and custom columns.
    
    Enables users to create custom organizational structures
    by linking framework elements to user-defined columns.
    """
    __tablename__ = "column_links"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Link relationship
    search_item_id = Column(Integer, ForeignKey("search_index.id"), nullable=False)
    column_id = Column(Integer, ForeignKey("custom_columns.id"), nullable=False)
    
    # Link properties
    link_type = Column(String(50), nullable=False, index=True)  # direct, related, mapped, custom
    link_strength = Column(Float, default=1.0)  # Strength of the relationship (0.0-1.0)
    
    # Link metadata
    description = Column(Text, nullable=True)  # Description of the link
    rationale = Column(Text, nullable=True)  # Rationale for the link
    tags = Column(JSON, nullable=True)  # Link-specific tags
    
    # Validation and quality
    is_validated = Column(Boolean, default=False)
    validated_by = Column(String(100), nullable=True)
    validated_at = Column(DateTime, nullable=True)
    confidence_score = Column(Float, default=0.0)  # Confidence in the link
    
    # User and context information
    created_by = Column(String(100), nullable=True, index=True)
    organization_context = Column(JSON, nullable=True)  # Organizational context
    
    # Link analytics
    usage_count = Column(Integer, default=0)  # How often this link is used
    last_accessed = Column(DateTime, nullable=True)
    effectiveness_rating = Column(Float, nullable=True)  # User-provided effectiveness
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    search_item = relationship("SearchIndex", back_populates="column_links")
    column = relationship("CustomColumn", back_populates="column_links")
    
    # Constraints
    __table_args__ = (
        Index('idx_link_item_column', 'search_item_id', 'column_id'),
        Index('idx_link_type_strength', 'link_type', 'link_strength'),
        Index('idx_link_user', 'created_by', 'created_at'),
        {"extend_existing": True}
    )


class CustomColumn(Base):
    """
    User-defined columns for organizing framework elements.
    
    Allows users to create custom organizational structures
    and categorization schemes for framework elements.
    """
    __tablename__ = "custom_columns"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Column definition
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    column_type = Column(String(50), nullable=False, index=True)  # category, priority, status, custom
    
    # Column properties
    data_type = Column(String(50), nullable=False)  # text, number, boolean, date, json
    allowed_values = Column(JSON, nullable=True)  # Predefined values for selection
    validation_rules = Column(JSON, nullable=True)  # Validation rules
    
    # Display and formatting
    display_order = Column(Integer, default=0)
    is_visible = Column(Boolean, default=True)
    color_scheme = Column(JSON, nullable=True)  # Color coding rules
    icon = Column(String(100), nullable=True)  # Icon identifier
    
    # Scope and access
    scope = Column(String(50), nullable=False, index=True)  # global, organization, user, project
    owner_id = Column(String(100), nullable=True, index=True)
    organization_id = Column(String(100), nullable=True, index=True)
    
    # Column metadata
    is_system_column = Column(Boolean, default=False)  # System-defined vs user-defined
    is_required = Column(Boolean, default=False)
    is_searchable = Column(Boolean, default=True)
    
    # Usage analytics
    usage_count = Column(Integer, default=0)
    last_used = Column(DateTime, nullable=True)
    
    # Metadata
    created_by = Column(String(100), nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    column_links = relationship("ColumnLink", back_populates="column", cascade="all, delete-orphan")
    
    # Constraints
    __table_args__ = (
        Index('idx_column_scope_owner', 'scope', 'owner_id'),
        Index('idx_column_type', 'column_type', 'data_type'),
        {"extend_existing": True}
    )


class CrossFrameworkRelationship(Base):
    """
    Advanced cross-framework relationships and mappings.
    
    Extends basic framework mapping with advanced relationship
    types, semantic understanding, and contextual information.
    """
    __tablename__ = "cross_framework_relationships"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Relationship endpoints
    source_item_id = Column(Integer, ForeignKey("search_index.id"), nullable=False)
    target_item_id = Column(Integer, ForeignKey("search_index.id"), nullable=False)
    
    # Relationship properties
    relationship_type = Column(String(50), nullable=False, index=True)  # equivalent, related, implements, supports
    relationship_strength = Column(Float, default=0.5)  # Strength of relationship (0.0-1.0)
    bidirectional = Column(Boolean, default=False)  # Is relationship bidirectional
    
    # Semantic and contextual information
    semantic_similarity = Column(Float, nullable=True)  # Computed semantic similarity
    context_similarity = Column(Float, nullable=True)  # Contextual similarity
    structural_similarity = Column(Float, nullable=True)  # Structural similarity
    
    # Relationship metadata
    description = Column(Text, nullable=True)
    rationale = Column(Text, nullable=True)
    evidence = Column(JSON, nullable=True)  # Supporting evidence
    
    # Quality and validation
    confidence_score = Column(Float, default=0.0)
    is_validated = Column(Boolean, default=False)
    validated_by = Column(String(100), nullable=True)
    validated_at = Column(DateTime, nullable=True)
    
    # Discovery and creation
    discovery_method = Column(String(50), nullable=True)  # manual, automatic, ml, expert
    created_by = Column(String(100), nullable=True)
    
    # Usage and analytics
    usage_count = Column(Integer, default=0)
    effectiveness_rating = Column(Float, nullable=True)
    last_accessed = Column(DateTime, nullable=True)
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    source_item = relationship("SearchIndex", foreign_keys=[source_item_id])
    target_item = relationship("SearchIndex", foreign_keys=[target_item_id])
    
    # Constraints
    __table_args__ = (
        Index('idx_relationship_source_target', 'source_item_id', 'target_item_id'),
        Index('idx_relationship_type_strength', 'relationship_type', 'relationship_strength'),
        Index('idx_relationship_confidence', 'confidence_score', 'is_validated'),
        {"extend_existing": True}
    )


class SearchFacet(Base):
    """
    Search facets for advanced filtering and navigation.
    
    Provides faceted search capabilities with dynamic
    facet generation and user-customizable filters.
    """
    __tablename__ = "search_facets"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Facet definition
    name = Column(String(255), nullable=False)
    display_name = Column(String(255), nullable=False)
    facet_type = Column(String(50), nullable=False, index=True)  # category, range, boolean, date
    
    # Facet properties
    field_name = Column(String(100), nullable=False)  # Field to facet on
    data_type = Column(String(50), nullable=False)  # text, number, date, boolean
    aggregation_method = Column(String(50), nullable=True)  # count, sum, avg, min, max
    
    # Display and behavior
    display_order = Column(Integer, default=0)
    is_visible = Column(Boolean, default=True)
    is_collapsible = Column(Boolean, default=True)
    default_expanded = Column(Boolean, default=True)
    
    # Facet configuration
    max_values = Column(Integer, default=10)  # Maximum values to show
    min_count = Column(Integer, default=1)  # Minimum count to show value
    sort_order = Column(String(20), default='count')  # count, alpha, value
    
    # Scope and access
    scope = Column(String(50), nullable=False, index=True)  # global, framework, user
    frameworks = Column(JSON, nullable=True)  # Applicable frameworks
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Constraints
    __table_args__ = (
        Index('idx_facet_type_scope', 'facet_type', 'scope'),
        Index('idx_facet_field', 'field_name', 'data_type'),
        {"extend_existing": True}
    )
