"""
Framework Mapping Models.

This module contains models for managing cross-framework mappings between
different cybersecurity frameworks (ISF, NIST CSF, MITRE ATT&CK, etc.).
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Float, <PERSON><PERSON><PERSON>
from sqlalchemy.orm import relationship
from datetime import datetime
from enum import Enum

from api.database import Base


class MappingType(str, Enum):
    """Types of framework mappings."""
    DIRECT = "direct"  # Direct one-to-one mapping
    PARTIAL = "partial"  # Partial coverage mapping
    RELATED = "related"  # Related but not direct mapping
    COMPLEMENTARY = "complementary"  # Complementary controls
    HIERARCHICAL = "hierarchical"  # Parent-child relationship


class MappingConfidence(str, Enum):
    """Confidence levels for mappings."""
    HIGH = "high"  # 90-100% confidence
    MEDIUM = "medium"  # 70-89% confidence
    LOW = "low"  # 50-69% confidence
    EXPERIMENTAL = "experimental"  # <50% confidence


class FrameworkMapping(Base):
    """
    Cross-framework mapping model.
    
    Maps controls/techniques between different cybersecurity frameworks
    with confidence scoring and effectiveness metrics.
    """
    __tablename__ = "framework_mappings"
    
    id = Column(Integer, primary_key=True, index=True)
    
    # Source framework information
    source_framework = Column(String(50), nullable=False, index=True)  # e.g., "ISF", "NIST_CSF", "MITRE_ATTACK"
    source_control_id = Column(String(100), nullable=False, index=True)  # e.g., "ISF.05.01", "PR.AC-1"
    source_version = Column(String(20), nullable=True)  # Framework version
    
    # Target framework information
    target_framework = Column(String(50), nullable=False, index=True)
    target_control_id = Column(String(100), nullable=False, index=True)
    target_version = Column(String(20), nullable=True)
    
    # Mapping metadata
    mapping_type = Column(String(20), nullable=False, default=MappingType.DIRECT)
    confidence_level = Column(String(20), nullable=False, default=MappingConfidence.MEDIUM)
    confidence_score = Column(Float, nullable=False, default=0.75)  # 0.0 to 1.0
    
    # Effectiveness and coverage
    effectiveness_score = Column(Float, nullable=True)  # How effective is this mapping
    coverage_percentage = Column(Float, nullable=True)  # What % of control is covered
    
    # Mapping details
    description = Column(Text, nullable=True)
    rationale = Column(Text, nullable=True)  # Why this mapping exists
    notes = Column(Text, nullable=True)  # Additional notes
    
    # Validation and approval
    is_validated = Column(Boolean, default=False)
    validated_by = Column(String, ForeignKey("users.id"), nullable=True)
    validated_at = Column(DateTime, nullable=True)

    # Mapping set association
    mapping_set_id = Column(Integer, ForeignKey("mapping_sets.id"), nullable=True)
    
    # Metadata
    created_by = Column(String, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Additional mapping data (JSON field for flexibility)
    mapping_data = Column(JSON, nullable=True)
    
    # Relationships
    creator = relationship("User", foreign_keys=[created_by], back_populates="created_mappings")
    validator = relationship("User", foreign_keys=[validated_by], back_populates="validated_mappings")
    mapping_set = relationship("MappingSet", back_populates="mappings")
    validations = relationship("MappingValidation", back_populates="mapping")
    audit_trail = relationship("MappingAudit", back_populates="mapping")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class MappingSet(Base):
    """
    Collection of related mappings.
    
    Groups mappings together for bulk operations, validation,
    and organizational purposes.
    """
    __tablename__ = "mapping_sets"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Framework scope
    source_framework = Column(String(50), nullable=False)
    target_framework = Column(String(50), nullable=False)
    
    # Set metadata
    version = Column(String(20), nullable=False, default="1.0")
    is_official = Column(Boolean, default=False)  # Official vs community mapping
    is_published = Column(Boolean, default=False)
    
    # Statistics
    total_mappings = Column(Integer, default=0)
    validated_mappings = Column(Integer, default=0)
    average_confidence = Column(Float, nullable=True)
    
    # Metadata
    created_by = Column(String, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    creator = relationship("User", back_populates="created_mapping_sets")
    mappings = relationship("FrameworkMapping", back_populates="mapping_set")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class MappingValidation(Base):
    """
    Validation records for framework mappings.
    
    Tracks validation history, reviewer comments, and approval workflow.
    """
    __tablename__ = "mapping_validations"
    
    id = Column(Integer, primary_key=True, index=True)
    mapping_id = Column(Integer, ForeignKey("framework_mappings.id"), nullable=False)
    
    # Validation details
    validation_status = Column(String(20), nullable=False)  # pending, approved, rejected, needs_review
    reviewer_id = Column(String, ForeignKey("users.id"), nullable=False)
    review_date = Column(DateTime, default=datetime.utcnow)
    
    # Review feedback
    comments = Column(Text, nullable=True)
    suggested_changes = Column(JSON, nullable=True)
    confidence_assessment = Column(Float, nullable=True)
    
    # Validation criteria scores
    accuracy_score = Column(Float, nullable=True)  # How accurate is the mapping
    completeness_score = Column(Float, nullable=True)  # How complete is the mapping
    relevance_score = Column(Float, nullable=True)  # How relevant is the mapping
    
    # Metadata
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    mapping = relationship("FrameworkMapping", back_populates="validations")
    reviewer = relationship("User", back_populates="mapping_reviews")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class MappingTemplate(Base):
    """
    Templates for common mapping patterns.
    
    Provides reusable mapping templates for common framework relationships
    to speed up mapping creation and ensure consistency.
    """
    __tablename__ = "mapping_templates"
    
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String(255), nullable=False)
    description = Column(Text, nullable=True)
    
    # Template scope
    source_framework = Column(String(50), nullable=False)
    target_framework = Column(String(50), nullable=False)
    
    # Template pattern
    source_pattern = Column(String(255), nullable=True)  # Regex pattern for source controls
    target_pattern = Column(String(255), nullable=True)  # Regex pattern for target controls
    
    # Default mapping settings
    default_mapping_type = Column(String(20), nullable=False, default=MappingType.DIRECT)
    default_confidence = Column(String(20), nullable=False, default=MappingConfidence.MEDIUM)
    default_confidence_score = Column(Float, nullable=False, default=0.75)
    
    # Template data
    template_data = Column(JSON, nullable=True)  # Additional template configuration
    
    # Usage statistics
    usage_count = Column(Integer, default=0)
    success_rate = Column(Float, nullable=True)  # Success rate of mappings created from this template
    
    # Metadata
    created_by = Column(String, ForeignKey("users.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    deleted_at = Column(DateTime, nullable=True)
    
    # Relationships
    creator = relationship("User", back_populates="created_mapping_templates")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )


class MappingAudit(Base):
    """
    Audit trail for mapping changes.
    
    Tracks all changes to mappings for compliance and history tracking.
    """
    __tablename__ = "mapping_audits"
    
    id = Column(Integer, primary_key=True, index=True)
    mapping_id = Column(Integer, ForeignKey("framework_mappings.id"), nullable=False)
    
    # Change details
    action = Column(String(50), nullable=False)  # created, updated, deleted, validated, etc.
    field_name = Column(String(100), nullable=True)  # Which field was changed
    old_value = Column(Text, nullable=True)  # Previous value
    new_value = Column(Text, nullable=True)  # New value
    
    # Change context
    change_reason = Column(Text, nullable=True)
    change_description = Column(Text, nullable=True)
    
    # Metadata
    changed_by = Column(String, ForeignKey("users.id"), nullable=False)
    changed_at = Column(DateTime, default=datetime.utcnow)
    
    # Additional data
    audit_data = Column(JSON, nullable=True)
    
    # Relationships
    mapping = relationship("FrameworkMapping", back_populates="audit_trail")
    user = relationship("User", back_populates="mapping_audit_entries")
    
    # Constraints
    __table_args__ = (
        {"extend_existing": True}
    )
