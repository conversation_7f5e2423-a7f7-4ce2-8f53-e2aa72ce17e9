"""Database models for the Enhanced Testcase Chaining & Sequencing feature.

This module contains the SQLAlchemy models for testcase chains, nodes, edges,
executions, and conditions.
"""

from datetime import datetime
from typing import Any, List, Optional

from sqlalchemy import (
    Column,
    DateTime,
    ForeignKey,
    func,
)
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.orm import Mapped, mapped_column, relationship

from api.database import Base
from api.models.mixins import VersionMixin


class TestcaseChainDB(Base, VersionMixin):
    """Database model for testcase chains."""

    __tablename__ = "testcase_chains"
    __table_args__ = {"extend_existing": True}

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(nullable=False)
    description: Mapped[Optional[str]]
    created_by: Mapped[str] = mapped_column(ForeignKey("users.id"))
    status: Mapped[str] = mapped_column(server_default="draft")  # draft, active, completed, archived

    # Relationships
    # creator: Mapped["Any"] = relationship("User", foreign_keys=[created_by])  # Temporarily disabled for ISF testing
    chain_nodes: Mapped[List["TestcaseChainNodeDB"]] = relationship(
        back_populates="chain",
        cascade="all, delete-orphan",
    )
    executions: Mapped[List["ChainExecutionDB"]] = relationship(
        back_populates="chain",
        cascade="all, delete-orphan",
    )

    def __repr__(self):
        return f"<TestcaseChain id={self.id} name={self.name} status={self.status}>"


class TestcaseChainNodeDB(Base, VersionMixin):
    """Database model for nodes in a testcase chain."""

    __tablename__ = "testcase_chain_nodes"
    __table_args__ = {"extend_existing": True}

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    chain_id: Mapped[int] = mapped_column(
        ForeignKey("testcase_chains.id", ondelete="CASCADE")
    )
    testcase_id: Mapped[int] = mapped_column(
        ForeignKey("test_cases.id", ondelete="CASCADE")
    )
    node_type: Mapped[str] = mapped_column(
        server_default="standard"
    )  # standard, conditional, start, end
    position_x: Mapped[float] = mapped_column(default=0)  # For UI positioning
    position_y: Mapped[float] = mapped_column(default=0)  # For UI positioning
    execution_order: Mapped[int] = mapped_column(default=0)
    condition_expression: Mapped[Optional[str]]  # For conditional nodes

    # Relationships
    chain: Mapped["TestcaseChainDB"] = relationship(back_populates="chain_nodes")
    testcase: Mapped[Any] = relationship("TestCaseDB")
    outgoing_edges: Mapped[List["TestcaseChainEdgeDB"]] = relationship(
        foreign_keys="[TestcaseChainEdgeDB.source_node_id]",
        back_populates="source_node",
        cascade="all, delete-orphan",
    )
    incoming_edges: Mapped[List["TestcaseChainEdgeDB"]] = relationship(
        foreign_keys="[TestcaseChainEdgeDB.target_node_id]",
        back_populates="target_node",
        cascade="all, delete-orphan",
    )
    node_executions: Mapped[List["NodeExecutionDB"]] = relationship(
        back_populates="node",
    )

    def __repr__(self):
        return f"<TestcaseChainNode id={self.id} chain_id={self.chain_id} testcase_id={self.testcase_id} type={self.node_type}>"


class TestcaseChainEdgeDB(Base, VersionMixin):
    """Database model for edges connecting testcase chain nodes."""

    __tablename__ = "testcase_chain_edges"
    __table_args__ = {"extend_existing": True}

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    source_node_id: Mapped[int] = mapped_column(
        ForeignKey("testcase_chain_nodes.id", ondelete="CASCADE")
    )
    target_node_id: Mapped[int] = mapped_column(
        ForeignKey("testcase_chain_nodes.id", ondelete="CASCADE")
    )
    edge_type: Mapped[str] = mapped_column(
        server_default="standard"
    )  # standard, success_path, failure_path
    condition: Mapped[Optional[str]]  # Condition for taking this edge

    # Relationships
    source_node: Mapped["TestcaseChainNodeDB"] = relationship(
        foreign_keys=[source_node_id],
        back_populates="outgoing_edges",
    )
    target_node: Mapped["TestcaseChainNodeDB"] = relationship(
        foreign_keys=[target_node_id],
        back_populates="incoming_edges",
    )

    def __repr__(self):
        return f"<TestcaseChainEdge id={self.id} source={self.source_node_id} target={self.target_node_id} type={self.edge_type}>"


class ChainExecutionDB(Base, VersionMixin):
    """Database model for testcase chain executions."""

    __tablename__ = "chain_executions"
    __table_args__ = {"extend_existing": True}

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    chain_id: Mapped[int] = mapped_column(ForeignKey("testcase_chains.id", ondelete="CASCADE"))
    started_by: Mapped[str] = mapped_column(ForeignKey("users.id"))
    start_time: Mapped[datetime] = mapped_column(default=func.now())
    end_time: Mapped[Optional[datetime]]
    status: Mapped[str] = mapped_column(
        server_default="running"
    )  # running, completed, failed, aborted

    # Relationships
    chain: Mapped["TestcaseChainDB"] = relationship(back_populates="executions")
    # starter: Mapped[Any] = relationship("User", foreign_keys=[started_by])  # Temporarily disabled for ISF testing
    node_executions: Mapped[List["NodeExecutionDB"]] = relationship(
        back_populates="chain_execution",
        cascade="all, delete-orphan",
    )

    def __repr__(self):
        return f"<ChainExecution id={self.id} chain_id={self.chain_id} status={self.status}>"


class NodeExecutionDB(Base, VersionMixin):
    """Database model for testcase chain node executions."""

    __tablename__ = "node_executions"
    __table_args__ = {"extend_existing": True}

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    chain_execution_id: Mapped[int] = mapped_column(
        ForeignKey("chain_executions.id", ondelete="CASCADE")
    )
    node_id: Mapped[int] = mapped_column(
        ForeignKey("testcase_chain_nodes.id", ondelete="CASCADE")
    )
    start_time: Mapped[Optional[datetime]]
    end_time: Mapped[Optional[datetime]]
    status: Mapped[str] = mapped_column(
        server_default="pending"
    )  # pending, running, completed, failed, skipped
    result_data: Mapped[Optional[dict]] = mapped_column(type_=JSONB)

    # Relationships
    chain_execution: Mapped["ChainExecutionDB"] = relationship(
        back_populates="node_executions"
    )
    node: Mapped["TestcaseChainNodeDB"] = relationship(back_populates="node_executions")

    def __repr__(self):
        return (
            f"<NodeExecution id={self.id} node_id={self.node_id} status={self.status}>"
        )


class TestcaseConditionDB(Base, VersionMixin):
    """Database model for testcase preconditions and postconditions."""

    __tablename__ = "testcase_conditions"
    __table_args__ = {"extend_existing": True}

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    testcase_id: Mapped[int] = mapped_column(ForeignKey("test_cases_base.id", ondelete="CASCADE"))
    condition_type: Mapped[str] = mapped_column(nullable=False)  # precondition, postcondition
    name: Mapped[str] = mapped_column(nullable=False)
    description: Mapped[Optional[str]]
    validation_script: Mapped[Optional[str]]  # Script to validate the condition
    required: Mapped[bool] = mapped_column(
        default=True
    )  # Is this condition required for execution?

    # Relationships
    testcase: Mapped[Any] = relationship("TestCaseDB", back_populates="conditions")

    def __repr__(self):
        return f"<TestcaseCondition id={self.id} testcase_id={self.testcase_id} type={self.condition_type} name={self.name}>"
