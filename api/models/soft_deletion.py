"""
Soft Deletion Models.

This module contains models for managing soft deletion functionality
across the cybersecurity framework management system.
"""

from sqlalchemy import Column, Integer, String, Text, DateTime, Boolean, ForeignKey
from sqlalchemy.orm import relationship
from datetime import datetime

from api.database import Base


class SoftDeletionAudit(Base):
    """Audit trail for soft deletion operations."""
    __tablename__ = "soft_deletion_audit"
    
    id = Column(Integer, primary_key=True, index=True)
    table_name = Column(String(255), nullable=False)
    record_id = Column(Integer, nullable=False)
    action = Column(String(50), nullable=False)  # 'delete', 'restore', 'purge'
    user_id = Column(Integer, ForeignKey("users.id"), nullable=True)
    reason = Column(Text, nullable=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationship to user
    user = relationship("User", back_populates="soft_deletion_audits")


class SoftDeletionNotification(Base):
    """Notifications for soft deletion events."""
    __tablename__ = "soft_deletion_notifications"
    
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    table_name = Column(String(255), nullable=False)
    record_id = Column(Integer, nullable=False)
    notification_type = Column(String(50), nullable=False)  # 'pending_purge', 'restored'
    message = Column(Text, nullable=False)
    is_read = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationship to user
    user = relationship("User", back_populates="soft_deletion_notifications")


class SoftDeletionPolicy(Base):
    """Policies for soft deletion behavior."""
    __tablename__ = "soft_deletion_policies"
    
    id = Column(Integer, primary_key=True, index=True)
    table_name = Column(String(255), nullable=False, unique=True)
    retention_days = Column(Integer, default=30)  # Days before permanent deletion
    auto_purge_enabled = Column(Boolean, default=True)
    require_approval = Column(Boolean, default=False)
    notification_enabled = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)


class SoftDeletionSchedule(Base):
    """Scheduled soft deletion operations."""
    __tablename__ = "soft_deletion_schedules"
    
    id = Column(Integer, primary_key=True, index=True)
    table_name = Column(String(255), nullable=False)
    record_id = Column(Integer, nullable=False)
    scheduled_purge_date = Column(DateTime, nullable=False)
    status = Column(String(50), default="scheduled")  # 'scheduled', 'completed', 'cancelled'
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
