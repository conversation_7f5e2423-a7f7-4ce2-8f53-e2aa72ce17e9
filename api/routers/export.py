"""
Export API Router for Cybersecurity Frameworks.

This module provides REST API endpoints for exporting framework data
in various formats including JSON, CSV, XML, STIX, and compliance reports.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from io import BytesIO

from fastapi import APIRouter, Depends, HTTPException, Query, Path, status
from fastapi.responses import StreamingResponse, JSONResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from api.core.database import get_db
from api.auth.dependencies import get_current_user
from api.models.user import User
from api.services.export_services import (
    FrameworkExportService,
    ISFExportService,
    NISTCSFExportService,
    STIXExportService,
    ComplianceReportGenerator,
    CrossFrameworkAnalysisExporter,
    ExportFormat,
    ExportResult
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/export", tags=["Framework Export"])


# Pydantic models for request/response
class ExportRequest(BaseModel):
    """Base export request model."""
    format: str = Field(..., description="Export format (json, csv, xml, stix)")
    include_metadata: bool = Field(True, description="Include export metadata")
    filters: Optional[Dict[str, Any]] = Field(None, description="Export filters")


class ISFExportRequest(ExportRequest):
    """ISF export request model."""
    version: Optional[str] = Field(None, description="ISF version to export")
    include_mappings: bool = Field(False, description="Include cross-framework mappings")
    security_areas: Optional[List[str]] = Field(None, description="Specific security areas to export")


class NISTCSFExportRequest(ExportRequest):
    """NIST CSF export request model."""
    version: Optional[str] = Field("2.0", description="NIST CSF version to export")
    preserve_hierarchy: bool = Field(True, description="Preserve hierarchical structure")
    functions: Optional[List[str]] = Field(None, description="Specific functions to export")


class STIXExportRequest(ExportRequest):
    """STIX export request model."""
    frameworks: List[str] = Field(..., description="Frameworks to include in STIX export")
    include_mappings: bool = Field(True, description="Include framework mappings")
    stix_version: str = Field("2.1", description="STIX version")


class ComplianceReportRequest(BaseModel):
    """Compliance report request model."""
    framework: str = Field(..., description="Framework for compliance report (isf, nist_csf)")
    format: str = Field("html", description="Report format (html, pdf)")
    assessment_data: Dict[str, Any] = Field(..., description="Assessment data for compliance")
    organization: str = Field(..., description="Organization name")
    report_date: Optional[datetime] = Field(None, description="Report date")


class CrossFrameworkAnalysisRequest(BaseModel):
    """Cross-framework analysis request model."""
    frameworks: List[str] = Field(..., description="Frameworks to analyze")
    analysis_type: str = Field("coverage", description="Analysis type (coverage, gaps, overlap)")
    format: str = Field("json", description="Export format")


class ExportStatusResponse(BaseModel):
    """Export status response model."""
    success: bool
    format: str
    file_size: Optional[int] = None
    record_count: Optional[int] = None
    processing_time: Optional[float] = None
    download_url: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    error: Optional[str] = None


@router.post("/isf", response_model=ExportStatusResponse)
async def export_isf_framework(
    request: ISFExportRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Export ISF (Information Security Framework) data.
    
    Supports multiple formats and filtering options for ISF framework data export.
    """
    try:
        logger.info(f"Starting ISF export for user {current_user.id} in format {request.format}")
        
        # Initialize ISF export service
        export_service = ISFExportService(db)
        
        # Perform export based on format
        if request.format.lower() == "json":
            result = export_service.export_to_json(
                filters=request.filters,
                include_mappings=request.include_mappings
            )
        elif request.format.lower() == "csv":
            result = export_service.export_to_csv(
                filters=request.filters
            )
        elif request.format.lower() == "xml":
            result = export_service.export_to_xml(
                filters=request.filters
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported export format: {request.format}"
            )
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Export failed: {result.error}"
            )
        
        # Create response
        response = ExportStatusResponse(
            success=result.success,
            format=result.format.value,
            file_size=result.file_size,
            record_count=result.record_count,
            processing_time=result.processing_time,
            metadata=result.metadata
        )
        
        # Return file as streaming response
        if request.format.lower() == "json":
            media_type = "application/json"
            filename = f"isf_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        elif request.format.lower() == "csv":
            media_type = "text/csv"
            filename = f"isf_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        elif request.format.lower() == "xml":
            media_type = "application/xml"
            filename = f"isf_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xml"
        
        # Create file stream
        file_stream = BytesIO(result.data.encode('utf-8'))
        
        return StreamingResponse(
            iter([file_stream.getvalue()]),
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"ISF export error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error during ISF export: {str(e)}"
        )


@router.post("/nist-csf", response_model=ExportStatusResponse)
async def export_nist_csf_framework(
    request: NISTCSFExportRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Export NIST Cybersecurity Framework data.
    
    Supports hierarchical export with multiple format options.
    """
    try:
        logger.info(f"Starting NIST CSF export for user {current_user.id} in format {request.format}")
        
        # Initialize NIST CSF export service
        export_service = NISTCSFExportService(db)
        
        # Perform export based on format
        if request.format.lower() == "json":
            result = export_service.export_to_json(
                preserve_hierarchy=request.preserve_hierarchy
            )
        elif request.format.lower() == "csv":
            result = export_service.export_to_csv()
        elif request.format.lower() == "xml":
            result = export_service.export_to_xml()
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported export format: {request.format}"
            )
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Export failed: {result.error}"
            )
        
        # Determine media type and filename
        if request.format.lower() == "json":
            media_type = "application/json"
            filename = f"nist_csf_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        elif request.format.lower() == "csv":
            media_type = "text/csv"
            filename = f"nist_csf_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
        elif request.format.lower() == "xml":
            media_type = "application/xml"
            filename = f"nist_csf_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.xml"
        
        # Create file stream
        file_stream = BytesIO(result.data.encode('utf-8'))
        
        return StreamingResponse(
            iter([file_stream.getvalue()]),
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"NIST CSF export error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error during NIST CSF export: {str(e)}"
        )


@router.post("/stix")
async def export_frameworks_to_stix(
    request: STIXExportRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Export framework data to STIX format.
    
    Supports multiple frameworks and TAXII compatibility.
    """
    try:
        logger.info(f"Starting STIX export for user {current_user.id}")
        
        # Initialize STIX export service
        export_service = STIXExportService(db)
        
        # Perform STIX export
        result = export_service.export_frameworks_to_stix(
            frameworks=request.frameworks,
            include_mappings=request.include_mappings
        )
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"STIX export failed: {result.error}"
            )
        
        # Return STIX data as streaming response
        filename = f"frameworks_stix_export_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        file_stream = BytesIO(result.data.encode('utf-8'))
        
        return StreamingResponse(
            iter([file_stream.getvalue()]),
            media_type="application/json",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"STIX export error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error during STIX export: {str(e)}"
        )


@router.post("/compliance-report")
async def generate_compliance_report(
    request: ComplianceReportRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Generate compliance report for specified framework.
    
    Creates detailed compliance reports with assessment data.
    """
    try:
        logger.info(f"Generating compliance report for {request.framework} by user {current_user.id}")
        
        # Initialize compliance report generator
        report_generator = ComplianceReportGenerator(db)
        
        # Generate report based on framework
        if request.framework.lower() == "isf":
            result = report_generator.generate_isf_compliance_report(
                config={
                    "format": request.format,
                    "organization": request.organization,
                    "report_date": request.report_date or datetime.now()
                },
                assessment_data=request.assessment_data
            )
        elif request.framework.lower() == "nist_csf":
            result = report_generator.generate_nist_csf_compliance_report(
                config={
                    "format": request.format,
                    "organization": request.organization,
                    "report_date": request.report_date or datetime.now()
                },
                assessment_data=request.assessment_data
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported framework for compliance report: {request.framework}"
            )
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Compliance report generation failed: {result.error}"
            )
        
        # Determine media type and filename
        if request.format.lower() == "html":
            media_type = "text/html"
            filename = f"{request.framework}_compliance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.html"
        elif request.format.lower() == "pdf":
            media_type = "application/pdf"
            filename = f"{request.framework}_compliance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.pdf"
        else:
            media_type = "application/octet-stream"
            filename = f"{request.framework}_compliance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        
        # Create file stream
        file_stream = BytesIO(result.data.encode('utf-8'))
        
        return StreamingResponse(
            iter([file_stream.getvalue()]),
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Compliance report error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error during compliance report generation: {str(e)}"
        )


@router.post("/cross-framework-analysis")
async def export_cross_framework_analysis(
    request: CrossFrameworkAnalysisRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Export cross-framework analysis data.
    
    Provides coverage analysis, gap analysis, and framework overlap reports.
    """
    try:
        logger.info(f"Starting cross-framework analysis for user {current_user.id}")
        
        # Initialize cross-framework analysis exporter
        analysis_exporter = CrossFrameworkAnalysisExporter(db)
        
        # Perform analysis based on type
        if request.analysis_type.lower() == "coverage":
            result = analysis_exporter.export_coverage_analysis(
                frameworks=request.frameworks,
                format=request.format
            )
        elif request.analysis_type.lower() == "gaps":
            result = analysis_exporter.export_gap_analysis(
                frameworks=request.frameworks,
                format=request.format
            )
        elif request.analysis_type.lower() == "overlap":
            result = analysis_exporter.export_overlap_analysis(
                frameworks=request.frameworks,
                format=request.format
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported analysis type: {request.analysis_type}"
            )
        
        if not result.success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Cross-framework analysis failed: {result.error}"
            )
        
        # Determine media type and filename
        if request.format.lower() == "json":
            media_type = "application/json"
            extension = "json"
        elif request.format.lower() == "csv":
            media_type = "text/csv"
            extension = "csv"
        else:
            media_type = "application/octet-stream"
            extension = "txt"
        
        filename = f"cross_framework_{request.analysis_type}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.{extension}"
        
        # Create file stream
        file_stream = BytesIO(result.data.encode('utf-8'))
        
        return StreamingResponse(
            iter([file_stream.getvalue()]),
            media_type=media_type,
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Cross-framework analysis error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error during cross-framework analysis: {str(e)}"
        )


@router.get("/formats")
async def get_supported_export_formats():
    """
    Get list of supported export formats.
    
    Returns available export formats for each service type.
    """
    return {
        "framework_exports": {
            "isf": ["json", "csv", "xml"],
            "nist_csf": ["json", "csv", "xml"],
            "stix": ["json"]
        },
        "compliance_reports": {
            "formats": ["html", "pdf"],
            "frameworks": ["isf", "nist_csf"]
        },
        "cross_framework_analysis": {
            "formats": ["json", "csv"],
            "analysis_types": ["coverage", "gaps", "overlap"]
        }
    }


@router.get("/health")
async def export_service_health():
    """
    Check export service health status.
    
    Returns health status of all export services.
    """
    return {
        "status": "healthy",
        "services": {
            "isf_export": "operational",
            "nist_csf_export": "operational", 
            "stix_export": "operational",
            "compliance_reports": "operational",
            "cross_framework_analysis": "operational"
        },
        "timestamp": datetime.now().isoformat()
    }
