"""
Health check endpoints for all services.

This module provides comprehensive health check endpoints for monitoring
the status of all services in the cybersecurity framework management platform.
"""

import asyncio
import time
from typing import Dict, Any, List
from datetime import datetime, timezone
from fastapi import APIRouter, HTTPException, Depends
from sqlalchemy.orm import Session
from sqlalchemy import text
import aiohttp
import redis
import psutil

from api.core.config import settings
from api.database import get_db
from api.utils.service_url_manager import CyberSecurityServiceURLManager

router = APIRouter(prefix="/api/v1/health", tags=["health"])


@router.get("/")
async def health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint.
    
    Returns:
        Basic health status and timestamp
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "environment": settings.ENVIRONMENT,
        "version": settings.APP_VERSION
    }


@router.get("/detailed")
async def detailed_health_check(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    Detailed health check including all service dependencies.
    
    Returns:
        Comprehensive health status for all services
    """
    start_time = time.time()
    health_status = {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "environment": settings.ENVIRONMENT,
        "version": settings.APP_VERSION,
        "checks": {},
        "summary": {
            "total_checks": 0,
            "healthy_checks": 0,
            "unhealthy_checks": 0,
            "response_time_ms": 0
        }
    }
    
    # Database health check
    try:
        db.execute(text("SELECT 1"))
        health_status["checks"]["database"] = {
            "status": "healthy",
            "message": "Database connection successful",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        health_status["checks"]["database"] = {
            "status": "unhealthy",
            "message": f"Database connection failed: {str(e)}",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        health_status["status"] = "unhealthy"
    
    # Redis health check
    try:
        redis_client = redis.Redis.from_url(settings.REDIS_URL)
        redis_client.ping()
        health_status["checks"]["redis"] = {
            "status": "healthy",
            "message": "Redis connection successful",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
    except Exception as e:
        health_status["checks"]["redis"] = {
            "status": "unhealthy",
            "message": f"Redis connection failed: {str(e)}",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        health_status["status"] = "unhealthy"
    
    # System resources check
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        system_healthy = (
            cpu_percent < 90 and
            memory.percent < 90 and
            disk.percent < 90
        )
        
        health_status["checks"]["system"] = {
            "status": "healthy" if system_healthy else "warning",
            "message": "System resources within normal limits" if system_healthy else "High resource usage detected",
            "metrics": {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "disk_percent": disk.percent
            },
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
        if not system_healthy:
            health_status["status"] = "warning"
            
    except Exception as e:
        health_status["checks"]["system"] = {
            "status": "unhealthy",
            "message": f"System check failed: {str(e)}",
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        health_status["status"] = "unhealthy"
    
    # Calculate summary
    total_checks = len(health_status["checks"])
    healthy_checks = sum(1 for check in health_status["checks"].values() if check["status"] == "healthy")
    unhealthy_checks = sum(1 for check in health_status["checks"].values() if check["status"] == "unhealthy")
    
    health_status["summary"] = {
        "total_checks": total_checks,
        "healthy_checks": healthy_checks,
        "unhealthy_checks": unhealthy_checks,
        "response_time_ms": round((time.time() - start_time) * 1000, 2)
    }
    
    return health_status


@router.get("/services")
async def service_health_checks() -> Dict[str, Any]:
    """
    Check health of all external services using ServiceURLManager.
    
    Returns:
        Health status for all configured services
    """
    url_manager = CyberSecurityServiceURLManager(settings.ENVIRONMENT)
    health_urls = url_manager.health_check_urls()
    
    async def check_service_health(session: aiohttp.ClientSession, name: str, url: str) -> Dict[str, Any]:
        """Check health of a single service."""
        start_time = time.time()
        try:
            async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                response_time = round((time.time() - start_time) * 1000, 2)
                
                if response.status == 200:
                    try:
                        data = await response.json()
                        return {
                            "name": name,
                            "status": "healthy",
                            "url": url,
                            "status_code": response.status,
                            "response_time_ms": response_time,
                            "data": data,
                            "timestamp": datetime.now(timezone.utc).isoformat()
                        }
                    except:
                        return {
                            "name": name,
                            "status": "healthy",
                            "url": url,
                            "status_code": response.status,
                            "response_time_ms": response_time,
                            "timestamp": datetime.now(timezone.utc).isoformat()
                        }
                else:
                    return {
                        "name": name,
                        "status": "unhealthy",
                        "url": url,
                        "status_code": response.status,
                        "response_time_ms": response_time,
                        "error": f"HTTP {response.status}",
                        "timestamp": datetime.now(timezone.utc).isoformat()
                    }
        except Exception as e:
            response_time = round((time.time() - start_time) * 1000, 2)
            return {
                "name": name,
                "status": "error",
                "url": url,
                "error": str(e),
                "response_time_ms": response_time,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    # Check all services concurrently
    async with aiohttp.ClientSession() as session:
        tasks = [
            check_service_health(session, name, url)
            for name, url in health_urls.items()
        ]
        results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # Process results
    service_checks = {}
    overall_status = "healthy"
    
    for result in results:
        if isinstance(result, dict):
            service_name = result["name"]
            service_checks[service_name] = result
            
            if result["status"] in ["unhealthy", "error"]:
                overall_status = "unhealthy"
        else:
            # Handle exceptions
            overall_status = "error"
    
    return {
        "status": overall_status,
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "environment": settings.ENVIRONMENT,
        "services": service_checks,
        "summary": {
            "total_services": len(health_urls),
            "healthy_services": sum(1 for s in service_checks.values() if s["status"] == "healthy"),
            "unhealthy_services": sum(1 for s in service_checks.values() if s["status"] in ["unhealthy", "error"])
        }
    }


@router.get("/database")
async def database_health_check(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    Detailed database health check.
    
    Returns:
        Database connection status and performance metrics
    """
    start_time = time.time()
    
    try:
        # Basic connection test
        db.execute(text("SELECT 1"))
        
        # Check database version
        version_result = db.execute(text("SELECT version()")).fetchone()
        db_version = version_result[0] if version_result else "Unknown"
        
        # Check active connections
        connections_result = db.execute(text(
            "SELECT count(*) FROM pg_stat_activity WHERE state = 'active'"
        )).fetchone()
        active_connections = connections_result[0] if connections_result else 0
        
        # Check database size
        size_result = db.execute(text(
            "SELECT pg_size_pretty(pg_database_size(current_database()))"
        )).fetchone()
        db_size = size_result[0] if size_result else "Unknown"
        
        response_time = round((time.time() - start_time) * 1000, 2)
        
        return {
            "status": "healthy",
            "message": "Database is healthy",
            "metrics": {
                "version": db_version,
                "active_connections": active_connections,
                "database_size": db_size,
                "response_time_ms": response_time
            },
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        response_time = round((time.time() - start_time) * 1000, 2)
        return {
            "status": "unhealthy",
            "message": f"Database health check failed: {str(e)}",
            "metrics": {
                "response_time_ms": response_time
            },
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


@router.get("/frameworks")
async def frameworks_health_check(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    Check health of framework data and services.
    
    Returns:
        Status of all framework implementations
    """
    framework_status = {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "frameworks": {}
    }
    
    frameworks = ["ISF", "NIST_CSF_2", "ISO_27001", "CIS_CONTROLS"]
    
    for framework in frameworks:
        try:
            # Check if framework tables exist and have data
            if framework == "ISF":
                result = db.execute(text("SELECT COUNT(*) FROM isf_controls")).fetchone()
                count = result[0] if result else 0
            elif framework == "NIST_CSF_2":
                result = db.execute(text("SELECT COUNT(*) FROM nist_csf_subcategories")).fetchone()
                count = result[0] if result else 0
            elif framework == "ISO_27001":
                result = db.execute(text("SELECT COUNT(*) FROM iso_27001_controls")).fetchone()
                count = result[0] if result else 0
            elif framework == "CIS_CONTROLS":
                result = db.execute(text("SELECT COUNT(*) FROM cis_safeguards")).fetchone()
                count = result[0] if result else 0
            else:
                count = 0
            
            framework_status["frameworks"][framework] = {
                "status": "healthy" if count > 0 else "warning",
                "message": f"Framework has {count} elements" if count > 0 else "No framework data found",
                "element_count": count,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            
            if count == 0:
                framework_status["status"] = "warning"
                
        except Exception as e:
            framework_status["frameworks"][framework] = {
                "status": "error",
                "message": f"Framework check failed: {str(e)}",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
            framework_status["status"] = "unhealthy"
    
    return framework_status


@router.get("/readiness")
async def readiness_check(db: Session = Depends(get_db)) -> Dict[str, Any]:
    """
    Kubernetes-style readiness probe.
    
    Returns:
        Simple ready/not ready status for load balancer
    """
    try:
        # Check database connection
        db.execute(text("SELECT 1"))
        
        # Check if essential framework data exists
        isf_count = db.execute(text("SELECT COUNT(*) FROM isf_controls")).fetchone()[0]
        
        if isf_count > 0:
            return {
                "status": "ready",
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        else:
            raise HTTPException(
                status_code=503,
                detail="Service not ready - missing framework data"
            )
            
    except Exception as e:
        raise HTTPException(
            status_code=503,
            detail=f"Service not ready: {str(e)}"
        )


@router.get("/liveness")
async def liveness_check() -> Dict[str, Any]:
    """
    Kubernetes-style liveness probe.
    
    Returns:
        Simple alive status for container orchestration
    """
    return {
        "status": "alive",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "uptime_seconds": time.time() - psutil.boot_time()
    }
