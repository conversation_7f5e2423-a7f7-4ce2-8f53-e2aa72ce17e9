"""
Configuration API endpoints.

This module provides endpoints for retrieving application configuration,
service URLs, and feature flags for frontend applications.
"""

from typing import Dict, Any
from fastapi import APIRouter, Depends, HTTPException
from api.core.config import settings
from api.utils.service_url_manager import CyberSecurityServiceURLManager

router = APIRouter(prefix="/api/v1/config", tags=["configuration"])


@router.get("/urls")
async def get_service_urls() -> Dict[str, Any]:
    """
    Get all service URLs for frontend configuration.
    
    Returns comprehensive service URL configuration including:
    - API base URLs
    - Health check endpoints
    - Framework-specific endpoints
    - Search and analytics endpoints
    - Authentication and admin endpoints
    
    Returns:
        Dictionary containing all service URLs organized by category
    """
    try:
        return settings.get_all_service_urls()
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve service URLs: {str(e)}"
        )


@router.get("/urls/health")
async def get_health_check_urls() -> Dict[str, str]:
    """
    Get health check URLs for all services.
    
    Returns:
        Dictionary mapping service names to health check URLs
    """
    try:
        return settings.url_manager.health_check_urls()
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve health check URLs: {str(e)}"
        )


@router.get("/urls/frameworks")
async def get_framework_urls() -> Dict[str, Dict[str, str]]:
    """
    Get all framework-specific URLs.
    
    Returns:
        Nested dictionary of framework URLs organized by framework and operation
    """
    try:
        return settings.url_manager.get_all_framework_urls()
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve framework URLs: {str(e)}"
        )


@router.get("/urls/framework/{framework}")
async def get_framework_specific_urls(framework: str) -> Dict[str, str]:
    """
    Get URLs for a specific framework.
    
    Args:
        framework: Framework name (isf, nist_csf, iso_27001, cis_controls)
        
    Returns:
        Dictionary of URLs for the specified framework
    """
    try:
        operations = ['list', 'import', 'export']
        urls = {}
        
        for operation in operations:
            try:
                urls[operation] = settings.get_framework_endpoint(framework, operation)
            except ValueError:
                # Skip if endpoint doesn't exist
                continue
                
        if not urls:
            raise HTTPException(
                status_code=404,
                detail=f"No URLs found for framework: {framework}"
            )
            
        return urls
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve URLs for framework {framework}: {str(e)}"
        )


@router.get("/settings")
async def get_application_settings() -> Dict[str, Any]:
    """
    Get public application settings for frontend configuration.
    
    Returns:
        Dictionary containing public configuration settings
    """
    return {
        "app_name": settings.APP_NAME,
        "app_version": settings.APP_VERSION,
        "environment": settings.ENVIRONMENT,
        "debug": settings.DEBUG,
        "features": {
            "rate_limiting": settings.RATE_LIMIT_ENABLED,
            "caching": settings.CACHE_ENABLED,
            "query_optimization": settings.ENABLE_QUERY_OPTIMIZATION,
            "response_compression": settings.ENABLE_RESPONSE_COMPRESSION,
            "security_headers": settings.ENABLE_SECURITY_HEADERS,
            "csrf_protection": settings.ENABLE_CSRF_PROTECTION,
            "strict_validation": settings.ENABLE_STRICT_VALIDATION
        },
        "limits": {
            "max_search_results": settings.MAX_SEARCH_RESULTS,
            "default_page_size": settings.DEFAULT_PAGE_SIZE,
            "max_page_size": settings.MAX_PAGE_SIZE,
            "max_file_size": settings.MAX_FILE_SIZE,
            "max_import_records": settings.MAX_IMPORT_RECORDS
        },
        "frameworks": {
            "isf_default_version": settings.ISF_DEFAULT_VERSION,
            "nist_csf_default_version": settings.NIST_CSF_DEFAULT_VERSION,
            "mitre_attack_default_version": settings.MITRE_ATTACK_DEFAULT_VERSION
        },
        "thresholds": {
            "min_confidence": settings.MIN_CONFIDENCE_THRESHOLD,
            "min_effectiveness": settings.MIN_EFFECTIVENESS_THRESHOLD
        }
    }


@router.get("/features")
async def get_feature_flags() -> Dict[str, bool]:
    """
    Get feature flags for conditional frontend functionality.
    
    Returns:
        Dictionary of feature flags
    """
    return {
        "rate_limiting": settings.RATE_LIMIT_ENABLED,
        "caching": settings.CACHE_ENABLED,
        "query_optimization": settings.ENABLE_QUERY_OPTIMIZATION,
        "response_compression": settings.ENABLE_RESPONSE_COMPRESSION,
        "security_headers": settings.ENABLE_SECURITY_HEADERS,
        "csrf_protection": settings.ENABLE_CSRF_PROTECTION,
        "strict_validation": settings.ENABLE_STRICT_VALIDATION,
        "https_redirect": settings.ENABLE_HTTPS_REDIRECT,
        "framework_validation": settings.VALIDATE_FRAMEWORK_REFERENCES
    }


@router.get("/environment")
async def get_environment_info() -> Dict[str, Any]:
    """
    Get current environment information.
    
    Returns:
        Environment configuration details
    """
    try:
        env_info = settings.url_manager.get_environment_info()
        return {
            "environment": settings.ENVIRONMENT,
            "domain": env_info.get("domain"),
            "protocol": env_info.get("protocol"),
            "services": list(env_info.get("services", {}).keys()),
            "available_environments": settings.url_manager.list_environments()
        }
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to retrieve environment information: {str(e)}"
        )


@router.post("/validate-urls")
async def validate_service_urls() -> Dict[str, Any]:
    """
    Validate all service URLs and health checks.
    
    Returns:
        Validation results for all services
    """
    import aiohttp
    import asyncio
    
    async def check_url(session: aiohttp.ClientSession, name: str, url: str) -> Dict[str, Any]:
        """Check if a URL is accessible."""
        try:
            async with session.get(url, timeout=aiohttp.ClientTimeout(total=10)) as response:
                return {
                    "name": name,
                    "url": url,
                    "status": "healthy" if response.status == 200 else "unhealthy",
                    "status_code": response.status,
                    "response_time": None  # Could add timing if needed
                }
        except Exception as e:
            return {
                "name": name,
                "url": url,
                "status": "error",
                "error": str(e),
                "status_code": None,
                "response_time": None
            }
    
    try:
        health_urls = settings.url_manager.health_check_urls()
        
        async with aiohttp.ClientSession() as session:
            tasks = [
                check_url(session, name, url)
                for name, url in health_urls.items()
            ]
            results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # Process results
        validation_results = {
            "timestamp": "2024-01-01T00:00:00Z",  # Would use actual timestamp
            "environment": settings.ENVIRONMENT,
            "total_services": len(health_urls),
            "healthy_services": sum(1 for r in results if isinstance(r, dict) and r.get("status") == "healthy"),
            "services": [r for r in results if isinstance(r, dict)]
        }
        
        return validation_results
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to validate service URLs: {str(e)}"
        )
