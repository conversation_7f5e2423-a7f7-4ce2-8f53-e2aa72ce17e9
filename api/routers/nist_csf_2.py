"""
NIST CSF 2.0 API Router.

This module provides REST API endpoints for the NIST Cybersecurity Framework 2.0,
including functions, categories, subcategories, profiles, and assessments.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime
import json

from api.database import get_db
from api.auth.dependencies import get_current_user, require_admin
from api.models.user import User
from api.models.nist_csf_2 import (
    NISTCSFVersion, NISTCSFFunction, NISTCSFCategory, 
    NISTCSFSubcategory, NISTCSFImplementationExample,
    NISTCSFProfile, NISTCSFAssessment
)
from api.schemas.nist_csf_2 import (
    NISTCSFVersionListResponse, NISTCSFVersionResponse,
    NISTCSFFunctionListResponse, NISTCSFFunctionResponse,
    NISTCSFCategoryListResponse, NISTCSFCategoryResponse,
    NISTCSFSubcategoryListResponse, NISTCSFSubcategoryResponse,
    NISTCSFSubcategoryUpdate, NISTCSFSearchRequest, NISTCSFSearchResponse,
    NISTCSFExportRequest, NISTCSFExportResponse,
    NISTCSFProfileCreate, NISTCSFProfileUpdate, NISTCSFProfileResponse, NISTCSFProfileListResponse,
    NISTCSFAssessmentCreate, NISTCSFAssessmentUpdate, NISTCSFAssessmentResponse, NISTCSFAssessmentListResponse
)
from api.services.nist_csf_2_import import NISTCSFImportService

router = APIRouter()


@router.get("/versions", response_model=NISTCSFVersionListResponse)
async def get_nist_csf_versions(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Items per page"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get NIST CSF versions with pagination.
    
    Returns a paginated list of NIST Cybersecurity Framework versions
    with release information and current version status.
    """
    # Calculate offset
    offset = (page - 1) * page_size
    
    # Query versions
    query = db.query(NISTCSFVersion).filter(NISTCSFVersion.deleted_at.is_(None))
    total = query.count()
    versions = query.offset(offset).limit(page_size).all()
    
    return NISTCSFVersionListResponse(
        versions=[NISTCSFVersionResponse.from_orm(v) for v in versions],
        total=total,
        page=page,
        page_size=page_size,
        total_pages=(total + page_size - 1) // page_size
    )


@router.get("/versions/current", response_model=NISTCSFVersionResponse)
async def get_current_nist_csf_version(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get the current NIST CSF version.
    
    Returns the currently active version of the NIST Cybersecurity Framework
    with all metadata and documentation links.
    """
    version = db.query(NISTCSFVersion).filter(
        NISTCSFVersion.is_current == True,
        NISTCSFVersion.deleted_at.is_(None)
    ).first()
    
    if not version:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No current NIST CSF version found"
        )
    
    return NISTCSFVersionResponse.from_orm(version)


@router.get("/versions/{version_id}", response_model=NISTCSFVersionResponse)
async def get_nist_csf_version(
    version_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get a specific NIST CSF version by ID.
    
    Returns detailed information about a specific NIST CSF version
    including release notes and documentation links.
    """
    version = db.query(NISTCSFVersion).filter(
        NISTCSFVersion.id == version_id,
        NISTCSFVersion.deleted_at.is_(None)
    ).first()
    
    if not version:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="NIST CSF version not found"
        )
    
    return NISTCSFVersionResponse.from_orm(version)


@router.get("/functions", response_model=NISTCSFFunctionListResponse)
async def get_nist_csf_functions(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Items per page"),
    version: Optional[str] = Query(None, description="Filter by framework version"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get NIST CSF functions with pagination and filtering.
    
    Returns the six core functions of the NIST CSF 2.0:
    Govern, Identify, Protect, Detect, Respond, Recover
    """
    # Calculate offset
    offset = (page - 1) * page_size
    
    # Build query
    query = db.query(NISTCSFFunction).filter(NISTCSFFunction.deleted_at.is_(None))
    
    # Filter by version if specified
    if version:
        query = query.join(NISTCSFVersion).filter(NISTCSFVersion.version == version)
    
    # Order by index
    query = query.order_by(NISTCSFFunction.order_index)
    
    # Get total count and paginated results
    total = query.count()
    functions = query.offset(offset).limit(page_size).all()
    
    return NISTCSFFunctionListResponse(
        functions=[NISTCSFFunctionResponse.from_orm(f) for f in functions],
        total=total,
        page=page,
        page_size=page_size,
        total_pages=(total + page_size - 1) // page_size
    )


@router.get("/categories", response_model=NISTCSFCategoryListResponse)
async def get_nist_csf_categories(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Items per page"),
    function_id: Optional[str] = Query(None, description="Filter by function ID"),
    version: Optional[str] = Query(None, description="Filter by framework version"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get NIST CSF categories with pagination and filtering.
    
    Returns categories within each function that group related
    cybersecurity outcomes with optional filtering by function.
    """
    # Calculate offset
    offset = (page - 1) * page_size
    
    # Build query
    query = db.query(NISTCSFCategory).filter(NISTCSFCategory.deleted_at.is_(None))
    
    # Filter by function if specified
    if function_id:
        query = query.join(NISTCSFFunction).filter(NISTCSFFunction.function_id == function_id)
    
    # Filter by version if specified
    if version:
        query = query.join(NISTCSFVersion).filter(NISTCSFVersion.version == version)
    
    # Order by function and category index
    query = query.join(NISTCSFFunction).order_by(
        NISTCSFFunction.order_index,
        NISTCSFCategory.order_index
    )
    
    # Get total count and paginated results
    total = query.count()
    categories = query.offset(offset).limit(page_size).all()
    
    return NISTCSFCategoryListResponse(
        categories=[NISTCSFCategoryResponse.from_orm(c) for c in categories],
        total=total,
        page=page,
        page_size=page_size,
        total_pages=(total + page_size - 1) // page_size
    )


@router.get("/subcategories", response_model=NISTCSFSubcategoryListResponse)
async def get_nist_csf_subcategories(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Items per page"),
    function_id: Optional[str] = Query(None, description="Filter by function ID"),
    category_id: Optional[str] = Query(None, description="Filter by category ID"),
    version: Optional[str] = Query(None, description="Filter by framework version"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get NIST CSF subcategories with pagination and filtering.
    
    Returns specific cybersecurity outcomes that support the achievement
    of each category with optional filtering by function or category.
    """
    # Calculate offset
    offset = (page - 1) * page_size
    
    # Build query
    query = db.query(NISTCSFSubcategory).filter(NISTCSFSubcategory.deleted_at.is_(None))
    
    # Filter by function if specified
    if function_id:
        query = query.join(NISTCSFFunction).filter(NISTCSFFunction.function_id == function_id)
    
    # Filter by category if specified
    if category_id:
        query = query.join(NISTCSFCategory).filter(NISTCSFCategory.category_id == category_id)
    
    # Filter by version if specified
    if version:
        query = query.join(NISTCSFVersion).filter(NISTCSFVersion.version == version)
    
    # Order by function, category, and subcategory index
    query = query.join(NISTCSFFunction).join(NISTCSFCategory).order_by(
        NISTCSFFunction.order_index,
        NISTCSFCategory.order_index,
        NISTCSFSubcategory.order_index
    )
    
    # Get total count and paginated results
    total = query.count()
    subcategories = query.offset(offset).limit(page_size).all()
    
    return NISTCSFSubcategoryListResponse(
        subcategories=[NISTCSFSubcategoryResponse.from_orm(s) for s in subcategories],
        total=total,
        page=page,
        page_size=page_size,
        total_pages=(total + page_size - 1) // page_size
    )


@router.get("/subcategories/{subcategory_id}", response_model=NISTCSFSubcategoryResponse)
async def get_nist_csf_subcategory(
    subcategory_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get a specific NIST CSF subcategory by ID.
    
    Returns detailed information about a specific subcategory including
    implementation guidance, examples, and informative references.
    """
    subcategory = db.query(NISTCSFSubcategory).filter(
        NISTCSFSubcategory.id == subcategory_id,
        NISTCSFSubcategory.deleted_at.is_(None)
    ).first()
    
    if not subcategory:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="NIST CSF subcategory not found"
        )
    
    return NISTCSFSubcategoryResponse.from_orm(subcategory)


@router.put("/subcategories/{subcategory_id}", response_model=NISTCSFSubcategoryResponse)
async def update_nist_csf_subcategory(
    subcategory_id: int,
    subcategory_data: NISTCSFSubcategoryUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """
    Update a NIST CSF subcategory.
    
    Updates subcategory information including implementation guidance,
    examples, and references. Requires admin privileges.
    """
    subcategory = db.query(NISTCSFSubcategory).filter(
        NISTCSFSubcategory.id == subcategory_id,
        NISTCSFSubcategory.deleted_at.is_(None)
    ).first()
    
    if not subcategory:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="NIST CSF subcategory not found"
        )
    
    # Update fields
    for field, value in subcategory_data.dict(exclude_unset=True).items():
        if hasattr(subcategory, field):
            setattr(subcategory, field, value)
    
    subcategory.updated_at = datetime.utcnow()
    
    try:
        db.commit()
        db.refresh(subcategory)
        return NISTCSFSubcategoryResponse.from_orm(subcategory)
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update subcategory: {str(e)}"
        )


@router.post("/search", response_model=NISTCSFSearchResponse)
async def search_nist_csf(
    search_request: NISTCSFSearchRequest,
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Items per page"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Search NIST CSF subcategories with advanced filtering.
    
    Performs full-text search across subcategories with filtering by
    function, category, version, and other criteria.
    """
    from sqlalchemy import or_
    
    # Build query
    query = db.query(NISTCSFSubcategory).filter(NISTCSFSubcategory.deleted_at.is_(None))
    
    # Apply filters
    if search_request.function_id:
        query = query.join(NISTCSFFunction).filter(NISTCSFFunction.function_id == search_request.function_id)
    
    if search_request.category_id:
        query = query.join(NISTCSFCategory).filter(NISTCSFCategory.category_id == search_request.category_id)
    
    if search_request.version:
        query = query.join(NISTCSFVersion).filter(NISTCSFVersion.version == search_request.version)
    
    # Text search
    if search_request.query:
        search_term = f"%{search_request.query}%"
        query = query.filter(
            or_(
                NISTCSFSubcategory.subcategory_id.ilike(search_term),
                NISTCSFSubcategory.name.ilike(search_term),
                NISTCSFSubcategory.description.ilike(search_term),
                NISTCSFSubcategory.implementation_guidance.ilike(search_term)
            )
        )
    
    # Get total count
    total = query.count()
    
    # Apply pagination
    offset = (page - 1) * page_size
    subcategories = query.offset(offset).limit(page_size).all()
    
    return NISTCSFSearchResponse(
        results=[NISTCSFSubcategoryResponse.from_orm(s) for s in subcategories],
        total=total,
        page=page,
        page_size=page_size,
        total_pages=(total + page_size - 1) // page_size,
        search_metadata={
            "query": search_request.query,
            "filters_applied": len([f for f in search_request.dict().values() if f is not None]),
            "execution_time": 0.1  # Mock execution time
        }
    )


@router.post("/export", response_model=NISTCSFExportResponse)
async def export_nist_csf_data(
    export_request: NISTCSFExportRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Export NIST CSF data in various formats.
    
    Exports framework data including functions, categories, subcategories,
    and optional profiles/assessments in JSON, CSV, or Excel format.
    """
    try:
        # Build query based on export request
        query = db.query(NISTCSFSubcategory).filter(NISTCSFSubcategory.deleted_at.is_(None))
        
        # Apply filters
        if export_request.version:
            query = query.join(NISTCSFVersion).filter(NISTCSFVersion.version == export_request.version)
        
        if export_request.functions:
            query = query.join(NISTCSFFunction).filter(NISTCSFFunction.function_id.in_(export_request.functions))
        
        if export_request.categories:
            query = query.join(NISTCSFCategory).filter(NISTCSFCategory.category_id.in_(export_request.categories))
        
        # Get data
        subcategories = query.all()
        
        # Format data based on export format
        if export_request.format.lower() == "json":
            export_data = [
                {
                    "subcategory_id": s.subcategory_id,
                    "name": s.name,
                    "description": s.description,
                    "implementation_guidance": s.implementation_guidance,
                    "informative_references": s.informative_references
                }
                for s in subcategories
            ]
            data_str = json.dumps(export_data, indent=2)
        else:
            # For CSV/Excel, create a simplified format
            data_str = "subcategory_id,name,description\n"
            for s in subcategories:
                data_str += f'"{s.subcategory_id}","{s.name}","{s.description or ""}"\n'
        
        return NISTCSFExportResponse(
            success=True,
            format=export_request.format,
            data=data_str,
            file_size=len(data_str.encode('utf-8')),
            record_count=len(subcategories),
            export_metadata={
                "version": export_request.version,
                "functions_included": export_request.functions or [],
                "categories_included": export_request.categories or [],
                "export_timestamp": datetime.utcnow().isoformat()
            },
            processing_time=0.2  # Mock processing time
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Export failed: {str(e)}"
        )


@router.post("/import/nist-csf-2-0", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
async def import_nist_csf_2_0_data(
    db: Session = Depends(get_db),
    current_user: User = Depends(require_admin)
):
    """
    Import official NIST CSF 2.0 framework data.
    
    Imports the complete NIST CSF 2.0 framework including all functions,
    categories, and subcategories. This is a one-time setup operation
    that requires admin privileges.
    """
    try:
        import_service = NISTCSFImportService(db)
        result = import_service.import_nist_csf_2_0()
        
        return {
            "success": True,
            "message": "NIST CSF 2.0 data imported successfully",
            "data": result
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"NIST CSF 2.0 import failed: {str(e)}"
        )
