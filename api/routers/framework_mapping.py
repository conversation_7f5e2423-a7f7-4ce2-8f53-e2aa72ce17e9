"""
Framework Mapping API Router.

This module provides REST API endpoints for managing cross-framework mappings
between different cybersecurity frameworks.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any

from api.database import get_db
from api.auth.dependencies import get_current_user, require_admin
from api.models.user import User
from api.models.framework_mapping import FrameworkMapping, MappingSet, MappingValidation
from api.schemas.framework_mapping import (
    FrameworkMappingCreate, FrameworkMappingUpdate, FrameworkMappingResponse,
    FrameworkMappingListResponse, MappingSetCreate, MappingSetUpdate, 
    MappingSetResponse, MappingSetListResponse, MappingValidationCreate,
    MappingValidationResponse, MappingSearchRequest, MappingSearchResponse,
    BulkMappingCreate, BulkMappingResponse, MappingAnalytics
)
from api.services.framework_mapping import FrameworkMappingService

router = APIRouter()


@router.post("/mappings", response_model=FrameworkMappingResponse, status_code=status.HTTP_201_CREATED)
async def create_mapping(
    mapping_data: FrameworkMappingCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new framework mapping.
    
    Creates a mapping between controls from different cybersecurity frameworks
    with confidence scoring and effectiveness metrics.
    """
    try:
        service = FrameworkMappingService(db)
        mapping = service.create_mapping(mapping_data, current_user.id)
        return FrameworkMappingResponse.from_orm(mapping)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create mapping: {str(e)}"
        )


@router.get("/mappings", response_model=FrameworkMappingListResponse)
async def get_mappings(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Items per page"),
    source_framework: Optional[str] = Query(None, description="Filter by source framework"),
    target_framework: Optional[str] = Query(None, description="Filter by target framework"),
    is_validated: Optional[bool] = Query(None, description="Filter by validation status"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get framework mappings with pagination and filtering.
    
    Returns a paginated list of framework mappings with optional filtering
    by source/target framework and validation status.
    """
    # Calculate offset
    offset = (page - 1) * page_size
    
    # Build query
    query = db.query(FrameworkMapping).filter(FrameworkMapping.deleted_at.is_(None))
    
    # Apply filters
    if source_framework:
        query = query.filter(FrameworkMapping.source_framework == source_framework)
    if target_framework:
        query = query.filter(FrameworkMapping.target_framework == target_framework)
    if is_validated is not None:
        query = query.filter(FrameworkMapping.is_validated == is_validated)
    
    # Get total count and paginated results
    total = query.count()
    mappings = query.offset(offset).limit(page_size).all()
    
    return FrameworkMappingListResponse(
        mappings=[FrameworkMappingResponse.from_orm(m) for m in mappings],
        total=total,
        page=page,
        page_size=page_size,
        total_pages=(total + page_size - 1) // page_size
    )


@router.get("/mappings/{mapping_id}", response_model=FrameworkMappingResponse)
async def get_mapping(
    mapping_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get a specific framework mapping by ID.
    
    Returns detailed information about a specific framework mapping including
    all metadata, confidence scores, and validation status.
    """
    mapping = db.query(FrameworkMapping).filter(
        FrameworkMapping.id == mapping_id,
        FrameworkMapping.deleted_at.is_(None)
    ).first()
    
    if not mapping:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Framework mapping not found"
        )
    
    return FrameworkMappingResponse.from_orm(mapping)


@router.put("/mappings/{mapping_id}", response_model=FrameworkMappingResponse)
async def update_mapping(
    mapping_id: int,
    mapping_data: FrameworkMappingUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update an existing framework mapping.
    
    Updates mapping metadata, confidence scores, effectiveness metrics,
    and other properties with full audit trail tracking.
    """
    try:
        service = FrameworkMappingService(db)
        mapping = service.update_mapping(mapping_id, mapping_data, current_user.id)
        return FrameworkMappingResponse.from_orm(mapping)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update mapping: {str(e)}"
        )


@router.delete("/mappings/{mapping_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_mapping(
    mapping_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Delete a framework mapping.
    
    Performs soft deletion of the mapping with audit trail tracking.
    Requires appropriate permissions.
    """
    try:
        service = FrameworkMappingService(db)
        service.delete_mapping(mapping_id, current_user.id)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete mapping: {str(e)}"
        )


@router.post("/mappings/search", response_model=MappingSearchResponse)
async def search_mappings(
    search_request: MappingSearchRequest,
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Items per page"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Search framework mappings with advanced filtering.
    
    Performs full-text search across mappings with filtering by framework,
    mapping type, confidence level, and other criteria.
    """
    try:
        service = FrameworkMappingService(db)
        mappings, total = service.search_mappings(search_request, page, page_size)
        
        return MappingSearchResponse(
            results=[FrameworkMappingResponse.from_orm(m) for m in mappings],
            total=total,
            page=page,
            page_size=page_size,
            total_pages=(total + page_size - 1) // page_size,
            search_metadata={
                "query": search_request.query,
                "filters_applied": len([f for f in search_request.dict().values() if f is not None]),
                "execution_time": 0.1  # Mock execution time
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Search failed: {str(e)}"
        )


@router.post("/mappings/bulk", response_model=BulkMappingResponse, status_code=status.HTTP_201_CREATED)
async def create_bulk_mappings(
    bulk_data: BulkMappingCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Create multiple framework mappings in bulk.
    
    Creates multiple mappings in a single transaction with optional
    immediate validation and mapping set association.
    """
    try:
        service = FrameworkMappingService(db)
        created_mappings = []
        errors = []
        
        for i, mapping_data in enumerate(bulk_data.mappings):
            try:
                # Set mapping set if specified
                if bulk_data.mapping_set_id:
                    mapping_data.mapping_set_id = bulk_data.mapping_set_id
                
                mapping = service.create_mapping(mapping_data, current_user.id)
                created_mappings.append(mapping)
                
                # Validate immediately if requested
                if bulk_data.validate_immediately:
                    # Auto-approve for bulk operations (could be configurable)
                    validation_data = MappingValidationCreate(
                        mapping_id=mapping.id,
                        validation_status="approved",
                        comments="Auto-validated during bulk import"
                    )
                    service.validate_mapping(validation_data, current_user.id)
                
            except Exception as e:
                errors.append({
                    "index": i,
                    "mapping_data": mapping_data.dict(),
                    "error": str(e)
                })
        
        return BulkMappingResponse(
            success=len(errors) == 0,
            created_count=len(created_mappings),
            failed_count=len(errors),
            created_mappings=[FrameworkMappingResponse.from_orm(m) for m in created_mappings],
            errors=errors,
            processing_time=0.5  # Mock processing time
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Bulk creation failed: {str(e)}"
        )


@router.post("/mappings/{mapping_id}/validate", response_model=MappingValidationResponse, status_code=status.HTTP_201_CREATED)
async def validate_mapping(
    mapping_id: int,
    validation_data: MappingValidationCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Validate a framework mapping.
    
    Creates a validation record for the mapping with reviewer comments,
    confidence assessment, and quality scores.
    """
    try:
        # Set the mapping ID from the URL
        validation_data.mapping_id = mapping_id
        
        service = FrameworkMappingService(db)
        validation = service.validate_mapping(validation_data, current_user.id)
        return MappingValidationResponse.from_orm(validation)
        
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Validation failed: {str(e)}"
        )


@router.get("/analytics", response_model=MappingAnalytics)
async def get_mapping_analytics(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get framework mapping analytics and statistics.
    
    Returns comprehensive analytics including mapping counts by framework,
    validation statistics, confidence scores, and recent activity.
    """
    try:
        service = FrameworkMappingService(db)
        analytics = service.get_mapping_analytics()
        
        return MappingAnalytics(
            total_mappings=analytics["total_mappings"],
            mappings_by_framework=analytics["mappings_by_framework"],
            mappings_by_type=analytics["mappings_by_type"],
            mappings_by_confidence={},  # Would be populated from analytics
            validation_statistics=analytics["validation_statistics"],
            average_confidence_score=analytics["average_confidence_score"],
            coverage_statistics={},  # Would be calculated
            recent_activity=[]  # Would be populated from audit trail
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Analytics failed: {str(e)}"
        )


# Mapping Set endpoints
@router.post("/mapping-sets", response_model=MappingSetResponse, status_code=status.HTTP_201_CREATED)
async def create_mapping_set(
    set_data: MappingSetCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new mapping set.
    
    Creates a collection for organizing related mappings with metadata
    and statistics tracking.
    """
    try:
        service = FrameworkMappingService(db)
        mapping_set = service.create_mapping_set(set_data, current_user.id)
        return MappingSetResponse.from_orm(mapping_set)
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create mapping set: {str(e)}"
        )


@router.get("/mapping-sets", response_model=MappingSetListResponse)
async def get_mapping_sets(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(10, ge=1, le=100, description="Items per page"),
    source_framework: Optional[str] = Query(None, description="Filter by source framework"),
    target_framework: Optional[str] = Query(None, description="Filter by target framework"),
    is_official: Optional[bool] = Query(None, description="Filter by official status"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get mapping sets with pagination and filtering.
    
    Returns a paginated list of mapping sets with optional filtering
    by framework and official status.
    """
    # Calculate offset
    offset = (page - 1) * page_size
    
    # Build query
    query = db.query(MappingSet).filter(MappingSet.deleted_at.is_(None))
    
    # Apply filters
    if source_framework:
        query = query.filter(MappingSet.source_framework == source_framework)
    if target_framework:
        query = query.filter(MappingSet.target_framework == target_framework)
    if is_official is not None:
        query = query.filter(MappingSet.is_official == is_official)
    
    # Get total count and paginated results
    total = query.count()
    mapping_sets = query.offset(offset).limit(page_size).all()
    
    return MappingSetListResponse(
        mapping_sets=[MappingSetResponse.from_orm(ms) for ms in mapping_sets],
        total=total,
        page=page,
        page_size=page_size,
        total_pages=(total + page_size - 1) // page_size
    )
