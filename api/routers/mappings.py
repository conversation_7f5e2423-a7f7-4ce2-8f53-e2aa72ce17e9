"""
Cross-Framework Mapping API Router.

This module implements REST API endpoints for cross-framework mapping operations
including intelligent suggestions, CRUD operations, analysis, and export capabilities.

Features:
- MITRE ATT&CK to ISF/NIST CSF mapping suggestions
- Mapping validation and quality assessment
- Effectiveness analysis and coverage reporting
- Bulk operations and advanced search
- Export in multiple formats including STIX
"""

from fastapi import APIRouter, Depends, HTTPException, status, Query, BackgroundTasks
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import json
from datetime import datetime

from api.database import get_db
from api.auth.dependencies import get_current_user, require_admin
from api.schemas.mappings import (
    MappingSuggestionResponse,
    MappingValidationResponse,
    EffectivenessAnalysisResponse,
    CrossFrameworkAnalysisResponse,
    MappingCreateRequest,
    MappingUpdateRequest,
    MappingSearchRequest,
    MappingSearchResponse,
    MappingResponse,
    MappingListResponse,
    MappingExportRequest,
    MappingExportResponse,
    BulkMappingSuggestionsRequest,
    BulkMappingSuggestionsResponse,
    MappingAnalysisRequest,
    MappingAnalysisResponse
)
from api.models.users import User
from api.services.mapping_algorithms import (
    MitreToISFMappingAlgorithm,
    MitreToNISTCSFMappingAlgorithm,
    ISFToNISTCSFMappingAlgorithm,
    EffectivenessScorer,
    MappingQualityAssessor,
    MLMappingPredictor
)
from api.services.export_services import STIXExportService, CrossFrameworkAnalysisExporter
from api.models.framework_mappings import MitreToISFMapping, MitreToNISTCSFMapping, ISFToNISTCSFMapping

router = APIRouter()


@router.get("/mitre-to-isf/{technique_id}/suggestions", response_model=MappingSuggestionResponse)
async def get_mitre_to_isf_suggestions(
    technique_id: str,
    include_effectiveness: bool = Query(True, description="Include effectiveness scores"),
    min_confidence: float = Query(0.7, ge=0.0, le=1.0, description="Minimum confidence threshold"),
    limit: int = Query(10, ge=1, le=50, description="Maximum number of suggestions"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get MITRE ATT&CK to ISF mapping suggestions.
    
    Returns intelligent mapping suggestions for a MITRE technique to ISF controls
    with effectiveness scores and confidence ratings.
    """
    try:
        # Get technique data (mock implementation)
        technique = {
            "technique_id": technique_id,
            "name": "Phishing" if technique_id == "T1566" else "Unknown Technique",
            "description": "Adversaries may send phishing messages to gain access to victim systems",
            "tactics": ["initial-access"],
            "platforms": ["Windows", "Linux", "macOS"]
        }
        
        # Get ISF controls (mock implementation)
        controls = [
            {
                "control_id": "EM1",
                "name": "Email Security",
                "description": "Implement email security controls to prevent malicious emails",
                "control_type": "technical",
                "maturity_level": "intermediate"
            },
            {
                "control_id": "AT1",
                "name": "Security Awareness Training",
                "description": "Train users to recognize and report phishing attempts",
                "control_type": "administrative",
                "maturity_level": "basic"
            }
        ]
        
        # Generate suggestions using mapping algorithm
        mapping_algorithm = MitreToISFMappingAlgorithm()
        suggestions = mapping_algorithm.suggest_mappings(technique, controls)
        
        # Filter by confidence threshold and limit
        filtered_suggestions = [
            s for s in suggestions 
            if s.confidence_score >= min_confidence
        ][:limit]
        
        return MappingSuggestionResponse(
            technique_id=technique_id,
            technique_name=technique["name"],
            suggestions=[
                {
                    "control_id": s.target_id,
                    "control_name": next(c["name"] for c in controls if c["control_id"] == s.target_id),
                    "mapping_type": s.mapping_type,
                    "confidence_score": s.confidence_score,
                    "effectiveness_score": s.effectiveness_score,
                    "rationale": s.rationale,
                    "semantic_similarity": s.semantic_similarity,
                    "keyword_overlap": s.keyword_overlap
                }
                for s in filtered_suggestions
            ],
            metadata={
                "total_suggestions": len(suggestions),
                "filtered_suggestions": len(filtered_suggestions),
                "min_confidence_applied": min_confidence,
                "algorithm_version": "1.0"
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate suggestions: {str(e)}"
        )


@router.get("/mitre-to-nist-csf/{technique_id}/suggestions", response_model=MappingSuggestionResponse)
async def get_mitre_to_nist_csf_suggestions(
    technique_id: str,
    nist_csf_version: str = Query("2.0", description="NIST CSF version"),
    functions: Optional[str] = Query(None, description="Comma-separated function IDs"),
    include_subcategory_details: bool = Query(True, description="Include subcategory details"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get MITRE ATT&CK to NIST CSF mapping suggestions.
    
    Returns intelligent mapping suggestions for a MITRE technique to NIST CSF subcategories
    with optional function filtering.
    """
    try:
        # Parse function filter
        function_filter = functions.split(",") if functions else None
        
        # Get technique data (mock implementation)
        technique = {
            "technique_id": technique_id,
            "name": "Phishing" if technique_id == "T1566" else "Unknown Technique",
            "description": "Adversaries may send phishing messages to gain access to victim systems",
            "tactics": ["initial-access"]
        }
        
        # Get NIST CSF subcategories (mock implementation)
        subcategories = [
            {
                "subcategory_id": "PR.AT-01",
                "name": "All users are informed and trained",
                "function_id": "PR",
                "category_id": "PR.AT"
            },
            {
                "subcategory_id": "DE.CM-01",
                "name": "Networks and network services are monitored",
                "function_id": "DE",
                "category_id": "DE.CM"
            }
        ]
        
        # Filter by functions if specified
        if function_filter:
            subcategories = [
                s for s in subcategories 
                if s["function_id"] in function_filter
            ]
        
        # Generate suggestions using mapping algorithm
        mapping_algorithm = MitreToNISTCSFMappingAlgorithm()
        suggestions = mapping_algorithm.suggest_mappings(technique, subcategories)
        
        return MappingSuggestionResponse(
            technique_id=technique_id,
            technique_name=technique["name"],
            suggestions=[
                {
                    "subcategory_id": s.target_id,
                    "subcategory_name": next(sc["name"] for sc in subcategories if sc["subcategory_id"] == s.target_id),
                    "function_id": next(sc["function_id"] for sc in subcategories if sc["subcategory_id"] == s.target_id),
                    "category_id": next(sc["category_id"] for sc in subcategories if sc["subcategory_id"] == s.target_id),
                    "mapping_type": s.mapping_type,
                    "confidence_score": s.confidence_score,
                    "effectiveness_score": s.effectiveness_score,
                    "rationale": s.rationale
                }
                for s in suggestions
            ],
            metadata={
                "nist_csf_version": nist_csf_version,
                "functions_filtered": function_filter,
                "total_suggestions": len(suggestions)
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate NIST CSF suggestions: {str(e)}"
        )


@router.post("/suggestions/bulk", response_model=BulkMappingSuggestionsResponse)
async def get_bulk_mapping_suggestions(
    bulk_request: BulkMappingSuggestionsRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get bulk mapping suggestions for multiple techniques.
    
    Processes multiple MITRE techniques and returns mapping suggestions for each
    to the specified target framework.
    """
    try:
        results = []
        
        for technique_id in bulk_request.technique_ids:
            # Mock technique data
            technique = {
                "technique_id": technique_id,
                "name": f"Technique {technique_id}",
                "description": f"Description for {technique_id}"
            }
            
            # Mock controls/subcategories
            if bulk_request.target_framework == "isf":
                targets = [
                    {"control_id": "EM1", "name": "Email Security"},
                    {"control_id": "AT1", "name": "Security Training"}
                ]
                mapping_algorithm = MitreToISFMappingAlgorithm()
            else:  # nist_csf
                targets = [
                    {"subcategory_id": "PR.AT-01", "name": "All users are informed and trained"},
                    {"subcategory_id": "DE.CM-01", "name": "Networks are monitored"}
                ]
                mapping_algorithm = MitreToNISTCSFMappingAlgorithm()
            
            # Generate suggestions
            suggestions = mapping_algorithm.suggest_mappings(technique, targets)
            
            # Apply options
            options = bulk_request.suggestion_options
            if options.min_confidence:
                suggestions = [s for s in suggestions if s.confidence_score >= options.min_confidence]
            
            if options.max_suggestions_per_technique:
                suggestions = suggestions[:options.max_suggestions_per_technique]
            
            results.append({
                "technique_id": technique_id,
                "suggestions": [
                    {
                        "target_id": s.target_id,
                        "mapping_type": s.mapping_type,
                        "confidence_score": s.confidence_score,
                        "effectiveness_score": s.effectiveness_score if options.include_effectiveness else None
                    }
                    for s in suggestions
                ],
                "suggestion_count": len(suggestions)
            })
        
        return BulkMappingSuggestionsResponse(
            results=results,
            summary={
                "total_techniques": len(bulk_request.technique_ids),
                "total_suggestions": sum(r["suggestion_count"] for r in results),
                "average_suggestions_per_technique": sum(r["suggestion_count"] for r in results) / len(results) if results else 0
            }
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Bulk suggestions failed: {str(e)}"
        )


@router.post("/validate", response_model=MappingValidationResponse)
async def validate_mapping_suggestion(
    validation_request: Dict[str, Any],
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Validate a mapping suggestion.
    
    Performs comprehensive validation of a proposed mapping including semantic
    similarity, effectiveness analysis, and implementation feasibility.
    """
    try:
        technique_id = validation_request["technique_id"]
        control_id = validation_request["control_id"]
        mapping_type = validation_request["mapping_type"]
        criteria = validation_request.get("validation_criteria", {})
        
        # Mock technique and control data
        technique = {
            "technique_id": technique_id,
            "name": "Phishing",
            "description": "Adversaries may send phishing messages"
        }
        
        control = {
            "control_id": control_id,
            "name": "Email Security",
            "description": "Implement email security controls"
        }
        
        # Perform validation
        quality_assessor = MappingQualityAssessor()
        effectiveness_scorer = EffectivenessScorer()
        
        # Calculate effectiveness
        effectiveness_result = effectiveness_scorer.calculate_effectiveness(technique, control)
        
        # Assess quality
        mapping_data = {
            "effectiveness_score": effectiveness_result["score"],
            "confidence_score": effectiveness_result["confidence"],
            "semantic_similarity": 0.8,  # Mock value
            "expert_validations": 2,
            "implementation_evidence": 0.7
        }
        
        quality_assessment = quality_assessor.assess_quality(mapping_data)
        
        return MappingValidationResponse(
            validation_result="valid" if quality_assessment["overall_quality"] > 0.7 else "invalid",
            is_valid=quality_assessment["overall_quality"] > 0.7,
            confidence_score=effectiveness_result["confidence"],
            effectiveness_score=effectiveness_result["score"],
            validation_details={
                "semantic_similarity": mapping_data["semantic_similarity"],
                "effectiveness_analysis": effectiveness_result["factors"],
                "implementation_feasibility": 0.8,  # Mock value
                "quality_score": quality_assessment["overall_quality"]
            },
            recommendations=quality_assessment["recommendations"]
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Validation failed: {str(e)}"
        )


@router.post("/", response_model=MappingResponse, status_code=status.HTTP_201_CREATED)
async def create_mapping(
    create_request: MappingCreateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Create a new cross-framework mapping.
    
    Creates a new mapping between frameworks with validation and metadata tracking.
    """
    try:
        # Determine mapping model based on frameworks
        if (create_request.source_framework == "mitre_attack" and 
            create_request.target_framework == "isf"):
            mapping_model = MitreToISFMapping
        elif (create_request.source_framework == "mitre_attack" and 
              create_request.target_framework == "nist_csf"):
            mapping_model = MitreToNISTCSFMapping
        elif (create_request.source_framework == "isf" and 
              create_request.target_framework == "nist_csf"):
            mapping_model = ISFToNISTCSFMapping
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Unsupported framework combination"
            )
        
        # Create mapping instance
        mapping = mapping_model(
            source_id=create_request.source_id,
            target_id=create_request.target_id,
            mapping_type=create_request.mapping_type,
            effectiveness_score=create_request.effectiveness_score,
            confidence_score=create_request.confidence_score,
            rationale=create_request.rationale,
            evidence=create_request.evidence,
            validation_status=create_request.validation_status,
            created_by=current_user.id,
            created_at=datetime.utcnow()
        )
        
        db.add(mapping)
        db.commit()
        db.refresh(mapping)
        
        return MappingResponse(
            id=mapping.id,
            source_framework=create_request.source_framework,
            target_framework=create_request.target_framework,
            source_id=mapping.source_id,
            target_id=mapping.target_id,
            mapping_type=mapping.mapping_type,
            effectiveness_score=mapping.effectiveness_score,
            confidence_score=mapping.confidence_score,
            rationale=mapping.rationale,
            evidence=mapping.evidence,
            validation_status=mapping.validation_status,
            created_at=mapping.created_at,
            created_by=current_user.username,
            validation_history=[]  # Would load from database
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create mapping: {str(e)}"
        )


@router.get("/{mapping_id}", response_model=MappingResponse)
async def get_mapping(
    mapping_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Get a specific mapping by ID.
    
    Returns detailed information about a cross-framework mapping including
    validation history and metadata.
    """
    # Try to find mapping in any of the mapping tables
    mapping = None
    source_framework = None
    target_framework = None
    
    # Check MITRE to ISF mappings
    mapping = db.query(MitreToISFMapping).filter_by(id=mapping_id).first()
    if mapping:
        source_framework = "mitre_attack"
        target_framework = "isf"
    
    # Check MITRE to NIST CSF mappings
    if not mapping:
        mapping = db.query(MitreToNISTCSFMapping).filter_by(id=mapping_id).first()
        if mapping:
            source_framework = "mitre_attack"
            target_framework = "nist_csf"
    
    # Check ISF to NIST CSF mappings
    if not mapping:
        mapping = db.query(ISFToNISTCSFMapping).filter_by(id=mapping_id).first()
        if mapping:
            source_framework = "isf"
            target_framework = "nist_csf"
    
    if not mapping:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Mapping not found"
        )
    
    return MappingResponse(
        id=mapping.id,
        source_framework=source_framework,
        target_framework=target_framework,
        source_id=mapping.source_id,
        target_id=mapping.target_id,
        mapping_type=mapping.mapping_type,
        effectiveness_score=mapping.effectiveness_score,
        confidence_score=mapping.confidence_score,
        rationale=mapping.rationale,
        evidence=mapping.evidence,
        validation_status=mapping.validation_status,
        created_at=mapping.created_at,
        created_by=mapping.created_by_user.username if hasattr(mapping, 'created_by_user') else "Unknown",
        validation_history=[]  # Would load from database
    )


@router.put("/{mapping_id}", response_model=MappingResponse)
async def update_mapping(
    mapping_id: int,
    update_request: MappingUpdateRequest,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Update an existing mapping.
    
    Updates mapping properties with validation and audit trail tracking.
    """
    # Find mapping (similar logic as get_mapping)
    mapping = db.query(MitreToISFMapping).filter_by(id=mapping_id).first()
    if not mapping:
        mapping = db.query(MitreToNISTCSFMapping).filter_by(id=mapping_id).first()
    if not mapping:
        mapping = db.query(ISFToNISTCSFMapping).filter_by(id=mapping_id).first()
    
    if not mapping:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Mapping not found"
        )
    
    try:
        # Update fields
        if update_request.effectiveness_score is not None:
            mapping.effectiveness_score = update_request.effectiveness_score
        if update_request.confidence_score is not None:
            mapping.confidence_score = update_request.confidence_score
        if update_request.rationale is not None:
            mapping.rationale = update_request.rationale
        if update_request.evidence is not None:
            mapping.evidence = update_request.evidence
        if update_request.validation_status is not None:
            mapping.validation_status = update_request.validation_status
        
        mapping.updated_at = datetime.utcnow()
        mapping.updated_by = current_user.id
        
        db.commit()
        db.refresh(mapping)
        
        return MappingResponse(
            id=mapping.id,
            source_framework="mitre_attack",  # Simplified
            target_framework="isf",  # Simplified
            source_id=mapping.source_id,
            target_id=mapping.target_id,
            mapping_type=mapping.mapping_type,
            effectiveness_score=mapping.effectiveness_score,
            confidence_score=mapping.confidence_score,
            rationale=mapping.rationale,
            evidence=mapping.evidence,
            validation_status=mapping.validation_status,
            created_at=mapping.created_at,
            updated_at=mapping.updated_at,
            created_by="Unknown",  # Would get from user relationship
            updated_by=current_user.username,
            validation_history=[]
        )
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update mapping: {str(e)}"
        )


@router.delete("/{mapping_id}")
async def delete_mapping(
    mapping_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_user)
):
    """
    Delete a mapping (soft delete).
    
    Performs soft delete of a mapping to maintain audit trail.
    """
    # Find mapping
    mapping = db.query(MitreToISFMapping).filter_by(id=mapping_id).first()
    if not mapping:
        mapping = db.query(MitreToNISTCSFMapping).filter_by(id=mapping_id).first()
    if not mapping:
        mapping = db.query(ISFToNISTCSFMapping).filter_by(id=mapping_id).first()
    
    if not mapping:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Mapping not found"
        )
    
    try:
        # Soft delete
        mapping.deleted_at = datetime.utcnow()
        mapping.deleted_by = current_user.id
        
        db.commit()
        
        return {
            "message": "Mapping deleted successfully",
            "deleted_at": mapping.deleted_at,
            "soft_deleted": True
        }
        
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete mapping: {str(e)}"
        )
