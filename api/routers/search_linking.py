"""
Advanced Search and Linking API Endpoints.

This module provides comprehensive API endpoints for advanced search capabilities,
cross-framework linking, and intelligent relationship management.
"""

from fastapi import APIRouter, Depends, HTTPException, Query, Body
from sqlalchemy.orm import Session
from typing import List, Dict, Any, Optional
import logging

from api.database import get_db
from api.services.search_service import AdvancedSearchService, SearchIndexService
from api.services.linking_service import LinkingService, CrossFrameworkService
from api.models.search_linking import CustomColumn
from api.schemas.search_linking import (
    SearchRequest, SearchResponse, ColumnLinkCreate, ColumnLinkResponse,
    CustomColumnCreate, CustomColumnResponse, CrossFrameworkRelationshipCreate,
    CrossFrameworkRelationshipResponse, SearchSuggestionResponse
)

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/api/v1/search", tags=["Search & Linking"])


# Search Endpoints
@router.post("/", response_model=SearchResponse)
async def advanced_search(
    request: SearchRequest,
    db: Session = Depends(get_db)
):
    """
    Perform advanced search across all cybersecurity frameworks.
    
    Supports:
    - Full-text search with semantic understanding
    - Faceted filtering by framework, element type, category
    - Cross-framework relationship discovery
    - Intelligent ranking and relevance scoring
    """
    try:
        search_service = AdvancedSearchService(db)
        
        result = await search_service.search(
            query=request.query,
            frameworks=request.frameworks,
            element_types=request.element_types,
            filters=request.filters,
            sort_by=request.sort_by,
            limit=request.limit,
            offset=request.offset,
            user_id=request.user_id,
            include_facets=request.include_facets,
            search_type=request.search_type
        )
        
        return SearchResponse(**result)
        
    except Exception as e:
        logger.error(f"Search error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Search failed: {str(e)}")


@router.get("/suggestions", response_model=List[SearchSuggestionResponse])
async def get_search_suggestions(
    query: str = Query(..., description="Partial search query"),
    frameworks: Optional[List[str]] = Query(None, description="Frameworks to search in"),
    limit: int = Query(10, description="Maximum number of suggestions"),
    db: Session = Depends(get_db)
):
    """
    Get search suggestions based on partial query and search history.
    """
    try:
        search_service = AdvancedSearchService(db)
        suggestions = await search_service._get_search_suggestions(query, frameworks)
        
        return [
            SearchSuggestionResponse(suggestion=suggestion, confidence=0.8)
            for suggestion in suggestions[:limit]
        ]
        
    except Exception as e:
        logger.error(f"Search suggestions error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get suggestions: {str(e)}")


@router.post("/index/rebuild")
async def rebuild_search_index(
    db: Session = Depends(get_db)
):
    """
    Rebuild the search index from all framework data.
    
    This operation:
    - Clears the existing search index
    - Re-indexes all framework elements
    - Updates search vectors and metadata
    - Returns indexing statistics
    """
    try:
        index_service = SearchIndexService(db)
        stats = await index_service.rebuild_search_index()
        
        return {
            "message": "Search index rebuilt successfully",
            "statistics": stats
        }
        
    except Exception as e:
        logger.error(f"Index rebuild error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Index rebuild failed: {str(e)}")


# Custom Column Endpoints
@router.post("/columns", response_model=CustomColumnResponse)
async def create_custom_column(
    column: CustomColumnCreate,
    db: Session = Depends(get_db)
):
    """
    Create a custom column for organizing framework elements.
    
    Custom columns allow users to create their own organizational
    structures and categorization schemes.
    """
    try:
        new_column = CustomColumn(
            name=column.name,
            description=column.description,
            column_type=column.column_type,
            data_type=column.data_type,
            allowed_values=column.allowed_values,
            validation_rules=column.validation_rules,
            display_order=column.display_order,
            is_visible=column.is_visible,
            color_scheme=column.color_scheme,
            icon=column.icon,
            scope=column.scope,
            owner_id=column.owner_id,
            organization_id=column.organization_id,
            is_required=column.is_required,
            is_searchable=column.is_searchable,
            created_by=column.created_by
        )
        
        db.add(new_column)
        db.commit()
        db.refresh(new_column)
        
        return CustomColumnResponse.from_orm(new_column)
        
    except Exception as e:
        db.rollback()
        logger.error(f"Column creation error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create column: {str(e)}")


@router.get("/columns", response_model=List[CustomColumnResponse])
async def get_custom_columns(
    scope: Optional[str] = Query(None, description="Filter by scope"),
    owner_id: Optional[str] = Query(None, description="Filter by owner"),
    column_type: Optional[str] = Query(None, description="Filter by column type"),
    limit: int = Query(100, description="Maximum number of results"),
    offset: int = Query(0, description="Result offset"),
    db: Session = Depends(get_db)
):
    """
    Get custom columns with optional filtering.
    """
    try:
        query = db.query(CustomColumn).filter(CustomColumn.deleted_at.is_(None))
        
        if scope:
            query = query.filter(CustomColumn.scope == scope)
        
        if owner_id:
            query = query.filter(CustomColumn.owner_id == owner_id)
        
        if column_type:
            query = query.filter(CustomColumn.column_type == column_type)
        
        columns = query.offset(offset).limit(limit).all()
        
        return [CustomColumnResponse.from_orm(column) for column in columns]
        
    except Exception as e:
        logger.error(f"Get columns error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get columns: {str(e)}")


# Column Linking Endpoints
@router.post("/links", response_model=ColumnLinkResponse)
async def create_column_link(
    link: ColumnLinkCreate,
    db: Session = Depends(get_db)
):
    """
    Create a link between a search item and a custom column.
    
    Links enable users to organize framework elements according
    to their custom organizational structures.
    """
    try:
        linking_service = LinkingService(db)
        
        column_link = await linking_service.create_column_link(
            search_item_id=link.search_item_id,
            column_id=link.column_id,
            link_type=link.link_type,
            link_strength=link.link_strength,
            description=link.description,
            rationale=link.rationale,
            tags=link.tags,
            created_by=link.created_by,
            organization_context=link.organization_context
        )
        
        return ColumnLinkResponse.from_orm(column_link)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Link creation error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create link: {str(e)}")


@router.get("/links", response_model=List[Dict[str, Any]])
async def get_column_links(
    search_item_id: Optional[int] = Query(None, description="Filter by search item ID"),
    column_id: Optional[int] = Query(None, description="Filter by column ID"),
    link_type: Optional[str] = Query(None, description="Filter by link type"),
    min_confidence: Optional[float] = Query(None, description="Minimum confidence score"),
    created_by: Optional[str] = Query(None, description="Filter by creator"),
    limit: int = Query(100, description="Maximum number of results"),
    offset: int = Query(0, description="Result offset"),
    db: Session = Depends(get_db)
):
    """
    Get column links with optional filtering.
    """
    try:
        linking_service = LinkingService(db)
        
        links = await linking_service.get_column_links(
            search_item_id=search_item_id,
            column_id=column_id,
            link_type=link_type,
            min_confidence=min_confidence,
            created_by=created_by,
            limit=limit,
            offset=offset
        )
        
        return links
        
    except Exception as e:
        logger.error(f"Get links error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get links: {str(e)}")


@router.get("/links/suggestions/{search_item_id}")
async def get_link_suggestions(
    search_item_id: int,
    limit: int = Query(10, description="Maximum number of suggestions"),
    db: Session = Depends(get_db)
):
    """
    Get suggested column links for a search item.
    
    Uses machine learning algorithms to suggest the most
    appropriate columns for linking based on content similarity,
    usage patterns, and organizational context.
    """
    try:
        linking_service = LinkingService(db)
        
        suggestions = await linking_service.suggest_column_links(
            search_item_id=search_item_id,
            limit=limit
        )
        
        return {
            "search_item_id": search_item_id,
            "suggestions": suggestions
        }
        
    except ValueError as e:
        raise HTTPException(status_code=404, detail=str(e))
    except Exception as e:
        logger.error(f"Link suggestions error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get suggestions: {str(e)}")


# Cross-Framework Relationship Endpoints
@router.post("/relationships", response_model=CrossFrameworkRelationshipResponse)
async def create_cross_framework_relationship(
    relationship: CrossFrameworkRelationshipCreate,
    db: Session = Depends(get_db)
):
    """
    Create a cross-framework relationship between two items.
    
    Enables linking of related elements across different
    cybersecurity frameworks with confidence scoring and
    semantic analysis.
    """
    try:
        cross_framework_service = CrossFrameworkService(db)
        
        new_relationship = await cross_framework_service.create_cross_framework_relationship(
            source_item_id=relationship.source_item_id,
            target_item_id=relationship.target_item_id,
            relationship_type=relationship.relationship_type,
            relationship_strength=relationship.relationship_strength,
            bidirectional=relationship.bidirectional,
            description=relationship.description,
            rationale=relationship.rationale,
            evidence=relationship.evidence,
            created_by=relationship.created_by
        )
        
        return CrossFrameworkRelationshipResponse.from_orm(new_relationship)
        
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        logger.error(f"Relationship creation error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to create relationship: {str(e)}")


@router.post("/relationships/discover")
async def discover_cross_framework_relationships(
    framework_a: str = Body(..., description="First framework to analyze"),
    framework_b: str = Body(..., description="Second framework to analyze"),
    min_confidence: float = Body(0.3, description="Minimum confidence threshold"),
    limit: int = Body(50, description="Maximum number of suggestions"),
    db: Session = Depends(get_db)
):
    """
    Discover potential relationships between two frameworks.
    
    Uses advanced machine learning algorithms to identify
    potential relationships based on semantic similarity,
    contextual analysis, and structural compatibility.
    """
    try:
        cross_framework_service = CrossFrameworkService(db)
        
        suggestions = await cross_framework_service.discover_potential_relationships(
            framework_a=framework_a,
            framework_b=framework_b,
            min_confidence=min_confidence,
            limit=limit
        )
        
        return {
            "framework_a": framework_a,
            "framework_b": framework_b,
            "min_confidence": min_confidence,
            "suggestions_count": len(suggestions),
            "suggestions": suggestions
        }
        
    except Exception as e:
        logger.error(f"Relationship discovery error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to discover relationships: {str(e)}")


@router.get("/analytics/search-trends")
async def get_search_analytics(
    days: int = Query(30, description="Number of days to analyze"),
    db: Session = Depends(get_db)
):
    """
    Get search analytics and trends.
    
    Provides insights into:
    - Popular search terms
    - Framework usage patterns
    - Search success rates
    - User behavior trends
    """
    try:
        # This would implement search analytics
        # For now, return placeholder data
        return {
            "period_days": days,
            "total_searches": 1250,
            "unique_users": 89,
            "popular_terms": [
                {"term": "access control", "count": 156},
                {"term": "risk management", "count": 134},
                {"term": "incident response", "count": 98},
                {"term": "asset management", "count": 87},
                {"term": "security governance", "count": 76}
            ],
            "framework_usage": {
                "NIST_CSF_2": 35.2,
                "ISO_27001": 28.7,
                "CIS_CONTROLS": 22.1,
                "ISF": 14.0
            },
            "search_success_rate": 78.5,
            "average_results_per_search": 12.3
        }
        
    except Exception as e:
        logger.error(f"Search analytics error: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Failed to get analytics: {str(e)}")
