"""
Analytics API Router for Cybersecurity Frameworks.

This module provides REST API endpoints for framework analytics,
reporting, and insights generation.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any

from fastapi import APIRouter, Depends, HTTPException, Query, status
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from pydantic import BaseModel, Field

from api.core.database import get_db
from api.auth.dependencies import get_current_user
from api.models.user import User
from api.services.analytics_service import FrameworkAnalyticsService, ReportingService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/analytics", tags=["Framework Analytics"])


# Pydantic models for request/response
class AnalyticsRequest(BaseModel):
    """Base analytics request model."""
    frameworks: List[str] = Field(..., description="Frameworks to analyze")
    time_period: Optional[int] = Field(30, description="Analysis time period in days")


class CoverageAnalysisRequest(BaseModel):
    """Coverage analysis request model."""
    framework: str = Field(..., description="Framework to analyze")
    include_details: bool = Field(True, description="Include detailed breakdown")


class EffectivenessAnalysisRequest(BaseModel):
    """Effectiveness analysis request model."""
    source_framework: str = Field(..., description="Source framework")
    target_framework: str = Field(..., description="Target framework")
    time_period: Optional[int] = Field(30, description="Analysis period in days")
    min_threshold: Optional[float] = Field(0.5, description="Minimum effectiveness threshold")


class TrendAnalysisRequest(BaseModel):
    """Trend analysis request model."""
    metric: str = Field(..., description="Metric to analyze (mapping_count, effectiveness, coverage)")
    framework: str = Field(..., description="Framework to analyze")
    period_days: int = Field(90, description="Analysis period in days")


class ComplianceAssessmentRequest(BaseModel):
    """Compliance assessment request model."""
    framework: str = Field(..., description="Framework for compliance assessment")
    assessment_data: Dict[str, Any] = Field(..., description="Assessment data")
    organization: str = Field(..., description="Organization name")


class ReportRequest(BaseModel):
    """Report generation request model."""
    report_type: str = Field(..., description="Report type (executive, detailed, compliance)")
    frameworks: List[str] = Field(..., description="Frameworks to include")
    format: str = Field("json", description="Report format")
    include_charts: bool = Field(False, description="Include chart data")


@router.get("/coverage/{framework}")
async def get_framework_coverage_analysis(
    framework: str,
    include_details: bool = Query(True, description="Include detailed breakdown"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get comprehensive coverage analysis for a specific framework.
    
    Provides coverage metrics, quality assessment, and detailed breakdowns.
    """
    try:
        logger.info(f"Getting coverage analysis for {framework} by user {current_user.id}")
        
        analytics_service = FrameworkAnalyticsService(db)
        coverage_metrics = analytics_service.get_framework_coverage_metrics(framework)
        
        if "error" in coverage_metrics:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=coverage_metrics["error"]
            )
        
        response = {
            "framework": framework,
            "analysis_timestamp": datetime.now().isoformat(),
            "coverage_metrics": coverage_metrics,
            "user_id": current_user.id
        }
        
        if include_details:
            # Add additional detailed analysis
            gap_analysis = analytics_service.get_coverage_gap_analysis([framework])
            response["gap_analysis"] = gap_analysis
        
        return JSONResponse(content=response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Coverage analysis error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error during coverage analysis: {str(e)}"
        )


@router.post("/effectiveness")
async def analyze_mapping_effectiveness(
    request: EffectivenessAnalysisRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Analyze mapping effectiveness between frameworks.
    
    Provides effectiveness metrics, quality assessment, and recommendations.
    """
    try:
        logger.info(f"Analyzing effectiveness between {request.source_framework} and {request.target_framework}")
        
        analytics_service = FrameworkAnalyticsService(db)
        effectiveness_analysis = analytics_service.get_mapping_effectiveness_analysis(
            source_framework=request.source_framework,
            target_framework=request.target_framework,
            time_period=request.time_period
        )
        
        if "error" in effectiveness_analysis:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=effectiveness_analysis["error"]
            )
        
        response = {
            "source_framework": request.source_framework,
            "target_framework": request.target_framework,
            "analysis_period_days": request.time_period,
            "analysis_timestamp": datetime.now().isoformat(),
            "effectiveness_analysis": effectiveness_analysis,
            "user_id": current_user.id
        }
        
        return JSONResponse(content=response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Effectiveness analysis error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error during effectiveness analysis: {str(e)}"
        )


@router.post("/gaps")
async def analyze_coverage_gaps(
    request: AnalyticsRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Analyze coverage gaps across multiple frameworks.
    
    Identifies gaps, priorities, and provides recommendations.
    """
    try:
        logger.info(f"Analyzing coverage gaps for frameworks: {request.frameworks}")
        
        analytics_service = FrameworkAnalyticsService(db)
        gap_analysis = analytics_service.get_coverage_gap_analysis(request.frameworks)
        
        if "error" in gap_analysis:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=gap_analysis["error"]
            )
        
        response = {
            "frameworks": request.frameworks,
            "analysis_timestamp": datetime.now().isoformat(),
            "gap_analysis": gap_analysis,
            "user_id": current_user.id
        }
        
        return JSONResponse(content=response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Gap analysis error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error during gap analysis: {str(e)}"
        )


@router.post("/trends")
async def analyze_trends(
    request: TrendAnalysisRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Analyze trends for specific metrics over time.
    
    Provides trend data, direction, and strength analysis.
    """
    try:
        logger.info(f"Analyzing {request.metric} trend for {request.framework}")
        
        analytics_service = FrameworkAnalyticsService(db)
        trend_data = analytics_service.get_trend_analysis(
            metric=request.metric,
            framework=request.framework,
            period_days=request.period_days
        )
        
        response = {
            "metric": request.metric,
            "framework": request.framework,
            "period_days": request.period_days,
            "analysis_timestamp": datetime.now().isoformat(),
            "trend_data": {
                "period": trend_data.period,
                "values": trend_data.values,
                "labels": trend_data.labels,
                "trend_direction": trend_data.trend_direction,
                "trend_strength": trend_data.trend_strength
            },
            "user_id": current_user.id
        }
        
        return JSONResponse(content=response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Trend analysis error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error during trend analysis: {str(e)}"
        )


@router.post("/compliance")
async def assess_compliance_readiness(
    request: ComplianceAssessmentRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Assess compliance readiness for a framework.
    
    Calculates compliance score and provides recommendations.
    """
    try:
        logger.info(f"Assessing compliance readiness for {request.framework}")
        
        analytics_service = FrameworkAnalyticsService(db)
        compliance_score = analytics_service.get_compliance_readiness_score(
            framework=request.framework,
            assessment_data=request.assessment_data
        )
        
        if "error" in compliance_score:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=compliance_score["error"]
            )
        
        response = {
            "framework": request.framework,
            "organization": request.organization,
            "assessment_timestamp": datetime.now().isoformat(),
            "compliance_assessment": compliance_score,
            "user_id": current_user.id
        }
        
        return JSONResponse(content=response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Compliance assessment error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error during compliance assessment: {str(e)}"
        )


@router.post("/reports")
async def generate_analytics_report(
    request: ReportRequest,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Generate comprehensive analytics reports.
    
    Supports executive summary, detailed analysis, and compliance reports.
    """
    try:
        logger.info(f"Generating {request.report_type} report for frameworks: {request.frameworks}")
        
        reporting_service = ReportingService(db)
        
        if request.report_type.lower() == "executive":
            report_data = reporting_service.generate_executive_summary(request.frameworks)
        elif request.report_type.lower() == "detailed":
            if len(request.frameworks) != 1:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Detailed reports require exactly one framework"
                )
            report_data = reporting_service.generate_detailed_analysis_report(request.frameworks[0])
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Unsupported report type: {request.report_type}"
            )
        
        response = {
            "report_type": request.report_type,
            "frameworks": request.frameworks,
            "format": request.format,
            "generated_timestamp": datetime.now().isoformat(),
            "report_data": report_data,
            "user_id": current_user.id
        }
        
        return JSONResponse(content=response)
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Report generation error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error during report generation: {str(e)}"
        )


@router.get("/metrics/summary")
async def get_analytics_summary(
    frameworks: List[str] = Query(..., description="Frameworks to summarize"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get high-level analytics summary for multiple frameworks.
    
    Provides key metrics and overall health indicators.
    """
    try:
        logger.info(f"Getting analytics summary for frameworks: {frameworks}")
        
        analytics_service = FrameworkAnalyticsService(db)
        summary = {}
        
        for framework in frameworks:
            coverage_metrics = analytics_service.get_framework_coverage_metrics(framework)
            if "error" not in coverage_metrics:
                summary[framework] = {
                    "coverage_percentage": coverage_metrics.get("overall_coverage_percentage", 0),
                    "quality_score": coverage_metrics.get("coverage_quality", {}).get("score", 0),
                    "total_controls": coverage_metrics.get("total_controls", 0),
                    "mapped_controls": coverage_metrics.get("mapped_controls", 0)
                }
        
        response = {
            "frameworks": frameworks,
            "summary_timestamp": datetime.now().isoformat(),
            "framework_summaries": summary,
            "overall_health": {
                "average_coverage": sum(s["coverage_percentage"] for s in summary.values()) / len(summary) if summary else 0,
                "average_quality": sum(s["quality_score"] for s in summary.values()) / len(summary) if summary else 0,
                "total_frameworks": len(frameworks),
                "healthy_frameworks": len([s for s in summary.values() if s["coverage_percentage"] >= 75])
            },
            "user_id": current_user.id
        }
        
        return JSONResponse(content=response)
        
    except Exception as e:
        logger.error(f"Analytics summary error: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error during analytics summary: {str(e)}"
        )


@router.get("/health")
async def analytics_service_health():
    """
    Check analytics service health status.
    
    Returns health status of analytics services.
    """
    return {
        "status": "healthy",
        "services": {
            "framework_analytics": "operational",
            "reporting_service": "operational",
            "trend_analysis": "operational",
            "compliance_assessment": "operational"
        },
        "timestamp": datetime.now().isoformat()
    }
