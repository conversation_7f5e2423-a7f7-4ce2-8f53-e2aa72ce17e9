"""
Framework Data Validation Service.

This module provides comprehensive validation services for cybersecurity framework
data including structure validation, content validation, and cross-reference validation.
It supports ISF, NIST CSF, and MITRE ATT&CK frameworks with detailed error reporting.

Features:
- JSON and CSV structure validation
- Content validation with business rules
- Cross-reference validation between frameworks
- Data integrity checks and constraint validation
- Performance-optimized validation for large datasets
- Detailed error reporting with suggestions
"""

import re
import json
import logging
from typing import Dict, List, Any, Optional, Set, Tuple
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum

from api.core.config import isf_config, nist_csf_config, mitre_config

logger = logging.getLogger(__name__)


class ValidationSeverity(Enum):
    """Validation issue severity levels."""
    ERROR = "error"
    WARNING = "warning"
    INFO = "info"


@dataclass
class ValidationIssue:
    """Individual validation issue."""
    severity: ValidationSeverity
    message: str
    field: Optional[str] = None
    value: Optional[str] = None
    suggestion: Optional[str] = None
    line_number: Optional[int] = None


@dataclass
class ValidationResult:
    """Result of validation operation."""
    is_valid: bool
    issues: List[ValidationIssue] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    info: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        """Populate convenience lists from issues."""
        for issue in self.issues:
            if issue.severity == ValidationSeverity.ERROR:
                self.errors.append(issue.message)
            elif issue.severity == ValidationSeverity.WARNING:
                self.warnings.append(issue.message)
            elif issue.severity == ValidationSeverity.INFO:
                self.info.append(issue.message)
    
    def add_issue(self, severity: ValidationSeverity, message: str, **kwargs):
        """Add a validation issue."""
        issue = ValidationIssue(severity=severity, message=message, **kwargs)
        self.issues.append(issue)
        
        if severity == ValidationSeverity.ERROR:
            self.errors.append(message)
            self.is_valid = False
        elif severity == ValidationSeverity.WARNING:
            self.warnings.append(message)
        elif severity == ValidationSeverity.INFO:
            self.info.append(message)


class BaseValidator:
    """Base validator with common validation utilities."""
    
    def __init__(self):
        """Initialize base validator."""
        self.required_fields = set()
        self.optional_fields = set()
        self.field_patterns = {}
        self.field_constraints = {}
    
    def validate_required_fields(self, data: Dict[str, Any], result: ValidationResult) -> None:
        """Validate that all required fields are present."""
        for field in self.required_fields:
            if field not in data:
                result.add_issue(
                    ValidationSeverity.ERROR,
                    f"Required field '{field}' is missing",
                    field=field,
                    suggestion=f"Add the '{field}' field to the data"
                )
            elif data[field] is None or (isinstance(data[field], str) and not data[field].strip()):
                result.add_issue(
                    ValidationSeverity.ERROR,
                    f"Required field '{field}' is empty",
                    field=field,
                    value=str(data[field]),
                    suggestion=f"Provide a valid value for '{field}'"
                )
    
    def validate_field_patterns(self, data: Dict[str, Any], result: ValidationResult) -> None:
        """Validate field patterns using regex."""
        for field, pattern in self.field_patterns.items():
            if field in data and data[field]:
                value = str(data[field])
                if not re.match(pattern, value):
                    result.add_issue(
                        ValidationSeverity.ERROR,
                        f"Field '{field}' has invalid format: '{value}'",
                        field=field,
                        value=value,
                        suggestion=f"Ensure '{field}' matches the expected pattern"
                    )
    
    def validate_field_constraints(self, data: Dict[str, Any], result: ValidationResult) -> None:
        """Validate field constraints."""
        for field, constraints in self.field_constraints.items():
            if field in data and data[field] is not None:
                value = data[field]
                
                # Length constraints
                if 'min_length' in constraints and len(str(value)) < constraints['min_length']:
                    result.add_issue(
                        ValidationSeverity.ERROR,
                        f"Field '{field}' is too short (minimum {constraints['min_length']} characters)",
                        field=field,
                        value=str(value)
                    )
                
                if 'max_length' in constraints and len(str(value)) > constraints['max_length']:
                    result.add_issue(
                        ValidationSeverity.ERROR,
                        f"Field '{field}' is too long (maximum {constraints['max_length']} characters)",
                        field=field,
                        value=str(value)
                    )
                
                # Value constraints
                if 'allowed_values' in constraints and value not in constraints['allowed_values']:
                    result.add_issue(
                        ValidationSeverity.ERROR,
                        f"Field '{field}' has invalid value: '{value}'",
                        field=field,
                        value=str(value),
                        suggestion=f"Use one of: {', '.join(constraints['allowed_values'])}"
                    )


class ISFDataValidator(BaseValidator):
    """Validator for ISF framework data."""
    
    def __init__(self):
        """Initialize ISF validator."""
        super().__init__()
        
        # ISF-specific validation rules
        self.required_fields = {'version', 'security_areas'}
        self.optional_fields = {'description', 'release_date', 'url'}
        
        self.field_patterns = {
            'version': r'^\d{4}\.\d+$',  # e.g., 2020.1
            'area_id': r'^[A-Z]{2,3}$',  # e.g., SG, RM
            'control_id': r'^[A-Z]{2,3}\d+$'  # e.g., SG1, RM2
        }
        
        self.field_constraints = {
            'control_type': {
                'allowed_values': isf_config.CONTROL_TYPES
            },
            'maturity_level': {
                'allowed_values': isf_config.MATURITY_LEVELS
            },
            'area_id': {
                'allowed_values': isf_config.SECURITY_AREAS
            }
        }
    
    def validate_json_structure(self, data: Dict[str, Any]) -> ValidationResult:
        """Validate ISF JSON structure."""
        result = ValidationResult(is_valid=True)
        
        # Validate top-level structure
        self.validate_required_fields(data, result)
        
        # Validate version format
        if 'version' in data:
            version = data['version']
            if isinstance(version, dict):
                version_id = version.get('version', '')
            else:
                version_id = str(version)
            
            if version_id not in isf_config.SUPPORTED_VERSIONS:
                result.add_issue(
                    ValidationSeverity.WARNING,
                    f"Version '{version_id}' is not in supported versions list",
                    field='version',
                    value=version_id,
                    suggestion=f"Consider using one of: {', '.join(isf_config.SUPPORTED_VERSIONS)}"
                )
        
        # Validate security areas
        if 'security_areas' in data:
            self._validate_security_areas(data['security_areas'], result)
        
        return result
    
    def validate_csv_structure(self, csv_rows: List[Dict[str, Any]]) -> ValidationResult:
        """Validate ISF CSV structure."""
        result = ValidationResult(is_valid=True)
        
        if not csv_rows:
            result.add_issue(
                ValidationSeverity.ERROR,
                "CSV data is empty",
                suggestion="Provide at least one row of data"
            )
            return result
        
        # Check required CSV columns
        required_columns = {'area_id', 'control_id', 'control_name'}
        first_row = csv_rows[0]
        missing_columns = required_columns - set(first_row.keys())
        
        if missing_columns:
            result.add_issue(
                ValidationSeverity.ERROR,
                f"Missing required CSV columns: {', '.join(missing_columns)}",
                suggestion=f"Add columns: {', '.join(missing_columns)}"
            )
        
        # Validate each row
        for i, row in enumerate(csv_rows):
            self._validate_csv_row(row, i + 1, result)
        
        return result
    
    def _validate_security_areas(self, security_areas: List[Dict[str, Any]], result: ValidationResult) -> None:
        """Validate security areas structure."""
        if not isinstance(security_areas, list):
            result.add_issue(
                ValidationSeverity.ERROR,
                "Security areas must be a list",
                field='security_areas'
            )
            return
        
        area_ids = set()
        for i, area in enumerate(security_areas):
            if not isinstance(area, dict):
                result.add_issue(
                    ValidationSeverity.ERROR,
                    f"Security area {i} must be an object",
                    field=f'security_areas[{i}]'
                )
                continue
            
            # Validate area structure
            area_required = {'area_id', 'name'}
            for field in area_required:
                if field not in area:
                    result.add_issue(
                        ValidationSeverity.ERROR,
                        f"Security area {i} missing required field '{field}'",
                        field=f'security_areas[{i}].{field}'
                    )
            
            # Check for duplicate area IDs
            area_id = area.get('area_id')
            if area_id:
                if area_id in area_ids:
                    result.add_issue(
                        ValidationSeverity.ERROR,
                        f"Duplicate area ID '{area_id}' found",
                        field=f'security_areas[{i}].area_id',
                        value=area_id
                    )
                area_ids.add(area_id)
            
            # Validate controls
            if 'controls' in area:
                self._validate_controls(area['controls'], f'security_areas[{i}]', result)
    
    def _validate_controls(self, controls: List[Dict[str, Any]], parent_path: str, result: ValidationResult) -> None:
        """Validate controls structure."""
        if not isinstance(controls, list):
            result.add_issue(
                ValidationSeverity.ERROR,
                "Controls must be a list",
                field=f'{parent_path}.controls'
            )
            return
        
        control_ids = set()
        for i, control in enumerate(controls):
            if not isinstance(control, dict):
                result.add_issue(
                    ValidationSeverity.ERROR,
                    f"Control {i} must be an object",
                    field=f'{parent_path}.controls[{i}]'
                )
                continue
            
            # Validate control structure
            control_required = {'control_id', 'name'}
            for field in control_required:
                if field not in control:
                    result.add_issue(
                        ValidationSeverity.ERROR,
                        f"Control {i} missing required field '{field}'",
                        field=f'{parent_path}.controls[{i}].{field}'
                    )
            
            # Check for duplicate control IDs
            control_id = control.get('control_id')
            if control_id:
                if control_id in control_ids:
                    result.add_issue(
                        ValidationSeverity.ERROR,
                        f"Duplicate control ID '{control_id}' found",
                        field=f'{parent_path}.controls[{i}].control_id',
                        value=control_id
                    )
                control_ids.add(control_id)
            
            # Validate field constraints
            self.validate_field_constraints(control, result)
    
    def _validate_csv_row(self, row: Dict[str, Any], row_number: int, result: ValidationResult) -> None:
        """Validate individual CSV row."""
        # Validate required fields
        if not row.get('area_id', '').strip():
            result.add_issue(
                ValidationSeverity.ERROR,
                f"Row {row_number}: area_id is required",
                line_number=row_number,
                field='area_id'
            )
        
        if not row.get('control_id', '').strip():
            result.add_issue(
                ValidationSeverity.ERROR,
                f"Row {row_number}: control_id is required",
                line_number=row_number,
                field='control_id'
            )
        
        # Validate field patterns and constraints
        self.validate_field_patterns(row, result)
        self.validate_field_constraints(row, result)


class NISTCSFDataValidator(BaseValidator):
    """Validator for NIST CSF framework data."""
    
    def __init__(self):
        """Initialize NIST CSF validator."""
        super().__init__()
        
        # NIST CSF-specific validation rules
        self.required_fields = {'version', 'functions'}
        
        self.field_patterns = {
            'version': r'^\d+\.\d+$',  # e.g., 2.0, 1.1
            'function_id': r'^[A-Z]{2,3}$',  # e.g., GV, ID, PR
            'category_id': r'^[A-Z]{2,3}\.[A-Z]{2,3}$',  # e.g., GV.OC, ID.AM
            'subcategory_id': r'^[A-Z]{2,3}\.[A-Z]{2,3}-\d{2}$'  # e.g., GV.OC-01
        }
    
    def validate_hierarchical_structure(self, data: Dict[str, Any]) -> ValidationResult:
        """Validate NIST CSF hierarchical structure."""
        result = ValidationResult(is_valid=True)
        
        # Validate top-level structure
        self.validate_required_fields(data, result)
        
        # Validate version
        if 'version' in data:
            version = data['version']
            if version not in nist_csf_config.SUPPORTED_VERSIONS:
                result.add_issue(
                    ValidationSeverity.WARNING,
                    f"Version '{version}' is not in supported versions list",
                    field='version',
                    value=version,
                    suggestion=f"Consider using one of: {', '.join(nist_csf_config.SUPPORTED_VERSIONS)}"
                )
        
        # Validate functions
        if 'functions' in data:
            self._validate_functions(data['functions'], result)
        
        return result
    
    def _validate_functions(self, functions: List[Dict[str, Any]], result: ValidationResult) -> None:
        """Validate NIST CSF functions structure."""
        if not isinstance(functions, list):
            result.add_issue(
                ValidationSeverity.ERROR,
                "Functions must be a list",
                field='functions'
            )
            return
        
        function_ids = set()
        for i, function in enumerate(functions):
            if not isinstance(function, dict):
                result.add_issue(
                    ValidationSeverity.ERROR,
                    f"Function {i} must be an object",
                    field=f'functions[{i}]'
                )
                continue
            
            # Validate function structure
            function_required = {'function_id', 'name'}
            for field in function_required:
                if field not in function:
                    result.add_issue(
                        ValidationSeverity.ERROR,
                        f"Function {i} missing required field '{field}'",
                        field=f'functions[{i}].{field}'
                    )
            
            # Check for duplicate function IDs
            function_id = function.get('function_id')
            if function_id:
                if function_id in function_ids:
                    result.add_issue(
                        ValidationSeverity.ERROR,
                        f"Duplicate function ID '{function_id}' found",
                        field=f'functions[{i}].function_id',
                        value=function_id
                    )
                function_ids.add(function_id)
            
            # Validate categories
            if 'categories' in function:
                self._validate_categories(function['categories'], f'functions[{i}]', result)
    
    def _validate_categories(self, categories: List[Dict[str, Any]], parent_path: str, result: ValidationResult) -> None:
        """Validate NIST CSF categories structure."""
        if not isinstance(categories, list):
            result.add_issue(
                ValidationSeverity.ERROR,
                "Categories must be a list",
                field=f'{parent_path}.categories'
            )
            return
        
        category_ids = set()
        for i, category in enumerate(categories):
            if not isinstance(category, dict):
                result.add_issue(
                    ValidationSeverity.ERROR,
                    f"Category {i} must be an object",
                    field=f'{parent_path}.categories[{i}]'
                )
                continue
            
            # Validate category structure
            category_required = {'category_id', 'name'}
            for field in category_required:
                if field not in category:
                    result.add_issue(
                        ValidationSeverity.ERROR,
                        f"Category {i} missing required field '{field}'",
                        field=f'{parent_path}.categories[{i}].{field}'
                    )
            
            # Check for duplicate category IDs
            category_id = category.get('category_id')
            if category_id:
                if category_id in category_ids:
                    result.add_issue(
                        ValidationSeverity.ERROR,
                        f"Duplicate category ID '{category_id}' found",
                        field=f'{parent_path}.categories[{i}].category_id',
                        value=category_id
                    )
                category_ids.add(category_id)
            
            # Validate subcategories
            if 'subcategories' in category:
                self._validate_subcategories(category['subcategories'], f'{parent_path}.categories[{i}]', result)
    
    def _validate_subcategories(self, subcategories: List[Dict[str, Any]], parent_path: str, result: ValidationResult) -> None:
        """Validate NIST CSF subcategories structure."""
        if not isinstance(subcategories, list):
            result.add_issue(
                ValidationSeverity.ERROR,
                "Subcategories must be a list",
                field=f'{parent_path}.subcategories'
            )
            return
        
        subcategory_ids = set()
        for i, subcategory in enumerate(subcategories):
            if not isinstance(subcategory, dict):
                result.add_issue(
                    ValidationSeverity.ERROR,
                    f"Subcategory {i} must be an object",
                    field=f'{parent_path}.subcategories[{i}]'
                )
                continue
            
            # Validate subcategory structure
            subcategory_required = {'subcategory_id', 'name'}
            for field in subcategory_required:
                if field not in subcategory:
                    result.add_issue(
                        ValidationSeverity.ERROR,
                        f"Subcategory {i} missing required field '{field}'",
                        field=f'{parent_path}.subcategories[{i}].{field}'
                    )
            
            # Check for duplicate subcategory IDs
            subcategory_id = subcategory.get('subcategory_id')
            if subcategory_id:
                if subcategory_id in subcategory_ids:
                    result.add_issue(
                        ValidationSeverity.ERROR,
                        f"Duplicate subcategory ID '{subcategory_id}' found",
                        field=f'{parent_path}.subcategories[{i}].subcategory_id',
                        value=subcategory_id
                    )
                subcategory_ids.add(subcategory_id)
            
            # Validate field patterns
            self.validate_field_patterns(subcategory, result)
