"""
Advanced Linking Service.

This service provides comprehensive linking capabilities between search items
and custom columns, with cross-framework relationship management and
intelligent suggestion algorithms.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, desc, asc
import json
from datetime import datetime

from api.models.search_linking import (
    ColumnLink, CustomColumn, CrossFrameworkRelationship, SearchIndex
)

logger = logging.getLogger(__name__)


class LinkingService:
    """
    Service for managing links between search items and custom columns.
    """
    
    def __init__(self, db: Session):
        self.db = db
    
    async def create_column_link(
        self,
        search_item_id: int,
        column_id: int,
        link_type: str = "direct",
        link_strength: float = 1.0,
        description: Optional[str] = None,
        rationale: Optional[str] = None,
        tags: Optional[List[str]] = None,
        created_by: Optional[str] = None,
        organization_context: Optional[Dict[str, Any]] = None
    ) -> ColumnLink:
        """
        Create a link between a search item and a custom column.
        
        Args:
            search_item_id: ID of the search item to link
            column_id: ID of the custom column to link to
            link_type: Type of link (direct, related, mapped, custom)
            link_strength: Strength of the relationship (0.0-1.0)
            description: Description of the link
            rationale: Rationale for creating the link
            tags: Tags associated with the link
            created_by: User who created the link
            organization_context: Organizational context for the link
        
        Returns:
            Created ColumnLink instance
        """
        try:
            # Validate search item exists
            search_item = self.db.query(SearchIndex).filter(
                SearchIndex.id == search_item_id,
                SearchIndex.deleted_at.is_(None)
            ).first()
            
            if not search_item:
                raise ValueError(f"Search item {search_item_id} not found")
            
            # Validate column exists
            column = self.db.query(CustomColumn).filter(
                CustomColumn.id == column_id,
                CustomColumn.deleted_at.is_(None)
            ).first()
            
            if not column:
                raise ValueError(f"Custom column {column_id} not found")
            
            # Check for existing link
            existing_link = self.db.query(ColumnLink).filter(
                ColumnLink.search_item_id == search_item_id,
                ColumnLink.column_id == column_id,
                ColumnLink.deleted_at.is_(None)
            ).first()
            
            if existing_link:
                raise ValueError("Link already exists between this item and column")
            
            # Create the link
            column_link = ColumnLink(
                search_item_id=search_item_id,
                column_id=column_id,
                link_type=link_type,
                link_strength=link_strength,
                description=description,
                rationale=rationale,
                tags=tags,
                created_by=created_by,
                organization_context=organization_context,
                confidence_score=self._calculate_link_confidence(
                    search_item, column, link_type, link_strength
                )
            )
            
            self.db.add(column_link)
            self.db.commit()
            
            # Update usage statistics
            await self._update_column_usage(column_id)
            
            logger.info(f"Created column link: item {search_item_id} -> column {column_id}")
            return column_link
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating column link: {str(e)}")
            raise
    
    async def get_column_links(
        self,
        search_item_id: Optional[int] = None,
        column_id: Optional[int] = None,
        link_type: Optional[str] = None,
        min_confidence: Optional[float] = None,
        created_by: Optional[str] = None,
        limit: int = 100,
        offset: int = 0
    ) -> List[Dict[str, Any]]:
        """
        Get column links with optional filtering.
        
        Args:
            search_item_id: Filter by search item ID
            column_id: Filter by column ID
            link_type: Filter by link type
            min_confidence: Minimum confidence score
            created_by: Filter by creator
            limit: Maximum number of results
            offset: Result offset for pagination
        
        Returns:
            List of column link data
        """
        try:
            query = self.db.query(ColumnLink).filter(
                ColumnLink.deleted_at.is_(None)
            )
            
            # Apply filters
            if search_item_id:
                query = query.filter(ColumnLink.search_item_id == search_item_id)
            
            if column_id:
                query = query.filter(ColumnLink.column_id == column_id)
            
            if link_type:
                query = query.filter(ColumnLink.link_type == link_type)
            
            if min_confidence:
                query = query.filter(ColumnLink.confidence_score >= min_confidence)
            
            if created_by:
                query = query.filter(ColumnLink.created_by == created_by)
            
            # Order by confidence and creation date
            query = query.order_by(
                desc(ColumnLink.confidence_score),
                desc(ColumnLink.created_at)
            )
            
            # Apply pagination
            links = query.offset(offset).limit(limit).all()
            
            # Process results
            result = []
            for link in links:
                link_data = {
                    "id": link.id,
                    "search_item_id": link.search_item_id,
                    "column_id": link.column_id,
                    "link_type": link.link_type,
                    "link_strength": link.link_strength,
                    "description": link.description,
                    "rationale": link.rationale,
                    "tags": link.tags or [],
                    "confidence_score": link.confidence_score,
                    "is_validated": link.is_validated,
                    "validated_by": link.validated_by,
                    "validated_at": link.validated_at,
                    "created_by": link.created_by,
                    "created_at": link.created_at,
                    "usage_count": link.usage_count,
                    "effectiveness_rating": link.effectiveness_rating,
                    "search_item": {
                        "framework": link.search_item.framework,
                        "element_type": link.search_item.element_type,
                        "element_id": link.search_item.element_id,
                        "title": link.search_item.title
                    } if link.search_item else None,
                    "column": {
                        "name": link.column.name,
                        "column_type": link.column.column_type,
                        "data_type": link.column.data_type
                    } if link.column else None
                }
                result.append(link_data)
            
            return result
            
        except Exception as e:
            logger.error(f"Error getting column links: {str(e)}")
            raise
    
    async def update_column_link(
        self,
        link_id: int,
        link_type: Optional[str] = None,
        link_strength: Optional[float] = None,
        description: Optional[str] = None,
        rationale: Optional[str] = None,
        tags: Optional[List[str]] = None,
        effectiveness_rating: Optional[float] = None
    ) -> ColumnLink:
        """
        Update an existing column link.
        
        Args:
            link_id: ID of the link to update
            link_type: New link type
            link_strength: New link strength
            description: New description
            rationale: New rationale
            tags: New tags
            effectiveness_rating: User-provided effectiveness rating
        
        Returns:
            Updated ColumnLink instance
        """
        try:
            link = self.db.query(ColumnLink).filter(
                ColumnLink.id == link_id,
                ColumnLink.deleted_at.is_(None)
            ).first()
            
            if not link:
                raise ValueError(f"Column link {link_id} not found")
            
            # Update fields
            if link_type is not None:
                link.link_type = link_type
            
            if link_strength is not None:
                link.link_strength = link_strength
            
            if description is not None:
                link.description = description
            
            if rationale is not None:
                link.rationale = rationale
            
            if tags is not None:
                link.tags = tags
            
            if effectiveness_rating is not None:
                link.effectiveness_rating = effectiveness_rating
            
            # Recalculate confidence score
            link.confidence_score = self._calculate_link_confidence(
                link.search_item, link.column, link.link_type, link.link_strength
            )
            
            link.updated_at = datetime.utcnow()
            
            self.db.commit()
            
            logger.info(f"Updated column link {link_id}")
            return link
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error updating column link: {str(e)}")
            raise
    
    async def delete_column_link(self, link_id: int) -> bool:
        """
        Soft delete a column link.
        
        Args:
            link_id: ID of the link to delete
        
        Returns:
            True if successful
        """
        try:
            link = self.db.query(ColumnLink).filter(
                ColumnLink.id == link_id,
                ColumnLink.deleted_at.is_(None)
            ).first()
            
            if not link:
                raise ValueError(f"Column link {link_id} not found")
            
            link.deleted_at = datetime.utcnow()
            self.db.commit()
            
            logger.info(f"Deleted column link {link_id}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Error deleting column link: {str(e)}")
            raise
    
    async def suggest_column_links(
        self,
        search_item_id: int,
        limit: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Suggest potential column links for a search item.
        
        Args:
            search_item_id: ID of the search item
            limit: Maximum number of suggestions
        
        Returns:
            List of suggested column links with confidence scores
        """
        try:
            search_item = self.db.query(SearchIndex).filter(
                SearchIndex.id == search_item_id,
                SearchIndex.deleted_at.is_(None)
            ).first()
            
            if not search_item:
                raise ValueError(f"Search item {search_item_id} not found")
            
            # Get available columns (not already linked)
            existing_links = self.db.query(ColumnLink.column_id).filter(
                ColumnLink.search_item_id == search_item_id,
                ColumnLink.deleted_at.is_(None)
            ).subquery()
            
            available_columns = self.db.query(CustomColumn).filter(
                CustomColumn.deleted_at.is_(None),
                ~CustomColumn.id.in_(existing_links)
            ).all()
            
            suggestions = []
            
            for column in available_columns:
                confidence = await self._calculate_suggestion_confidence(search_item, column)
                
                if confidence > 0.1:  # Only suggest if there's some confidence
                    suggestion = {
                        "column_id": column.id,
                        "column_name": column.name,
                        "column_type": column.column_type,
                        "confidence_score": confidence,
                        "suggested_link_type": self._suggest_link_type(search_item, column),
                        "rationale": self._generate_suggestion_rationale(search_item, column)
                    }
                    suggestions.append(suggestion)
            
            # Sort by confidence and limit
            suggestions.sort(key=lambda x: x["confidence_score"], reverse=True)
            return suggestions[:limit]
            
        except Exception as e:
            logger.error(f"Error suggesting column links: {str(e)}")
            raise
    
    def _calculate_link_confidence(
        self,
        search_item: SearchIndex,
        column: CustomColumn,
        link_type: str,
        link_strength: float
    ) -> float:
        """Calculate confidence score for a link."""
        confidence = 0.0
        
        # Base confidence from link strength
        confidence += link_strength * 0.4
        
        # Link type confidence
        type_weights = {
            "direct": 1.0,
            "related": 0.8,
            "mapped": 0.9,
            "custom": 0.6
        }
        confidence += type_weights.get(link_type, 0.5) * 0.3
        
        # Framework and column type compatibility
        if self._are_compatible(search_item, column):
            confidence += 0.2
        
        # Quality factors
        confidence += search_item.quality_score * 0.1
        
        return min(1.0, confidence)
    
    async def _calculate_suggestion_confidence(
        self,
        search_item: SearchIndex,
        column: CustomColumn
    ) -> float:
        """Calculate confidence for suggesting a link."""
        confidence = 0.0
        
        # Framework compatibility
        if self._are_compatible(search_item, column):
            confidence += 0.3
        
        # Semantic similarity (simplified)
        if self._has_semantic_similarity(search_item, column):
            confidence += 0.4
        
        # Usage patterns (how often similar items are linked to this column)
        usage_score = await self._get_usage_pattern_score(search_item, column)
        confidence += usage_score * 0.3
        
        return min(1.0, confidence)
    
    def _are_compatible(self, search_item: SearchIndex, column: CustomColumn) -> bool:
        """Check if search item and column are compatible."""
        # Framework-specific compatibility rules
        framework_column_compatibility = {
            "ISF": ["security_area", "control_type", "priority", "status"],
            "NIST_CSF_2": ["function", "category", "tier", "profile"],
            "ISO_27001": ["domain", "control_type", "certification", "audit"],
            "CIS_CONTROLS": ["implementation_group", "asset_type", "safeguard_type"]
        }
        
        compatible_types = framework_column_compatibility.get(search_item.framework, [])
        return column.column_type in compatible_types or column.column_type == "custom"
    
    def _has_semantic_similarity(self, search_item: SearchIndex, column: CustomColumn) -> bool:
        """Check for semantic similarity between item and column."""
        # Simple keyword matching (could be enhanced with ML)
        item_text = f"{search_item.title} {search_item.description or ''}".lower()
        column_text = f"{column.name} {column.description or ''}".lower()
        
        # Extract key terms
        item_terms = set(item_text.split())
        column_terms = set(column_text.split())
        
        # Calculate overlap
        overlap = len(item_terms.intersection(column_terms))
        total_terms = len(item_terms.union(column_terms))
        
        return (overlap / total_terms) > 0.1 if total_terms > 0 else False
    
    async def _get_usage_pattern_score(
        self,
        search_item: SearchIndex,
        column: CustomColumn
    ) -> float:
        """Get usage pattern score based on similar items."""
        # Find similar items (same framework and element type)
        similar_items = self.db.query(SearchIndex).filter(
            SearchIndex.framework == search_item.framework,
            SearchIndex.element_type == search_item.element_type,
            SearchIndex.id != search_item.id,
            SearchIndex.deleted_at.is_(None)
        ).limit(50).all()
        
        if not similar_items:
            return 0.0
        
        # Count how many similar items are linked to this column
        linked_count = self.db.query(ColumnLink).filter(
            ColumnLink.column_id == column.id,
            ColumnLink.search_item_id.in_([item.id for item in similar_items]),
            ColumnLink.deleted_at.is_(None)
        ).count()
        
        return linked_count / len(similar_items)
    
    def _suggest_link_type(self, search_item: SearchIndex, column: CustomColumn) -> str:
        """Suggest the most appropriate link type."""
        if column.column_type == "category" and search_item.category:
            return "direct"
        elif column.column_type == "priority":
            return "related"
        elif column.column_type == "status":
            return "direct"
        else:
            return "custom"
    
    def _generate_suggestion_rationale(
        self,
        search_item: SearchIndex,
        column: CustomColumn
    ) -> str:
        """Generate rationale for link suggestion."""
        rationales = []
        
        if self._are_compatible(search_item, column):
            rationales.append(f"Compatible with {search_item.framework} framework")
        
        if self._has_semantic_similarity(search_item, column):
            rationales.append("Semantic similarity detected")
        
        if column.usage_count > 0:
            rationales.append(f"Column used {column.usage_count} times")
        
        return "; ".join(rationales) if rationales else "Potential organizational fit"
    
    async def _update_column_usage(self, column_id: int):
        """Update column usage statistics."""
        column = self.db.query(CustomColumn).filter(
            CustomColumn.id == column_id
        ).first()
        
        if column:
            column.usage_count += 1
            column.last_used = datetime.utcnow()
            self.db.commit()


class CrossFrameworkService:
    """
    Service for managing cross-framework relationships and intelligent mapping.
    """

    def __init__(self, db: Session):
        self.db = db

    async def create_cross_framework_relationship(
        self,
        source_item_id: int,
        target_item_id: int,
        relationship_type: str = "related",
        relationship_strength: float = 0.5,
        bidirectional: bool = False,
        description: Optional[str] = None,
        rationale: Optional[str] = None,
        evidence: Optional[Dict[str, Any]] = None,
        created_by: Optional[str] = None
    ) -> CrossFrameworkRelationship:
        """
        Create a cross-framework relationship between two items.

        Args:
            source_item_id: ID of the source search item
            target_item_id: ID of the target search item
            relationship_type: Type of relationship (equivalent, related, implements, supports)
            relationship_strength: Strength of relationship (0.0-1.0)
            bidirectional: Whether the relationship is bidirectional
            description: Description of the relationship
            rationale: Rationale for the relationship
            evidence: Supporting evidence
            created_by: User who created the relationship

        Returns:
            Created CrossFrameworkRelationship instance
        """
        try:
            # Validate source and target items
            source_item = self.db.query(SearchIndex).filter(
                SearchIndex.id == source_item_id,
                SearchIndex.deleted_at.is_(None)
            ).first()

            target_item = self.db.query(SearchIndex).filter(
                SearchIndex.id == target_item_id,
                SearchIndex.deleted_at.is_(None)
            ).first()

            if not source_item:
                raise ValueError(f"Source item {source_item_id} not found")

            if not target_item:
                raise ValueError(f"Target item {target_item_id} not found")

            if source_item.framework == target_item.framework:
                raise ValueError("Cannot create cross-framework relationship within same framework")

            # Check for existing relationship
            existing = self.db.query(CrossFrameworkRelationship).filter(
                CrossFrameworkRelationship.source_item_id == source_item_id,
                CrossFrameworkRelationship.target_item_id == target_item_id,
                CrossFrameworkRelationship.deleted_at.is_(None)
            ).first()

            if existing:
                raise ValueError("Relationship already exists between these items")

            # Calculate semantic similarities
            semantic_sim = await self._calculate_semantic_similarity(source_item, target_item)
            context_sim = await self._calculate_context_similarity(source_item, target_item)
            structural_sim = await self._calculate_structural_similarity(source_item, target_item)

            # Create relationship
            relationship = CrossFrameworkRelationship(
                source_item_id=source_item_id,
                target_item_id=target_item_id,
                relationship_type=relationship_type,
                relationship_strength=relationship_strength,
                bidirectional=bidirectional,
                description=description,
                rationale=rationale,
                evidence=evidence,
                semantic_similarity=semantic_sim,
                context_similarity=context_sim,
                structural_similarity=structural_sim,
                confidence_score=self._calculate_relationship_confidence(
                    relationship_strength, semantic_sim, context_sim, structural_sim
                ),
                discovery_method="manual",
                created_by=created_by
            )

            self.db.add(relationship)

            # Create reverse relationship if bidirectional
            if bidirectional:
                reverse_relationship = CrossFrameworkRelationship(
                    source_item_id=target_item_id,
                    target_item_id=source_item_id,
                    relationship_type=relationship_type,
                    relationship_strength=relationship_strength,
                    bidirectional=True,
                    description=description,
                    rationale=rationale,
                    evidence=evidence,
                    semantic_similarity=semantic_sim,
                    context_similarity=context_sim,
                    structural_similarity=structural_sim,
                    confidence_score=relationship.confidence_score,
                    discovery_method="manual",
                    created_by=created_by
                )
                self.db.add(reverse_relationship)

            self.db.commit()

            logger.info(f"Created cross-framework relationship: {source_item.framework}:{source_item.element_id} -> {target_item.framework}:{target_item.element_id}")
            return relationship

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error creating cross-framework relationship: {str(e)}")
            raise

    async def discover_potential_relationships(
        self,
        framework_a: str,
        framework_b: str,
        min_confidence: float = 0.3,
        limit: int = 50
    ) -> List[Dict[str, Any]]:
        """
        Discover potential relationships between two frameworks using ML algorithms.

        Args:
            framework_a: First framework to analyze
            framework_b: Second framework to analyze
            min_confidence: Minimum confidence threshold
            limit: Maximum number of suggestions

        Returns:
            List of potential relationship suggestions
        """
        try:
            # Get items from both frameworks
            items_a = self.db.query(SearchIndex).filter(
                SearchIndex.framework == framework_a,
                SearchIndex.deleted_at.is_(None)
            ).all()

            items_b = self.db.query(SearchIndex).filter(
                SearchIndex.framework == framework_b,
                SearchIndex.deleted_at.is_(None)
            ).all()

            suggestions = []

            # Compare each item from framework A with items from framework B
            for item_a in items_a:
                for item_b in items_b:
                    # Skip if relationship already exists
                    existing = self.db.query(CrossFrameworkRelationship).filter(
                        CrossFrameworkRelationship.source_item_id == item_a.id,
                        CrossFrameworkRelationship.target_item_id == item_b.id,
                        CrossFrameworkRelationship.deleted_at.is_(None)
                    ).first()

                    if existing:
                        continue

                    # Calculate similarity scores
                    semantic_sim = await self._calculate_semantic_similarity(item_a, item_b)
                    context_sim = await self._calculate_context_similarity(item_a, item_b)
                    structural_sim = await self._calculate_structural_similarity(item_a, item_b)

                    # Calculate overall confidence
                    confidence = self._calculate_relationship_confidence(
                        0.5, semantic_sim, context_sim, structural_sim
                    )

                    if confidence >= min_confidence:
                        suggestion = {
                            "source_item": {
                                "id": item_a.id,
                                "framework": item_a.framework,
                                "element_id": item_a.element_id,
                                "title": item_a.title,
                                "category": item_a.category
                            },
                            "target_item": {
                                "id": item_b.id,
                                "framework": item_b.framework,
                                "element_id": item_b.element_id,
                                "title": item_b.title,
                                "category": item_b.category
                            },
                            "confidence_score": confidence,
                            "semantic_similarity": semantic_sim,
                            "context_similarity": context_sim,
                            "structural_similarity": structural_sim,
                            "suggested_type": self._suggest_relationship_type(
                                semantic_sim, context_sim, structural_sim
                            ),
                            "rationale": self._generate_relationship_rationale(
                                item_a, item_b, semantic_sim, context_sim, structural_sim
                            )
                        }
                        suggestions.append(suggestion)

            # Sort by confidence and limit
            suggestions.sort(key=lambda x: x["confidence_score"], reverse=True)
            return suggestions[:limit]

        except Exception as e:
            logger.error(f"Error discovering relationships: {str(e)}")
            raise

    async def _calculate_semantic_similarity(
        self,
        item_a: SearchIndex,
        item_b: SearchIndex
    ) -> float:
        """Calculate semantic similarity between two items."""
        # Simple text-based similarity (could be enhanced with embeddings)
        text_a = f"{item_a.title} {item_a.description or ''}".lower()
        text_b = f"{item_b.title} {item_b.description or ''}".lower()

        # Extract words
        words_a = set(text_a.split())
        words_b = set(text_b.split())

        # Calculate Jaccard similarity
        intersection = len(words_a.intersection(words_b))
        union = len(words_a.union(words_b))

        return intersection / union if union > 0 else 0.0

    async def _calculate_context_similarity(
        self,
        item_a: SearchIndex,
        item_b: SearchIndex
    ) -> float:
        """Calculate contextual similarity between two items."""
        similarity = 0.0

        # Category similarity
        if item_a.category and item_b.category:
            if item_a.category.lower() == item_b.category.lower():
                similarity += 0.4
            elif any(word in item_b.category.lower() for word in item_a.category.lower().split()):
                similarity += 0.2

        # Tag similarity
        tags_a = set(item_a.tags or [])
        tags_b = set(item_b.tags or [])

        if tags_a and tags_b:
            tag_intersection = len(tags_a.intersection(tags_b))
            tag_union = len(tags_a.union(tags_b))
            similarity += (tag_intersection / tag_union) * 0.3 if tag_union > 0 else 0

        # Quality similarity (items of similar quality are more likely related)
        quality_diff = abs(item_a.quality_score - item_b.quality_score)
        similarity += (1.0 - quality_diff) * 0.3

        return min(1.0, similarity)

    async def _calculate_structural_similarity(
        self,
        item_a: SearchIndex,
        item_b: SearchIndex
    ) -> float:
        """Calculate structural similarity between two items."""
        similarity = 0.0

        # Element type similarity
        if item_a.element_type == item_b.element_type:
            similarity += 0.5
        elif self._are_similar_element_types(item_a.element_type, item_b.element_type):
            similarity += 0.3

        # Framework compatibility
        framework_compatibility = {
            ("ISF", "NIST_CSF_2"): 0.8,
            ("ISF", "ISO_27001"): 0.9,
            ("NIST_CSF_2", "ISO_27001"): 0.7,
            ("CIS_CONTROLS", "NIST_CSF_2"): 0.6,
            ("CIS_CONTROLS", "ISO_27001"): 0.5,
            ("CIS_CONTROLS", "ISF"): 0.4
        }

        framework_pair = (item_a.framework, item_b.framework)
        reverse_pair = (item_b.framework, item_a.framework)

        compatibility = framework_compatibility.get(framework_pair) or \
                       framework_compatibility.get(reverse_pair, 0.3)

        similarity += compatibility * 0.5

        return min(1.0, similarity)

    def _are_similar_element_types(self, type_a: str, type_b: str) -> bool:
        """Check if two element types are similar."""
        similar_types = {
            "control": ["safeguard", "subcategory"],
            "safeguard": ["control", "subcategory"],
            "subcategory": ["control", "safeguard"],
            "category": ["domain", "area"],
            "domain": ["category", "area"],
            "area": ["domain", "category"]
        }

        return type_b in similar_types.get(type_a, [])

    def _calculate_relationship_confidence(
        self,
        strength: float,
        semantic_sim: float,
        context_sim: float,
        structural_sim: float
    ) -> float:
        """Calculate overall confidence score for a relationship."""
        # Weighted combination of similarity scores
        confidence = (
            semantic_sim * 0.4 +
            context_sim * 0.3 +
            structural_sim * 0.2 +
            strength * 0.1
        )

        return min(1.0, confidence)

    def _suggest_relationship_type(
        self,
        semantic_sim: float,
        context_sim: float,
        structural_sim: float
    ) -> str:
        """Suggest the most appropriate relationship type."""
        if semantic_sim > 0.8 and context_sim > 0.7:
            return "equivalent"
        elif semantic_sim > 0.6 and structural_sim > 0.6:
            return "implements"
        elif context_sim > 0.5:
            return "supports"
        else:
            return "related"

    def _generate_relationship_rationale(
        self,
        item_a: SearchIndex,
        item_b: SearchIndex,
        semantic_sim: float,
        context_sim: float,
        structural_sim: float
    ) -> str:
        """Generate rationale for the suggested relationship."""
        rationales = []

        if semantic_sim > 0.6:
            rationales.append(f"High semantic similarity ({semantic_sim:.2f})")

        if context_sim > 0.5:
            rationales.append(f"Similar context ({context_sim:.2f})")

        if structural_sim > 0.5:
            rationales.append(f"Compatible structure ({structural_sim:.2f})")

        if item_a.category and item_b.category and item_a.category.lower() == item_b.category.lower():
            rationales.append("Same category")

        return "; ".join(rationales) if rationales else "Potential relationship detected"
