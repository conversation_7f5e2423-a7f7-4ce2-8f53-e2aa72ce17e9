"""
ISF (Information Security Forum) Import Service.

This module implements the ISF Standard of Good Practice import service
that was driven by TDD tests. It provides comprehensive data parsing,
validation, and import functionality for ISF framework data.

Features:
- Multi-format parsing (JSON, CSV, XML)
- Data validation and error handling
- Batch processing and transaction management
- Progress tracking and logging
- Version management and conflict resolution
"""

import json
import csv
import xml.etree.ElementTree as ET
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable, Union, IO
from dataclasses import dataclass, field
from decimal import Decimal
import logging
from io import StringIO

from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError, SQLAlchemyError
from sqlalchemy import text

from api.models.isf import ISFVersion, ISFSecurityArea, ISFControl
from api.database import get_db
from api.services.base_service import ImportServiceBase, ServiceError, ValidationError

logger = logging.getLogger(__name__)


@dataclass
class ISFImportProgress:
    """Progress tracking for ISF import operations."""
    stage: str
    progress_percentage: float
    current_item: Optional[str] = None
    total_items: int = 0
    processed_items: int = 0
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)


@dataclass
class ISFImportResult:
    """Result of ISF import operation."""
    success: bool
    version_id: Optional[int] = None
    imported_security_areas: int = 0
    imported_controls: int = 0
    processing_time: float = 0.0
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    replaced_existing: bool = False
    
    @property
    def total_imported(self) -> int:
        """Total number of imported items."""
        return self.imported_security_areas + self.imported_controls
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary."""
        return {
            "success": self.success,
            "version_id": self.version_id,
            "imported_security_areas": self.imported_security_areas,
            "imported_controls": self.imported_controls,
            "total_imported": self.total_imported,
            "processing_time": self.processing_time,
            "errors": self.errors,
            "warnings": self.warnings,
            "replaced_existing": self.replaced_existing
        }
    
    def to_json(self) -> str:
        """Convert result to JSON string."""
        return json.dumps(self.to_dict(), default=str)


class ISFImportError(Exception):
    """Custom exception for ISF import errors."""
    
    def __init__(self, message: str, error_type: str = "IMPORT_ERROR"):
        super().__init__(message)
        self.error_type = error_type
        self.message = message


@dataclass
class ParsedISFData:
    """Parsed ISF framework data structure."""
    version: str
    release_date: Optional[datetime] = None
    description: Optional[str] = None
    security_areas: List[Dict[str, Any]] = field(default_factory=list)


class ISFDataParser:
    """Parser for ISF framework data in various formats."""
    
    def parse_json(self, json_data: str) -> ParsedISFData:
        """Parse ISF data from JSON format."""
        try:
            data = json.loads(json_data)
        except json.JSONDecodeError as e:
            raise ISFImportError(f"Invalid JSON format: {str(e)}", "PARSE_ERROR")
        
        # Validate required fields
        if "version" not in data:
            raise ISFImportError("Missing required field: version", "VALIDATION_ERROR")
        
        if "security_areas" not in data:
            raise ISFImportError("Missing required field: security_areas", "VALIDATION_ERROR")
        
        # Parse release date if provided
        release_date = None
        if "release_date" in data:
            if isinstance(data["release_date"], str):
                try:
                    release_date = datetime.fromisoformat(data["release_date"].replace("Z", "+00:00"))
                except ValueError:
                    try:
                        release_date = datetime.strptime(data["release_date"], "%Y-%m-%d")
                    except ValueError:
                        logger.warning(f"Could not parse release_date: {data['release_date']}")
        
        return ParsedISFData(
            version=data["version"],
            release_date=release_date,
            description=data.get("description"),
            security_areas=data["security_areas"]
        )
    
    def parse_csv(self, csv_data: str, version: str = None) -> ParsedISFData:
        """Parse ISF data from CSV format."""
        try:
            csv_reader = csv.DictReader(StringIO(csv_data))
            
            # Group data by security areas
            areas_dict = {}
            
            for row in csv_reader:
                area_id = row.get("area_id")
                if not area_id:
                    continue
                
                if area_id not in areas_dict:
                    areas_dict[area_id] = {
                        "area_id": area_id,
                        "name": row.get("area_name", ""),
                        "description": row.get("area_description", ""),
                        "controls": []
                    }
                
                # Add control if present
                control_id = row.get("control_id")
                if control_id:
                    control = {
                        "control_id": control_id,
                        "name": row.get("control_name", ""),
                        "description": row.get("control_description", ""),
                        "objective": row.get("control_objective", ""),
                        "guidance": row.get("control_guidance", ""),
                        "control_type": row.get("control_type"),
                        "maturity_level": row.get("maturity_level")
                    }
                    areas_dict[area_id]["controls"].append(control)
            
            return ParsedISFData(
                version=version or "imported",
                security_areas=list(areas_dict.values())
            )
            
        except Exception as e:
            raise ISFImportError(f"CSV parsing error: {str(e)}", "PARSE_ERROR")
    
    def parse_xml(self, xml_data: str) -> ParsedISFData:
        """Parse ISF data from XML format."""
        try:
            root = ET.fromstring(xml_data)
            
            version = root.get("version")
            if not version:
                raise ISFImportError("Missing version attribute in XML root", "VALIDATION_ERROR")
            
            release_date = None
            release_date_str = root.get("release_date")
            if release_date_str:
                try:
                    release_date = datetime.fromisoformat(release_date_str)
                except ValueError:
                    try:
                        release_date = datetime.strptime(release_date_str, "%Y-%m-%d")
                    except ValueError:
                        logger.warning(f"Could not parse release_date: {release_date_str}")
            
            security_areas = []
            for area_elem in root.findall("security_area"):
                area = {
                    "area_id": area_elem.get("area_id"),
                    "name": area_elem.get("name"),
                    "description": area_elem.find("description").text if area_elem.find("description") is not None else "",
                    "controls": []
                }
                
                for control_elem in area_elem.findall("control"):
                    control = {
                        "control_id": control_elem.get("control_id"),
                        "name": control_elem.get("name"),
                        "control_type": control_elem.get("type"),
                        "maturity_level": control_elem.get("maturity"),
                        "description": control_elem.find("description").text if control_elem.find("description") is not None else "",
                        "objective": control_elem.find("objective").text if control_elem.find("objective") is not None else ""
                    }
                    area["controls"].append(control)
                
                security_areas.append(area)
            
            return ParsedISFData(
                version=version,
                release_date=release_date,
                security_areas=security_areas
            )
            
        except ET.ParseError as e:
            raise ISFImportError(f"XML parsing error: {str(e)}", "PARSE_ERROR")
        except Exception as e:
            raise ISFImportError(f"XML processing error: {str(e)}", "PARSE_ERROR")


class ISFDataValidator:
    """Validator for ISF framework data."""
    
    VALID_CONTROL_TYPES = {"policy", "technical", "administrative"}
    VALID_MATURITY_LEVELS = {"basic", "intermediate", "advanced"}
    
    def validate_framework_structure(self, data: Dict[str, Any]) -> tuple[bool, List[str]]:
        """Validate ISF framework data structure."""
        errors = []
        
        # Validate version
        if not data.get("version"):
            errors.append("Missing or empty version field")
        
        # Validate security areas
        security_areas = data.get("security_areas", [])
        if not security_areas:
            errors.append("No security areas found")
        
        seen_area_ids = set()
        seen_control_ids = set()
        
        for i, area in enumerate(security_areas):
            area_prefix = f"Security area {i + 1}"
            
            # Validate area structure
            if not area.get("area_id"):
                errors.append(f"{area_prefix}: Missing area_id")
            elif area["area_id"] in seen_area_ids:
                errors.append(f"{area_prefix}: Duplicate area_id: {area['area_id']}")
            else:
                seen_area_ids.add(area["area_id"])
            
            if not area.get("name"):
                errors.append(f"{area_prefix}: Missing name")
            
            # Validate controls
            controls = area.get("controls", [])
            for j, control in enumerate(controls):
                control_prefix = f"{area_prefix}, Control {j + 1}"
                
                # Validate control structure
                if not control.get("control_id"):
                    errors.append(f"{control_prefix}: Missing control_id")
                elif control["control_id"] in seen_control_ids:
                    errors.append(f"Duplicate control_id: {control['control_id']}")
                else:
                    seen_control_ids.add(control["control_id"])
                
                if not control.get("name"):
                    errors.append(f"{control_prefix}: Missing name")
                
                # Validate control type
                control_type = control.get("control_type")
                if control_type and control_type not in self.VALID_CONTROL_TYPES:
                    errors.append(f"{control_prefix}: Invalid control_type: {control_type}")
                
                # Validate maturity level
                maturity_level = control.get("maturity_level")
                if maturity_level and maturity_level not in self.VALID_MATURITY_LEVELS:
                    errors.append(f"{control_prefix}: Invalid maturity_level: {maturity_level}")
        
        return len(errors) == 0, errors


class ISFImportService(ImportServiceBase):
    """Optimized service for importing ISF framework data."""

    def __init__(self, db_session: Session, config: Optional[Dict[str, Any]] = None):
        super().__init__(db_session, config)
        self.parser = ISFDataParser()
        self.validator = ISFDataValidator()

    def _get_import_schema(self) -> Dict[str, Any]:
        """Get validation schema for ISF import data."""
        return {
            "version": {"required": True, "type": str, "min_length": 1},
            "security_areas": {"required": True, "type": list, "min_length": 1},
            "release_date": {"required": False, "type": str},
            "description": {"required": False, "type": str}
        }

    def _query_version_exists(self, version: str, framework: str) -> bool:
        """Query database to check if ISF version exists."""
        result = self.execute_query(
            "SELECT COUNT(*) FROM isf_versions WHERE version = :version AND deleted_at IS NULL",
            {"version": version}
        )
        return result.scalar() > 0

    def validate_service_config(self) -> bool:
        """Validate ISF import service configuration."""
        return True  # Basic validation - can be extended
    
    def import_from_json(
        self,
        json_data: str,
        replace_existing: bool = False,
        progress_callback: Optional[Callable[[ISFImportProgress], None]] = None
    ) -> ISFImportResult:
        """Import ISF framework from JSON data with optimized performance."""
        with self.performance_tracking("isf_json_import") as metrics:
            result = ISFImportResult(success=False)

            try:
                # Progress: Parsing
                if progress_callback:
                    progress_callback(ISFImportProgress("parsing", 0.0))

                # Parse JSON data with retry mechanism
                parsed_data = self.retry_operation(
                    lambda: self.parser.parse_json(json_data),
                    max_attempts=2
                )

                # Progress: Validation
                if progress_callback:
                    progress_callback(ISFImportProgress("validation", 20.0))

                # Validate using base service validation
                self.validate_import_data({
                    "version": parsed_data.version,
                    "security_areas": parsed_data.security_areas,
                    "release_date": parsed_data.release_date,
                    "description": parsed_data.description
                })

                # Additional ISF-specific validation
                is_valid, validation_errors = self.validator.validate_framework_structure({
                    "version": parsed_data.version,
                    "security_areas": parsed_data.security_areas
                })

                if not is_valid:
                    result.errors.extend(validation_errors)
                    return result

                # Check for existing version using cache
                if self.check_duplicate_version(parsed_data.version, "isf") and not replace_existing:
                    raise ServiceError(
                        f"Version {parsed_data.version} already exists",
                        "VERSION_CONFLICT"
                    )

                # Progress: Database operations
                if progress_callback:
                    progress_callback(ISFImportProgress("database_operations", 40.0))

                # Import to database with transaction management
                result = self._import_to_database_optimized(parsed_data, replace_existing, progress_callback)

                # Update metrics
                metrics.records_processed = result.total_imported

                # Progress: Completed
                if progress_callback:
                    progress_callback(ISFImportProgress("completed", 100.0))

                return result

            except (ServiceError, ValidationError) as e:
                result.errors.append(f"{e.error_code}: {e.message}")
                return result
            except ISFImportError as e:
                result.errors.append(f"{e.error_type}: {e.message}")
                return result
            except Exception as e:
                result.errors.append(f"Unexpected error: {str(e)}")
                logger.exception("Unexpected error during ISF import")
                return result
    
    def import_from_csv(
        self,
        csv_data: str,
        version: str,
        replace_existing: bool = False,
        progress_callback: Optional[Callable[[ISFImportProgress], None]] = None
    ) -> ISFImportResult:
        """Import ISF framework from CSV data."""
        start_time = datetime.now()
        result = ISFImportResult(success=False)
        
        try:
            # Parse CSV data
            parsed_data = self.parser.parse_csv(csv_data, version)
            
            # Validate and import
            is_valid, validation_errors = self.validator.validate_framework_structure({
                "version": parsed_data.version,
                "security_areas": parsed_data.security_areas
            })
            
            if not is_valid:
                result.errors.extend(validation_errors)
                return result
            
            # Import to database
            result = self._import_to_database(parsed_data, replace_existing, progress_callback)
            result.processing_time = (datetime.now() - start_time).total_seconds()
            
            return result
            
        except Exception as e:
            result.errors.append(f"CSV import error: {str(e)}")
            result.processing_time = (datetime.now() - start_time).total_seconds()
            return result
    
    def _import_to_database(
        self,
        parsed_data: ParsedISFData,
        replace_existing: bool,
        progress_callback: Optional[Callable[[ISFImportProgress], None]] = None,
        batch_size: int = 100
    ) -> ISFImportResult:
        """Import parsed data to database with transaction management."""
        result = ISFImportResult(success=False)
        
        try:
            # Check for existing version
            existing_version = self.db.query(ISFVersion).filter_by(version=parsed_data.version).first()
            
            if existing_version and not replace_existing:
                raise ISFImportError(
                    f"Version {parsed_data.version} already exists",
                    "VERSION_CONFLICT"
                )
            
            # Begin transaction
            if existing_version and replace_existing:
                # Soft delete existing version and related data
                existing_version.soft_delete(self.db)
                result.replaced_existing = True
            
            # Create new version
            version = ISFVersion(
                version=parsed_data.version,
                release_date=parsed_data.release_date,
                description=parsed_data.description,
                is_current=True,
                import_date=datetime.utcnow()
            )
            
            # Set as current version
            version.set_as_current(self.db)
            self.db.add(version)
            self.db.flush()  # Get version ID
            
            result.version_id = version.id
            
            # Import security areas and controls
            total_items = len(parsed_data.security_areas) + sum(
                len(area.get("controls", [])) for area in parsed_data.security_areas
            )
            processed_items = 0
            
            for area_index, area_data in enumerate(parsed_data.security_areas):
                # Create security area
                security_area = ISFSecurityArea(
                    area_id=area_data["area_id"],
                    name=area_data["name"],
                    description=area_data.get("description"),
                    version_id=version.id,
                    order_index=area_index + 1
                )
                self.db.add(security_area)
                self.db.flush()  # Get area ID
                
                result.imported_security_areas += 1
                processed_items += 1
                
                # Progress update
                if progress_callback:
                    progress = 40.0 + (processed_items / total_items) * 50.0
                    progress_callback(ISFImportProgress(
                        "importing",
                        progress,
                        f"Security area: {area_data['name']}",
                        total_items,
                        processed_items
                    ))
                
                # Import controls for this area
                controls = area_data.get("controls", [])
                for control_index, control_data in enumerate(controls):
                    control = ISFControl(
                        control_id=control_data["control_id"],
                        name=control_data["name"],
                        description=control_data.get("description"),
                        objective=control_data.get("objective"),
                        guidance=control_data.get("guidance"),
                        security_area_id=security_area.id,
                        version_id=version.id,
                        order_index=control_index + 1,
                        control_type=control_data.get("control_type"),
                        maturity_level=control_data.get("maturity_level")
                    )
                    self.db.add(control)
                    
                    result.imported_controls += 1
                    processed_items += 1
                    
                    # Batch commit for performance
                    if processed_items % batch_size == 0:
                        self.db.commit()
                    
                    # Progress update
                    if progress_callback:
                        progress = 40.0 + (processed_items / total_items) * 50.0
                        progress_callback(ISFImportProgress(
                            "importing",
                            progress,
                            f"Control: {control_data['name']}",
                            total_items,
                            processed_items
                        ))
            
            # Final commit
            self.db.commit()
            result.success = True
            
            return result
            
        except SQLAlchemyError as e:
            self.db.rollback()
            raise ISFImportError(f"Database error: {str(e)}", "DATABASE_ERROR")
        except Exception as e:
            self.db.rollback()
            raise ISFImportError(f"Import error: {str(e)}", "IMPORT_ERROR")

    def _import_to_database_optimized(
        self,
        parsed_data: ParsedISFData,
        replace_existing: bool,
        progress_callback: Optional[Callable[[ISFImportProgress], None]] = None
    ) -> ISFImportResult:
        """Optimized database import with batch processing and transaction management."""
        result = ISFImportResult(success=False)

        with self.database_transaction():
            # Check for existing version
            if self.check_duplicate_version(parsed_data.version, "isf") and replace_existing:
                # Soft delete existing version
                self.execute_query(
                    "UPDATE isf_versions SET deleted_at = NOW() WHERE version = :version",
                    {"version": parsed_data.version}
                )
                result.replaced_existing = True

            # Create new version using ORM for compatibility
            version = ISFVersion(
                version=parsed_data.version,
                release_date=parsed_data.release_date,
                description=parsed_data.description,
                is_current=True,
                import_date=datetime.utcnow()
            )

            # Set as current version
            version.set_as_current(self.db)
            self.db.add(version)
            self.db.flush()  # Get version ID

            result.version_id = version.id

            # Calculate total items for progress tracking
            total_items = len(parsed_data.security_areas) + sum(
                len(area.get("controls", [])) for area in parsed_data.security_areas
            )
            processed_items = 0

            # Process security areas and controls in optimized batches
            area_id_mapping = {}

            # Batch process security areas
            def process_security_areas_batch(areas_batch):
                nonlocal processed_items
                for area_index, area_data in enumerate(areas_batch):
                    security_area = ISFSecurityArea(
                        area_id=area_data["area_id"],
                        name=area_data["name"],
                        description=area_data.get("description"),
                        version_id=version.id,
                        order_index=area_index + 1
                    )
                    self.db.add(security_area)
                    self.db.flush()  # Get area ID for controls

                    area_id_mapping[area_data["area_id"]] = security_area.id
                    result.imported_security_areas += 1
                    processed_items += 1

                    # Progress update
                    if progress_callback:
                        progress = 40.0 + (processed_items / total_items) * 50.0
                        progress_callback(ISFImportProgress(
                            "importing",
                            progress,
                            f"Security area: {area_data['name']}",
                            total_items,
                            processed_items
                        ))

            # Process areas in smaller batches
            self.process_in_batches(
                parsed_data.security_areas,
                process_security_areas_batch,
                batch_size=10  # Smaller batches for areas
            )

            # Batch process controls
            all_controls = []
            for area_data in parsed_data.security_areas:
                for control_index, control_data in enumerate(area_data.get("controls", [])):
                    control_data_with_area = control_data.copy()
                    control_data_with_area["area_id"] = area_data["area_id"]
                    control_data_with_area["order_index"] = control_index + 1
                    all_controls.append(control_data_with_area)

            def process_controls_batch(controls_batch):
                nonlocal processed_items
                for control_data in controls_batch:
                    area_db_id = area_id_mapping.get(control_data["area_id"])
                    if not area_db_id:
                        continue

                    control = ISFControl(
                        control_id=control_data["control_id"],
                        name=control_data["name"],
                        description=control_data.get("description"),
                        objective=control_data.get("objective"),
                        guidance=control_data.get("guidance"),
                        security_area_id=area_db_id,
                        version_id=version.id,
                        order_index=control_data["order_index"],
                        control_type=control_data.get("control_type"),
                        maturity_level=control_data.get("maturity_level")
                    )
                    self.db.add(control)

                    result.imported_controls += 1
                    processed_items += 1

                    # Progress update
                    if progress_callback:
                        progress = 40.0 + (processed_items / total_items) * 50.0
                        progress_callback(ISFImportProgress(
                            "importing",
                            progress,
                            f"Control: {control_data['name']}",
                            total_items,
                            processed_items
                        ))

            # Process controls in optimized batches
            self.process_in_batches(
                all_controls,
                process_controls_batch,
                batch_size=self.batch_config.batch_size
            )

            result.success = True

            # Clear cache for version checks
            self.cache.clear()

            return result
