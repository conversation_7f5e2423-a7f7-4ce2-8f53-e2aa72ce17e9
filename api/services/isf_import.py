"""
ISF Data Import Service.

This module provides functionality to import official ISF (Information Security Framework)
data into the database, including security areas, controls, and framework versions.
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from api.database import get_db
from api.models.isf import ISFVersion, ISFSecurityArea, ISFControl

logger = logging.getLogger(__name__)


class ISFImportService:
    """Service for importing ISF framework data."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def import_isf_2022(self) -> Dict[str, Any]:
        """
        Import the official ISF 2022 framework data.
        
        Returns:
            Dict containing import results and statistics
        """
        logger.info("Starting ISF 2022 data import...")
        
        try:
            # Create ISF 2022 version
            version = self._create_isf_version()
            
            # Import security areas
            security_areas = self._import_security_areas(version.id)
            
            # Import controls
            controls = self._import_controls(version.id, security_areas)
            
            # Commit all changes
            self.db.commit()
            
            result = {
                "success": True,
                "version_id": version.id,
                "version": version.version,
                "statistics": {
                    "security_areas": len(security_areas),
                    "controls": len(controls),
                    "total_records": 1 + len(security_areas) + len(controls)
                },
                "import_time": datetime.utcnow().isoformat(),
                "message": "ISF 2022 data imported successfully"
            }
            
            logger.info(f"ISF 2022 import completed successfully: {result['statistics']}")
            return result
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"ISF 2022 import failed: {str(e)}")
            raise
    
    def _create_isf_version(self) -> ISFVersion:
        """Create the ISF 2022 version record."""
        # Check if version already exists
        existing = self.db.query(ISFVersion).filter(
            ISFVersion.version == "2022",
            ISFVersion.deleted_at.is_(None)
        ).first()
        
        if existing:
            logger.info("ISF 2022 version already exists, updating...")
            existing.is_current = True
            existing.updated_at = datetime.utcnow()
            return existing
        
        # Create new version
        version = ISFVersion(
            version="2022",
            release_date="2022-01-01",
            description="Information Security Framework 2022 - Comprehensive security controls and guidelines",
            is_current=True,
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        # Set all other versions as non-current
        self.db.query(ISFVersion).filter(
            ISFVersion.deleted_at.is_(None)
        ).update({"is_current": False})
        
        self.db.add(version)
        self.db.flush()  # Get the ID
        
        logger.info(f"Created ISF version: {version.version} (ID: {version.id})")
        return version
    
    def _import_security_areas(self, version_id: int) -> List[ISFSecurityArea]:
        """Import ISF 2022 security areas."""
        security_areas_data = self._get_isf_2022_security_areas()
        security_areas = []
        
        for area_data in security_areas_data:
            # Check if area already exists
            existing = self.db.query(ISFSecurityArea).filter(
                ISFSecurityArea.area_id == area_data["area_id"],
                ISFSecurityArea.version_id == version_id,
                ISFSecurityArea.deleted_at.is_(None)
            ).first()
            
            if existing:
                # Update existing area
                existing.name = area_data["name"]
                existing.description = area_data["description"]
                existing.order_index = area_data["order_index"]
                existing.updated_at = datetime.utcnow()
                security_areas.append(existing)
            else:
                # Create new area
                area = ISFSecurityArea(
                    area_id=area_data["area_id"],
                    name=area_data["name"],
                    description=area_data["description"],
                    version_id=version_id,
                    order_index=area_data["order_index"],
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                self.db.add(area)
                security_areas.append(area)
        
        self.db.flush()  # Get IDs
        logger.info(f"Imported {len(security_areas)} security areas")
        return security_areas
    
    def _import_controls(self, version_id: int, security_areas: List[ISFSecurityArea]) -> List[ISFControl]:
        """Import ISF 2022 controls."""
        # Create area_id to database_id mapping
        area_mapping = {area.area_id: area.id for area in security_areas}
        
        controls_data = self._get_isf_2022_controls()
        controls = []
        
        for control_data in controls_data:
            security_area_id = area_mapping.get(control_data["security_area_id"])
            if not security_area_id:
                logger.warning(f"Security area not found for control {control_data['control_id']}")
                continue
            
            # Check if control already exists
            existing = self.db.query(ISFControl).filter(
                ISFControl.control_id == control_data["control_id"],
                ISFControl.version_id == version_id,
                ISFControl.deleted_at.is_(None)
            ).first()
            
            if existing:
                # Update existing control
                existing.name = control_data["name"]
                existing.description = control_data["description"]
                existing.objective = control_data.get("objective")
                existing.guidance = control_data.get("guidance")
                existing.security_area_id = security_area_id
                existing.control_type = control_data.get("control_type")
                existing.maturity_level = control_data.get("maturity_level")
                existing.order_index = control_data["order_index"]
                existing.updated_at = datetime.utcnow()
                controls.append(existing)
            else:
                # Create new control
                control = ISFControl(
                    control_id=control_data["control_id"],
                    name=control_data["name"],
                    description=control_data["description"],
                    objective=control_data.get("objective"),
                    guidance=control_data.get("guidance"),
                    security_area_id=security_area_id,
                    version_id=version_id,
                    control_type=control_data.get("control_type", "preventive"),
                    maturity_level=control_data.get("maturity_level", 1),
                    order_index=control_data["order_index"],
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                self.db.add(control)
                controls.append(control)
        
        self.db.flush()  # Get IDs
        logger.info(f"Imported {len(controls)} controls")
        return controls
    
    def _get_isf_2022_security_areas(self) -> List[Dict[str, Any]]:
        """Get ISF 2022 security areas data."""
        return [
            {
                "area_id": "ISF.01",
                "name": "Information Security Policy",
                "description": "Establish and maintain information security policies and procedures",
                "order_index": 1
            },
            {
                "area_id": "ISF.02", 
                "name": "Organization of Information Security",
                "description": "Organize information security management and assign responsibilities",
                "order_index": 2
            },
            {
                "area_id": "ISF.03",
                "name": "Human Resource Security",
                "description": "Ensure personnel understand their responsibilities and are suitable for roles",
                "order_index": 3
            },
            {
                "area_id": "ISF.04",
                "name": "Asset Management",
                "description": "Identify, classify, and protect organizational assets",
                "order_index": 4
            },
            {
                "area_id": "ISF.05",
                "name": "Access Control",
                "description": "Control access to information and information processing facilities",
                "order_index": 5
            },
            {
                "area_id": "ISF.06",
                "name": "Cryptography",
                "description": "Ensure proper and effective use of cryptography to protect information",
                "order_index": 6
            },
            {
                "area_id": "ISF.07",
                "name": "Physical and Environmental Security",
                "description": "Prevent unauthorized physical access and protect against environmental threats",
                "order_index": 7
            },
            {
                "area_id": "ISF.08",
                "name": "Operations Security",
                "description": "Ensure correct and secure operation of information processing facilities",
                "order_index": 8
            },
            {
                "area_id": "ISF.09",
                "name": "Communications Security",
                "description": "Protect information in networks and supporting information processing facilities",
                "order_index": 9
            },
            {
                "area_id": "ISF.10",
                "name": "System Acquisition, Development and Maintenance",
                "description": "Ensure security is built into information systems throughout their lifecycle",
                "order_index": 10
            },
            {
                "area_id": "ISF.11",
                "name": "Supplier Relationships",
                "description": "Protect organization's assets accessible by suppliers",
                "order_index": 11
            },
            {
                "area_id": "ISF.12",
                "name": "Information Security Incident Management",
                "description": "Ensure consistent and effective approach to information security incidents",
                "order_index": 12
            },
            {
                "area_id": "ISF.13",
                "name": "Information Security in Business Continuity",
                "description": "Ensure information security continuity during adverse situations",
                "order_index": 13
            },
            {
                "area_id": "ISF.14",
                "name": "Compliance",
                "description": "Avoid breaches of legal, statutory, regulatory or contractual obligations",
                "order_index": 14
            }
        ]
    
    def _get_isf_2022_controls(self) -> List[Dict[str, Any]]:
        """Get ISF 2022 controls data."""
        # This would typically be loaded from a comprehensive data file
        # For now, providing a sample of key controls from each area
        return [
            # ISF.01 - Information Security Policy
            {
                "control_id": "ISF.01.01",
                "name": "Information Security Policy Document",
                "description": "Establish, document, approve, communicate, apply, evaluate and maintain policies for information security",
                "objective": "To provide management direction and support for information security",
                "guidance": "The policy should be reviewed at planned intervals or if significant changes occur",
                "security_area_id": "ISF.01",
                "control_type": "administrative",
                "maturity_level": 1,
                "order_index": 1
            },
            {
                "control_id": "ISF.01.02", 
                "name": "Review of Information Security Policies",
                "description": "Information security policies should be reviewed at planned intervals",
                "objective": "To ensure policies remain suitable, adequate and effective",
                "guidance": "Reviews should consider changes in business requirements, risk environment, and legal obligations",
                "security_area_id": "ISF.01",
                "control_type": "administrative",
                "maturity_level": 2,
                "order_index": 2
            },
            # ISF.02 - Organization of Information Security  
            {
                "control_id": "ISF.02.01",
                "name": "Information Security Roles and Responsibilities",
                "description": "All information security responsibilities should be defined and allocated",
                "objective": "To ensure information security responsibilities are clearly defined",
                "guidance": "Responsibilities should be documented and communicated to relevant personnel",
                "security_area_id": "ISF.02",
                "control_type": "administrative",
                "maturity_level": 1,
                "order_index": 1
            },
            # ISF.05 - Access Control
            {
                "control_id": "ISF.05.01",
                "name": "Access Control Policy",
                "description": "Establish, document and review access control policy based on business and security requirements",
                "objective": "To limit access to information and information processing facilities",
                "guidance": "Policy should address all aspects of access control including user access management",
                "security_area_id": "ISF.05",
                "control_type": "technical",
                "maturity_level": 1,
                "order_index": 1
            },
            {
                "control_id": "ISF.05.02",
                "name": "User Registration and De-registration",
                "description": "Formal user registration and de-registration process should be implemented",
                "objective": "To ensure authorized user access and prevent unauthorized access",
                "guidance": "Process should include approval procedures and regular review of access rights",
                "security_area_id": "ISF.05",
                "control_type": "administrative",
                "maturity_level": 2,
                "order_index": 2
            }
            # Additional controls would be added here for a complete implementation
        ]


def import_isf_2022_data(db: Session = None) -> Dict[str, Any]:
    """
    Convenience function to import ISF 2022 data.

    Args:
        db: Database session (optional, will create one if not provided)

    Returns:
        Dict containing import results
    """
    if db is None:
        db = next(get_db())

    service = ISFImportService(db)
    return service.import_isf_2022()
