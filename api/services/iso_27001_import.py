"""
ISO/IEC 27001 Data Import Service.

This module provides functionality to import official ISO/IEC 27001 Information
Security Management System data into the database, including domains, controls,
and implementation guidance.
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from api.database import get_db
from api.models.iso_27001 import (
    ISO27001Version, ISO27001Domain, ISO27001Control, 
    ISO27001ImplementationExample
)

logger = logging.getLogger(__name__)


class ISO27001ImportService:
    """Service for importing ISO/IEC 27001 framework data."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def import_iso_27001_2022(self) -> Dict[str, Any]:
        """
        Import the official ISO/IEC 27001:2022 framework data.
        
        Returns:
            Dict containing import results and statistics
        """
        logger.info("Starting ISO/IEC 27001:2022 data import...")
        
        try:
            # Create ISO/IEC 27001:2022 version
            version = self._create_iso_27001_version()
            
            # Import domains
            domains = self._import_domains(version.id)
            
            # Import controls
            controls = self._import_controls(version.id, domains)
            
            # Import implementation examples
            examples = self._import_implementation_examples(controls)
            
            # Commit all changes
            self.db.commit()
            
            result = {
                "success": True,
                "version_id": version.id,
                "version": version.version,
                "statistics": {
                    "domains": len(domains),
                    "controls": len(controls),
                    "implementation_examples": len(examples),
                    "total_records": 1 + len(domains) + len(controls) + len(examples)
                },
                "import_time": datetime.utcnow().isoformat(),
                "message": "ISO/IEC 27001:2022 data imported successfully"
            }
            
            logger.info(f"ISO/IEC 27001:2022 import completed successfully: {result['statistics']}")
            return result
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"ISO/IEC 27001:2022 import failed: {str(e)}")
            raise
    
    def _create_iso_27001_version(self) -> ISO27001Version:
        """Create the ISO/IEC 27001:2022 version record."""
        # Check if version already exists
        existing = self.db.query(ISO27001Version).filter(
            ISO27001Version.version == "2022",
            ISO27001Version.deleted_at.is_(None)
        ).first()
        
        if existing:
            logger.info("ISO/IEC 27001:2022 version already exists, updating...")
            existing.is_current = True
            existing.updated_at = datetime.utcnow()
            return existing
        
        # Create new version
        version = ISO27001Version(
            version="2022",
            release_date="2022-10-25",
            description="ISO/IEC 27001:2022 Information Security Management Systems - Requirements",
            is_current=True,
            standard_url="https://www.iso.org/standard/27001.html",
            documentation_url="https://www.iso.org/obp/ui/#iso:std:iso-iec:27001:ed-3:v1:en",
            certification_body="ISO/IEC",
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        # Set all other versions as non-current
        self.db.query(ISO27001Version).filter(
            ISO27001Version.deleted_at.is_(None)
        ).update({"is_current": False})
        
        self.db.add(version)
        self.db.flush()  # Get the ID
        
        logger.info(f"Created ISO/IEC 27001 version: {version.version} (ID: {version.id})")
        return version
    
    def _import_domains(self, version_id: int) -> List[ISO27001Domain]:
        """Import ISO/IEC 27001:2022 domains."""
        domains_data = self._get_iso_27001_2022_domains()
        domains = []
        
        for domain_data in domains_data:
            # Check if domain already exists
            existing = self.db.query(ISO27001Domain).filter(
                ISO27001Domain.domain_id == domain_data["domain_id"],
                ISO27001Domain.version_id == version_id,
                ISO27001Domain.deleted_at.is_(None)
            ).first()
            
            if existing:
                # Update existing domain
                existing.name = domain_data["name"]
                existing.description = domain_data["description"]
                existing.order_index = domain_data["order_index"]
                existing.implementation_guidance = domain_data.get("implementation_guidance")
                existing.best_practices = domain_data.get("best_practices")
                existing.common_challenges = domain_data.get("common_challenges")
                existing.updated_at = datetime.utcnow()
                domains.append(existing)
            else:
                # Create new domain
                domain = ISO27001Domain(
                    domain_id=domain_data["domain_id"],
                    name=domain_data["name"],
                    description=domain_data["description"],
                    version_id=version_id,
                    order_index=domain_data["order_index"],
                    implementation_guidance=domain_data.get("implementation_guidance"),
                    best_practices=domain_data.get("best_practices"),
                    common_challenges=domain_data.get("common_challenges"),
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                self.db.add(domain)
                domains.append(domain)
        
        self.db.flush()  # Get IDs
        logger.info(f"Imported {len(domains)} domains")
        return domains
    
    def _import_controls(self, version_id: int, domains: List[ISO27001Domain]) -> List[ISO27001Control]:
        """Import ISO/IEC 27001:2022 controls."""
        # Create domain_id to database_id mapping
        domain_mapping = {domain.domain_id: domain.id for domain in domains}
        
        controls_data = self._get_iso_27001_2022_controls()
        controls = []
        
        for control_data in controls_data:
            domain_id = domain_mapping.get(control_data["domain_id"])
            if not domain_id:
                logger.warning(f"Domain not found for control {control_data['control_id']}")
                continue
            
            # Check if control already exists
            existing = self.db.query(ISO27001Control).filter(
                ISO27001Control.control_id == control_data["control_id"],
                ISO27001Control.version_id == version_id,
                ISO27001Control.deleted_at.is_(None)
            ).first()
            
            if existing:
                # Update existing control
                existing.name = control_data["name"]
                existing.description = control_data.get("description")
                existing.objective = control_data.get("objective")
                existing.implementation_guidance = control_data.get("implementation_guidance")
                existing.other_information = control_data.get("other_information")
                existing.domain_id = domain_id
                existing.control_type = control_data.get("control_type")
                existing.order_index = control_data["order_index"]
                existing.requirements = control_data.get("requirements")
                existing.evidence_examples = control_data.get("evidence_examples")
                existing.external_references = control_data.get("external_references")
                existing.updated_at = datetime.utcnow()
                controls.append(existing)
            else:
                # Create new control
                control = ISO27001Control(
                    control_id=control_data["control_id"],
                    name=control_data["name"],
                    description=control_data.get("description"),
                    objective=control_data.get("objective"),
                    implementation_guidance=control_data.get("implementation_guidance"),
                    other_information=control_data.get("other_information"),
                    domain_id=domain_id,
                    version_id=version_id,
                    control_type=control_data.get("control_type", "preventive"),
                    order_index=control_data["order_index"],
                    requirements=control_data.get("requirements"),
                    evidence_examples=control_data.get("evidence_examples"),
                    external_references=control_data.get("external_references"),
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                self.db.add(control)
                controls.append(control)
        
        self.db.flush()  # Get IDs
        logger.info(f"Imported {len(controls)} controls")
        return controls
    
    def _import_implementation_examples(self, controls: List[ISO27001Control]) -> List[ISO27001ImplementationExample]:
        """Import implementation examples for controls."""
        # Create control mapping
        control_mapping = {control.control_id: control.id for control in controls}
        
        examples_data = self._get_iso_27001_2022_implementation_examples()
        examples = []
        
        for example_data in examples_data:
            control_id = control_mapping.get(example_data["control_id"])
            if not control_id:
                logger.warning(f"Control not found for example {example_data['title']}")
                continue
            
            # Create implementation example
            example = ISO27001ImplementationExample(
                control_id=control_id,
                title=example_data["title"],
                description=example_data["description"],
                implementation_approach=example_data.get("implementation_approach"),
                organization_size=example_data.get("organization_size"),
                industry_sector=example_data.get("industry_sector"),
                technology_context=example_data.get("technology_context"),
                geographic_region=example_data.get("geographic_region"),
                implementation_steps=example_data.get("implementation_steps"),
                tools_and_technologies=example_data.get("tools_and_technologies"),
                roles_and_responsibilities=example_data.get("roles_and_responsibilities"),
                policies_and_procedures=example_data.get("policies_and_procedures"),
                implementation_difficulty=example_data.get("implementation_difficulty"),
                estimated_effort=example_data.get("estimated_effort"),
                budget_considerations=example_data.get("budget_considerations"),
                prerequisites=example_data.get("prerequisites"),
                expected_outcomes=example_data.get("expected_outcomes"),
                success_metrics=example_data.get("success_metrics"),
                business_benefits=example_data.get("business_benefits"),
                common_pitfalls=example_data.get("common_pitfalls"),
                lessons_learned=example_data.get("lessons_learned"),
                mitigation_strategies=example_data.get("mitigation_strategies"),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            self.db.add(example)
            examples.append(example)
        
        self.db.flush()  # Get IDs
        logger.info(f"Imported {len(examples)} implementation examples")
        return examples
    
    def _get_iso_27001_2022_domains(self) -> List[Dict[str, Any]]:
        """Get ISO/IEC 27001:2022 domains data."""
        return [
            {
                "domain_id": "A.5",
                "name": "Information security policies",
                "description": "To provide management direction and support for information security in accordance with business requirements and relevant laws and regulations",
                "order_index": 1,
                "implementation_guidance": "Establish comprehensive information security policies that align with business objectives",
                "best_practices": [
                    "Involve senior management in policy development",
                    "Ensure policies are communicated to all personnel",
                    "Review and update policies regularly",
                    "Align policies with business objectives"
                ],
                "common_challenges": [
                    "Gaining management buy-in",
                    "Ensuring policy compliance",
                    "Keeping policies current with business changes"
                ]
            },
            {
                "domain_id": "A.6",
                "name": "Organization of information security",
                "description": "To establish a management framework to initiate and control the implementation and operation of information security within the organization",
                "order_index": 2,
                "implementation_guidance": "Define clear roles, responsibilities, and organizational structures for information security",
                "best_practices": [
                    "Establish clear reporting lines",
                    "Define security roles and responsibilities",
                    "Implement segregation of duties",
                    "Regular security awareness training"
                ],
                "common_challenges": [
                    "Resource allocation",
                    "Role clarity and accountability",
                    "Cross-functional coordination"
                ]
            },
            {
                "domain_id": "A.7",
                "name": "Human resource security",
                "description": "To ensure that personnel understand their responsibilities and are suitable for the roles for which they are considered",
                "order_index": 3,
                "implementation_guidance": "Implement comprehensive HR security processes from hiring to termination",
                "best_practices": [
                    "Background verification for sensitive roles",
                    "Security awareness training programs",
                    "Clear disciplinary processes",
                    "Secure termination procedures"
                ],
                "common_challenges": [
                    "Balancing security with privacy",
                    "Maintaining training effectiveness",
                    "Managing contractor security"
                ]
            },
            {
                "domain_id": "A.8",
                "name": "Asset management",
                "description": "To identify organizational assets and define appropriate protection responsibilities",
                "order_index": 4,
                "implementation_guidance": "Maintain comprehensive asset inventories and implement appropriate protection measures",
                "best_practices": [
                    "Maintain accurate asset inventories",
                    "Classify assets based on value and sensitivity",
                    "Implement asset labeling and handling procedures",
                    "Regular asset reviews and updates"
                ],
                "common_challenges": [
                    "Asset discovery and tracking",
                    "Classification consistency",
                    "Managing asset lifecycle"
                ]
            },
            {
                "domain_id": "A.9",
                "name": "Access control",
                "description": "To limit access to information and information processing facilities",
                "order_index": 5,
                "implementation_guidance": "Implement comprehensive access control policies and procedures",
                "best_practices": [
                    "Principle of least privilege",
                    "Regular access reviews",
                    "Strong authentication mechanisms",
                    "Automated provisioning and deprovisioning"
                ],
                "common_challenges": [
                    "Balancing security with usability",
                    "Managing privileged access",
                    "Access review completeness"
                ]
            }
        ]

    def _get_iso_27001_2022_controls(self) -> List[Dict[str, Any]]:
        """Get ISO/IEC 27001:2022 controls data."""
        return [
            # A.5 - Information security policies
            {
                "control_id": "A.5.1",
                "domain_id": "A.5",
                "name": "Information security policy",
                "description": "An information security policy shall be defined, approved by management, published, communicated to and acknowledged by relevant personnel and relevant interested parties, and reviewed at planned intervals and if significant changes occur",
                "objective": "To provide management direction and support for information security",
                "implementation_guidance": "Develop a comprehensive information security policy that covers all aspects of information security management",
                "other_information": "The policy should be reviewed annually or when significant changes occur",
                "order_index": 1,
                "control_type": "preventive",
                "requirements": [
                    "Policy must be approved by management",
                    "Policy must be communicated to all personnel",
                    "Policy must be reviewed at planned intervals",
                    "Policy must address legal and regulatory requirements"
                ],
                "evidence_examples": [
                    "Signed information security policy document",
                    "Communication records to personnel",
                    "Policy review meeting minutes",
                    "Management approval documentation"
                ],
                "external_references": {
                    "NIST CSF": ["GV.GV-01", "GV.PO-01"],
                    "ISO 27002": ["5.1.1"],
                    "CIS Controls": ["1.1"]
                }
            },

            # A.6 - Organization of information security
            {
                "control_id": "A.6.1",
                "domain_id": "A.6",
                "name": "Information security roles and responsibilities",
                "description": "Information security roles and responsibilities shall be defined and allocated according to the organization needs",
                "objective": "To ensure that information security responsibilities are clearly defined and allocated",
                "implementation_guidance": "Define clear roles and responsibilities for all aspects of information security management",
                "other_information": "Roles should be documented and communicated to relevant personnel",
                "order_index": 1,
                "control_type": "preventive",
                "requirements": [
                    "Security roles must be clearly defined",
                    "Responsibilities must be allocated to specific individuals",
                    "Role definitions must be documented",
                    "Personnel must be aware of their security responsibilities"
                ],
                "evidence_examples": [
                    "Job descriptions with security responsibilities",
                    "RACI matrix for security activities",
                    "Training records for security roles",
                    "Organizational charts showing security reporting"
                ],
                "external_references": {
                    "NIST CSF": ["GV.RR-01", "GV.RR-02"],
                    "ISO 27002": ["6.1.1"],
                    "CIS Controls": ["1.2"]
                }
            },
            {
                "control_id": "A.6.2",
                "domain_id": "A.6",
                "name": "Segregation of duties",
                "description": "Conflicting duties and areas of responsibility shall be segregated to reduce opportunities for unauthorized or unintentional modification or misuse of the organization's assets",
                "objective": "To reduce the risk of accidental or deliberate system misuse",
                "implementation_guidance": "Implement segregation of duties for critical business processes and system administration",
                "other_information": "Consider both technical and procedural segregation of duties",
                "order_index": 2,
                "control_type": "preventive",
                "requirements": [
                    "Conflicting duties must be identified",
                    "Duties must be segregated appropriately",
                    "Segregation must be enforced through procedures",
                    "Regular review of duty segregation"
                ],
                "evidence_examples": [
                    "Segregation of duties matrix",
                    "System access controls enforcing segregation",
                    "Procedure documents defining segregation",
                    "Regular review reports"
                ],
                "external_references": {
                    "NIST CSF": ["PR.AC-04"],
                    "ISO 27002": ["6.1.2"],
                    "CIS Controls": ["3.3"]
                }
            },

            # A.8 - Asset management
            {
                "control_id": "A.8.1",
                "domain_id": "A.8",
                "name": "Inventory of assets",
                "description": "Assets associated with information and information processing facilities shall be identified and an inventory of these assets shall be drawn up and maintained",
                "objective": "To identify organizational assets and define appropriate protection responsibilities",
                "implementation_guidance": "Maintain comprehensive inventories of all information assets",
                "other_information": "Inventories should be regularly updated and reviewed",
                "order_index": 1,
                "control_type": "preventive",
                "requirements": [
                    "All assets must be identified and inventoried",
                    "Asset owners must be assigned",
                    "Inventory must be regularly updated",
                    "Asset classification must be documented"
                ],
                "evidence_examples": [
                    "Asset inventory databases",
                    "Asset ownership documentation",
                    "Regular inventory update procedures",
                    "Asset classification records"
                ],
                "external_references": {
                    "NIST CSF": ["ID.AM-01", "ID.AM-02"],
                    "ISO 27002": ["8.1.1"],
                    "CIS Controls": ["1.1", "2.1"]
                }
            },

            # A.9 - Access control
            {
                "control_id": "A.9.1",
                "domain_id": "A.9",
                "name": "Access control policy",
                "description": "An access control policy shall be established, documented and reviewed based on business and information security requirements",
                "objective": "To limit access to information and information processing facilities",
                "implementation_guidance": "Develop comprehensive access control policies covering all access scenarios",
                "other_information": "Policy should address both logical and physical access controls",
                "order_index": 1,
                "control_type": "preventive",
                "requirements": [
                    "Access control policy must be documented",
                    "Policy must be based on business requirements",
                    "Policy must be regularly reviewed",
                    "Policy must cover all access types"
                ],
                "evidence_examples": [
                    "Access control policy document",
                    "Policy review meeting minutes",
                    "Business requirement documentation",
                    "Policy communication records"
                ],
                "external_references": {
                    "NIST CSF": ["PR.AC-01"],
                    "ISO 27002": ["9.1.1"],
                    "CIS Controls": ["3.1"]
                }
            }
        ]

    def _get_iso_27001_2022_implementation_examples(self) -> List[Dict[str, Any]]:
        """Get ISO/IEC 27001:2022 implementation examples data."""
        return [
            {
                "control_id": "A.5.1",
                "title": "Enterprise Information Security Policy Implementation",
                "description": "Comprehensive implementation of information security policy for large enterprise organization",
                "implementation_approach": "Top-down approach with executive sponsorship and phased rollout across business units",
                "organization_size": "enterprise",
                "industry_sector": "financial_services",
                "technology_context": "hybrid_cloud",
                "geographic_region": "global",
                "implementation_steps": [
                    "Conduct business impact assessment",
                    "Develop policy framework with legal review",
                    "Obtain executive approval and sponsorship",
                    "Create communication and training plan",
                    "Roll out policy across business units",
                    "Implement monitoring and compliance tracking",
                    "Establish regular review and update process"
                ],
                "tools_and_technologies": ["Policy management platforms", "Training management systems", "Compliance tracking tools"],
                "roles_and_responsibilities": {
                    "CISO": "Policy development and oversight",
                    "Legal": "Regulatory compliance review",
                    "HR": "Training and awareness programs",
                    "Business Units": "Policy implementation and compliance"
                },
                "policies_and_procedures": [
                    "Information Security Policy",
                    "Policy Management Procedure",
                    "Training and Awareness Procedure",
                    "Compliance Monitoring Procedure"
                ],
                "implementation_difficulty": "medium",
                "estimated_effort": "6-12 months",
                "budget_considerations": "Significant investment in policy development, training, and compliance tools",
                "prerequisites": ["Executive sponsorship", "Legal framework understanding", "Business impact assessment"],
                "expected_outcomes": [
                    "Clear security direction for organization",
                    "Improved security awareness",
                    "Regulatory compliance",
                    "Risk reduction"
                ],
                "success_metrics": [
                    "Policy approval and publication",
                    "Training completion rates",
                    "Compliance assessment scores",
                    "Security incident reduction"
                ],
                "business_benefits": [
                    "Reduced security risks",
                    "Regulatory compliance",
                    "Improved stakeholder confidence",
                    "Clear security governance"
                ],
                "common_pitfalls": [
                    "Lack of executive support",
                    "Overly complex policy language",
                    "Insufficient training and awareness",
                    "Poor compliance monitoring"
                ],
                "lessons_learned": [
                    "Executive sponsorship is critical",
                    "Simple, clear language improves adoption",
                    "Regular training reinforces compliance",
                    "Continuous monitoring ensures effectiveness"
                ],
                "mitigation_strategies": [
                    "Secure executive sponsorship early",
                    "Use plain language in policies",
                    "Implement regular training programs",
                    "Establish automated compliance monitoring"
                ]
            },
            {
                "control_id": "A.8.1",
                "title": "Automated Asset Discovery and Management",
                "description": "Implementation of automated asset discovery and inventory management system",
                "implementation_approach": "Deploy automated discovery tools integrated with CMDB and security tools",
                "organization_size": "large",
                "industry_sector": "technology",
                "technology_context": "cloud",
                "geographic_region": "north_america",
                "implementation_steps": [
                    "Assess current asset inventory capabilities",
                    "Select and deploy asset discovery tools",
                    "Integrate with existing CMDB systems",
                    "Establish asset classification scheme",
                    "Implement automated inventory updates",
                    "Create asset ownership assignment process",
                    "Establish regular inventory validation"
                ],
                "tools_and_technologies": ["Network discovery tools", "CMDB systems", "Asset management platforms", "Cloud asset discovery"],
                "roles_and_responsibilities": {
                    "IT Operations": "Tool deployment and maintenance",
                    "Security Team": "Asset classification and risk assessment",
                    "Asset Owners": "Asset validation and ownership",
                    "Compliance": "Inventory accuracy verification"
                },
                "policies_and_procedures": [
                    "Asset Management Policy",
                    "Asset Classification Procedure",
                    "Asset Discovery Procedure",
                    "Asset Ownership Assignment Procedure"
                ],
                "implementation_difficulty": "medium",
                "estimated_effort": "3-6 months",
                "budget_considerations": "Investment in discovery tools, integration costs, and ongoing maintenance",
                "prerequisites": ["Network access for discovery", "CMDB system", "Asset classification framework"],
                "expected_outcomes": [
                    "Comprehensive asset visibility",
                    "Accurate asset inventory",
                    "Improved security posture",
                    "Better compliance reporting"
                ],
                "success_metrics": [
                    "Asset discovery coverage percentage",
                    "Inventory accuracy rate",
                    "Time to detect new assets",
                    "Asset classification completeness"
                ],
                "business_benefits": [
                    "Improved security visibility",
                    "Better risk management",
                    "Compliance with regulations",
                    "Optimized asset utilization"
                ],
                "common_pitfalls": [
                    "Incomplete network coverage",
                    "Poor tool integration",
                    "Inconsistent asset classification",
                    "Lack of ownership assignment"
                ],
                "lessons_learned": [
                    "Comprehensive discovery requires multiple tools",
                    "Integration is key to success",
                    "Clear classification schemes improve accuracy",
                    "Ownership assignment is critical for accountability"
                ],
                "mitigation_strategies": [
                    "Use multiple discovery methods",
                    "Plan integration carefully",
                    "Develop clear classification guidelines",
                    "Implement ownership assignment workflows"
                ]
            }
        ]


def import_iso_27001_2022_data(db: Session = None) -> Dict[str, Any]:
    """
    Convenience function to import ISO/IEC 27001:2022 data.

    Args:
        db: Database session (optional, will create one if not provided)

    Returns:
        Dict containing import results
    """
    if db is None:
        db = next(get_db())

    service = ISO27001ImportService(db)
    return service.import_iso_27001_2022()
