"""
NIST CSF Migration Service.

This module provides migration capabilities between different versions of the
NIST Cybersecurity Framework, particularly from CSF 1.1 to CSF 2.0. It handles
structural changes, ID format updates, and new function additions.

Features:
- CSF 1.1 to 2.0 migration with structure mapping
- Subcategory ID format conversion (e.g., ID.AM-1 → ID.AM-01)
- Addition of new Govern (GV) function for CSF 2.0
- Mapping preservation during migration
- Detailed migration notes and change tracking
- Rollback capabilities for failed migrations
"""

import logging
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import re

from api.core.config import nist_csf_config

logger = logging.getLogger(__name__)


@dataclass
class MigrationResult:
    """Result of a migration operation."""
    success: bool
    source_version: str
    target_version: str
    migrated_functions: int = 0
    migrated_categories: int = 0
    migrated_subcategories: int = 0
    migration_notes: List[str] = None
    errors: List[str] = None
    warnings: List[str] = None
    
    def __post_init__(self):
        if self.migration_notes is None:
            self.migration_notes = []
        if self.errors is None:
            self.errors = []
        if self.warnings is None:
            self.warnings = []


class NISTCSFMigrator:
    """
    Service for migrating between NIST CSF versions.
    
    Handles version-specific migrations with structure mapping,
    ID format conversion, and change tracking.
    """
    
    def __init__(self):
        """Initialize NIST CSF migrator."""
        # CSF 1.1 to 2.0 subcategory ID mappings
        self.v11_to_v20_mappings = {
            # Identity mappings (ID function)
            "ID.AM-1": "ID.AM-01",
            "ID.AM-2": "ID.AM-02",
            "ID.AM-3": "ID.AM-03",
            "ID.AM-4": "ID.AM-04",
            "ID.AM-5": "ID.AM-05",
            "ID.AM-6": "ID.AM-06",
            "ID.BE-1": "ID.BE-01",
            "ID.BE-2": "ID.BE-02",
            "ID.BE-3": "ID.BE-03",
            "ID.BE-4": "ID.BE-04",
            "ID.BE-5": "ID.BE-05",
            "ID.GV-1": "ID.GV-01",
            "ID.GV-2": "ID.GV-02",
            "ID.GV-3": "ID.GV-03",
            "ID.GV-4": "ID.GV-04",
            "ID.RA-1": "ID.RA-01",
            "ID.RA-2": "ID.RA-02",
            "ID.RA-3": "ID.RA-03",
            "ID.RA-4": "ID.RA-04",
            "ID.RA-5": "ID.RA-05",
            "ID.RA-6": "ID.RA-06",
            "ID.RM-1": "ID.RM-01",
            "ID.RM-2": "ID.RM-02",
            "ID.RM-3": "ID.RM-03",
            "ID.SC-1": "ID.SC-01",
            "ID.SC-2": "ID.SC-02",
            "ID.SC-3": "ID.SC-03",
            "ID.SC-4": "ID.SC-04",
            "ID.SC-5": "ID.SC-05",
            
            # Protect mappings (PR function)
            "PR.AC-1": "PR.AC-01",
            "PR.AC-2": "PR.AC-02",
            "PR.AC-3": "PR.AC-03",
            "PR.AC-4": "PR.AC-04",
            "PR.AC-5": "PR.AC-05",
            "PR.AC-6": "PR.AC-06",
            "PR.AC-7": "PR.AC-07",
            "PR.AT-1": "PR.AT-01",
            "PR.AT-2": "PR.AT-02",
            "PR.AT-3": "PR.AT-03",
            "PR.AT-4": "PR.AT-04",
            "PR.AT-5": "PR.AT-05",
            "PR.DS-1": "PR.DS-01",
            "PR.DS-2": "PR.DS-02",
            "PR.DS-3": "PR.DS-03",
            "PR.DS-4": "PR.DS-04",
            "PR.DS-5": "PR.DS-05",
            "PR.DS-6": "PR.DS-06",
            "PR.DS-7": "PR.DS-07",
            "PR.DS-8": "PR.DS-08",
            "PR.IP-1": "PR.IP-01",
            "PR.IP-2": "PR.IP-02",
            "PR.IP-3": "PR.IP-03",
            "PR.IP-4": "PR.IP-04",
            "PR.IP-5": "PR.IP-05",
            "PR.IP-6": "PR.IP-06",
            "PR.IP-7": "PR.IP-07",
            "PR.IP-8": "PR.IP-08",
            "PR.IP-9": "PR.IP-09",
            "PR.IP-10": "PR.IP-10",
            "PR.IP-11": "PR.IP-11",
            "PR.IP-12": "PR.IP-12",
            "PR.MA-1": "PR.MA-01",
            "PR.MA-2": "PR.MA-02",
            "PR.PT-1": "PR.PT-01",
            "PR.PT-2": "PR.PT-02",
            "PR.PT-3": "PR.PT-03",
            "PR.PT-4": "PR.PT-04",
            "PR.PT-5": "PR.PT-05",
            
            # Detect mappings (DE function)
            "DE.AE-1": "DE.AE-01",
            "DE.AE-2": "DE.AE-02",
            "DE.AE-3": "DE.AE-03",
            "DE.AE-4": "DE.AE-04",
            "DE.AE-5": "DE.AE-05",
            "DE.CM-1": "DE.CM-01",
            "DE.CM-2": "DE.CM-02",
            "DE.CM-3": "DE.CM-03",
            "DE.CM-4": "DE.CM-04",
            "DE.CM-5": "DE.CM-05",
            "DE.CM-6": "DE.CM-06",
            "DE.CM-7": "DE.CM-07",
            "DE.CM-8": "DE.CM-08",
            "DE.DP-1": "DE.DP-01",
            "DE.DP-2": "DE.DP-02",
            "DE.DP-3": "DE.DP-03",
            "DE.DP-4": "DE.DP-04",
            "DE.DP-5": "DE.DP-05",
            
            # Respond mappings (RS function)
            "RS.RP-1": "RS.RP-01",
            "RS.CO-1": "RS.CO-01",
            "RS.CO-2": "RS.CO-02",
            "RS.CO-3": "RS.CO-03",
            "RS.CO-4": "RS.CO-04",
            "RS.CO-5": "RS.CO-05",
            "RS.AN-1": "RS.AN-01",
            "RS.AN-2": "RS.AN-02",
            "RS.AN-3": "RS.AN-03",
            "RS.AN-4": "RS.AN-04",
            "RS.AN-5": "RS.AN-05",
            "RS.MI-1": "RS.MI-01",
            "RS.MI-2": "RS.MI-02",
            "RS.MI-3": "RS.MI-03",
            "RS.IM-1": "RS.IM-01",
            "RS.IM-2": "RS.IM-02",
            
            # Recover mappings (RC function)
            "RC.RP-1": "RC.RP-01",
            "RC.IM-1": "RC.IM-01",
            "RC.IM-2": "RC.IM-02",
            "RC.CO-1": "RC.CO-01",
            "RC.CO-2": "RC.CO-02",
            "RC.CO-3": "RC.CO-03"
        }
        
        # New CSF 2.0 Govern function structure
        self.govern_function_data = {
            "function_id": "GV",
            "name": "Govern",
            "description": "The organization's cybersecurity risk management strategy, expectations, and policy are established, communicated, and monitored.",
            "categories": [
                {
                    "category_id": "GV.OC",
                    "name": "Organizational Context",
                    "description": "The circumstances that frame the organization's risk management decisions are understood.",
                    "subcategories": [
                        {
                            "subcategory_id": "GV.OC-01",
                            "name": "Organizational mission is understood and informs cybersecurity risk management",
                            "description": "The organization's mission, objectives, stakeholders, and activities are understood and used to inform cybersecurity roles, responsibilities, and risk management decisions."
                        },
                        {
                            "subcategory_id": "GV.OC-02",
                            "name": "Internal and external legal, regulatory, and contractual requirements regarding cybersecurity are understood and managed",
                            "description": "Legal, regulatory, and contractual requirements regarding cybersecurity are understood and managed."
                        }
                    ]
                },
                {
                    "category_id": "GV.RM",
                    "name": "Risk Management Strategy",
                    "description": "The organization's priorities, constraints, risk tolerances, and assumptions are established and used to support operational risk decisions.",
                    "subcategories": [
                        {
                            "subcategory_id": "GV.RM-01",
                            "name": "Risk management objectives are established and agreed to by organizational stakeholders",
                            "description": "Risk management objectives are established and agreed to by organizational stakeholders."
                        },
                        {
                            "subcategory_id": "GV.RM-02",
                            "name": "Risk appetite and risk tolerance statements are established, communicated, and maintained",
                            "description": "Risk appetite and risk tolerance statements are established, communicated, and maintained."
                        }
                    ]
                }
            ]
        }
    
    def migrate_to_v2_0(self, v11_data: Dict[str, Any]) -> MigrationResult:
        """
        Migrate NIST CSF 1.1 data to version 2.0.
        
        Args:
            v11_data: CSF 1.1 framework data
            
        Returns:
            MigrationResult with migration details
        """
        result = MigrationResult(
            success=False,
            source_version="1.1",
            target_version="2.0"
        )
        
        try:
            # Create new 2.0 structure
            v20_data = {
                "version": "2.0",
                "description": "NIST Cybersecurity Framework 2.0 (migrated from 1.1)",
                "functions": []
            }
            
            # Add new Govern function first
            v20_data["functions"].append(self.govern_function_data.copy())
            result.migrated_functions += 1
            result.migration_notes.append("Added new Govern (GV) function with organizational context and risk management categories")
            
            # Migrate existing functions
            existing_functions = v11_data.get("functions", [])
            for function in existing_functions:
                migrated_function = self._migrate_function(function, result)
                if migrated_function:
                    v20_data["functions"].append(migrated_function)
                    result.migrated_functions += 1
            
            # Update result
            result.success = True
            result.migration_notes.append(f"Successfully migrated {result.migrated_functions} functions")
            result.migration_notes.append(f"Updated {result.migrated_subcategories} subcategory IDs to new format")
            
            # Store migrated data in result for further processing
            result.migrated_data = v20_data
            
            logger.info(f"Successfully migrated CSF 1.1 to 2.0: {result.migrated_functions} functions, {result.migrated_subcategories} subcategories")
            
        except Exception as e:
            result.success = False
            result.errors.append(f"Migration failed: {str(e)}")
            logger.error(f"CSF migration failed: {e}")
        
        return result
    
    def _migrate_function(self, function: Dict[str, Any], result: MigrationResult) -> Optional[Dict[str, Any]]:
        """
        Migrate individual function from 1.1 to 2.0.
        
        Args:
            function: Function data from CSF 1.1
            result: Migration result to update
            
        Returns:
            Migrated function data or None if migration fails
        """
        try:
            migrated_function = {
                "function_id": function.get("function_id", ""),
                "name": function.get("name", ""),
                "description": function.get("description", ""),
                "categories": []
            }
            
            # Migrate categories
            categories = function.get("categories", [])
            for category in categories:
                migrated_category = self._migrate_category(category, result)
                if migrated_category:
                    migrated_function["categories"].append(migrated_category)
                    result.migrated_categories += 1
            
            return migrated_function
            
        except Exception as e:
            result.errors.append(f"Error migrating function {function.get('function_id', 'unknown')}: {str(e)}")
            return None
    
    def _migrate_category(self, category: Dict[str, Any], result: MigrationResult) -> Optional[Dict[str, Any]]:
        """
        Migrate individual category from 1.1 to 2.0.
        
        Args:
            category: Category data from CSF 1.1
            result: Migration result to update
            
        Returns:
            Migrated category data or None if migration fails
        """
        try:
            migrated_category = {
                "category_id": category.get("category_id", ""),
                "name": category.get("name", ""),
                "description": category.get("description", ""),
                "subcategories": []
            }
            
            # Migrate subcategories
            subcategories = category.get("subcategories", [])
            for subcategory in subcategories:
                migrated_subcategory = self._migrate_subcategory(subcategory, result)
                if migrated_subcategory:
                    migrated_category["subcategories"].append(migrated_subcategory)
                    result.migrated_subcategories += 1
            
            return migrated_category
            
        except Exception as e:
            result.errors.append(f"Error migrating category {category.get('category_id', 'unknown')}: {str(e)}")
            return None
    
    def _migrate_subcategory(self, subcategory: Dict[str, Any], result: MigrationResult) -> Optional[Dict[str, Any]]:
        """
        Migrate individual subcategory from 1.1 to 2.0.
        
        Args:
            subcategory: Subcategory data from CSF 1.1
            result: Migration result to update
            
        Returns:
            Migrated subcategory data or None if migration fails
        """
        try:
            old_id = subcategory.get("subcategory_id", "")
            
            # Map old ID to new ID format
            new_id = self.v11_to_v20_mappings.get(old_id, old_id)
            
            if old_id != new_id:
                result.migration_notes.append(f"Updated subcategory ID: {old_id} → {new_id}")
            
            migrated_subcategory = {
                "subcategory_id": new_id,
                "name": subcategory.get("name", ""),
                "description": subcategory.get("description", ""),
                "implementation_examples": subcategory.get("implementation_examples", []),
                "informative_references": subcategory.get("informative_references", [])
            }
            
            return migrated_subcategory
            
        except Exception as e:
            result.errors.append(f"Error migrating subcategory {subcategory.get('subcategory_id', 'unknown')}: {str(e)}")
            return None
    
    def get_migration_mapping(self, old_id: str) -> Optional[str]:
        """
        Get new ID for old subcategory ID.
        
        Args:
            old_id: Old subcategory ID (CSF 1.1 format)
            
        Returns:
            New subcategory ID (CSF 2.0 format) or None if not found
        """
        return self.v11_to_v20_mappings.get(old_id)
    
    def get_all_mappings(self) -> Dict[str, str]:
        """
        Get all ID mappings from 1.1 to 2.0.
        
        Returns:
            Dictionary of old_id -> new_id mappings
        """
        return self.v11_to_v20_mappings.copy()
    
    def validate_migration_compatibility(self, data: Dict[str, Any]) -> List[str]:
        """
        Validate that data is compatible for migration.
        
        Args:
            data: Framework data to validate
            
        Returns:
            List of validation issues
        """
        issues = []
        
        # Check version
        version = data.get("version", "")
        if version != "1.1":
            issues.append(f"Migration only supports CSF 1.1, found version: {version}")
        
        # Check structure
        if "functions" not in data:
            issues.append("Missing 'functions' in data structure")
        
        # Check for required functions
        functions = data.get("functions", [])
        expected_functions = ["ID", "PR", "DE", "RS", "RC"]
        found_functions = [f.get("function_id", "") for f in functions]
        
        for expected in expected_functions:
            if expected not in found_functions:
                issues.append(f"Missing expected function: {expected}")
        
        return issues
