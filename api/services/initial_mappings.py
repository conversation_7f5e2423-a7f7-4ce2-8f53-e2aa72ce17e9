"""
Initial Framework Mappings Service.

This module provides functionality to create initial cross-framework mappings
between ISF, NIST CSF 2.0, and ISO/IEC 27001 to demonstrate the mapping system
and provide valuable baseline mappings for users.
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from api.database import get_db
from api.models.framework_mapping import FrameworkMapping, MappingSet
from api.services.framework_mapping import FrameworkMappingService
from api.schemas.framework_mapping import (
    FrameworkMappingCreate, MappingSetCreate
)

logger = logging.getLogger(__name__)


class InitialMappingsService:
    """Service for creating initial framework mappings."""
    
    def __init__(self, db: Session):
        self.db = db
        self.mapping_service = FrameworkMappingService(db)
    
    def create_initial_mappings(self, created_by: str = "system") -> Dict[str, Any]:
        """
        Create initial cross-framework mappings.
        
        Args:
            created_by: User ID creating the mappings
            
        Returns:
            Dict containing creation results and statistics
        """
        logger.info("Starting initial framework mappings creation...")
        
        try:
            # Create mapping sets
            mapping_sets = self._create_mapping_sets(created_by)
            
            # Create ISF to NIST CSF mappings
            isf_nist_mappings = self._create_isf_to_nist_mappings(
                mapping_sets["isf_nist"], created_by
            )
            
            # Create ISF to ISO 27001 mappings
            isf_iso_mappings = self._create_isf_to_iso_mappings(
                mapping_sets["isf_iso"], created_by
            )
            
            # Create NIST CSF to ISO 27001 mappings
            nist_iso_mappings = self._create_nist_to_iso_mappings(
                mapping_sets["nist_iso"], created_by
            )
            
            # Commit all changes
            self.db.commit()
            
            result = {
                "success": True,
                "statistics": {
                    "mapping_sets": len(mapping_sets),
                    "isf_nist_mappings": len(isf_nist_mappings),
                    "isf_iso_mappings": len(isf_iso_mappings),
                    "nist_iso_mappings": len(nist_iso_mappings),
                    "total_mappings": len(isf_nist_mappings) + len(isf_iso_mappings) + len(nist_iso_mappings)
                },
                "mapping_sets": {
                    "isf_nist": mapping_sets["isf_nist"].id,
                    "isf_iso": mapping_sets["isf_iso"].id,
                    "nist_iso": mapping_sets["nist_iso"].id
                },
                "creation_time": datetime.utcnow().isoformat(),
                "message": "Initial framework mappings created successfully"
            }
            
            logger.info(f"Initial mappings creation completed successfully: {result['statistics']}")
            return result
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Initial mappings creation failed: {str(e)}")
            raise
    
    def _create_mapping_sets(self, created_by: str) -> Dict[str, Any]:
        """Create mapping sets for organizing the mappings."""
        mapping_sets = {}
        
        # ISF to NIST CSF mapping set
        isf_nist_set = MappingSetCreate(
            name="ISF to NIST CSF 2.0 Mappings",
            description="Official mappings between Information Security Framework and NIST Cybersecurity Framework 2.0",
            source_framework="ISF",
            target_framework="NIST_CSF_2",
            version="1.0",
            is_official=True
        )
        mapping_sets["isf_nist"] = self.mapping_service.create_mapping_set(isf_nist_set, created_by)
        
        # ISF to ISO 27001 mapping set
        isf_iso_set = MappingSetCreate(
            name="ISF to ISO/IEC 27001:2022 Mappings",
            description="Official mappings between Information Security Framework and ISO/IEC 27001:2022",
            source_framework="ISF",
            target_framework="ISO_27001",
            version="1.0",
            is_official=True
        )
        mapping_sets["isf_iso"] = self.mapping_service.create_mapping_set(isf_iso_set, created_by)
        
        # NIST CSF to ISO 27001 mapping set
        nist_iso_set = MappingSetCreate(
            name="NIST CSF 2.0 to ISO/IEC 27001:2022 Mappings",
            description="Official mappings between NIST Cybersecurity Framework 2.0 and ISO/IEC 27001:2022",
            source_framework="NIST_CSF_2",
            target_framework="ISO_27001",
            version="1.0",
            is_official=True
        )
        mapping_sets["nist_iso"] = self.mapping_service.create_mapping_set(nist_iso_set, created_by)
        
        logger.info(f"Created {len(mapping_sets)} mapping sets")
        return mapping_sets
    
    def _create_isf_to_nist_mappings(self, mapping_set: Any, created_by: str) -> List[FrameworkMapping]:
        """Create ISF to NIST CSF 2.0 mappings."""
        mappings_data = [
            {
                "source_control_id": "ISF.01.01",
                "target_control_id": "GV.GV-01",
                "mapping_type": "direct",
                "confidence_level": "high",
                "confidence_score": 0.9,
                "description": "Both controls focus on establishing organizational cybersecurity strategy and policy",
                "rationale": "ISF policy document requirement directly maps to NIST CSF governance strategy establishment"
            },
            {
                "source_control_id": "ISF.02.01",
                "target_control_id": "GV.RR-01",
                "mapping_type": "direct",
                "confidence_level": "high",
                "confidence_score": 0.85,
                "description": "Both controls address defining and allocating information security roles and responsibilities",
                "rationale": "Direct alignment between ISF organizational roles and NIST CSF governance responsibilities"
            },
            {
                "source_control_id": "ISF.05.01",
                "target_control_id": "PR.AC-01",
                "mapping_type": "direct",
                "confidence_level": "high",
                "confidence_score": 0.9,
                "description": "Both controls establish access control policies and identity management",
                "rationale": "Strong alignment between ISF access control policy and NIST CSF identity management"
            },
            {
                "source_control_id": "ISF.05.02",
                "target_control_id": "PR.AC-02",
                "mapping_type": "partial",
                "confidence_level": "medium",
                "confidence_score": 0.75,
                "description": "ISF user registration maps partially to NIST CSF physical access management",
                "rationale": "Partial overlap as ISF focuses on user registration while NIST includes physical access"
            }
        ]
        
        mappings = []
        for mapping_data in mappings_data:
            mapping_create = FrameworkMappingCreate(
                source_framework="ISF",
                source_control_id=mapping_data["source_control_id"],
                target_framework="NIST_CSF_2",
                target_control_id=mapping_data["target_control_id"],
                mapping_type=mapping_data["mapping_type"],
                confidence_level=mapping_data["confidence_level"],
                confidence_score=mapping_data["confidence_score"],
                description=mapping_data["description"],
                rationale=mapping_data["rationale"],
                mapping_set_id=mapping_set.id
            )
            
            mapping = self.mapping_service.create_mapping(mapping_create, created_by)
            mappings.append(mapping)
        
        logger.info(f"Created {len(mappings)} ISF to NIST CSF mappings")
        return mappings
    
    def _create_isf_to_iso_mappings(self, mapping_set: Any, created_by: str) -> List[FrameworkMapping]:
        """Create ISF to ISO/IEC 27001 mappings."""
        mappings_data = [
            {
                "source_control_id": "ISF.01.01",
                "target_control_id": "A.5.1",
                "mapping_type": "direct",
                "confidence_level": "high",
                "confidence_score": 0.95,
                "description": "Both controls require establishing and maintaining information security policies",
                "rationale": "Perfect alignment between ISF policy document and ISO 27001 information security policy"
            },
            {
                "source_control_id": "ISF.02.01",
                "target_control_id": "A.6.1",
                "mapping_type": "direct",
                "confidence_level": "high",
                "confidence_score": 0.9,
                "description": "Both controls define information security roles and responsibilities",
                "rationale": "Direct mapping between ISF organizational roles and ISO 27001 security responsibilities"
            },
            {
                "source_control_id": "ISF.05.01",
                "target_control_id": "A.9.1",
                "mapping_type": "direct",
                "confidence_level": "high",
                "confidence_score": 0.9,
                "description": "Both controls establish access control policies",
                "rationale": "Strong alignment between ISF and ISO 27001 access control policy requirements"
            },
            {
                "source_control_id": "ISF.05.02",
                "target_control_id": "A.9.2",
                "mapping_type": "related",
                "confidence_level": "medium",
                "confidence_score": 0.7,
                "description": "ISF user registration relates to ISO 27001 network access authorization",
                "rationale": "Related concepts but different scope - ISF focuses on user lifecycle, ISO on network access"
            }
        ]
        
        mappings = []
        for mapping_data in mappings_data:
            mapping_create = FrameworkMappingCreate(
                source_framework="ISF",
                source_control_id=mapping_data["source_control_id"],
                target_framework="ISO_27001",
                target_control_id=mapping_data["target_control_id"],
                mapping_type=mapping_data["mapping_type"],
                confidence_level=mapping_data["confidence_level"],
                confidence_score=mapping_data["confidence_score"],
                description=mapping_data["description"],
                rationale=mapping_data["rationale"],
                mapping_set_id=mapping_set.id
            )
            
            mapping = self.mapping_service.create_mapping(mapping_create, created_by)
            mappings.append(mapping)
        
        logger.info(f"Created {len(mappings)} ISF to ISO 27001 mappings")
        return mappings
    
    def _create_nist_to_iso_mappings(self, mapping_set: Any, created_by: str) -> List[FrameworkMapping]:
        """Create NIST CSF 2.0 to ISO/IEC 27001 mappings."""
        mappings_data = [
            {
                "source_control_id": "GV.GV-01",
                "target_control_id": "A.5.1",
                "mapping_type": "direct",
                "confidence_level": "high",
                "confidence_score": 0.85,
                "description": "Both controls establish organizational cybersecurity strategy and policy",
                "rationale": "NIST CSF governance strategy aligns with ISO 27001 information security policy"
            },
            {
                "source_control_id": "GV.RR-01",
                "target_control_id": "A.6.1",
                "mapping_type": "direct",
                "confidence_level": "high",
                "confidence_score": 0.9,
                "description": "Both controls define cybersecurity roles and responsibilities",
                "rationale": "Direct alignment between NIST CSF governance roles and ISO 27001 security responsibilities"
            },
            {
                "source_control_id": "ID.AM-01",
                "target_control_id": "A.8.1",
                "mapping_type": "direct",
                "confidence_level": "high",
                "confidence_score": 0.95,
                "description": "Both controls require inventorying physical devices and assets",
                "rationale": "Perfect alignment between NIST CSF asset management and ISO 27001 asset inventory"
            },
            {
                "source_control_id": "PR.AC-01",
                "target_control_id": "A.9.1",
                "mapping_type": "direct",
                "confidence_level": "high",
                "confidence_score": 0.9,
                "description": "Both controls establish identity management and access control policies",
                "rationale": "Strong alignment between NIST CSF access control and ISO 27001 access control policy"
            },
            {
                "source_control_id": "PR.AC-02",
                "target_control_id": "A.9.2",
                "mapping_type": "partial",
                "confidence_level": "medium",
                "confidence_score": 0.75,
                "description": "NIST CSF physical access partially maps to ISO 27001 network access",
                "rationale": "Partial overlap - NIST includes physical access while ISO focuses on network authorization"
            }
        ]
        
        mappings = []
        for mapping_data in mappings_data:
            mapping_create = FrameworkMappingCreate(
                source_framework="NIST_CSF_2",
                source_control_id=mapping_data["source_control_id"],
                target_framework="ISO_27001",
                target_control_id=mapping_data["target_control_id"],
                mapping_type=mapping_data["mapping_type"],
                confidence_level=mapping_data["confidence_level"],
                confidence_score=mapping_data["confidence_score"],
                description=mapping_data["description"],
                rationale=mapping_data["rationale"],
                mapping_set_id=mapping_set.id
            )
            
            mapping = self.mapping_service.create_mapping(mapping_create, created_by)
            mappings.append(mapping)
        
        logger.info(f"Created {len(mappings)} NIST CSF to ISO 27001 mappings")
        return mappings


def create_initial_framework_mappings(db: Session = None, created_by: str = "system") -> Dict[str, Any]:
    """
    Convenience function to create initial framework mappings.
    
    Args:
        db: Database session (optional, will create one if not provided)
        created_by: User ID creating the mappings
        
    Returns:
        Dict containing creation results
    """
    if db is None:
        db = next(get_db())
    
    service = InitialMappingsService(db)
    return service.create_initial_mappings(created_by)
