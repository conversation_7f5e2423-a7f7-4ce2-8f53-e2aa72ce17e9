"""
CIS Critical Security Controls v8 Data Import Service.

This module provides functionality to import official CIS Controls v8 data
into the database, including controls, safeguards, implementation groups,
and implementation guidance.
"""

import json
import logging
from datetime import datetime
from typing import Dict, List, Optional, Any
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from api.database import get_db
from api.models.cis_controls import (
    CISControlsVersion, CISControl, CISSafeguard, 
    CISImplementationExample, CISImplementationGroup
)

logger = logging.getLogger(__name__)


class CISControlsImportService:
    """Service for importing CIS Controls v8 framework data."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def import_cis_controls_v8(self) -> Dict[str, Any]:
        """
        Import the official CIS Controls v8 framework data.
        
        Returns:
            Dict containing import results and statistics
        """
        logger.info("Starting CIS Controls v8 data import...")
        
        try:
            # Create CIS Controls v8 version
            version = self._create_cis_controls_version()
            
            # Import implementation groups
            implementation_groups = self._import_implementation_groups(version.id)
            
            # Import controls
            controls = self._import_controls(version.id)
            
            # Import safeguards
            safeguards = self._import_safeguards(version.id, controls)
            
            # Import implementation examples
            examples = self._import_implementation_examples(safeguards)
            
            # Update version statistics
            self._update_version_statistics(version, controls, safeguards)
            
            # Commit all changes
            self.db.commit()
            
            result = {
                "success": True,
                "version_id": version.id,
                "version": version.version,
                "statistics": {
                    "implementation_groups": len(implementation_groups),
                    "controls": len(controls),
                    "safeguards": len(safeguards),
                    "implementation_examples": len(examples),
                    "total_records": 1 + len(implementation_groups) + len(controls) + len(safeguards) + len(examples)
                },
                "import_time": datetime.utcnow().isoformat(),
                "message": "CIS Controls v8 data imported successfully"
            }
            
            logger.info(f"CIS Controls v8 import completed successfully: {result['statistics']}")
            return result
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"CIS Controls v8 import failed: {str(e)}")
            raise
    
    def _create_cis_controls_version(self) -> CISControlsVersion:
        """Create the CIS Controls v8 version record."""
        # Check if version already exists
        existing = self.db.query(CISControlsVersion).filter(
            CISControlsVersion.version == "v8",
            CISControlsVersion.deleted_at.is_(None)
        ).first()
        
        if existing:
            logger.info("CIS Controls v8 version already exists, updating...")
            existing.is_current = True
            existing.updated_at = datetime.utcnow()
            return existing
        
        # Create new version
        version = CISControlsVersion(
            version="v8",
            release_date="2021-05-18",
            description="CIS Critical Security Controls v8 - A prioritized set of actions for cyber defense",
            is_current=True,
            standard_url="https://www.cisecurity.org/controls/v8",
            documentation_url="https://www.cisecurity.org/controls/cis-controls-list",
            organization="Center for Internet Security (CIS)",
            total_controls=18,
            total_safeguards=153,
            implementation_groups={
                "IG1": {
                    "name": "Implementation Group 1",
                    "description": "Essential cyber hygiene for small commercial enterprises and small government agencies",
                    "target_organizations": ["Small businesses", "Small government agencies", "Organizations with limited IT resources"],
                    "safeguards_count": 56
                },
                "IG2": {
                    "name": "Implementation Group 2", 
                    "description": "Enterprises with moderate cybersecurity resources and expertise",
                    "target_organizations": ["Medium enterprises", "Organizations with dedicated IT staff", "Higher risk tolerance organizations"],
                    "safeguards_count": 130
                },
                "IG3": {
                    "name": "Implementation Group 3",
                    "description": "Enterprises with significant cybersecurity expertise and resources",
                    "target_organizations": ["Large enterprises", "Organizations with mature security programs", "High-risk organizations"],
                    "safeguards_count": 153
                }
            },
            created_at=datetime.utcnow(),
            updated_at=datetime.utcnow()
        )
        
        # Set all other versions as non-current
        self.db.query(CISControlsVersion).filter(
            CISControlsVersion.deleted_at.is_(None)
        ).update({"is_current": False})
        
        self.db.add(version)
        self.db.flush()  # Get the ID
        
        logger.info(f"Created CIS Controls version: {version.version} (ID: {version.id})")
        return version
    
    def _import_implementation_groups(self, version_id: int) -> List[CISImplementationGroup]:
        """Import CIS Controls v8 implementation groups."""
        groups_data = self._get_cis_v8_implementation_groups()
        groups = []
        
        for group_data in groups_data:
            # Check if group already exists
            existing = self.db.query(CISImplementationGroup).filter(
                CISImplementationGroup.group_id == group_data["group_id"],
                CISImplementationGroup.version_id == version_id,
                CISImplementationGroup.deleted_at.is_(None)
            ).first()
            
            if existing:
                # Update existing group
                existing.name = group_data["name"]
                existing.description = group_data["description"]
                existing.target_organizations = group_data.get("target_organizations")
                existing.security_maturity_level = group_data.get("security_maturity_level")
                existing.resource_requirements = group_data.get("resource_requirements")
                existing.total_safeguards = group_data.get("total_safeguards", 0)
                existing.implementation_approach = group_data.get("implementation_approach")
                existing.prioritization_guidance = group_data.get("prioritization_guidance")
                existing.success_criteria = group_data.get("success_criteria")
                existing.updated_at = datetime.utcnow()
                groups.append(existing)
            else:
                # Create new group
                group = CISImplementationGroup(
                    group_id=group_data["group_id"],
                    name=group_data["name"],
                    description=group_data["description"],
                    version_id=version_id,
                    target_organizations=group_data.get("target_organizations"),
                    security_maturity_level=group_data.get("security_maturity_level"),
                    resource_requirements=group_data.get("resource_requirements"),
                    total_safeguards=group_data.get("total_safeguards", 0),
                    implementation_approach=group_data.get("implementation_approach"),
                    prioritization_guidance=group_data.get("prioritization_guidance"),
                    success_criteria=group_data.get("success_criteria"),
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                self.db.add(group)
                groups.append(group)
        
        self.db.flush()  # Get IDs
        logger.info(f"Imported {len(groups)} implementation groups")
        return groups
    
    def _import_controls(self, version_id: int) -> List[CISControl]:
        """Import CIS Controls v8 controls."""
        controls_data = self._get_cis_v8_controls()
        controls = []
        
        for control_data in controls_data:
            # Check if control already exists
            existing = self.db.query(CISControl).filter(
                CISControl.control_id == control_data["control_id"],
                CISControl.version_id == version_id,
                CISControl.deleted_at.is_(None)
            ).first()
            
            if existing:
                # Update existing control
                existing.title = control_data["title"]
                existing.description = control_data.get("description")
                existing.order_index = control_data["order_index"]
                existing.control_type = control_data.get("control_type")
                existing.asset_types = control_data.get("asset_types")
                existing.security_functions = control_data.get("security_functions")
                existing.why_is_this_important = control_data.get("why_is_this_important")
                existing.implementation_guidance = control_data.get("implementation_guidance")
                existing.related_controls = control_data.get("related_controls")
                existing.external_references = control_data.get("external_references")
                existing.updated_at = datetime.utcnow()
                controls.append(existing)
            else:
                # Create new control
                control = CISControl(
                    control_id=control_data["control_id"],
                    title=control_data["title"],
                    description=control_data.get("description"),
                    version_id=version_id,
                    order_index=control_data["order_index"],
                    control_type=control_data.get("control_type", "foundational"),
                    asset_types=control_data.get("asset_types"),
                    security_functions=control_data.get("security_functions"),
                    why_is_this_important=control_data.get("why_is_this_important"),
                    implementation_guidance=control_data.get("implementation_guidance"),
                    related_controls=control_data.get("related_controls"),
                    external_references=control_data.get("external_references"),
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                self.db.add(control)
                controls.append(control)
        
        self.db.flush()  # Get IDs
        logger.info(f"Imported {len(controls)} controls")
        return controls
    
    def _import_safeguards(self, version_id: int, controls: List[CISControl]) -> List[CISSafeguard]:
        """Import CIS Controls v8 safeguards."""
        # Create control mapping
        control_mapping = {control.control_id: control.id for control in controls}
        
        safeguards_data = self._get_cis_v8_safeguards()
        safeguards = []
        
        for safeguard_data in safeguards_data:
            control_id = control_mapping.get(safeguard_data["control_id"])
            if not control_id:
                logger.warning(f"Control not found for safeguard {safeguard_data['safeguard_id']}")
                continue
            
            # Check if safeguard already exists
            existing = self.db.query(CISSafeguard).filter(
                CISSafeguard.safeguard_id == safeguard_data["safeguard_id"],
                CISSafeguard.version_id == version_id,
                CISSafeguard.deleted_at.is_(None)
            ).first()
            
            if existing:
                # Update existing safeguard
                existing.title = safeguard_data["title"]
                existing.description = safeguard_data.get("description")
                existing.order_index = safeguard_data["order_index"]
                existing.implementation_group_1 = safeguard_data.get("implementation_group_1", False)
                existing.implementation_group_2 = safeguard_data.get("implementation_group_2", False)
                existing.implementation_group_3 = safeguard_data.get("implementation_group_3", False)
                existing.asset_types = safeguard_data.get("asset_types")
                existing.security_functions = safeguard_data.get("security_functions")
                existing.implementation_guidance = safeguard_data.get("implementation_guidance")
                existing.measurement_specification = safeguard_data.get("measurement_specification")
                existing.updated_at = datetime.utcnow()
                safeguards.append(existing)
            else:
                # Create new safeguard
                safeguard = CISSafeguard(
                    safeguard_id=safeguard_data["safeguard_id"],
                    title=safeguard_data["title"],
                    description=safeguard_data.get("description"),
                    control_id=control_id,
                    version_id=version_id,
                    order_index=safeguard_data["order_index"],
                    implementation_group_1=safeguard_data.get("implementation_group_1", False),
                    implementation_group_2=safeguard_data.get("implementation_group_2", False),
                    implementation_group_3=safeguard_data.get("implementation_group_3", False),
                    asset_types=safeguard_data.get("asset_types"),
                    security_functions=safeguard_data.get("security_functions"),
                    implementation_guidance=safeguard_data.get("implementation_guidance"),
                    measurement_specification=safeguard_data.get("measurement_specification"),
                    procedure_review=safeguard_data.get("procedure_review"),
                    automation_support=safeguard_data.get("automation_support"),
                    dependencies=safeguard_data.get("dependencies"),
                    prerequisites=safeguard_data.get("prerequisites"),
                    assessment_criteria=safeguard_data.get("assessment_criteria"),
                    validation_methods=safeguard_data.get("validation_methods"),
                    evidence_examples=safeguard_data.get("evidence_examples"),
                    external_references=safeguard_data.get("external_references"),
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                self.db.add(safeguard)
                safeguards.append(safeguard)
        
        self.db.flush()  # Get IDs
        logger.info(f"Imported {len(safeguards)} safeguards")
        return safeguards
    
    def _import_implementation_examples(self, safeguards: List[CISSafeguard]) -> List[CISImplementationExample]:
        """Import implementation examples for safeguards."""
        # Create safeguard mapping
        safeguard_mapping = {safeguard.safeguard_id: safeguard.id for safeguard in safeguards}
        
        examples_data = self._get_cis_v8_implementation_examples()
        examples = []
        
        for example_data in examples_data:
            safeguard_id = safeguard_mapping.get(example_data["safeguard_id"])
            if not safeguard_id:
                logger.warning(f"Safeguard not found for example {example_data['title']}")
                continue
            
            # Create implementation example
            example = CISImplementationExample(
                safeguard_id=safeguard_id,
                title=example_data["title"],
                description=example_data["description"],
                implementation_approach=example_data.get("implementation_approach"),
                implementation_group=example_data.get("implementation_group"),
                organization_size=example_data.get("organization_size"),
                industry_sector=example_data.get("industry_sector"),
                technology_context=example_data.get("technology_context"),
                asset_types=example_data.get("asset_types"),
                implementation_steps=example_data.get("implementation_steps"),
                tools_and_technologies=example_data.get("tools_and_technologies"),
                automation_opportunities=example_data.get("automation_opportunities"),
                implementation_difficulty=example_data.get("implementation_difficulty"),
                estimated_effort=example_data.get("estimated_effort"),
                budget_considerations=example_data.get("budget_considerations"),
                skill_requirements=example_data.get("skill_requirements"),
                expected_outcomes=example_data.get("expected_outcomes"),
                success_metrics=example_data.get("success_metrics"),
                measurement_procedures=example_data.get("measurement_procedures"),
                common_pitfalls=example_data.get("common_pitfalls"),
                lessons_learned=example_data.get("lessons_learned"),
                best_practices=example_data.get("best_practices"),
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            self.db.add(example)
            examples.append(example)
        
        self.db.flush()  # Get IDs
        logger.info(f"Imported {len(examples)} implementation examples")
        return examples
    
    def _update_version_statistics(self, version: CISControlsVersion, controls: List[CISControl], safeguards: List[CISSafeguard]):
        """Update version statistics with actual counts."""
        # Update control safeguard counts
        for control in controls:
            control_safeguards = [s for s in safeguards if s.control_id == control.id]
            control.total_safeguards = len(control_safeguards)
            control.ig1_safeguards = len([s for s in control_safeguards if s.implementation_group_1])
            control.ig2_safeguards = len([s for s in control_safeguards if s.implementation_group_2])
            control.ig3_safeguards = len([s for s in control_safeguards if s.implementation_group_3])
        
        # Update version statistics
        version.total_controls = len(controls)
        version.total_safeguards = len(safeguards)
        
        logger.info(f"Updated statistics: {len(controls)} controls, {len(safeguards)} safeguards")

    def _get_cis_v8_implementation_groups(self) -> List[Dict[str, Any]]:
        """Get CIS Controls v8 implementation groups data."""
        return [
            {
                "group_id": "IG1",
                "name": "Implementation Group 1",
                "description": "Essential cyber hygiene for small commercial enterprises and small government agencies",
                "target_organizations": [
                    "Small commercial enterprises",
                    "Small government agencies",
                    "Organizations with limited IT resources",
                    "Organizations new to cybersecurity"
                ],
                "security_maturity_level": "basic",
                "resource_requirements": "Limited cybersecurity expertise and resources",
                "total_safeguards": 56,
                "implementation_approach": "Focus on essential cyber hygiene practices that provide immediate security benefits",
                "prioritization_guidance": "Implement foundational safeguards first, focusing on asset management, access control, and basic protections",
                "success_criteria": [
                    "Basic asset inventory established",
                    "Essential access controls implemented",
                    "Basic security awareness training completed",
                    "Fundamental protective measures in place"
                ]
            },
            {
                "group_id": "IG2",
                "name": "Implementation Group 2",
                "description": "Enterprises with moderate cybersecurity resources and expertise",
                "target_organizations": [
                    "Medium-sized enterprises",
                    "Organizations with dedicated IT staff",
                    "Organizations with moderate risk tolerance",
                    "Organizations seeking to improve security posture"
                ],
                "security_maturity_level": "intermediate",
                "resource_requirements": "Moderate cybersecurity expertise and dedicated security resources",
                "total_safeguards": 130,
                "implementation_approach": "Build upon IG1 foundation with enhanced monitoring, incident response, and risk management",
                "prioritization_guidance": "Implement IG1 safeguards first, then add enhanced monitoring, logging, and incident response capabilities",
                "success_criteria": [
                    "All IG1 safeguards implemented",
                    "Enhanced monitoring and logging in place",
                    "Incident response capabilities established",
                    "Regular security assessments conducted"
                ]
            },
            {
                "group_id": "IG3",
                "name": "Implementation Group 3",
                "description": "Enterprises with significant cybersecurity expertise and resources",
                "target_organizations": [
                    "Large enterprises",
                    "Organizations with mature security programs",
                    "High-risk organizations",
                    "Organizations with advanced threat landscape"
                ],
                "security_maturity_level": "advanced",
                "resource_requirements": "Significant cybersecurity expertise and comprehensive security resources",
                "total_safeguards": 153,
                "implementation_approach": "Comprehensive implementation with advanced threat detection, response, and recovery capabilities",
                "prioritization_guidance": "Implement all IG1 and IG2 safeguards, then add advanced threat hunting, forensics, and recovery capabilities",
                "success_criteria": [
                    "All IG1 and IG2 safeguards implemented",
                    "Advanced threat detection and hunting capabilities",
                    "Comprehensive incident response and recovery",
                    "Continuous security improvement program"
                ]
            }
        ]

    def _get_cis_v8_controls(self) -> List[Dict[str, Any]]:
        """Get CIS Controls v8 controls data."""
        return [
            {
                "control_id": "1",
                "title": "Inventory and Control of Enterprise Assets",
                "description": "Actively manage (inventory, track, and correct) all enterprise assets (end-user devices, including portable and mobile; network devices; non-computing/IoT devices; and servers) connected to the infrastructure physically, virtually, remotely, and those within cloud environments, to accurately know the totality of assets that need to be monitored and protected within the enterprise. This will also support identifying unauthorized and unmanaged assets to remove or remediate.",
                "order_index": 1,
                "control_type": "foundational",
                "asset_types": ["Devices", "Network", "Data", "Software", "Hardware"],
                "security_functions": ["Identify"],
                "why_is_this_important": "Enterprises cannot defend what they do not know they have. Managed control of all enterprise assets also plays a critical role in planning and executing security activities, such as vulnerability management, configuration management, and incident response.",
                "implementation_guidance": "Establish and maintain an accurate, detailed, and up-to-date inventory of all enterprise assets with the potential to store or process data. This inventory shall include all assets, whether connected to the enterprise network or not.",
                "related_controls": ["2", "4", "12"],
                "external_references": {
                    "NIST CSF": ["ID.AM-01", "ID.AM-02"],
                    "ISO 27001": ["A.8.1"],
                    "NIST 800-53": ["CM-8"]
                }
            },
            {
                "control_id": "2",
                "title": "Inventory and Control of Software Assets",
                "description": "Actively manage (inventory, track, and correct) all software (operating systems and applications) on the network so that only authorized software is installed and can execute, and that unauthorized and unmanaged software is found and prevented from installation or execution.",
                "order_index": 2,
                "control_type": "foundational",
                "asset_types": ["Software", "Applications"],
                "security_functions": ["Identify", "Protect"],
                "why_is_this_important": "A complete software inventory is a critical foundation for preventing attacks. Attackers continuously scan target enterprises looking for vulnerable versions of software that can be remotely exploited.",
                "implementation_guidance": "Establish and maintain a detailed inventory of all licensed software installed on enterprise assets. The software inventory system should track the name, version, publisher, and install date for all software, including operating systems and applications.",
                "related_controls": ["1", "4", "7"],
                "external_references": {
                    "NIST CSF": ["ID.AM-02", "PR.IP-01"],
                    "ISO 27001": ["A.8.1", "A.12.6"],
                    "NIST 800-53": ["CM-8", "SI-2"]
                }
            },
            {
                "control_id": "3",
                "title": "Data Protection",
                "description": "Develop processes and technical controls to identify, classify, securely handle, retain, and dispose of data.",
                "order_index": 3,
                "control_type": "foundational",
                "asset_types": ["Data"],
                "security_functions": ["Identify", "Protect"],
                "why_is_this_important": "The enterprise's data is one of its most important assets. Some data has value to attackers, including personally identifiable information (PII), intellectual property, financial data, and authentication credentials.",
                "implementation_guidance": "Establish and maintain a data management process that addresses data governance, classification, and handling requirements. Ensure that data protection requirements are integrated into the enterprise's overall risk management process.",
                "related_controls": ["8", "13", "14"],
                "external_references": {
                    "NIST CSF": ["ID.AM-05", "PR.DS-01", "PR.DS-02"],
                    "ISO 27001": ["A.8.2", "A.18.1"],
                    "NIST 800-53": ["AC-16", "MP-6"]
                }
            },
            {
                "control_id": "4",
                "title": "Secure Configuration of Enterprise Assets and Software",
                "description": "Establish and maintain the secure configuration of enterprise assets (end-user devices, including portable and mobile; network devices; non-computing/IoT devices; and servers) and software (operating systems and applications).",
                "order_index": 4,
                "control_type": "foundational",
                "asset_types": ["Devices", "Network", "Software"],
                "security_functions": ["Protect"],
                "why_is_this_important": "Default configurations for enterprise assets and software are normally geared toward ease-of-deployment and ease-of-use rather than security. Attackers exploit poorly configured systems within the enterprise.",
                "implementation_guidance": "Establish and maintain secure configurations for enterprise assets and software. Review and update configurations regularly to address new threats and vulnerabilities.",
                "related_controls": ["1", "2", "5"],
                "external_references": {
                    "NIST CSF": ["PR.IP-01", "PR.IP-04"],
                    "ISO 27001": ["A.12.6", "A.14.2"],
                    "NIST 800-53": ["CM-6", "CM-7"]
                }
            },
            {
                "control_id": "5",
                "title": "Account Management",
                "description": "Use processes and tools to assign and manage authorization to credentials for user accounts, including administrator accounts, as well as service accounts, to enterprise assets and software.",
                "order_index": 5,
                "control_type": "foundational",
                "asset_types": ["Users", "Devices"],
                "security_functions": ["Protect"],
                "why_is_this_important": "Attackers frequently compromise credentials to gain initial access or to escalate privileges within an enterprise. Account management is critical for limiting unauthorized access and maintaining accountability.",
                "implementation_guidance": "Establish and maintain an inventory of all accounts managed in the enterprise. Ensure that accounts are assigned, managed, and removed according to enterprise policies.",
                "related_controls": ["6", "16"],
                "external_references": {
                    "NIST CSF": ["PR.AC-01", "PR.AC-04"],
                    "ISO 27001": ["A.9.2", "A.9.4"],
                    "NIST 800-53": ["AC-2", "IA-2"]
                }
            },
            {
                "control_id": "6",
                "title": "Access Control Management",
                "description": "Use processes and tools to create, assign, manage, and revoke access credentials and privileges for user, administrator, and service accounts for enterprise assets and software.",
                "order_index": 6,
                "control_type": "foundational",
                "asset_types": ["Users", "Devices", "Data"],
                "security_functions": ["Protect"],
                "why_is_this_important": "Where account management focuses on the entire lifecycle of an account, access control management focuses on the assignment and management of the privileges an account has, including the specific resources that an account can access and the actions that an account can perform.",
                "implementation_guidance": "Establish and maintain access control policies and procedures. Implement technical controls to enforce access control policies and regularly review access privileges.",
                "related_controls": ["5", "14"],
                "external_references": {
                    "NIST CSF": ["PR.AC-03", "PR.AC-04"],
                    "ISO 27001": ["A.9.1", "A.9.4"],
                    "NIST 800-53": ["AC-3", "AC-6"]
                }
            }
        ]

    def _get_cis_v8_safeguards(self) -> List[Dict[str, Any]]:
        """Get CIS Controls v8 safeguards data."""
        return [
            # Control 1 Safeguards
            {
                "safeguard_id": "1.1",
                "control_id": "1",
                "title": "Establish and Maintain Detailed Enterprise Asset Inventory",
                "description": "Establish and maintain an accurate, detailed, and up-to-date inventory of all enterprise assets with the potential to store or process data. Identify enterprise assets within the enterprise, at remote sites, and cloud environments. The inventory shall include all assets, whether connected to the enterprise network or not.",
                "order_index": 1,
                "implementation_group_1": True,
                "implementation_group_2": True,
                "implementation_group_3": True,
                "asset_types": ["Devices", "Network"],
                "security_functions": ["Identify"],
                "implementation_guidance": "Use automated discovery tools where possible, supplemented by manual processes for comprehensive coverage",
                "measurement_specification": "Percentage of assets discovered and documented in inventory system",
                "procedure_review": "Review inventory accuracy monthly and update procedures quarterly",
                "automation_support": "Network discovery tools, asset management systems, cloud asset discovery APIs",
                "dependencies": [],
                "prerequisites": ["Network access for discovery tools"],
                "assessment_criteria": {
                    "coverage": "95% of assets documented",
                    "accuracy": "Asset information updated within 72 hours",
                    "completeness": "All required asset attributes captured"
                },
                "validation_methods": ["Automated scanning", "Manual verification", "Audit sampling"],
                "evidence_examples": ["Asset inventory reports", "Discovery tool outputs", "Manual verification records"],
                "external_references": {
                    "NIST CSF": ["ID.AM-01"],
                    "ISO 27001": ["A.8.1.1"],
                    "NIST 800-53": ["CM-8"]
                }
            },
            {
                "safeguard_id": "1.2",
                "control_id": "1",
                "title": "Address Unauthorized Assets",
                "description": "Ensure that a process exists to address unauthorized assets on a weekly basis. The enterprise may choose to remove the asset from the network, deny the asset from connecting remotely to the network, or document the asset as authorized.",
                "order_index": 2,
                "implementation_group_1": True,
                "implementation_group_2": True,
                "implementation_group_3": True,
                "asset_types": ["Devices", "Network"],
                "security_functions": ["Identify", "Protect"],
                "implementation_guidance": "Implement automated processes to detect and quarantine unauthorized assets",
                "measurement_specification": "Time to detect and address unauthorized assets",
                "procedure_review": "Review unauthorized asset handling procedures monthly",
                "automation_support": "Network access control (NAC), SIEM alerts, automated quarantine systems",
                "dependencies": ["1.1"],
                "prerequisites": ["Asset inventory system", "Network monitoring capabilities"],
                "assessment_criteria": {
                    "detection_time": "Unauthorized assets detected within 24 hours",
                    "response_time": "Unauthorized assets addressed within 72 hours",
                    "documentation": "All decisions documented with rationale"
                },
                "validation_methods": ["Penetration testing", "Red team exercises", "Audit reviews"],
                "evidence_examples": ["Unauthorized asset reports", "Response time metrics", "Decision documentation"],
                "external_references": {
                    "NIST CSF": ["ID.AM-01", "PR.AC-01"],
                    "ISO 27001": ["A.11.2.6"],
                    "NIST 800-53": ["CM-8", "AC-17"]
                }
            },

            # Control 2 Safeguards
            {
                "safeguard_id": "2.1",
                "control_id": "2",
                "title": "Establish and Maintain a Software Inventory",
                "description": "Establish and maintain a detailed inventory of all licensed software installed on enterprise assets. The software inventory system should track the name, version, publisher, and install date for all software, including operating systems and applications. The inventory should be updated weekly, or more frequently.",
                "order_index": 1,
                "implementation_group_1": True,
                "implementation_group_2": True,
                "implementation_group_3": True,
                "asset_types": ["Software", "Applications"],
                "security_functions": ["Identify"],
                "implementation_guidance": "Use automated software inventory tools integrated with asset management systems",
                "measurement_specification": "Percentage of software assets documented with complete metadata",
                "procedure_review": "Review software inventory accuracy weekly",
                "automation_support": "Software asset management tools, endpoint agents, vulnerability scanners",
                "dependencies": ["1.1"],
                "prerequisites": ["Asset inventory system", "Endpoint management capabilities"],
                "assessment_criteria": {
                    "coverage": "95% of software documented",
                    "accuracy": "Software information updated weekly",
                    "metadata": "Name, version, publisher, install date captured"
                },
                "validation_methods": ["Automated scanning", "Sample verification", "License audits"],
                "evidence_examples": ["Software inventory reports", "License compliance reports", "Update logs"],
                "external_references": {
                    "NIST CSF": ["ID.AM-02"],
                    "ISO 27001": ["A.8.1.1", "A.12.6.2"],
                    "NIST 800-53": ["CM-8", "SI-2"]
                }
            },
            {
                "safeguard_id": "2.2",
                "control_id": "2",
                "title": "Ensure Authorized Software is Currently Supported",
                "description": "Ensure that only currently supported software is designated as authorized in the software inventory for enterprise assets. If software is unsupported, yet necessary for the fulfillment of the enterprise's mission, document an exception detailing mitigating controls and residual risk acceptance. For any unsupported software without an exception documentation, designate as unauthorized.",
                "order_index": 2,
                "implementation_group_1": True,
                "implementation_group_2": True,
                "implementation_group_3": True,
                "asset_types": ["Software", "Applications"],
                "security_functions": ["Identify", "Protect"],
                "implementation_guidance": "Regularly review software support status and maintain exception documentation",
                "measurement_specification": "Percentage of software with current support status documented",
                "procedure_review": "Review software support status quarterly",
                "automation_support": "Vulnerability management tools, software lifecycle databases",
                "dependencies": ["2.1"],
                "prerequisites": ["Software inventory", "Vendor support information"],
                "assessment_criteria": {
                    "support_status": "Support status documented for all software",
                    "exceptions": "Unsupported software has documented exceptions",
                    "risk_acceptance": "Residual risks formally accepted"
                },
                "validation_methods": ["Support status verification", "Exception review", "Risk assessment"],
                "evidence_examples": ["Support status reports", "Exception documentation", "Risk acceptance forms"],
                "external_references": {
                    "NIST CSF": ["ID.RA-01", "PR.IP-01"],
                    "ISO 27001": ["A.12.6.1"],
                    "NIST 800-53": ["SI-2", "RA-3"]
                }
            }
        ]

    def _get_cis_v8_implementation_examples(self) -> List[Dict[str, Any]]:
        """Get CIS Controls v8 implementation examples data."""
        return [
            {
                "safeguard_id": "1.1",
                "title": "Enterprise Asset Discovery with Lansweeper",
                "description": "Implementation of comprehensive asset discovery using Lansweeper for medium enterprise",
                "implementation_approach": "Deploy Lansweeper agents across network segments with centralized management console",
                "implementation_group": "IG1",
                "organization_size": "medium",
                "industry_sector": "manufacturing",
                "technology_context": "hybrid",
                "asset_types": ["Devices", "Network", "Software"],
                "implementation_steps": [
                    "Install Lansweeper server and database",
                    "Deploy scanning agents to network segments",
                    "Configure discovery schedules and credentials",
                    "Establish asset classification scheme",
                    "Create automated reporting workflows",
                    "Train staff on asset management procedures"
                ],
                "tools_and_technologies": ["Lansweeper", "Active Directory", "SNMP", "WMI"],
                "automation_opportunities": [
                    "Automated agent deployment",
                    "Scheduled discovery scans",
                    "Automated asset classification",
                    "Integration with ITSM tools"
                ],
                "implementation_difficulty": "medium",
                "estimated_effort": "4-6 weeks",
                "budget_considerations": "Software licensing, hardware for server, staff training costs",
                "skill_requirements": ["Network administration", "Database management", "Asset management"],
                "expected_outcomes": [
                    "95% asset discovery coverage",
                    "Automated asset inventory updates",
                    "Improved security visibility",
                    "Better compliance reporting"
                ],
                "success_metrics": [
                    "Asset discovery coverage percentage",
                    "Inventory accuracy rate",
                    "Time to detect new assets",
                    "Compliance audit results"
                ],
                "measurement_procedures": [
                    "Weekly discovery scan reports",
                    "Monthly accuracy validation",
                    "Quarterly compliance assessments"
                ],
                "common_pitfalls": [
                    "Insufficient network credentials",
                    "Firewall blocking discovery traffic",
                    "Incomplete network segment coverage",
                    "Poor asset classification"
                ],
                "lessons_learned": [
                    "Comprehensive credential management is critical",
                    "Network segmentation affects discovery scope",
                    "Regular validation prevents inventory drift",
                    "Staff training improves adoption"
                ],
                "best_practices": [
                    "Use service accounts for discovery",
                    "Implement discovery in phases",
                    "Validate results regularly",
                    "Integrate with existing tools"
                ]
            },
            {
                "safeguard_id": "2.1",
                "title": "Software Asset Management with Microsoft SCCM",
                "description": "Implementation of software inventory using Microsoft System Center Configuration Manager",
                "implementation_approach": "Leverage existing SCCM infrastructure for comprehensive software inventory",
                "implementation_group": "IG1",
                "organization_size": "large",
                "industry_sector": "healthcare",
                "technology_context": "on_premise",
                "asset_types": ["Software", "Applications", "Operating Systems"],
                "implementation_steps": [
                    "Enable software inventory in SCCM",
                    "Configure inventory collection schedules",
                    "Customize software inventory classes",
                    "Create software inventory reports",
                    "Establish software approval workflows",
                    "Train IT staff on inventory management"
                ],
                "tools_and_technologies": ["Microsoft SCCM", "SQL Server", "PowerShell", "Active Directory"],
                "automation_opportunities": [
                    "Automated inventory collection",
                    "Software deployment tracking",
                    "License compliance monitoring",
                    "Unauthorized software detection"
                ],
                "implementation_difficulty": "low",
                "estimated_effort": "2-3 weeks",
                "budget_considerations": "Minimal additional cost if SCCM already deployed",
                "skill_requirements": ["SCCM administration", "SQL reporting", "PowerShell scripting"],
                "expected_outcomes": [
                    "Complete software visibility",
                    "Automated license tracking",
                    "Improved compliance posture",
                    "Reduced security risks"
                ],
                "success_metrics": [
                    "Software inventory completeness",
                    "License compliance percentage",
                    "Unauthorized software detection rate",
                    "Inventory update frequency"
                ],
                "measurement_procedures": [
                    "Daily inventory collection monitoring",
                    "Weekly compliance reporting",
                    "Monthly license reconciliation"
                ],
                "common_pitfalls": [
                    "Incomplete client deployment",
                    "Insufficient inventory classes",
                    "Poor reporting configuration",
                    "Lack of approval workflows"
                ],
                "lessons_learned": [
                    "Client health is critical for accuracy",
                    "Custom inventory classes improve visibility",
                    "Regular reporting drives compliance",
                    "Approval workflows prevent unauthorized software"
                ],
                "best_practices": [
                    "Monitor client health regularly",
                    "Customize inventory for specific needs",
                    "Automate compliance reporting",
                    "Integrate with procurement processes"
                ]
            }
        ]


def import_cis_controls_v8_data(db: Session = None) -> Dict[str, Any]:
    """
    Convenience function to import CIS Controls v8 data.

    Args:
        db: Database session (optional, will create one if not provided)

    Returns:
        Dict containing import results
    """
    if db is None:
        db = next(get_db())

    service = CISControlsImportService(db)
    return service.import_cis_controls_v8()
