"""
Analytics Service for Cybersecurity Framework Management.

This module provides comprehensive analytics capabilities for framework data,
mapping effectiveness, coverage analysis, and compliance reporting.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from collections import defaultdict, Counter
import statistics
from dataclasses import dataclass

from sqlalchemy.orm import Session
from sqlalchemy import func, and_, or_

from api.models.mapping import Mapping
from api.models.isf import ISFVersion, ISFControl, SecurityArea
from api.models.nist_csf import NISTCSFVersion, Function, Category, Subcategory
from api.models.mitre import MitreTechnique, MitreTactic
from api.models.user import User

logger = logging.getLogger(__name__)


@dataclass
class AnalyticsMetric:
    """Data class for analytics metrics."""
    name: str
    value: float
    unit: str
    description: str
    timestamp: datetime
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class TrendData:
    """Data class for trend analysis."""
    period: str
    values: List[float]
    labels: List[str]
    trend_direction: str  # "increasing", "decreasing", "stable"
    trend_strength: float  # 0.0 to 1.0


class FrameworkAnalyticsService:
    """Service for framework analytics and insights."""

    def __init__(self, db: Session):
        self.db = db

    def get_framework_coverage_metrics(self, framework: str) -> Dict[str, Any]:
        """Get comprehensive coverage metrics for a framework."""
        try:
            if framework.lower() == "isf":
                return self._get_isf_coverage_metrics()
            elif framework.lower() == "nist_csf":
                return self._get_nist_csf_coverage_metrics()
            elif framework.lower() == "mitre_attack":
                return self._get_mitre_coverage_metrics()
            else:
                raise ValueError(f"Unsupported framework: {framework}")

        except Exception as e:
            logger.error(f"Error getting coverage metrics for {framework}: {str(e)}")
            return {"error": str(e)}

    def get_mapping_effectiveness_analysis(
        self,
        source_framework: str,
        target_framework: str,
        time_period: Optional[int] = 30
    ) -> Dict[str, Any]:
        """Analyze mapping effectiveness between frameworks."""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=time_period) if time_period else None

            # Query mappings
            query = self.db.query(Mapping).filter(
                Mapping.source_framework == source_framework,
                Mapping.target_framework == target_framework
            )

            if start_date:
                query = query.filter(Mapping.created_at >= start_date)

            mappings = query.all()

            if not mappings:
                return {
                    "total_mappings": 0,
                    "effectiveness_summary": {},
                    "recommendations": ["No mappings found for analysis"]
                }

            # Calculate effectiveness metrics
            effectiveness_scores = [m.effectiveness_score for m in mappings if m.effectiveness_score]
            confidence_scores = [m.confidence_score for m in mappings if m.confidence_score]

            effectiveness_analysis = {
                "total_mappings": len(mappings),
                "effectiveness_summary": {
                    "average_effectiveness": statistics.mean(effectiveness_scores) if effectiveness_scores else 0,
                    "median_effectiveness": statistics.median(effectiveness_scores) if effectiveness_scores else 0,
                    "min_effectiveness": min(effectiveness_scores) if effectiveness_scores else 0,
                    "max_effectiveness": max(effectiveness_scores) if effectiveness_scores else 0,
                    "std_deviation": statistics.stdev(effectiveness_scores) if len(effectiveness_scores) > 1 else 0
                },
                "confidence_summary": {
                    "average_confidence": statistics.mean(confidence_scores) if confidence_scores else 0,
                    "median_confidence": statistics.median(confidence_scores) if confidence_scores else 0,
                    "high_confidence_count": len([s for s in confidence_scores if s >= 0.8]),
                    "low_confidence_count": len([s for s in confidence_scores if s < 0.5])
                },
                "distribution_analysis": self._analyze_score_distribution(effectiveness_scores),
                "quality_assessment": self._assess_mapping_quality(mappings),
                "recommendations": self._generate_effectiveness_recommendations(mappings)
            }

            return effectiveness_analysis

        except Exception as e:
            logger.error(f"Error analyzing mapping effectiveness: {str(e)}")
            return {"error": str(e)}

    def get_coverage_gap_analysis(self, frameworks: List[str]) -> Dict[str, Any]:
        """Analyze coverage gaps across multiple frameworks."""
        try:
            gap_analysis = {
                "frameworks_analyzed": frameworks,
                "gap_summary": {},
                "detailed_gaps": {},
                "priority_recommendations": []
            }

            for framework in frameworks:
                framework_gaps = self._identify_framework_gaps(framework)
                gap_analysis["detailed_gaps"][framework] = framework_gaps

            # Cross-framework gap analysis
            gap_analysis["cross_framework_gaps"] = self._analyze_cross_framework_gaps(frameworks)
            gap_analysis["gap_summary"] = self._summarize_gaps(gap_analysis["detailed_gaps"])
            gap_analysis["priority_recommendations"] = self._generate_gap_recommendations(gap_analysis)

            return gap_analysis

        except Exception as e:
            logger.error(f"Error analyzing coverage gaps: {str(e)}")
            return {"error": str(e)}

    def get_trend_analysis(
        self,
        metric: str,
        framework: str,
        period_days: int = 90
    ) -> TrendData:
        """Analyze trends for specific metrics over time."""
        try:
            end_date = datetime.now()
            start_date = end_date - timedelta(days=period_days)

            if metric == "mapping_count":
                return self._analyze_mapping_count_trend(framework, start_date, end_date)
            elif metric == "effectiveness":
                return self._analyze_effectiveness_trend(framework, start_date, end_date)
            elif metric == "coverage":
                return self._analyze_coverage_trend(framework, start_date, end_date)
            else:
                raise ValueError(f"Unsupported metric: {metric}")

        except Exception as e:
            logger.error(f"Error analyzing trend for {metric}: {str(e)}")
            return TrendData(
                period=f"{period_days} days",
                values=[],
                labels=[],
                trend_direction="unknown",
                trend_strength=0.0
            )

    def get_compliance_readiness_score(
        self,
        framework: str,
        assessment_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Calculate compliance readiness score for a framework."""
        try:
            if framework.lower() == "isf":
                return self._calculate_isf_compliance_score(assessment_data)
            elif framework.lower() == "nist_csf":
                return self._calculate_nist_csf_compliance_score(assessment_data)
            else:
                raise ValueError(f"Compliance scoring not supported for: {framework}")

        except Exception as e:
            logger.error(f"Error calculating compliance score: {str(e)}")
            return {"error": str(e)}

    def _get_isf_coverage_metrics(self) -> Dict[str, Any]:
        """Get ISF-specific coverage metrics."""
        # Query ISF data
        total_controls = self.db.query(ISFControl).count()
        mapped_controls = self.db.query(Mapping).filter(
            Mapping.target_framework == "isf"
        ).distinct(Mapping.target_id).count()

        security_areas = self.db.query(SecurityArea).all()
        area_coverage = {}

        for area in security_areas:
            area_controls = self.db.query(ISFControl).filter(
                ISFControl.area_id == area.id
            ).count()
            
            area_mapped = self.db.query(Mapping).filter(
                Mapping.target_framework == "isf",
                Mapping.target_id.like(f"{area.area_id}%")
            ).count()

            area_coverage[area.area_id] = {
                "name": area.name,
                "total_controls": area_controls,
                "mapped_controls": area_mapped,
                "coverage_percentage": (area_mapped / area_controls * 100) if area_controls > 0 else 0
            }

        return {
            "framework": "ISF",
            "total_controls": total_controls,
            "mapped_controls": mapped_controls,
            "overall_coverage_percentage": (mapped_controls / total_controls * 100) if total_controls > 0 else 0,
            "security_area_coverage": area_coverage,
            "coverage_quality": self._assess_coverage_quality("isf", mapped_controls, total_controls)
        }

    def _get_nist_csf_coverage_metrics(self) -> Dict[str, Any]:
        """Get NIST CSF-specific coverage metrics."""
        # Query NIST CSF data
        total_subcategories = self.db.query(Subcategory).count()
        mapped_subcategories = self.db.query(Mapping).filter(
            Mapping.target_framework == "nist_csf"
        ).distinct(Mapping.target_id).count()

        functions = self.db.query(Function).all()
        function_coverage = {}

        for function in functions:
            function_subcategories = self.db.query(Subcategory).join(Category).filter(
                Category.function_id == function.id
            ).count()
            
            function_mapped = self.db.query(Mapping).filter(
                Mapping.target_framework == "nist_csf",
                Mapping.target_id.like(f"{function.function_id}.%")
            ).count()

            function_coverage[function.function_id] = {
                "name": function.name,
                "total_subcategories": function_subcategories,
                "mapped_subcategories": function_mapped,
                "coverage_percentage": (function_mapped / function_subcategories * 100) if function_subcategories > 0 else 0
            }

        return {
            "framework": "NIST CSF",
            "total_subcategories": total_subcategories,
            "mapped_subcategories": mapped_subcategories,
            "overall_coverage_percentage": (mapped_subcategories / total_subcategories * 100) if total_subcategories > 0 else 0,
            "function_coverage": function_coverage,
            "coverage_quality": self._assess_coverage_quality("nist_csf", mapped_subcategories, total_subcategories)
        }

    def _get_mitre_coverage_metrics(self) -> Dict[str, Any]:
        """Get MITRE ATT&CK-specific coverage metrics."""
        # Query MITRE data
        total_techniques = self.db.query(MitreTechnique).count()
        mapped_techniques = self.db.query(Mapping).filter(
            Mapping.source_framework == "mitre_attack"
        ).distinct(Mapping.source_id).count()

        tactics = self.db.query(MitreTactic).all()
        tactic_coverage = {}

        for tactic in tactics:
            # This is a simplified approach - in reality, you'd need to join through technique-tactic relationships
            tactic_techniques = self.db.query(MitreTechnique).filter(
                MitreTechnique.tactics.contains(tactic.tactic_id)
            ).count()
            
            tactic_mapped = self.db.query(Mapping).filter(
                Mapping.source_framework == "mitre_attack"
            ).count()  # Simplified - would need proper tactic filtering

            tactic_coverage[tactic.tactic_id] = {
                "name": tactic.name,
                "total_techniques": tactic_techniques,
                "mapped_techniques": tactic_mapped,
                "coverage_percentage": (tactic_mapped / tactic_techniques * 100) if tactic_techniques > 0 else 0
            }

        return {
            "framework": "MITRE ATT&CK",
            "total_techniques": total_techniques,
            "mapped_techniques": mapped_techniques,
            "overall_coverage_percentage": (mapped_techniques / total_techniques * 100) if total_techniques > 0 else 0,
            "tactic_coverage": tactic_coverage,
            "coverage_quality": self._assess_coverage_quality("mitre_attack", mapped_techniques, total_techniques)
        }

    def _analyze_score_distribution(self, scores: List[float]) -> Dict[str, Any]:
        """Analyze the distribution of effectiveness scores."""
        if not scores:
            return {"error": "No scores to analyze"}

        # Create score buckets
        buckets = {
            "excellent": len([s for s in scores if s >= 0.9]),
            "good": len([s for s in scores if 0.7 <= s < 0.9]),
            "fair": len([s for s in scores if 0.5 <= s < 0.7]),
            "poor": len([s for s in scores if s < 0.5])
        }

        return {
            "distribution": buckets,
            "percentiles": {
                "25th": statistics.quantiles(scores, n=4)[0] if len(scores) >= 4 else 0,
                "50th": statistics.median(scores),
                "75th": statistics.quantiles(scores, n=4)[2] if len(scores) >= 4 else 0,
                "90th": statistics.quantiles(scores, n=10)[8] if len(scores) >= 10 else 0
            }
        }

    def _assess_mapping_quality(self, mappings: List[Mapping]) -> Dict[str, Any]:
        """Assess the overall quality of mappings."""
        if not mappings:
            return {"quality_score": 0, "assessment": "No mappings to assess"}

        # Quality factors
        high_effectiveness_count = len([m for m in mappings if m.effectiveness_score and m.effectiveness_score >= 0.8])
        high_confidence_count = len([m for m in mappings if m.confidence_score and m.confidence_score >= 0.8])
        has_rationale_count = len([m for m in mappings if m.rationale and len(m.rationale) > 10])

        total_mappings = len(mappings)
        
        # Calculate quality score (0-100)
        effectiveness_factor = (high_effectiveness_count / total_mappings) * 40
        confidence_factor = (high_confidence_count / total_mappings) * 30
        rationale_factor = (has_rationale_count / total_mappings) * 30

        quality_score = effectiveness_factor + confidence_factor + rationale_factor

        # Determine quality level
        if quality_score >= 80:
            quality_level = "Excellent"
        elif quality_score >= 60:
            quality_level = "Good"
        elif quality_score >= 40:
            quality_level = "Fair"
        else:
            quality_level = "Poor"

        return {
            "quality_score": round(quality_score, 2),
            "quality_level": quality_level,
            "factors": {
                "high_effectiveness_percentage": round((high_effectiveness_count / total_mappings) * 100, 2),
                "high_confidence_percentage": round((high_confidence_count / total_mappings) * 100, 2),
                "documented_rationale_percentage": round((has_rationale_count / total_mappings) * 100, 2)
            }
        }

    def _generate_effectiveness_recommendations(self, mappings: List[Mapping]) -> List[str]:
        """Generate recommendations for improving mapping effectiveness."""
        recommendations = []

        if not mappings:
            return ["Create initial mappings between frameworks"]

        # Analyze effectiveness scores
        effectiveness_scores = [m.effectiveness_score for m in mappings if m.effectiveness_score]
        if effectiveness_scores:
            avg_effectiveness = statistics.mean(effectiveness_scores)
            if avg_effectiveness < 0.6:
                recommendations.append("Review and improve low-effectiveness mappings")
            
            low_effectiveness_count = len([s for s in effectiveness_scores if s < 0.5])
            if low_effectiveness_count > len(effectiveness_scores) * 0.2:
                recommendations.append("Focus on addressing mappings with effectiveness scores below 0.5")

        # Analyze confidence scores
        confidence_scores = [m.confidence_score for m in mappings if m.confidence_score]
        if confidence_scores:
            low_confidence_count = len([s for s in confidence_scores if s < 0.6])
            if low_confidence_count > len(confidence_scores) * 0.3:
                recommendations.append("Validate and improve confidence in mapping relationships")

        # Check for missing rationales
        missing_rationale_count = len([m for m in mappings if not m.rationale or len(m.rationale) < 10])
        if missing_rationale_count > len(mappings) * 0.4:
            recommendations.append("Add detailed rationales for mapping decisions")

        if not recommendations:
            recommendations.append("Mapping quality is good - continue regular reviews and updates")

        return recommendations

    def _assess_coverage_quality(self, framework: str, mapped_count: int, total_count: int) -> Dict[str, Any]:
        """Assess the quality of framework coverage."""
        if total_count == 0:
            return {"quality": "Unknown", "score": 0}

        coverage_percentage = (mapped_count / total_count) * 100

        if coverage_percentage >= 90:
            quality = "Excellent"
            score = 95
        elif coverage_percentage >= 75:
            quality = "Good"
            score = 85
        elif coverage_percentage >= 50:
            quality = "Fair"
            score = 65
        elif coverage_percentage >= 25:
            quality = "Poor"
            score = 40
        else:
            quality = "Very Poor"
            score = 20

        return {
            "quality": quality,
            "score": score,
            "coverage_percentage": round(coverage_percentage, 2),
            "mapped_count": mapped_count,
            "total_count": total_count
        }

    def _identify_framework_gaps(self, framework: str) -> Dict[str, Any]:
        """Identify gaps in framework coverage (mock implementation)."""
        # This would be implemented with actual database queries
        return {
            "unmapped_controls": [
                {"id": "SG5", "name": "Security Awareness Training", "priority": "high"},
                {"id": "RM3", "name": "Third-party Risk Assessment", "priority": "medium"}
            ],
            "weak_mappings": [
                {"control_id": "AC1", "technique_id": "T1078", "effectiveness": 0.3}
            ],
            "missing_control_types": ["detective", "corrective"],
            "priority_areas": ["incident_response", "supply_chain_security"]
        }

    def _analyze_cross_framework_gaps(self, frameworks: List[str]) -> Dict[str, Any]:
        """Analyze gaps across multiple frameworks."""
        return {
            "common_gaps": [
                "Supply chain risk management",
                "Advanced persistent threat detection",
                "Zero-day vulnerability response"
            ],
            "framework_specific_gaps": {
                "isf": ["Cloud security controls", "IoT security"],
                "nist_csf": ["Quantum cryptography readiness", "AI/ML security"]
            },
            "integration_gaps": [
                "Cross-framework mapping completeness",
                "Unified compliance reporting"
            ]
        }

    def _summarize_gaps(self, detailed_gaps: Dict[str, Any]) -> Dict[str, Any]:
        """Summarize gap analysis across frameworks."""
        total_gaps = sum(len(gaps.get("unmapped_controls", [])) for gaps in detailed_gaps.values())
        high_priority_gaps = sum(
            len([g for g in gaps.get("unmapped_controls", []) if g.get("priority") == "high"])
            for gaps in detailed_gaps.values()
        )

        return {
            "total_gaps_identified": total_gaps,
            "high_priority_gaps": high_priority_gaps,
            "frameworks_with_gaps": len([f for f, gaps in detailed_gaps.items() if gaps.get("unmapped_controls")]),
            "most_common_gap_types": ["incident_response", "supply_chain", "cloud_security"]
        }

    def _generate_gap_recommendations(self, gap_analysis: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on gap analysis."""
        return [
            "Prioritize high-priority unmapped controls for immediate attention",
            "Develop implementation roadmap for medium-priority gaps",
            "Establish regular gap analysis reviews",
            "Consider framework consolidation for overlapping areas",
            "Enhance cross-framework mapping coverage"
        ]

    def _analyze_mapping_count_trend(self, framework: str, start_date: datetime, end_date: datetime) -> TrendData:
        """Analyze mapping count trend over time."""
        # Mock implementation - would query actual database
        days = (end_date - start_date).days
        values = [10 + i * 2 for i in range(days // 7)]  # Weekly data points
        labels = [(start_date + timedelta(weeks=i)).strftime("%Y-%m-%d") for i in range(len(values))]

        return TrendData(
            period=f"{days} days",
            values=values,
            labels=labels,
            trend_direction="increasing",
            trend_strength=0.8
        )

    def _analyze_effectiveness_trend(self, framework: str, start_date: datetime, end_date: datetime) -> TrendData:
        """Analyze effectiveness trend over time."""
        # Mock implementation
        days = (end_date - start_date).days
        values = [0.7 + (i * 0.02) for i in range(days // 7)]
        labels = [(start_date + timedelta(weeks=i)).strftime("%Y-%m-%d") for i in range(len(values))]

        return TrendData(
            period=f"{days} days",
            values=values,
            labels=labels,
            trend_direction="increasing",
            trend_strength=0.6
        )

    def _analyze_coverage_trend(self, framework: str, start_date: datetime, end_date: datetime) -> TrendData:
        """Analyze coverage trend over time."""
        # Mock implementation
        days = (end_date - start_date).days
        values = [60 + (i * 1.5) for i in range(days // 7)]
        labels = [(start_date + timedelta(weeks=i)).strftime("%Y-%m-%d") for i in range(len(values))]

        return TrendData(
            period=f"{days} days",
            values=values,
            labels=labels,
            trend_direction="increasing",
            trend_strength=0.9
        )

    def _calculate_isf_compliance_score(self, assessment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate ISF compliance readiness score."""
        # Mock implementation based on assessment data
        implemented_controls = assessment_data.get("implemented_controls", [])
        total_controls = assessment_data.get("total_controls", 150)

        compliance_score = (len(implemented_controls) / total_controls) * 100

        return {
            "framework": "ISF",
            "compliance_score": round(compliance_score, 2),
            "compliance_level": self._get_compliance_level(compliance_score),
            "implemented_controls": len(implemented_controls),
            "total_controls": total_controls,
            "gap_count": total_controls - len(implemented_controls),
            "recommendations": self._get_compliance_recommendations(compliance_score)
        }

    def _calculate_nist_csf_compliance_score(self, assessment_data: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate NIST CSF compliance readiness score."""
        # Mock implementation
        maturity_scores = assessment_data.get("maturity_scores", {})
        avg_maturity = statistics.mean(maturity_scores.values()) if maturity_scores else 0

        compliance_score = (avg_maturity / 4) * 100  # Assuming 4-level maturity scale

        return {
            "framework": "NIST CSF",
            "compliance_score": round(compliance_score, 2),
            "compliance_level": self._get_compliance_level(compliance_score),
            "average_maturity": round(avg_maturity, 2),
            "function_maturity": maturity_scores,
            "recommendations": self._get_compliance_recommendations(compliance_score)
        }

    def _get_compliance_level(self, score: float) -> str:
        """Get compliance level based on score."""
        if score >= 90:
            return "Excellent"
        elif score >= 75:
            return "Good"
        elif score >= 60:
            return "Fair"
        elif score >= 40:
            return "Poor"
        else:
            return "Very Poor"

    def _get_compliance_recommendations(self, score: float) -> List[str]:
        """Get compliance recommendations based on score."""
        if score >= 90:
            return ["Maintain current compliance level", "Regular compliance reviews"]
        elif score >= 75:
            return ["Address remaining gaps", "Enhance documentation"]
        elif score >= 60:
            return ["Focus on critical controls", "Develop improvement plan"]
        else:
            return ["Immediate action required", "Comprehensive compliance program needed"]


class ReportingService:
    """Service for generating comprehensive reports."""

    def __init__(self, db: Session):
        self.db = db
        self.analytics_service = FrameworkAnalyticsService(db)

    def generate_executive_summary(self, frameworks: List[str]) -> Dict[str, Any]:
        """Generate executive summary report."""
        summary = {
            "report_type": "Executive Summary",
            "generated_at": datetime.now().isoformat(),
            "frameworks_analyzed": frameworks,
            "key_metrics": {},
            "recommendations": [],
            "risk_assessment": {}
        }

        # Collect key metrics for each framework
        for framework in frameworks:
            metrics = self.analytics_service.get_framework_coverage_metrics(framework)
            summary["key_metrics"][framework] = {
                "coverage_percentage": metrics.get("overall_coverage_percentage", 0),
                "quality_score": metrics.get("coverage_quality", {}).get("score", 0)
            }

        # Generate overall recommendations
        summary["recommendations"] = [
            "Prioritize frameworks with lowest coverage percentages",
            "Focus on improving mapping quality scores",
            "Establish regular compliance monitoring",
            "Develop integrated security framework strategy"
        ]

        # Risk assessment
        avg_coverage = statistics.mean([
            metrics["coverage_percentage"]
            for metrics in summary["key_metrics"].values()
        ])

        if avg_coverage < 50:
            risk_level = "High"
        elif avg_coverage < 75:
            risk_level = "Medium"
        else:
            risk_level = "Low"

        summary["risk_assessment"] = {
            "overall_risk_level": risk_level,
            "average_coverage": round(avg_coverage, 2),
            "critical_gaps": ["Incident response", "Supply chain security"]
        }

        return summary

    def generate_detailed_analysis_report(self, framework: str) -> Dict[str, Any]:
        """Generate detailed analysis report for a specific framework."""
        return {
            "report_type": "Detailed Analysis",
            "framework": framework,
            "generated_at": datetime.now().isoformat(),
            "coverage_analysis": self.analytics_service.get_framework_coverage_metrics(framework),
            "gap_analysis": self.analytics_service.get_coverage_gap_analysis([framework]),
            "trend_analysis": {
                "coverage_trend": self.analytics_service.get_trend_analysis("coverage", framework),
                "mapping_trend": self.analytics_service.get_trend_analysis("mapping_count", framework)
            },
            "recommendations": [
                "Implement missing controls identified in gap analysis",
                "Improve low-effectiveness mappings",
                "Regular framework updates and reviews"
            ]
        }
