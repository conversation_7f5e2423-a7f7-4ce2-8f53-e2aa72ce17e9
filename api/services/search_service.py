"""
Advanced Search Service.

This service provides comprehensive search capabilities across all
cybersecurity frameworks with semantic understanding, faceted search,
and intelligent ranking algorithms.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func, text, desc, asc
from sqlalchemy.dialects.postgresql import TSVECTOR
import json
import re
from datetime import datetime, timedelta

from api.models.search_linking import (
    SearchIndex, SearchQuery, SearchResult, SearchFacet,
    CrossFrameworkRelationship
)
from api.models.isf import ISFControl, ISFSecurityArea
from api.models.nist_csf_2 import NISTCSFSubcategory, NISTCSFCategory, NISTCSFFunction
from api.models.iso_27001 import ISO27001Control, ISO27001Domain
from api.models.cis_controls import CISSafeguard, CISControl

logger = logging.getLogger(__name__)


class AdvancedSearchService:
    """
    Advanced search service with semantic understanding and cross-framework capabilities.
    """
    
    def __init__(self, db: Session):
        self.db = db
        self.search_weights = {
            'title': 2.0,
            'description': 1.5,
            'content': 1.0,
            'keywords': 1.8,
            'tags': 1.2
        }
    
    async def search(
        self,
        query: str,
        frameworks: Optional[List[str]] = None,
        element_types: Optional[List[str]] = None,
        filters: Optional[Dict[str, Any]] = None,
        sort_by: str = "relevance",
        limit: int = 20,
        offset: int = 0,
        user_id: Optional[str] = None,
        include_facets: bool = True,
        search_type: str = "hybrid"
    ) -> Dict[str, Any]:
        """
        Perform advanced search across all frameworks.
        
        Args:
            query: Search query string
            frameworks: List of frameworks to search in
            element_types: List of element types to include
            filters: Additional filters to apply
            sort_by: Sort criteria (relevance, date, popularity, title)
            limit: Maximum number of results
            offset: Result offset for pagination
            user_id: User ID for personalization
            include_facets: Whether to include facet data
            search_type: Type of search (text, semantic, hybrid)
        
        Returns:
            Dictionary containing search results and metadata
        """
        try:
            # Record search query
            search_query = await self._record_search_query(
                query, frameworks, element_types, filters, user_id, search_type
            )
            
            # Build base query
            base_query = self._build_base_query(frameworks, element_types, filters)
            
            # Apply search logic based on type
            if search_type == "semantic":
                results_query = await self._semantic_search(base_query, query)
            elif search_type == "text":
                results_query = await self._text_search(base_query, query)
            else:  # hybrid
                results_query = await self._hybrid_search(base_query, query)
            
            # Apply sorting
            results_query = self._apply_sorting(results_query, sort_by)
            
            # Get total count
            total_count = results_query.count()
            
            # Apply pagination
            results = results_query.offset(offset).limit(limit).all()
            
            # Process results
            processed_results = await self._process_search_results(
                results, query, search_query.id
            )
            
            # Get facets if requested
            facets = {}
            if include_facets:
                facets = await self._generate_facets(base_query, filters)
            
            # Get related searches and suggestions
            suggestions = await self._get_search_suggestions(query, frameworks)
            
            # Update query with results
            search_query.results_count = total_count
            self.db.commit()
            
            return {
                "query": query,
                "total_results": total_count,
                "results": processed_results,
                "facets": facets,
                "suggestions": suggestions,
                "search_metadata": {
                    "search_type": search_type,
                    "frameworks_searched": frameworks or "all",
                    "element_types": element_types or "all",
                    "sort_by": sort_by,
                    "query_id": search_query.id
                },
                "pagination": {
                    "limit": limit,
                    "offset": offset,
                    "has_more": (offset + limit) < total_count
                }
            }
            
        except Exception as e:
            logger.error(f"Search error: {str(e)}")
            raise
    
    def _build_base_query(
        self,
        frameworks: Optional[List[str]] = None,
        element_types: Optional[List[str]] = None,
        filters: Optional[Dict[str, Any]] = None
    ):
        """Build base query with framework and type filters."""
        query = self.db.query(SearchIndex).filter(SearchIndex.deleted_at.is_(None))
        
        # Framework filter
        if frameworks:
            query = query.filter(SearchIndex.framework.in_(frameworks))
        
        # Element type filter
        if element_types:
            query = query.filter(SearchIndex.element_type.in_(element_types))
        
        # Additional filters
        if filters:
            for key, value in filters.items():
                if key == "category" and value:
                    query = query.filter(SearchIndex.category.in_(value if isinstance(value, list) else [value]))
                elif key == "tags" and value:
                    # JSON array contains filter
                    for tag in (value if isinstance(value, list) else [value]):
                        query = query.filter(SearchIndex.tags.contains([tag]))
                elif key == "quality_min" and value:
                    query = query.filter(SearchIndex.quality_score >= float(value))
                elif key == "date_from" and value:
                    query = query.filter(SearchIndex.created_at >= value)
                elif key == "date_to" and value:
                    query = query.filter(SearchIndex.created_at <= value)
        
        return query
    
    async def _text_search(self, base_query, query_text: str):
        """Perform full-text search using PostgreSQL text search."""
        # Clean and prepare search query
        search_terms = self._prepare_search_terms(query_text)
        
        if not search_terms:
            return base_query
        
        # Create tsquery
        tsquery = " & ".join([f"{term}:*" for term in search_terms])
        
        # Apply text search with ranking
        return base_query.filter(
            SearchIndex.search_vector.match(tsquery)
        ).order_by(
            desc(func.ts_rank(SearchIndex.search_vector, func.plainto_tsquery(query_text)))
        )
    
    async def _semantic_search(self, base_query, query_text: str):
        """Perform semantic search using embeddings (placeholder for ML integration)."""
        # This would integrate with a semantic search engine like Elasticsearch
        # or a vector database for now, we'll use enhanced text matching
        
        search_terms = self._prepare_search_terms(query_text)
        
        if not search_terms:
            return base_query
        
        # Enhanced text matching with semantic understanding
        conditions = []
        for term in search_terms:
            term_conditions = [
                SearchIndex.title.ilike(f"%{term}%"),
                SearchIndex.description.ilike(f"%{term}%"),
                SearchIndex.content.ilike(f"%{term}%"),
            ]
            conditions.append(or_(*term_conditions))
        
        return base_query.filter(and_(*conditions))
    
    async def _hybrid_search(self, base_query, query_text: str):
        """Combine text and semantic search for best results."""
        # For now, use enhanced text search with multiple ranking factors
        search_terms = self._prepare_search_terms(query_text)
        
        if not search_terms:
            return base_query.order_by(desc(SearchIndex.popularity_score))
        
        # Multi-field search with different weights
        search_conditions = []
        
        for term in search_terms:
            term_conditions = [
                SearchIndex.title.ilike(f"%{term}%"),
                SearchIndex.description.ilike(f"%{term}%"),
                SearchIndex.content.ilike(f"%{term}%"),
            ]
            search_conditions.append(or_(*term_conditions))
        
        # Apply search conditions
        query = base_query.filter(or_(*search_conditions))
        
        # Calculate relevance score
        relevance_score = (
            func.coalesce(
                func.ts_rank(SearchIndex.search_vector, func.plainto_tsquery(query_text)), 0
            ) * 2.0 +
            SearchIndex.quality_score * 1.5 +
            SearchIndex.popularity_score * 1.0 +
            SearchIndex.search_weight * 0.5
        )
        
        return query.order_by(desc(relevance_score))
    
    def _apply_sorting(self, query, sort_by: str):
        """Apply sorting to search results."""
        if sort_by == "relevance":
            # Already sorted by relevance in search methods
            return query
        elif sort_by == "title":
            return query.order_by(asc(SearchIndex.title))
        elif sort_by == "date":
            return query.order_by(desc(SearchIndex.created_at))
        elif sort_by == "popularity":
            return query.order_by(desc(SearchIndex.popularity_score))
        elif sort_by == "quality":
            return query.order_by(desc(SearchIndex.quality_score))
        else:
            return query
    
    async def _process_search_results(
        self, 
        results: List[SearchIndex], 
        query: str, 
        query_id: int
    ) -> List[Dict[str, Any]]:
        """Process search results and create SearchResult records."""
        processed_results = []
        
        for rank, result in enumerate(results, 1):
            # Create search result record
            search_result = SearchResult(
                query_id=query_id,
                search_item_id=result.id,
                rank_position=rank,
                relevance_score=self._calculate_relevance_score(result, query),
                result_snippet=self._generate_snippet(result, query)
            )
            self.db.add(search_result)
            
            # Process result for API response
            processed_result = {
                "id": result.id,
                "framework": result.framework,
                "element_type": result.element_type,
                "element_id": result.element_id,
                "title": result.title,
                "description": result.description,
                "category": result.category,
                "subcategory": result.subcategory,
                "tags": result.tags or [],
                "snippet": search_result.result_snippet,
                "relevance_score": search_result.relevance_score,
                "quality_score": result.quality_score,
                "popularity_score": result.popularity_score,
                "related_elements": result.related_elements or [],
                "mapped_elements": result.mapped_elements or [],
                "rank": rank
            }
            
            processed_results.append(processed_result)
        
        return processed_results
    
    def _calculate_relevance_score(self, result: SearchIndex, query: str) -> float:
        """Calculate relevance score for a search result."""
        score = 0.0
        query_lower = query.lower()
        
        # Title match
        if query_lower in (result.title or "").lower():
            score += self.search_weights['title']
        
        # Description match
        if query_lower in (result.description or "").lower():
            score += self.search_weights['description']
        
        # Content match
        if query_lower in (result.content or "").lower():
            score += self.search_weights['content']
        
        # Add quality and popularity factors
        score += result.quality_score * 0.5
        score += result.popularity_score * 0.3
        score += result.search_weight * 0.2
        
        return round(score, 3)
    
    def _generate_snippet(self, result: SearchIndex, query: str, max_length: int = 200) -> str:
        """Generate search result snippet with highlighted terms."""
        content = result.description or result.content or result.title or ""
        
        if len(content) <= max_length:
            return content
        
        # Find best snippet around query terms
        query_terms = self._prepare_search_terms(query)
        best_snippet = content[:max_length]
        
        for term in query_terms:
            term_pos = content.lower().find(term.lower())
            if term_pos != -1:
                start = max(0, term_pos - max_length // 2)
                end = min(len(content), start + max_length)
                snippet = content[start:end]
                
                if start > 0:
                    snippet = "..." + snippet
                if end < len(content):
                    snippet = snippet + "..."
                
                best_snippet = snippet
                break
        
        return best_snippet
    
    def _prepare_search_terms(self, query: str) -> List[str]:
        """Prepare search terms from query string."""
        # Remove special characters and split
        cleaned_query = re.sub(r'[^\w\s]', ' ', query)
        terms = [term.strip().lower() for term in cleaned_query.split() if len(term.strip()) > 2]
        return terms
    
    async def _record_search_query(
        self,
        query: str,
        frameworks: Optional[List[str]],
        element_types: Optional[List[str]],
        filters: Optional[Dict[str, Any]],
        user_id: Optional[str],
        search_type: str
    ) -> SearchQuery:
        """Record search query for analytics."""
        search_query = SearchQuery(
            query_text=query,
            query_type=search_type,
            frameworks=frameworks,
            element_types=element_types,
            filters=filters,
            user_id=user_id
        )
        
        self.db.add(search_query)
        self.db.flush()  # Get ID without committing
        
        return search_query
    
    async def _generate_facets(
        self, 
        base_query, 
        current_filters: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """Generate search facets for filtering."""
        facets = {}
        
        # Framework facet
        framework_counts = base_query.with_entities(
            SearchIndex.framework,
            func.count(SearchIndex.id).label('count')
        ).group_by(SearchIndex.framework).all()
        
        facets['frameworks'] = [
            {"value": fw, "count": count, "label": self._get_framework_label(fw)}
            for fw, count in framework_counts
        ]
        
        # Element type facet
        type_counts = base_query.with_entities(
            SearchIndex.element_type,
            func.count(SearchIndex.id).label('count')
        ).group_by(SearchIndex.element_type).all()
        
        facets['element_types'] = [
            {"value": et, "count": count, "label": et.replace('_', ' ').title()}
            for et, count in type_counts
        ]
        
        # Category facet
        category_counts = base_query.filter(
            SearchIndex.category.isnot(None)
        ).with_entities(
            SearchIndex.category,
            func.count(SearchIndex.id).label('count')
        ).group_by(SearchIndex.category).all()
        
        facets['categories'] = [
            {"value": cat, "count": count, "label": cat}
            for cat, count in category_counts
        ]
        
        # Quality score ranges
        quality_ranges = [
            ("0.0-0.2", 0.0, 0.2),
            ("0.2-0.4", 0.2, 0.4),
            ("0.4-0.6", 0.4, 0.6),
            ("0.6-0.8", 0.6, 0.8),
            ("0.8-1.0", 0.8, 1.0)
        ]
        
        quality_facets = []
        for label, min_val, max_val in quality_ranges:
            count = base_query.filter(
                and_(
                    SearchIndex.quality_score >= min_val,
                    SearchIndex.quality_score < max_val
                )
            ).count()
            if count > 0:
                quality_facets.append({
                    "value": f"{min_val}-{max_val}",
                    "count": count,
                    "label": f"Quality {label}"
                })
        
        facets['quality_ranges'] = quality_facets
        
        return facets
    
    async def _get_search_suggestions(
        self, 
        query: str, 
        frameworks: Optional[List[str]] = None
    ) -> List[str]:
        """Get search suggestions based on query and popular searches."""
        suggestions = []
        
        # Get similar queries from search history
        similar_queries = self.db.query(SearchQuery.query_text).filter(
            and_(
                SearchQuery.query_text.ilike(f"%{query}%"),
                SearchQuery.query_text != query,
                SearchQuery.results_count > 0
            )
        ).distinct().limit(5).all()
        
        suggestions.extend([q[0] for q in similar_queries])
        
        # Add framework-specific suggestions
        if frameworks:
            for framework in frameworks:
                framework_suggestions = self._get_framework_suggestions(framework, query)
                suggestions.extend(framework_suggestions)
        
        return list(set(suggestions))[:10]  # Remove duplicates and limit
    
    def _get_framework_label(self, framework: str) -> str:
        """Get human-readable framework label."""
        labels = {
            "ISF": "Information Security Framework",
            "NIST_CSF_2": "NIST Cybersecurity Framework 2.0",
            "ISO_27001": "ISO/IEC 27001",
            "CIS_CONTROLS": "CIS Critical Security Controls"
        }
        return labels.get(framework, framework)
    
    def _get_framework_suggestions(self, framework: str, query: str) -> List[str]:
        """Get framework-specific search suggestions."""
        # This could be enhanced with framework-specific terminology
        suggestions = []
        
        if framework == "ISF":
            if "security" in query.lower():
                suggestions.extend(["security governance", "security policy", "security awareness"])
        elif framework == "NIST_CSF_2":
            if "govern" in query.lower():
                suggestions.extend(["governance strategy", "risk management", "organizational context"])
        elif framework == "ISO_27001":
            if "control" in query.lower():
                suggestions.extend(["access control", "information security controls", "security controls"])
        elif framework == "CIS_CONTROLS":
            if "asset" in query.lower():
                suggestions.extend(["asset inventory", "asset management", "enterprise assets"])
        
        return suggestions


class SearchIndexService:
    """
    Service for managing the search index and keeping it synchronized.
    """

    def __init__(self, db: Session):
        self.db = db

    async def rebuild_search_index(self) -> Dict[str, int]:
        """Rebuild the entire search index from framework data."""
        try:
            # Clear existing index
            self.db.query(SearchIndex).delete()

            stats = {
                "isf_indexed": 0,
                "nist_csf_indexed": 0,
                "iso_27001_indexed": 0,
                "cis_controls_indexed": 0,
                "total_indexed": 0
            }

            # Index ISF data
            stats["isf_indexed"] = await self._index_isf_data()

            # Index NIST CSF data
            stats["nist_csf_indexed"] = await self._index_nist_csf_data()

            # Index ISO 27001 data
            stats["iso_27001_indexed"] = await self._index_iso_27001_data()

            # Index CIS Controls data
            stats["cis_controls_indexed"] = await self._index_cis_controls_data()

            stats["total_indexed"] = sum([
                stats["isf_indexed"],
                stats["nist_csf_indexed"],
                stats["iso_27001_indexed"],
                stats["cis_controls_indexed"]
            ])

            self.db.commit()
            logger.info(f"Search index rebuilt: {stats}")

            return stats

        except Exception as e:
            self.db.rollback()
            logger.error(f"Error rebuilding search index: {str(e)}")
            raise

    async def _index_isf_data(self) -> int:
        """Index ISF framework data."""
        count = 0

        # Index ISF controls
        isf_controls = self.db.query(ISFControl).filter(
            ISFControl.deleted_at.is_(None)
        ).all()

        for control in isf_controls:
            search_item = SearchIndex(
                framework="ISF",
                element_type="control",
                element_id=control.control_id,
                internal_id=control.id,
                title=control.title,
                description=control.description,
                content=f"{control.title} {control.description} {control.implementation_guidance or ''}",
                category=control.security_area.name if control.security_area else None,
                keywords=self._extract_keywords(control.title, control.description),
                tags=["isf", "control", "security"],
                quality_score=0.8,  # High quality for official framework data
                search_weight=1.0
            )

            self.db.add(search_item)
            count += 1

        return count

    async def _index_nist_csf_data(self) -> int:
        """Index NIST CSF framework data."""
        count = 0

        # Index NIST CSF subcategories
        nist_subcategories = self.db.query(NISTCSFSubcategory).filter(
            NISTCSFSubcategory.deleted_at.is_(None)
        ).all()

        for subcategory in nist_subcategories:
            search_item = SearchIndex(
                framework="NIST_CSF_2",
                element_type="subcategory",
                element_id=subcategory.subcategory_id,
                internal_id=subcategory.id,
                title=subcategory.outcome,
                description=subcategory.implementation_guidance,
                content=f"{subcategory.outcome} {subcategory.implementation_guidance or ''}",
                category=subcategory.category.name if subcategory.category else None,
                subcategory=subcategory.category.function.name if subcategory.category and subcategory.category.function else None,
                keywords=self._extract_keywords(subcategory.outcome, subcategory.implementation_guidance),
                tags=["nist", "csf", "subcategory"],
                quality_score=0.9,  # Very high quality for NIST data
                search_weight=1.0
            )

            self.db.add(search_item)
            count += 1

        return count

    async def _index_iso_27001_data(self) -> int:
        """Index ISO 27001 framework data."""
        count = 0

        # Index ISO 27001 controls
        iso_controls = self.db.query(ISO27001Control).filter(
            ISO27001Control.deleted_at.is_(None)
        ).all()

        for control in iso_controls:
            search_item = SearchIndex(
                framework="ISO_27001",
                element_type="control",
                element_id=control.control_id,
                internal_id=control.id,
                title=control.name,
                description=control.description,
                content=f"{control.name} {control.description} {control.implementation_guidance or ''}",
                category=control.domain.name if control.domain else None,
                keywords=self._extract_keywords(control.name, control.description),
                tags=["iso", "27001", "control", "isms"],
                quality_score=0.85,  # High quality for ISO standard
                search_weight=1.0
            )

            self.db.add(search_item)
            count += 1

        return count

    async def _index_cis_controls_data(self) -> int:
        """Index CIS Controls framework data."""
        count = 0

        # Index CIS safeguards
        cis_safeguards = self.db.query(CISSafeguard).filter(
            CISSafeguard.deleted_at.is_(None)
        ).all()

        for safeguard in cis_safeguards:
            # Determine implementation groups
            ig_tags = []
            if safeguard.implementation_group_1:
                ig_tags.append("ig1")
            if safeguard.implementation_group_2:
                ig_tags.append("ig2")
            if safeguard.implementation_group_3:
                ig_tags.append("ig3")

            search_item = SearchIndex(
                framework="CIS_CONTROLS",
                element_type="safeguard",
                element_id=safeguard.safeguard_id,
                internal_id=safeguard.id,
                title=safeguard.title,
                description=safeguard.description,
                content=f"{safeguard.title} {safeguard.description} {safeguard.implementation_guidance or ''}",
                category=safeguard.control.title if safeguard.control else None,
                keywords=self._extract_keywords(safeguard.title, safeguard.description),
                tags=["cis", "controls", "safeguard"] + ig_tags,
                quality_score=0.8,  # High quality for CIS data
                search_weight=1.0
            )

            self.db.add(search_item)
            count += 1

        return count

    def _extract_keywords(self, title: str, description: str) -> List[str]:
        """Extract keywords from title and description."""
        import re

        text = f"{title or ''} {description or ''}".lower()

        # Remove common stop words and extract meaningful terms
        stop_words = {
            'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for',
            'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have',
            'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
            'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'
        }

        # Extract words (3+ characters)
        words = re.findall(r'\b[a-zA-Z]{3,}\b', text)
        keywords = [word for word in words if word not in stop_words]

        # Remove duplicates and limit
        return list(set(keywords))[:20]
