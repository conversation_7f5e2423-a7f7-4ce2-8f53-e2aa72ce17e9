"""
Framework Mapping Service.

This module provides services for managing cross-framework mappings between
different cybersecurity frameworks (ISF, NIST CSF, MITRE ATT&CK, etc.).
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, func
from sqlalchemy.exc import IntegrityError

from api.models.framework_mapping import (
    FrameworkMapping, MappingSet, MappingValidation, 
    MappingTemplate, MappingAudit, MappingType, MappingConfidence
)
from api.schemas.framework_mapping import (
    FrameworkMappingCreate, FrameworkMappingUpdate,
    MappingSetCreate, MappingSetUpdate,
    MappingValidationCreate, MappingSearchRequest
)

logger = logging.getLogger(__name__)


class FrameworkMappingService:
    """Service for managing framework mappings."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_mapping(self, mapping_data: FrameworkMappingCreate, created_by: str) -> FrameworkMapping:
        """Create a new framework mapping."""
        try:
            # Check for existing mapping
            existing = self.db.query(FrameworkMapping).filter(
                and_(
                    FrameworkMapping.source_framework == mapping_data.source_framework,
                    FrameworkMapping.source_control_id == mapping_data.source_control_id,
                    FrameworkMapping.target_framework == mapping_data.target_framework,
                    FrameworkMapping.target_control_id == mapping_data.target_control_id,
                    FrameworkMapping.deleted_at.is_(None)
                )
            ).first()
            
            if existing:
                raise ValueError("Mapping already exists between these controls")
            
            # Create new mapping
            mapping = FrameworkMapping(
                source_framework=mapping_data.source_framework,
                source_control_id=mapping_data.source_control_id,
                source_version=mapping_data.source_version,
                target_framework=mapping_data.target_framework,
                target_control_id=mapping_data.target_control_id,
                target_version=mapping_data.target_version,
                mapping_type=mapping_data.mapping_type,
                confidence_level=mapping_data.confidence_level,
                confidence_score=mapping_data.confidence_score,
                effectiveness_score=mapping_data.effectiveness_score,
                coverage_percentage=mapping_data.coverage_percentage,
                description=mapping_data.description,
                rationale=mapping_data.rationale,
                notes=mapping_data.notes,
                mapping_set_id=mapping_data.mapping_set_id,
                mapping_data=mapping_data.mapping_data,
                created_by=created_by,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            self.db.add(mapping)
            self.db.flush()
            
            # Create audit entry
            self._create_audit_entry(
                mapping.id, "created", None, None, 
                "Mapping created", created_by
            )
            
            # Update mapping set statistics if applicable
            if mapping_data.mapping_set_id:
                self._update_mapping_set_statistics(mapping_data.mapping_set_id)
            
            self.db.commit()
            
            logger.info(f"Created mapping {mapping.id}: {mapping.source_framework}.{mapping.source_control_id} -> {mapping.target_framework}.{mapping.target_control_id}")
            return mapping
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create mapping: {str(e)}")
            raise
    
    def update_mapping(self, mapping_id: int, mapping_data: FrameworkMappingUpdate, updated_by: str) -> FrameworkMapping:
        """Update an existing framework mapping."""
        try:
            mapping = self.db.query(FrameworkMapping).filter(
                FrameworkMapping.id == mapping_id,
                FrameworkMapping.deleted_at.is_(None)
            ).first()
            
            if not mapping:
                raise ValueError("Mapping not found")
            
            # Track changes for audit
            changes = []
            
            # Update fields
            for field, value in mapping_data.dict(exclude_unset=True).items():
                if hasattr(mapping, field):
                    old_value = getattr(mapping, field)
                    if old_value != value:
                        changes.append((field, old_value, value))
                        setattr(mapping, field, value)
            
            mapping.updated_at = datetime.utcnow()
            
            # Create audit entries for changes
            for field, old_value, new_value in changes:
                self._create_audit_entry(
                    mapping.id, "updated", field, 
                    str(old_value), str(new_value), updated_by
                )
            
            # Update mapping set statistics if applicable
            if mapping.mapping_set_id:
                self._update_mapping_set_statistics(mapping.mapping_set_id)
            
            self.db.commit()
            
            logger.info(f"Updated mapping {mapping.id} with {len(changes)} changes")
            return mapping
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to update mapping {mapping_id}: {str(e)}")
            raise
    
    def delete_mapping(self, mapping_id: int, deleted_by: str) -> bool:
        """Soft delete a framework mapping."""
        try:
            mapping = self.db.query(FrameworkMapping).filter(
                FrameworkMapping.id == mapping_id,
                FrameworkMapping.deleted_at.is_(None)
            ).first()
            
            if not mapping:
                raise ValueError("Mapping not found")
            
            mapping.deleted_at = datetime.utcnow()
            
            # Create audit entry
            self._create_audit_entry(
                mapping.id, "deleted", None, None,
                "Mapping soft deleted", deleted_by
            )
            
            # Update mapping set statistics if applicable
            if mapping.mapping_set_id:
                self._update_mapping_set_statistics(mapping.mapping_set_id)
            
            self.db.commit()
            
            logger.info(f"Deleted mapping {mapping.id}")
            return True
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to delete mapping {mapping_id}: {str(e)}")
            raise
    
    def search_mappings(self, search_request: MappingSearchRequest, page: int = 1, page_size: int = 10) -> Tuple[List[FrameworkMapping], int]:
        """Search framework mappings with filters."""
        query = self.db.query(FrameworkMapping).filter(
            FrameworkMapping.deleted_at.is_(None)
        )
        
        # Apply filters
        if search_request.source_framework:
            query = query.filter(FrameworkMapping.source_framework == search_request.source_framework)
        
        if search_request.target_framework:
            query = query.filter(FrameworkMapping.target_framework == search_request.target_framework)
        
        if search_request.mapping_type:
            query = query.filter(FrameworkMapping.mapping_type == search_request.mapping_type)
        
        if search_request.confidence_level:
            query = query.filter(FrameworkMapping.confidence_level == search_request.confidence_level)
        
        if search_request.is_validated is not None:
            query = query.filter(FrameworkMapping.is_validated == search_request.is_validated)
        
        if search_request.min_confidence_score is not None:
            query = query.filter(FrameworkMapping.confidence_score >= search_request.min_confidence_score)
        
        if search_request.max_confidence_score is not None:
            query = query.filter(FrameworkMapping.confidence_score <= search_request.max_confidence_score)
        
        if search_request.created_after:
            query = query.filter(FrameworkMapping.created_at >= search_request.created_after)
        
        if search_request.created_before:
            query = query.filter(FrameworkMapping.created_at <= search_request.created_before)
        
        # Text search
        if search_request.query:
            search_term = f"%{search_request.query}%"
            query = query.filter(
                or_(
                    FrameworkMapping.source_control_id.ilike(search_term),
                    FrameworkMapping.target_control_id.ilike(search_term),
                    FrameworkMapping.description.ilike(search_term),
                    FrameworkMapping.rationale.ilike(search_term)
                )
            )
        
        # Get total count
        total = query.count()
        
        # Apply pagination
        offset = (page - 1) * page_size
        mappings = query.offset(offset).limit(page_size).all()
        
        return mappings, total
    
    def validate_mapping(self, validation_data: MappingValidationCreate, reviewer_id: str) -> MappingValidation:
        """Validate a framework mapping."""
        try:
            # Check if mapping exists
            mapping = self.db.query(FrameworkMapping).filter(
                FrameworkMapping.id == validation_data.mapping_id,
                FrameworkMapping.deleted_at.is_(None)
            ).first()
            
            if not mapping:
                raise ValueError("Mapping not found")
            
            # Create validation record
            validation = MappingValidation(
                mapping_id=validation_data.mapping_id,
                validation_status=validation_data.validation_status,
                reviewer_id=reviewer_id,
                review_date=datetime.utcnow(),
                comments=validation_data.comments,
                suggested_changes=validation_data.suggested_changes,
                confidence_assessment=validation_data.confidence_assessment,
                accuracy_score=validation_data.accuracy_score,
                completeness_score=validation_data.completeness_score,
                relevance_score=validation_data.relevance_score,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            self.db.add(validation)
            
            # Update mapping validation status if approved
            if validation_data.validation_status == "approved":
                mapping.is_validated = True
                mapping.validated_by = reviewer_id
                mapping.validated_at = datetime.utcnow()
                
                # Create audit entry
                self._create_audit_entry(
                    mapping.id, "validated", "is_validated",
                    "False", "True", reviewer_id
                )
            
            # Update mapping set statistics if applicable
            if mapping.mapping_set_id:
                self._update_mapping_set_statistics(mapping.mapping_set_id)
            
            self.db.commit()
            
            logger.info(f"Validated mapping {mapping.id} with status {validation_data.validation_status}")
            return validation
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to validate mapping: {str(e)}")
            raise
    
    def create_mapping_set(self, set_data: MappingSetCreate, created_by: str) -> MappingSet:
        """Create a new mapping set."""
        try:
            mapping_set = MappingSet(
                name=set_data.name,
                description=set_data.description,
                source_framework=set_data.source_framework,
                target_framework=set_data.target_framework,
                version=set_data.version,
                is_official=set_data.is_official,
                created_by=created_by,
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            self.db.add(mapping_set)
            self.db.commit()
            
            logger.info(f"Created mapping set {mapping_set.id}: {mapping_set.name}")
            return mapping_set
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"Failed to create mapping set: {str(e)}")
            raise
    
    def get_mapping_analytics(self) -> Dict[str, Any]:
        """Get mapping analytics and statistics."""
        try:
            # Total mappings
            total_mappings = self.db.query(FrameworkMapping).filter(
                FrameworkMapping.deleted_at.is_(None)
            ).count()
            
            # Mappings by framework
            framework_stats = self.db.query(
                FrameworkMapping.source_framework,
                FrameworkMapping.target_framework,
                func.count(FrameworkMapping.id).label('count')
            ).filter(
                FrameworkMapping.deleted_at.is_(None)
            ).group_by(
                FrameworkMapping.source_framework,
                FrameworkMapping.target_framework
            ).all()
            
            # Mappings by type
            type_stats = self.db.query(
                FrameworkMapping.mapping_type,
                func.count(FrameworkMapping.id).label('count')
            ).filter(
                FrameworkMapping.deleted_at.is_(None)
            ).group_by(FrameworkMapping.mapping_type).all()
            
            # Validation statistics
            validation_stats = self.db.query(
                FrameworkMapping.is_validated,
                func.count(FrameworkMapping.id).label('count')
            ).filter(
                FrameworkMapping.deleted_at.is_(None)
            ).group_by(FrameworkMapping.is_validated).all()
            
            # Average confidence score
            avg_confidence = self.db.query(
                func.avg(FrameworkMapping.confidence_score)
            ).filter(
                FrameworkMapping.deleted_at.is_(None)
            ).scalar() or 0.0
            
            return {
                "total_mappings": total_mappings,
                "mappings_by_framework": {f"{stat[0]}->{stat[1]}": stat[2] for stat in framework_stats},
                "mappings_by_type": {stat[0]: stat[1] for stat in type_stats},
                "validation_statistics": {
                    "validated" if stat[0] else "unvalidated": stat[1] 
                    for stat in validation_stats
                },
                "average_confidence_score": float(avg_confidence),
                "last_updated": datetime.utcnow().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to get mapping analytics: {str(e)}")
            raise
    
    def _create_audit_entry(self, mapping_id: int, action: str, field_name: Optional[str], 
                           old_value: Optional[str], new_value: Optional[str], changed_by: str):
        """Create an audit trail entry."""
        audit = MappingAudit(
            mapping_id=mapping_id,
            action=action,
            field_name=field_name,
            old_value=old_value,
            new_value=new_value,
            changed_by=changed_by,
            changed_at=datetime.utcnow()
        )
        self.db.add(audit)
    
    def _update_mapping_set_statistics(self, mapping_set_id: int):
        """Update statistics for a mapping set."""
        mapping_set = self.db.query(MappingSet).filter(
            MappingSet.id == mapping_set_id
        ).first()
        
        if mapping_set:
            # Count total mappings
            total = self.db.query(FrameworkMapping).filter(
                FrameworkMapping.mapping_set_id == mapping_set_id,
                FrameworkMapping.deleted_at.is_(None)
            ).count()
            
            # Count validated mappings
            validated = self.db.query(FrameworkMapping).filter(
                FrameworkMapping.mapping_set_id == mapping_set_id,
                FrameworkMapping.is_validated == True,
                FrameworkMapping.deleted_at.is_(None)
            ).count()
            
            # Calculate average confidence
            avg_confidence = self.db.query(
                func.avg(FrameworkMapping.confidence_score)
            ).filter(
                FrameworkMapping.mapping_set_id == mapping_set_id,
                FrameworkMapping.deleted_at.is_(None)
            ).scalar()
            
            # Update statistics
            mapping_set.total_mappings = total
            mapping_set.validated_mappings = validated
            mapping_set.average_confidence = float(avg_confidence) if avg_confidence else None
            mapping_set.updated_at = datetime.utcnow()
