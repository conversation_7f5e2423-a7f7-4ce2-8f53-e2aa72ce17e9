"""
Progress Tracking Service.

This module provides progress tracking capabilities for long-running operations
such as framework imports, exports, and analysis tasks. It supports real-time
progress updates, status monitoring, and completion notifications.

Features:
- Real-time progress tracking with percentage completion
- Stage-based progress reporting
- Error and warning collection during operations
- Estimated completion time calculation
- Thread-safe operations for concurrent tasks
- Persistent progress storage for recovery
"""

import time
import threading
import logging
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import uuid

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class TaskStage(Enum):
    """Task execution stages."""
    INITIALIZING = "initializing"
    VALIDATING = "validating"
    PROCESSING = "processing"
    FINALIZING = "finalizing"
    CLEANUP = "cleanup"


@dataclass
class ProgressUpdate:
    """Individual progress update."""
    timestamp: datetime
    stage: TaskStage
    progress_percentage: float
    message: str
    details: Optional[Dict[str, Any]] = None


@dataclass
class TaskProgress:
    """Complete task progress information."""
    task_id: str
    status: TaskStatus
    current_stage: TaskStage
    progress_percentage: float
    start_time: datetime
    end_time: Optional[datetime] = None
    estimated_completion: Optional[datetime] = None
    current_message: str = ""
    processed_items: int = 0
    total_items: int = 0
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    updates: List[ProgressUpdate] = field(default_factory=list)
    metadata: Dict[str, Any] = field(default_factory=dict)
    
    @property
    def elapsed_time(self) -> timedelta:
        """Calculate elapsed time."""
        end = self.end_time or datetime.utcnow()
        return end - self.start_time
    
    @property
    def is_complete(self) -> bool:
        """Check if task is complete."""
        return self.status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses."""
        return {
            "task_id": self.task_id,
            "status": self.status.value,
            "current_stage": self.current_stage.value,
            "progress_percentage": self.progress_percentage,
            "start_time": self.start_time.isoformat(),
            "end_time": self.end_time.isoformat() if self.end_time else None,
            "estimated_completion": self.estimated_completion.isoformat() if self.estimated_completion else None,
            "current_message": self.current_message,
            "processed_items": self.processed_items,
            "total_items": self.total_items,
            "elapsed_time": str(self.elapsed_time),
            "errors": self.errors,
            "warnings": self.warnings,
            "metadata": self.metadata
        }


class ProgressTracker:
    """
    Service for tracking progress of long-running operations.
    
    Provides thread-safe progress tracking with real-time updates,
    stage management, and completion estimation.
    """
    
    def __init__(self):
        """Initialize progress tracker."""
        self._tasks: Dict[str, TaskProgress] = {}
        self._lock = threading.RLock()
        self._callbacks: Dict[str, List[Callable]] = {}
        
    def create_task(
        self,
        task_id: Optional[str] = None,
        total_items: int = 0,
        metadata: Optional[Dict[str, Any]] = None
    ) -> str:
        """
        Create a new task for progress tracking.
        
        Args:
            task_id: Optional task identifier (generated if not provided)
            total_items: Total number of items to process
            metadata: Additional task metadata
            
        Returns:
            Task identifier
        """
        if task_id is None:
            task_id = str(uuid.uuid4())
        
        with self._lock:
            if task_id in self._tasks:
                raise ValueError(f"Task {task_id} already exists")
            
            self._tasks[task_id] = TaskProgress(
                task_id=task_id,
                status=TaskStatus.PENDING,
                current_stage=TaskStage.INITIALIZING,
                progress_percentage=0.0,
                start_time=datetime.utcnow(),
                total_items=total_items,
                metadata=metadata or {}
            )
            
            logger.info(f"Created task {task_id} with {total_items} total items")
            
        return task_id
    
    def start_task(self, task_id: str, message: str = "Task started") -> None:
        """
        Start task execution.
        
        Args:
            task_id: Task identifier
            message: Start message
        """
        with self._lock:
            if task_id not in self._tasks:
                raise ValueError(f"Task {task_id} not found")
            
            task = self._tasks[task_id]
            task.status = TaskStatus.RUNNING
            task.current_message = message
            
            self._add_update(task, TaskStage.INITIALIZING, 0.0, message)
            self._notify_callbacks(task_id, task)
            
            logger.info(f"Started task {task_id}: {message}")
    
    def update_progress(
        self,
        task_id: str,
        stage: TaskStage,
        progress_percentage: float,
        message: str,
        processed_items: Optional[int] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Update task progress.
        
        Args:
            task_id: Task identifier
            stage: Current execution stage
            progress_percentage: Progress percentage (0.0 to 100.0)
            message: Progress message
            processed_items: Number of processed items
            details: Additional progress details
        """
        with self._lock:
            if task_id not in self._tasks:
                raise ValueError(f"Task {task_id} not found")
            
            task = self._tasks[task_id]
            
            # Update task progress
            task.current_stage = stage
            task.progress_percentage = min(max(progress_percentage, 0.0), 100.0)
            task.current_message = message
            
            if processed_items is not None:
                task.processed_items = processed_items
            
            # Calculate estimated completion
            if task.progress_percentage > 0 and task.total_items > 0:
                elapsed = task.elapsed_time.total_seconds()
                estimated_total = elapsed / (task.progress_percentage / 100.0)
                remaining = estimated_total - elapsed
                task.estimated_completion = datetime.utcnow() + timedelta(seconds=remaining)
            
            self._add_update(task, stage, progress_percentage, message, details)
            self._notify_callbacks(task_id, task)
            
            logger.debug(f"Updated task {task_id}: {progress_percentage:.1f}% - {message}")
    
    def complete_task(
        self,
        task_id: str,
        message: str = "Task completed successfully",
        metadata: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Mark task as completed.
        
        Args:
            task_id: Task identifier
            message: Completion message
            metadata: Additional completion metadata
        """
        with self._lock:
            if task_id not in self._tasks:
                raise ValueError(f"Task {task_id} not found")
            
            task = self._tasks[task_id]
            task.status = TaskStatus.COMPLETED
            task.progress_percentage = 100.0
            task.current_message = message
            task.end_time = datetime.utcnow()
            
            if metadata:
                task.metadata.update(metadata)
            
            self._add_update(task, TaskStage.FINALIZING, 100.0, message)
            self._notify_callbacks(task_id, task)
            
            logger.info(f"Completed task {task_id}: {message}")
    
    def fail_task(
        self,
        task_id: str,
        error_message: str,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """
        Mark task as failed.
        
        Args:
            task_id: Task identifier
            error_message: Error message
            details: Additional error details
        """
        with self._lock:
            if task_id not in self._tasks:
                raise ValueError(f"Task {task_id} not found")
            
            task = self._tasks[task_id]
            task.status = TaskStatus.FAILED
            task.current_message = error_message
            task.end_time = datetime.utcnow()
            task.errors.append(error_message)
            
            self._add_update(task, task.current_stage, task.progress_percentage, error_message, details)
            self._notify_callbacks(task_id, task)
            
            logger.error(f"Failed task {task_id}: {error_message}")
    
    def add_error(self, task_id: str, error_message: str) -> None:
        """
        Add error to task without failing it.
        
        Args:
            task_id: Task identifier
            error_message: Error message
        """
        with self._lock:
            if task_id not in self._tasks:
                raise ValueError(f"Task {task_id} not found")
            
            task = self._tasks[task_id]
            task.errors.append(error_message)
            
            logger.warning(f"Added error to task {task_id}: {error_message}")
    
    def add_warning(self, task_id: str, warning_message: str) -> None:
        """
        Add warning to task.
        
        Args:
            task_id: Task identifier
            warning_message: Warning message
        """
        with self._lock:
            if task_id not in self._tasks:
                raise ValueError(f"Task {task_id} not found")
            
            task = self._tasks[task_id]
            task.warnings.append(warning_message)
            
            logger.info(f"Added warning to task {task_id}: {warning_message}")
    
    def get_progress(self, task_id: str) -> Dict[str, Any]:
        """
        Get current progress for a task.
        
        Args:
            task_id: Task identifier
            
        Returns:
            Progress information dictionary
        """
        with self._lock:
            if task_id not in self._tasks:
                raise ValueError(f"Task {task_id} not found")
            
            return self._tasks[task_id].to_dict()
    
    def get_all_tasks(self) -> Dict[str, Dict[str, Any]]:
        """
        Get progress for all tasks.
        
        Returns:
            Dictionary of all task progress information
        """
        with self._lock:
            return {task_id: task.to_dict() for task_id, task in self._tasks.items()}
    
    def cancel_task(self, task_id: str, message: str = "Task cancelled") -> None:
        """
        Cancel a running task.
        
        Args:
            task_id: Task identifier
            message: Cancellation message
        """
        with self._lock:
            if task_id not in self._tasks:
                raise ValueError(f"Task {task_id} not found")
            
            task = self._tasks[task_id]
            if task.is_complete:
                raise ValueError(f"Task {task_id} is already complete")
            
            task.status = TaskStatus.CANCELLED
            task.current_message = message
            task.end_time = datetime.utcnow()
            
            self._add_update(task, task.current_stage, task.progress_percentage, message)
            self._notify_callbacks(task_id, task)
            
            logger.info(f"Cancelled task {task_id}: {message}")
    
    def cleanup_completed_tasks(self, max_age_hours: int = 24) -> int:
        """
        Clean up completed tasks older than specified age.
        
        Args:
            max_age_hours: Maximum age in hours for completed tasks
            
        Returns:
            Number of tasks cleaned up
        """
        cutoff_time = datetime.utcnow() - timedelta(hours=max_age_hours)
        cleaned_count = 0
        
        with self._lock:
            tasks_to_remove = []
            
            for task_id, task in self._tasks.items():
                if task.is_complete and task.end_time and task.end_time < cutoff_time:
                    tasks_to_remove.append(task_id)
            
            for task_id in tasks_to_remove:
                del self._tasks[task_id]
                if task_id in self._callbacks:
                    del self._callbacks[task_id]
                cleaned_count += 1
            
            logger.info(f"Cleaned up {cleaned_count} completed tasks")
            
        return cleaned_count
    
    def register_callback(self, task_id: str, callback: Callable[[str, TaskProgress], None]) -> None:
        """
        Register callback for task progress updates.
        
        Args:
            task_id: Task identifier
            callback: Callback function
        """
        with self._lock:
            if task_id not in self._callbacks:
                self._callbacks[task_id] = []
            self._callbacks[task_id].append(callback)
    
    def _add_update(
        self,
        task: TaskProgress,
        stage: TaskStage,
        progress: float,
        message: str,
        details: Optional[Dict[str, Any]] = None
    ) -> None:
        """Add progress update to task."""
        update = ProgressUpdate(
            timestamp=datetime.utcnow(),
            stage=stage,
            progress_percentage=progress,
            message=message,
            details=details
        )
        task.updates.append(update)
        
        # Keep only last 100 updates to prevent memory issues
        if len(task.updates) > 100:
            task.updates = task.updates[-100:]
    
    def _notify_callbacks(self, task_id: str, task: TaskProgress) -> None:
        """Notify registered callbacks of progress update."""
        if task_id in self._callbacks:
            for callback in self._callbacks[task_id]:
                try:
                    callback(task_id, task)
                except Exception as e:
                    logger.error(f"Error in progress callback for task {task_id}: {e}")


# Global progress tracker instance
progress_tracker = ProgressTracker()
