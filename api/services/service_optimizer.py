"""
Service Optimization Utility.

This module provides automated optimization capabilities for framework services,
including query optimization, caching strategies, and performance tuning.
"""

import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple, Callable
from dataclasses import dataclass
from collections import defaultdict
import statistics

from sqlalchemy.orm import Session
from sqlalchemy import text, inspect

from api.services.performance_monitor import PerformanceMonitor, ServicePerformanceReport

logger = logging.getLogger(__name__)


@dataclass
class OptimizationRecommendation:
    """Optimization recommendation."""
    service: str
    category: str  # "query", "cache", "batch", "memory"
    priority: str  # "high", "medium", "low"
    description: str
    implementation: str
    expected_improvement: str
    effort_level: str  # "low", "medium", "high"


@dataclass
class OptimizationResult:
    """Result of applying an optimization."""
    recommendation: OptimizationRecommendation
    applied: bool
    before_metrics: Dict[str, float]
    after_metrics: Dict[str, float]
    improvement_percentage: float
    notes: str


class QueryOptimizer:
    """Optimizes database queries for better performance."""
    
    def __init__(self, db: Session):
        self.db = db
    
    def analyze_slow_queries(self, threshold_ms: float = 1000) -> List[Dict[str, Any]]:
        """Analyze slow queries from database statistics."""
        try:
            # PostgreSQL specific query for slow queries
            slow_queries_sql = """
            SELECT 
                query,
                calls,
                total_time,
                mean_time,
                rows,
                100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
            FROM pg_stat_statements 
            WHERE mean_time > :threshold
            ORDER BY mean_time DESC
            LIMIT 20
            """
            
            result = self.db.execute(text(slow_queries_sql), {"threshold": threshold_ms})
            return [dict(row._mapping) for row in result]
            
        except Exception as e:
            logger.warning(f"Could not analyze slow queries: {e}")
            return []
    
    def suggest_index_optimizations(self) -> List[OptimizationRecommendation]:
        """Suggest database index optimizations."""
        recommendations = []
        
        try:
            # Check for missing indexes on foreign keys
            missing_fk_indexes_sql = """
            SELECT 
                schemaname,
                tablename,
                attname,
                n_distinct,
                correlation
            FROM pg_stats 
            WHERE schemaname = 'public' 
            AND attname LIKE '%_id'
            AND n_distinct > 100
            """
            
            result = self.db.execute(text(missing_fk_indexes_sql))
            for row in result:
                recommendations.append(OptimizationRecommendation(
                    service="database",
                    category="query",
                    priority="medium",
                    description=f"Consider adding index on {row.tablename}.{row.attname}",
                    implementation=f"CREATE INDEX idx_{row.tablename}_{row.attname} ON {row.tablename}({row.attname})",
                    expected_improvement="20-50% faster queries on this column",
                    effort_level="low"
                ))
            
        except Exception as e:
            logger.warning(f"Could not analyze index optimizations: {e}")
        
        return recommendations
    
    def optimize_query_plan(self, query: str) -> Dict[str, Any]:
        """Analyze and suggest optimizations for a specific query."""
        try:
            # Get query execution plan
            explain_sql = f"EXPLAIN (ANALYZE, BUFFERS, FORMAT JSON) {query}"
            result = self.db.execute(text(explain_sql))
            plan = result.scalar()
            
            # Analyze plan for optimization opportunities
            optimizations = []
            
            # Check for sequential scans
            if "Seq Scan" in str(plan):
                optimizations.append("Consider adding indexes to avoid sequential scans")
            
            # Check for high cost operations
            if "cost=" in str(plan):
                # Extract cost information and suggest optimizations
                optimizations.append("High-cost operations detected - consider query restructuring")
            
            return {
                "query": query,
                "execution_plan": plan,
                "optimizations": optimizations
            }
            
        except Exception as e:
            logger.error(f"Error analyzing query plan: {e}")
            return {"error": str(e)}


class CacheOptimizer:
    """Optimizes caching strategies for services."""
    
    def __init__(self):
        self.cache_hit_rates = defaultdict(list)
        self.cache_access_patterns = defaultdict(list)
    
    def analyze_cache_performance(self, service_name: str, cache_stats: Dict[str, Any]) -> List[OptimizationRecommendation]:
        """Analyze cache performance and suggest optimizations."""
        recommendations = []
        
        hit_rate = cache_stats.get("hit_rate", 0)
        miss_rate = 1 - hit_rate
        
        # Low hit rate recommendations
        if hit_rate < 0.7:  # Less than 70% hit rate
            recommendations.append(OptimizationRecommendation(
                service=service_name,
                category="cache",
                priority="high",
                description=f"Low cache hit rate ({hit_rate:.1%}) - consider increasing cache size or TTL",
                implementation="Increase cache_max_size and cache_ttl in service configuration",
                expected_improvement="30-50% reduction in database queries",
                effort_level="low"
            ))
        
        # High miss rate with frequent access
        cache_size = cache_stats.get("size", 0)
        max_size = cache_stats.get("max_size", 1000)
        
        if cache_size >= max_size * 0.9 and miss_rate > 0.3:
            recommendations.append(OptimizationRecommendation(
                service=service_name,
                category="cache",
                priority="medium",
                description="Cache is near capacity with high miss rate - consider cache size increase",
                implementation="Increase cache_max_size configuration parameter",
                expected_improvement="15-25% improvement in response times",
                effort_level="low"
            ))
        
        return recommendations
    
    def suggest_caching_strategy(self, access_patterns: List[Dict[str, Any]]) -> Dict[str, Any]:
        """Suggest optimal caching strategy based on access patterns."""
        # Analyze access frequency
        access_frequency = defaultdict(int)
        for pattern in access_patterns:
            key = pattern.get("key", "")
            access_frequency[key] += 1
        
        # Identify hot keys
        total_accesses = sum(access_frequency.values())
        hot_keys = [
            key for key, count in access_frequency.items()
            if count > total_accesses * 0.1  # More than 10% of total accesses
        ]
        
        # Suggest strategy
        if len(hot_keys) > 0:
            strategy = {
                "type": "LRU with hot key prioritization",
                "hot_keys": hot_keys,
                "recommended_ttl": 3600,  # 1 hour for hot keys
                "recommended_size": max(1000, len(access_frequency) * 2)
            }
        else:
            strategy = {
                "type": "Standard LRU",
                "recommended_ttl": 1800,  # 30 minutes
                "recommended_size": max(500, len(access_frequency))
            }
        
        return strategy


class BatchOptimizer:
    """Optimizes batch processing operations."""
    
    def analyze_batch_performance(
        self,
        service_name: str,
        batch_metrics: Dict[str, Any]
    ) -> List[OptimizationRecommendation]:
        """Analyze batch processing performance."""
        recommendations = []
        
        batch_size = batch_metrics.get("batch_size", 100)
        processing_time = batch_metrics.get("avg_processing_time", 0)
        memory_usage = batch_metrics.get("memory_usage", 0)
        
        # Batch size optimization
        if processing_time > 5.0 and batch_size > 500:
            recommendations.append(OptimizationRecommendation(
                service=service_name,
                category="batch",
                priority="medium",
                description="Large batch size causing slow processing - consider reducing batch size",
                implementation="Reduce batch_size configuration parameter",
                expected_improvement="20-30% faster processing times",
                effort_level="low"
            ))
        
        elif processing_time < 0.5 and batch_size < 100:
            recommendations.append(OptimizationRecommendation(
                service=service_name,
                category="batch",
                priority="low",
                description="Small batch size may be inefficient - consider increasing batch size",
                implementation="Increase batch_size configuration parameter",
                expected_improvement="10-20% better throughput",
                effort_level="low"
            ))
        
        # Memory usage optimization
        if memory_usage > 1000:  # 1GB
            recommendations.append(OptimizationRecommendation(
                service=service_name,
                category="memory",
                priority="high",
                description="High memory usage in batch processing - consider streaming or smaller batches",
                implementation="Implement streaming processing or reduce batch size",
                expected_improvement="50-70% reduction in memory usage",
                effort_level="medium"
            ))
        
        return recommendations
    
    def calculate_optimal_batch_size(
        self,
        item_size_bytes: int,
        available_memory_mb: int,
        target_processing_time_seconds: float
    ) -> int:
        """Calculate optimal batch size based on constraints."""
        # Memory constraint
        memory_bytes = available_memory_mb * 1024 * 1024
        max_items_by_memory = memory_bytes // (item_size_bytes * 2)  # 2x safety factor
        
        # Time constraint (rough estimation)
        # Assume linear relationship between batch size and processing time
        estimated_time_per_item = 0.01  # 10ms per item (rough estimate)
        max_items_by_time = int(target_processing_time_seconds / estimated_time_per_item)
        
        # Take the minimum of constraints
        optimal_batch_size = min(max_items_by_memory, max_items_by_time)
        
        # Ensure reasonable bounds
        optimal_batch_size = max(10, min(optimal_batch_size, 10000))
        
        return optimal_batch_size


class ServiceOptimizer:
    """Main service optimization coordinator."""
    
    def __init__(self, db: Session, performance_monitor: PerformanceMonitor):
        self.db = db
        self.performance_monitor = performance_monitor
        self.query_optimizer = QueryOptimizer(db)
        self.cache_optimizer = CacheOptimizer()
        self.batch_optimizer = BatchOptimizer()
    
    def analyze_service_performance(self, service_name: str) -> List[OptimizationRecommendation]:
        """Comprehensive performance analysis for a service."""
        recommendations = []
        
        # Get performance report
        report = self.performance_monitor.get_service_report(service_name)
        
        # Query optimizations
        if report.database_metrics.get("average_queries_per_operation", 0) > 5:
            recommendations.extend(self.query_optimizer.suggest_index_optimizations())
        
        # Cache optimizations
        cache_stats = {
            "hit_rate": 0.6,  # Mock data - would come from actual cache metrics
            "size": 800,
            "max_size": 1000
        }
        recommendations.extend(self.cache_optimizer.analyze_cache_performance(service_name, cache_stats))
        
        # Batch processing optimizations
        if report.average_duration > 2.0:
            batch_metrics = {
                "batch_size": 1000,
                "avg_processing_time": report.average_duration,
                "memory_usage": report.memory_usage.get("peak", 0)
            }
            recommendations.extend(self.batch_optimizer.analyze_batch_performance(service_name, batch_metrics))
        
        # General performance recommendations
        if report.error_rate > 0.05:
            recommendations.append(OptimizationRecommendation(
                service=service_name,
                category="reliability",
                priority="high",
                description=f"High error rate ({report.error_rate:.1%}) - review error handling",
                implementation="Add comprehensive error handling and validation",
                expected_improvement="Significant reduction in error rate",
                effort_level="medium"
            ))
        
        if report.throughput < 1.0:
            recommendations.append(OptimizationRecommendation(
                service=service_name,
                category="performance",
                priority="medium",
                description="Low throughput - consider parallel processing",
                implementation="Implement parallel processing with ThreadPoolExecutor",
                expected_improvement="2-5x improvement in throughput",
                effort_level="medium"
            ))
        
        return recommendations
    
    def apply_optimization(self, recommendation: OptimizationRecommendation) -> OptimizationResult:
        """Apply an optimization recommendation."""
        # Get before metrics
        before_report = self.performance_monitor.get_service_report(recommendation.service)
        before_metrics = {
            "average_duration": before_report.average_duration,
            "error_rate": before_report.error_rate,
            "throughput": before_report.throughput
        }
        
        # Apply optimization (this would be service-specific implementation)
        applied = self._apply_optimization_implementation(recommendation)
        
        # Wait for metrics to stabilize
        time.sleep(30)
        
        # Get after metrics
        after_report = self.performance_monitor.get_service_report(recommendation.service)
        after_metrics = {
            "average_duration": after_report.average_duration,
            "error_rate": after_report.error_rate,
            "throughput": after_report.throughput
        }
        
        # Calculate improvement
        improvement = 0
        if before_metrics["average_duration"] > 0:
            improvement = (
                (before_metrics["average_duration"] - after_metrics["average_duration"]) /
                before_metrics["average_duration"] * 100
            )
        
        return OptimizationResult(
            recommendation=recommendation,
            applied=applied,
            before_metrics=before_metrics,
            after_metrics=after_metrics,
            improvement_percentage=improvement,
            notes="Optimization applied successfully" if applied else "Failed to apply optimization"
        )
    
    def _apply_optimization_implementation(self, recommendation: OptimizationRecommendation) -> bool:
        """Apply the actual optimization (placeholder for service-specific implementations)."""
        try:
            if recommendation.category == "query" and "CREATE INDEX" in recommendation.implementation:
                # Apply database index
                self.db.execute(text(recommendation.implementation))
                self.db.commit()
                return True
            
            # Other optimizations would be applied here
            # This is a placeholder - actual implementation would be service-specific
            logger.info(f"Applied optimization: {recommendation.description}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to apply optimization: {e}")
            return False
    
    def get_optimization_summary(self) -> Dict[str, Any]:
        """Get summary of all optimization opportunities."""
        services = ["isf_import", "nist_csf_import", "export", "analytics"]
        
        all_recommendations = []
        for service in services:
            recommendations = self.analyze_service_performance(service)
            all_recommendations.extend(recommendations)
        
        # Group by priority
        by_priority = defaultdict(list)
        for rec in all_recommendations:
            by_priority[rec.priority].append(rec)
        
        return {
            "total_recommendations": len(all_recommendations),
            "by_priority": {
                "high": len(by_priority["high"]),
                "medium": len(by_priority["medium"]),
                "low": len(by_priority["low"])
            },
            "by_category": {
                category: len([r for r in all_recommendations if r.category == category])
                for category in ["query", "cache", "batch", "memory", "reliability", "performance"]
            },
            "recommendations": all_recommendations
        }
