"""
Base Service Classes for Framework Management.

This module provides base classes and common functionality for all framework services,
implementing performance optimizations, error handling, and maintainability improvements.
"""

import logging
import time
from abc import ABC, abstractmethod
from contextlib import contextmanager
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable, Union, TypeVar, Generic
from dataclasses import dataclass, field
from enum import Enum
import asyncio
from concurrent.futures import ThreadPoolExecutor, as_completed

from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import text

logger = logging.getLogger(__name__)

T = TypeVar('T')


class ServiceError(Exception):
    """Base exception for service errors."""
    
    def __init__(self, message: str, error_code: str = "SERVICE_ERROR", details: Dict[str, Any] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
        self.timestamp = datetime.now()


class ValidationError(ServiceError):
    """Exception for validation errors."""
    
    def __init__(self, message: str, field: str = None, value: Any = None):
        super().__init__(message, "VALIDATION_ERROR", {"field": field, "value": value})
        self.field = field
        self.value = value


class PerformanceError(ServiceError):
    """Exception for performance-related errors."""
    
    def __init__(self, message: str, operation: str = None, duration: float = None):
        super().__init__(message, "PERFORMANCE_ERROR", {"operation": operation, "duration": duration})
        self.operation = operation
        self.duration = duration


@dataclass
class ServiceMetrics:
    """Metrics for service operations."""
    operation_name: str
    start_time: datetime
    end_time: Optional[datetime] = None
    duration: Optional[float] = None
    success: bool = False
    error_count: int = 0
    records_processed: int = 0
    memory_usage: Optional[float] = None
    database_queries: int = 0
    cache_hits: int = 0
    cache_misses: int = 0


@dataclass
class BatchProcessingConfig:
    """Configuration for batch processing operations."""
    batch_size: int = 1000
    max_workers: int = 4
    timeout_seconds: int = 300
    retry_attempts: int = 3
    retry_delay: float = 1.0
    enable_parallel: bool = True
    commit_frequency: int = 100


class CacheManager:
    """Simple in-memory cache manager for service operations."""
    
    def __init__(self, max_size: int = 1000, ttl_seconds: int = 3600):
        self._cache: Dict[str, Dict[str, Any]] = {}
        self.max_size = max_size
        self.ttl_seconds = ttl_seconds
    
    def get(self, key: str) -> Optional[Any]:
        """Get value from cache."""
        if key in self._cache:
            entry = self._cache[key]
            if datetime.now().timestamp() - entry["timestamp"] < self.ttl_seconds:
                return entry["value"]
            else:
                del self._cache[key]
        return None
    
    def set(self, key: str, value: Any) -> None:
        """Set value in cache."""
        if len(self._cache) >= self.max_size:
            # Remove oldest entry
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k]["timestamp"])
            del self._cache[oldest_key]
        
        self._cache[key] = {
            "value": value,
            "timestamp": datetime.now().timestamp()
        }
    
    def clear(self) -> None:
        """Clear all cache entries."""
        self._cache.clear()
    
    def get_stats(self) -> Dict[str, Any]:
        """Get cache statistics."""
        return {
            "size": len(self._cache),
            "max_size": self.max_size,
            "ttl_seconds": self.ttl_seconds
        }


class BaseService(ABC):
    """Base class for all framework services."""
    
    def __init__(self, db: Session, config: Optional[Dict[str, Any]] = None):
        self.db = db
        self.config = config or {}
        self.metrics = ServiceMetrics(operation_name="unknown", start_time=datetime.now())
        self.cache = CacheManager(
            max_size=self.config.get("cache_max_size", 1000),
            ttl_seconds=self.config.get("cache_ttl", 3600)
        )
        self.batch_config = BatchProcessingConfig(**self.config.get("batch_processing", {}))
        self._query_count = 0
        self._cache_hits = 0
        self._cache_misses = 0
    
    @contextmanager
    def performance_tracking(self, operation_name: str):
        """Context manager for tracking operation performance."""
        start_time = datetime.now()
        self.metrics = ServiceMetrics(operation_name=operation_name, start_time=start_time)
        
        try:
            yield self.metrics
            self.metrics.success = True
        except Exception as e:
            self.metrics.error_count += 1
            logger.error(f"Operation {operation_name} failed: {str(e)}")
            raise
        finally:
            self.metrics.end_time = datetime.now()
            self.metrics.duration = (self.metrics.end_time - start_time).total_seconds()
            self.metrics.database_queries = self._query_count
            self.metrics.cache_hits = self._cache_hits
            self.metrics.cache_misses = self._cache_misses
            
            # Log performance metrics
            self._log_performance_metrics()
    
    def _log_performance_metrics(self):
        """Log performance metrics for monitoring."""
        logger.info(
            f"Operation: {self.metrics.operation_name}, "
            f"Duration: {self.metrics.duration:.3f}s, "
            f"Records: {self.metrics.records_processed}, "
            f"Queries: {self.metrics.database_queries}, "
            f"Cache: {self.metrics.cache_hits}/{self.metrics.cache_hits + self.metrics.cache_misses}"
        )
    
    @contextmanager
    def database_transaction(self, rollback_on_error: bool = True):
        """Context manager for database transactions with error handling."""
        try:
            yield self.db
            self.db.commit()
        except SQLAlchemyError as e:
            if rollback_on_error:
                self.db.rollback()
            logger.error(f"Database error: {str(e)}")
            raise ServiceError(f"Database operation failed: {str(e)}", "DATABASE_ERROR")
        except Exception as e:
            if rollback_on_error:
                self.db.rollback()
            logger.error(f"Unexpected error in transaction: {str(e)}")
            raise
    
    def execute_query(self, query: str, params: Dict[str, Any] = None) -> Any:
        """Execute database query with tracking."""
        self._query_count += 1
        try:
            return self.db.execute(text(query), params or {})
        except SQLAlchemyError as e:
            logger.error(f"Query execution failed: {str(e)}")
            raise ServiceError(f"Query failed: {str(e)}", "QUERY_ERROR")
    
    def get_cached_or_compute(self, cache_key: str, compute_func: Callable[[], T]) -> T:
        """Get value from cache or compute and cache it."""
        cached_value = self.cache.get(cache_key)
        if cached_value is not None:
            self._cache_hits += 1
            return cached_value
        
        self._cache_misses += 1
        computed_value = compute_func()
        self.cache.set(cache_key, computed_value)
        return computed_value
    
    def process_in_batches(
        self,
        items: List[T],
        process_func: Callable[[List[T]], Any],
        batch_size: Optional[int] = None
    ) -> List[Any]:
        """Process items in batches for better performance."""
        batch_size = batch_size or self.batch_config.batch_size
        results = []
        
        for i in range(0, len(items), batch_size):
            batch = items[i:i + batch_size]
            try:
                result = process_func(batch)
                results.append(result)
                self.metrics.records_processed += len(batch)
            except Exception as e:
                logger.error(f"Batch processing failed for batch {i//batch_size + 1}: {str(e)}")
                raise ServiceError(f"Batch processing failed: {str(e)}", "BATCH_ERROR")
        
        return results
    
    async def process_in_parallel(
        self,
        items: List[T],
        process_func: Callable[[T], Any],
        max_workers: Optional[int] = None
    ) -> List[Any]:
        """Process items in parallel using thread pool."""
        max_workers = max_workers or self.batch_config.max_workers
        results = []
        
        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # Submit all tasks
            future_to_item = {executor.submit(process_func, item): item for item in items}
            
            # Collect results as they complete
            for future in as_completed(future_to_item, timeout=self.batch_config.timeout_seconds):
                try:
                    result = future.result()
                    results.append(result)
                    self.metrics.records_processed += 1
                except Exception as e:
                    item = future_to_item[future]
                    logger.error(f"Parallel processing failed for item {item}: {str(e)}")
                    self.metrics.error_count += 1
        
        return results
    
    def validate_input(self, data: Dict[str, Any], schema: Dict[str, Any]) -> None:
        """Validate input data against schema."""
        for field, requirements in schema.items():
            if requirements.get("required", False) and field not in data:
                raise ValidationError(f"Required field missing: {field}", field)
            
            if field in data:
                value = data[field]
                field_type = requirements.get("type")
                
                if field_type and not isinstance(value, field_type):
                    raise ValidationError(
                        f"Invalid type for field {field}: expected {field_type.__name__}, got {type(value).__name__}",
                        field,
                        value
                    )
                
                min_length = requirements.get("min_length")
                if min_length and hasattr(value, "__len__") and len(value) < min_length:
                    raise ValidationError(f"Field {field} too short: minimum {min_length}", field, value)
                
                max_length = requirements.get("max_length")
                if max_length and hasattr(value, "__len__") and len(value) > max_length:
                    raise ValidationError(f"Field {field} too long: maximum {max_length}", field, value)
    
    def retry_operation(
        self,
        operation: Callable[[], T],
        max_attempts: Optional[int] = None,
        delay: Optional[float] = None
    ) -> T:
        """Retry operation with exponential backoff."""
        max_attempts = max_attempts or self.batch_config.retry_attempts
        delay = delay or self.batch_config.retry_delay
        
        for attempt in range(max_attempts):
            try:
                return operation()
            except Exception as e:
                if attempt == max_attempts - 1:
                    raise
                
                wait_time = delay * (2 ** attempt)
                logger.warning(f"Operation failed (attempt {attempt + 1}/{max_attempts}), retrying in {wait_time}s: {str(e)}")
                time.sleep(wait_time)
        
        raise ServiceError("Max retry attempts exceeded", "RETRY_EXHAUSTED")
    
    def get_service_health(self) -> Dict[str, Any]:
        """Get service health status."""
        try:
            # Test database connection
            self.db.execute(text("SELECT 1"))
            db_status = "healthy"
        except Exception:
            db_status = "unhealthy"
        
        return {
            "service": self.__class__.__name__,
            "status": "healthy" if db_status == "healthy" else "degraded",
            "database": db_status,
            "cache_stats": self.cache.get_stats(),
            "metrics": {
                "last_operation": self.metrics.operation_name,
                "last_duration": self.metrics.duration,
                "total_queries": self._query_count,
                "cache_hit_rate": self._cache_hits / (self._cache_hits + self._cache_misses) if (self._cache_hits + self._cache_misses) > 0 else 0
            }
        }
    
    @abstractmethod
    def validate_service_config(self) -> bool:
        """Validate service-specific configuration."""
        pass


class ImportServiceBase(BaseService):
    """Base class for import services."""
    
    def __init__(self, db: Session, config: Optional[Dict[str, Any]] = None):
        super().__init__(db, config)
        self.import_schema = self._get_import_schema()
    
    @abstractmethod
    def _get_import_schema(self) -> Dict[str, Any]:
        """Get validation schema for import data."""
        pass
    
    def validate_import_data(self, data: Dict[str, Any]) -> None:
        """Validate import data."""
        self.validate_input(data, self.import_schema)
    
    def check_duplicate_version(self, version: str, framework: str) -> bool:
        """Check if version already exists."""
        cache_key = f"version_exists_{framework}_{version}"
        return self.get_cached_or_compute(
            cache_key,
            lambda: self._query_version_exists(version, framework)
        )
    
    @abstractmethod
    def _query_version_exists(self, version: str, framework: str) -> bool:
        """Query database to check if version exists."""
        pass


class ExportServiceBase(BaseService):
    """Base class for export services."""
    
    def __init__(self, db: Session, config: Optional[Dict[str, Any]] = None):
        super().__init__(db, config)
        self.supported_formats = self._get_supported_formats()
    
    @abstractmethod
    def _get_supported_formats(self) -> List[str]:
        """Get list of supported export formats."""
        pass
    
    def validate_export_format(self, format_type: str) -> None:
        """Validate export format."""
        if format_type not in self.supported_formats:
            raise ValidationError(
                f"Unsupported export format: {format_type}. Supported: {', '.join(self.supported_formats)}",
                "format",
                format_type
            )
    
    def optimize_query_for_export(self, base_query: str, filters: Dict[str, Any] = None) -> str:
        """Optimize database query for large exports."""
        # Add pagination and indexing hints
        optimized_query = base_query
        
        if filters:
            # Add efficient filtering
            filter_conditions = []
            for field, value in filters.items():
                if isinstance(value, list):
                    filter_conditions.append(f"{field} IN ({','.join(['?' for _ in value])})")
                else:
                    filter_conditions.append(f"{field} = ?")
            
            if filter_conditions:
                optimized_query += " WHERE " + " AND ".join(filter_conditions)
        
        # Add ordering for consistent results
        optimized_query += " ORDER BY id"
        
        return optimized_query
