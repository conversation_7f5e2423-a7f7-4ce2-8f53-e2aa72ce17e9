"""
NIST CSF (Cybersecurity Framework) Import Service.

This module implements the NIST Cybersecurity Framework 2.0 import service
that was driven by TDD tests. It provides hierarchical data processing,
version management, and migration capabilities.

Features:
- Hierarchical data processing (Functions → Categories → Subcategories)
- Version management and migration (1.1 to 2.0)
- Implementation examples and informative references import
- Data transformation and normalization
- Hierarchical validation and integrity checks
"""

import json
import csv
from datetime import datetime
from typing import Dict, List, Any, Optional, Callable, Tuple
from dataclasses import dataclass, field
import logging
from io import StringIO

from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError, SQLAlchemyError

from api.models.nist_csf import (
    NISTCSFVersion,
    NISTCSFFunction,
    NISTCSFCategory,
    NISTCSFSubcategory,
    NISTCSFImplementationExample,
    NISTCSFInformativeReference
)

logger = logging.getLogger(__name__)


@dataclass
class NISTCSFImportResult:
    """Result of NIST CSF import operation."""
    success: bool
    version_id: Optional[int] = None
    imported_functions: int = 0
    imported_categories: int = 0
    imported_subcategories: int = 0
    imported_implementation_examples: int = 0
    imported_informative_references: int = 0
    processing_time: float = 0.0
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    superseded_version_id: Optional[int] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert result to dictionary."""
        return {
            "success": self.success,
            "version_id": self.version_id,
            "imported_functions": self.imported_functions,
            "imported_categories": self.imported_categories,
            "imported_subcategories": self.imported_subcategories,
            "imported_implementation_examples": self.imported_implementation_examples,
            "imported_informative_references": self.imported_informative_references,
            "processing_time": self.processing_time,
            "errors": self.errors,
            "warnings": self.warnings,
            "superseded_version_id": self.superseded_version_id
        }


class NISTCSFImportError(Exception):
    """Custom exception for NIST CSF import errors."""
    
    def __init__(self, message: str, error_type: str = "IMPORT_ERROR"):
        super().__init__(message)
        self.error_type = error_type
        self.message = message


@dataclass
class ParsedNISTCSFData:
    """Parsed NIST CSF framework data structure."""
    version: str
    release_date: Optional[datetime] = None
    description: Optional[str] = None
    url: Optional[str] = None
    supersedes_version: Optional[str] = None
    functions: List[Dict[str, Any]] = field(default_factory=list)


class NISTCSFDataParser:
    """Parser for NIST CSF framework data in various formats."""
    
    def parse_json(self, json_data: str) -> ParsedNISTCSFData:
        """Parse NIST CSF data from JSON format."""
        try:
            data = json.loads(json_data)
        except json.JSONDecodeError as e:
            raise NISTCSFImportError(f"Invalid JSON format: {str(e)}", "PARSE_ERROR")
        
        # Validate required fields
        if "version" not in data:
            raise NISTCSFImportError("Missing required field: version", "VALIDATION_ERROR")
        
        if "functions" not in data:
            raise NISTCSFImportError("Missing required field: functions", "VALIDATION_ERROR")
        
        # Parse release date if provided
        release_date = None
        if "release_date" in data:
            if isinstance(data["release_date"], str):
                try:
                    release_date = datetime.fromisoformat(data["release_date"].replace("Z", "+00:00"))
                except ValueError:
                    try:
                        release_date = datetime.strptime(data["release_date"], "%Y-%m-%d")
                    except ValueError:
                        logger.warning(f"Could not parse release_date: {data['release_date']}")
        
        return ParsedNISTCSFData(
            version=data["version"],
            release_date=release_date,
            description=data.get("description"),
            url=data.get("url"),
            supersedes_version=data.get("supersedes_version"),
            functions=data["functions"]
        )
    
    def parse_csv(self, csv_data: str, version: str = None) -> ParsedNISTCSFData:
        """Parse NIST CSF data from CSV format and build hierarchy."""
        try:
            csv_reader = csv.DictReader(StringIO(csv_data))
            
            # Use hierarchy builder to construct nested structure
            hierarchy_builder = NISTCSFHierarchyBuilder()
            flat_data = list(csv_reader)
            hierarchy = hierarchy_builder.build_from_flat_data(flat_data)
            
            return ParsedNISTCSFData(
                version=version or "imported",
                functions=hierarchy.functions
            )
            
        except Exception as e:
            raise NISTCSFImportError(f"CSV parsing error: {str(e)}", "PARSE_ERROR")
    
    def parse_official_format(self, json_data: str) -> ParsedNISTCSFData:
        """Parse official NIST CSF format."""
        try:
            data = json.loads(json_data)
            framework = data.get("Framework", {})
            
            version = framework.get("Version")
            if not version:
                raise NISTCSFImportError("Missing version in official format", "VALIDATION_ERROR")
            
            functions_data = framework.get("Functions", {})
            functions = []
            
            for func_id, func_data in functions_data.items():
                function = {
                    "function_id": func_id,
                    "name": func_data.get("Name", ""),
                    "description": func_data.get("Description", ""),
                    "categories": []
                }
                
                categories_data = func_data.get("Categories", {})
                for cat_id, cat_data in categories_data.items():
                    category = {
                        "category_id": cat_id,
                        "name": cat_data.get("Name", ""),
                        "description": cat_data.get("Description", ""),
                        "subcategories": []
                    }
                    
                    subcategories_data = cat_data.get("Subcategories", {})
                    for subcat_id, subcat_data in subcategories_data.items():
                        subcategory = {
                            "subcategory_id": subcat_id,
                            "name": subcat_data.get("Outcome", ""),
                            "description": subcat_data.get("Description", ""),
                            "implementation_examples": [],
                            "informative_references": []
                        }
                        category["subcategories"].append(subcategory)
                    
                    function["categories"].append(category)
                
                functions.append(function)
            
            return ParsedNISTCSFData(
                version=version,
                functions=functions
            )
            
        except Exception as e:
            raise NISTCSFImportError(f"Official format parsing error: {str(e)}", "PARSE_ERROR")


class NISTCSFHierarchyBuilder:
    """Builder for NIST CSF hierarchical structure from flat data."""
    
    @dataclass
    class HierarchyResult:
        functions: List[Dict[str, Any]] = field(default_factory=list)
    
    def build_from_flat_data(self, flat_data: List[Dict[str, Any]]) -> HierarchyResult:
        """Build hierarchical structure from flat data."""
        functions_dict = {}
        
        for row in flat_data:
            function_id = row.get("function_id")
            category_id = row.get("category_id")
            subcategory_id = row.get("subcategory_id")
            
            if not all([function_id, category_id, subcategory_id]):
                continue
            
            # Validate hierarchy integrity
            if not category_id.startswith(function_id):
                raise NISTCSFImportError(
                    f"Category ID prefix mismatch: {category_id} should start with {function_id}",
                    "HIERARCHY_ERROR"
                )
            
            if category_id not in subcategory_id:
                raise NISTCSFImportError(
                    f"Subcategory ID should contain category ID: {subcategory_id} should contain {category_id}",
                    "HIERARCHY_ERROR"
                )
            
            # Build function
            if function_id not in functions_dict:
                functions_dict[function_id] = {
                    "function_id": function_id,
                    "name": row.get("function_name", ""),
                    "description": row.get("function_description", ""),
                    "categories": {}
                }
            
            # Build category
            if category_id not in functions_dict[function_id]["categories"]:
                functions_dict[function_id]["categories"][category_id] = {
                    "category_id": category_id,
                    "name": row.get("category_name", ""),
                    "description": row.get("category_description", ""),
                    "subcategories": []
                }
            
            # Build subcategory
            subcategory = {
                "subcategory_id": subcategory_id,
                "name": row.get("subcategory_name", ""),
                "description": row.get("subcategory_description", ""),
                "implementation_examples": [],
                "informative_references": []
            }
            
            functions_dict[function_id]["categories"][category_id]["subcategories"].append(subcategory)
        
        # Convert to list format
        functions = []
        for function_data in functions_dict.values():
            function_data["categories"] = list(function_data["categories"].values())
            functions.append(function_data)
        
        return self.HierarchyResult(functions=functions)


class NISTCSFDataValidator:
    """Validator for NIST CSF framework data."""
    
    VALID_FUNCTION_IDS = {"GV", "ID", "PR", "DE", "RS", "RC"}
    
    def validate_framework_structure(self, data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """Validate NIST CSF framework data structure."""
        errors = []
        
        # Validate version
        if not data.get("version"):
            errors.append("Missing or empty version field")
        
        # Validate functions
        functions = data.get("functions", [])
        if not functions:
            errors.append("No functions found")
        
        seen_function_ids = set()
        
        for i, function in enumerate(functions):
            func_prefix = f"Function {i + 1}"
            
            # Validate function structure
            function_id = function.get("function_id")
            if not function_id:
                errors.append(f"{func_prefix}: Missing function_id")
            elif function_id in seen_function_ids:
                errors.append(f"{func_prefix}: Duplicate function_id: {function_id}")
            elif function_id not in self.VALID_FUNCTION_IDS:
                errors.append(f"{func_prefix}: Invalid function_id: {function_id}")
            else:
                seen_function_ids.add(function_id)
            
            if not function.get("name"):
                errors.append(f"{func_prefix}: Missing name")
            
            # Validate categories
            categories = function.get("categories", [])
            seen_category_ids = set()
            
            for j, category in enumerate(categories):
                cat_prefix = f"{func_prefix}, Category {j + 1}"
                
                category_id = category.get("category_id")
                if not category_id:
                    errors.append(f"{cat_prefix}: Missing category_id")
                elif category_id in seen_category_ids:
                    errors.append(f"{cat_prefix}: Duplicate category_id: {category_id}")
                elif function_id and not category_id.startswith(function_id):
                    errors.append(f"{cat_prefix}: Category ID prefix mismatch: {category_id} should start with {function_id}")
                else:
                    seen_category_ids.add(category_id)
                
                if not category.get("name"):
                    errors.append(f"{cat_prefix}: Missing name")
                
                # Validate subcategories
                subcategories = category.get("subcategories", [])
                seen_subcategory_ids = set()
                
                for k, subcategory in enumerate(subcategories):
                    subcat_prefix = f"{cat_prefix}, Subcategory {k + 1}"
                    
                    subcategory_id = subcategory.get("subcategory_id")
                    if not subcategory_id:
                        errors.append(f"{subcat_prefix}: Missing subcategory_id")
                    elif subcategory_id in seen_subcategory_ids:
                        errors.append(f"{subcat_prefix}: Duplicate subcategory_id: {subcategory_id}")
                    elif category_id and category_id not in subcategory_id:
                        errors.append(f"{subcat_prefix}: Subcategory ID prefix mismatch: {subcategory_id} should contain {category_id}")
                    else:
                        seen_subcategory_ids.add(subcategory_id)
                    
                    if not subcategory.get("name"):
                        errors.append(f"{subcat_prefix}: Missing name")
        
        return len(errors) == 0, errors


class NISTCSFVersionMigrator:
    """Migrator for NIST CSF versions (e.g., 1.1 to 2.0)."""
    
    def __init__(self, db_session: Session):
        self.db = db_session
    
    @dataclass
    class MigrationResult:
        success: bool
        source_version: str
        target_version: str
        migrated_functions: List[Dict[str, Any]] = field(default_factory=list)
        migration_notes: List[str] = field(default_factory=list)
    
    def migrate_to_v2_0(self, csf_v11_data: Dict[str, Any]) -> MigrationResult:
        """Migrate CSF 1.1 data to 2.0 format."""
        result = self.MigrationResult(
            success=False,
            source_version="1.1",
            target_version="2.0"
        )
        
        try:
            functions = csf_v11_data.get("functions", [])
            migrated_functions = []
            
            # Add new Govern function for v2.0
            govern_function = {
                "function_id": "GV",
                "name": "Govern",
                "description": "The organization's cybersecurity risk management strategy, expectations, and policy are established, communicated, and monitored.",
                "categories": []
            }
            migrated_functions.append(govern_function)
            result.migration_notes.append("Added new Govern (GV) function for CSF 2.0")
            
            # Migrate existing functions
            for function in functions:
                migrated_function = function.copy()
                
                # Update categories and subcategories for v2.0 format
                if "categories" in migrated_function:
                    for category in migrated_function["categories"]:
                        if "subcategories" in category:
                            for subcategory in category["subcategories"]:
                                # Update subcategory ID format (e.g., ID.AM-1 -> ID.AM-01)
                                old_id = subcategory.get("subcategory_id", "")
                                if old_id and "-" in old_id:
                                    parts = old_id.split("-")
                                    if len(parts) == 2 and parts[1].isdigit():
                                        new_id = f"{parts[0]}-{parts[1].zfill(2)}"
                                        subcategory["subcategory_id"] = new_id
                                        result.migration_notes.append(f"Updated subcategory ID: {old_id} -> {new_id}")
                
                migrated_functions.append(migrated_function)
            
            result.migrated_functions = migrated_functions
            result.success = True
            
            return result
            
        except Exception as e:
            result.migration_notes.append(f"Migration error: {str(e)}")
            return result
    
    def map_subcategory_v11_to_v20(self, v11_subcategory: Dict[str, Any]) -> Dict[str, Any]:
        """Map v1.1 subcategory to v2.0 format."""
        subcategory_id = v11_subcategory.get("subcategory_id", "")
        
        # Update ID format
        if "-" in subcategory_id:
            parts = subcategory_id.split("-")
            if len(parts) == 2 and parts[1].isdigit():
                new_id = f"{parts[0]}-{parts[1].zfill(2)}"
            else:
                new_id = subcategory_id
        else:
            new_id = subcategory_id
        
        return {
            "subcategory_id": new_id,
            "name": v11_subcategory.get("name", ""),
            "description": v11_subcategory.get("description", ""),
            "migration_confidence": 0.9,  # High confidence for direct mapping
            "migration_notes": f"Migrated from v1.1 subcategory {subcategory_id}"
        }


class NISTCSFImportService:
    """Service for importing NIST CSF framework data."""
    
    def __init__(self, db_session: Session):
        self.db = db_session
        self.parser = NISTCSFDataParser()
        self.validator = NISTCSFDataValidator()
        self.migrator = NISTCSFVersionMigrator(db_session)
    
    def import_from_json(self, json_data: str) -> NISTCSFImportResult:
        """Import NIST CSF framework from JSON data."""
        start_time = datetime.now()
        result = NISTCSFImportResult(success=False)
        
        try:
            # Parse JSON data
            parsed_data = self.parser.parse_json(json_data)
            
            # Validate data structure
            is_valid, validation_errors = self.validator.validate_framework_structure({
                "version": parsed_data.version,
                "functions": parsed_data.functions
            })
            
            if not is_valid:
                result.errors.extend(validation_errors)
                return result
            
            # Import to database
            result = self._import_to_database(parsed_data)
            result.processing_time = (datetime.now() - start_time).total_seconds()
            
            return result
            
        except NISTCSFImportError as e:
            result.errors.append(f"{e.error_type}: {e.message}")
            result.processing_time = (datetime.now() - start_time).total_seconds()
            return result
        except Exception as e:
            result.errors.append(f"Unexpected error: {str(e)}")
            result.processing_time = (datetime.now() - start_time).total_seconds()
            logger.exception("Unexpected error during NIST CSF import")
            return result
    
    def _import_to_database(self, parsed_data: ParsedNISTCSFData) -> NISTCSFImportResult:
        """Import parsed data to database with hierarchical relationships."""
        result = NISTCSFImportResult(success=False)
        
        try:
            # Handle version supersession
            superseded_version = None
            if parsed_data.supersedes_version:
                superseded_version = self.db.query(NISTCSFVersion).filter_by(
                    version=parsed_data.supersedes_version
                ).first()
            
            # Create new version
            version = NISTCSFVersion(
                version=parsed_data.version,
                release_date=parsed_data.release_date,
                description=parsed_data.description,
                url=parsed_data.url,
                is_current=True,
                import_date=datetime.utcnow(),
                supersedes_version_id=superseded_version.id if superseded_version else None
            )
            
            # Set as current version and handle supersession
            if superseded_version:
                superseded_version.is_current = False
                result.superseded_version_id = superseded_version.id
            
            self.db.add(version)
            self.db.flush()  # Get version ID
            result.version_id = version.id
            
            # Import functions, categories, and subcategories
            for func_index, func_data in enumerate(parsed_data.functions):
                # Create function
                function = NISTCSFFunction(
                    function_id=func_data["function_id"],
                    name=func_data["name"],
                    description=func_data.get("description"),
                    version_id=version.id,
                    order_index=func_index + 1
                )
                self.db.add(function)
                self.db.flush()
                result.imported_functions += 1
                
                # Import categories
                for cat_index, cat_data in enumerate(func_data.get("categories", [])):
                    category = NISTCSFCategory(
                        category_id=cat_data["category_id"],
                        name=cat_data["name"],
                        description=cat_data.get("description"),
                        function_id=function.id,
                        version_id=version.id,
                        order_index=cat_index + 1
                    )
                    self.db.add(category)
                    self.db.flush()
                    result.imported_categories += 1
                    
                    # Import subcategories
                    for subcat_index, subcat_data in enumerate(cat_data.get("subcategories", [])):
                        subcategory = NISTCSFSubcategory(
                            subcategory_id=subcat_data["subcategory_id"],
                            name=subcat_data["name"],
                            description=subcat_data.get("description"),
                            category_id=category.id,
                            function_id=function.id,
                            version_id=version.id,
                            order_index=subcat_index + 1
                        )
                        self.db.add(subcategory)
                        self.db.flush()
                        result.imported_subcategories += 1
                        
                        # Import implementation examples
                        for ex_index, example_data in enumerate(subcat_data.get("implementation_examples", [])):
                            example = NISTCSFImplementationExample(
                                subcategory_id=subcategory.id,
                                example_text=example_data["example_text"],
                                example_type=example_data.get("example_type"),
                                order_index=ex_index + 1
                            )
                            self.db.add(example)
                            result.imported_implementation_examples += 1
                        
                        # Import informative references
                        for ref_data in subcat_data.get("informative_references", []):
                            reference = NISTCSFInformativeReference(
                                subcategory_id=subcategory.id,
                                framework_name=ref_data["framework_name"],
                                reference_id=ref_data["reference_id"],
                                description=ref_data.get("description"),
                                url=ref_data.get("url")
                            )
                            self.db.add(reference)
                            result.imported_informative_references += 1
            
            # Commit all changes
            self.db.commit()
            result.success = True
            
            return result
            
        except SQLAlchemyError as e:
            self.db.rollback()
            raise NISTCSFImportError(f"Database error: {str(e)}", "DATABASE_ERROR")
        except Exception as e:
            self.db.rollback()
            raise NISTCSFImportError(f"Import error: {str(e)}", "IMPORT_ERROR")
