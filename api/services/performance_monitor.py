"""
Performance Monitoring Service for Framework Services.

This module provides comprehensive performance monitoring, metrics collection,
and optimization recommendations for all framework services.
"""

import logging
import time
import psutil
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, field
from collections import defaultdict, deque
from contextlib import contextmanager
import statistics

from sqlalchemy.orm import Session
from sqlalchemy import text

logger = logging.getLogger(__name__)


@dataclass
class PerformanceMetric:
    """Individual performance metric."""
    name: str
    value: float
    unit: str
    timestamp: datetime
    service: str
    operation: str
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ServicePerformanceReport:
    """Performance report for a service."""
    service_name: str
    report_period: timedelta
    total_operations: int
    average_duration: float
    min_duration: float
    max_duration: float
    p95_duration: float
    p99_duration: float
    error_rate: float
    throughput: float  # operations per second
    memory_usage: Dict[str, float]
    database_metrics: Dict[str, Any]
    recommendations: List[str]
    bottlenecks: List[str]


class PerformanceCollector:
    """Collects performance metrics from services."""
    
    def __init__(self, max_metrics: int = 10000):
        self.metrics: deque = deque(maxlen=max_metrics)
        self.service_metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self.operation_metrics: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._lock = threading.Lock()
    
    def record_metric(self, metric: PerformanceMetric):
        """Record a performance metric."""
        with self._lock:
            self.metrics.append(metric)
            self.service_metrics[metric.service].append(metric)
            self.operation_metrics[f"{metric.service}.{metric.operation}"].append(metric)
    
    def get_metrics(
        self,
        service: Optional[str] = None,
        operation: Optional[str] = None,
        since: Optional[datetime] = None
    ) -> List[PerformanceMetric]:
        """Get metrics with optional filtering."""
        with self._lock:
            if service and operation:
                metrics = list(self.operation_metrics.get(f"{service}.{operation}", []))
            elif service:
                metrics = list(self.service_metrics.get(service, []))
            else:
                metrics = list(self.metrics)
        
        if since:
            metrics = [m for m in metrics if m.timestamp >= since]
        
        return metrics
    
    def clear_old_metrics(self, older_than: timedelta):
        """Clear metrics older than specified time."""
        cutoff = datetime.now() - older_than
        
        with self._lock:
            # Clear from main metrics
            self.metrics = deque(
                [m for m in self.metrics if m.timestamp >= cutoff],
                maxlen=self.metrics.maxlen
            )
            
            # Clear from service metrics
            for service in self.service_metrics:
                self.service_metrics[service] = deque(
                    [m for m in self.service_metrics[service] if m.timestamp >= cutoff],
                    maxlen=self.service_metrics[service].maxlen
                )
            
            # Clear from operation metrics
            for operation in self.operation_metrics:
                self.operation_metrics[operation] = deque(
                    [m for m in self.operation_metrics[operation] if m.timestamp >= cutoff],
                    maxlen=self.operation_metrics[operation].maxlen
                )


class PerformanceAnalyzer:
    """Analyzes performance metrics and generates insights."""
    
    def __init__(self, collector: PerformanceCollector):
        self.collector = collector
    
    def analyze_service_performance(
        self,
        service: str,
        period: timedelta = timedelta(hours=1)
    ) -> ServicePerformanceReport:
        """Analyze performance for a specific service."""
        since = datetime.now() - period
        metrics = self.collector.get_metrics(service=service, since=since)
        
        if not metrics:
            return ServicePerformanceReport(
                service_name=service,
                report_period=period,
                total_operations=0,
                average_duration=0,
                min_duration=0,
                max_duration=0,
                p95_duration=0,
                p99_duration=0,
                error_rate=0,
                throughput=0,
                memory_usage={},
                database_metrics={},
                recommendations=[],
                bottlenecks=[]
            )
        
        # Extract duration metrics
        durations = [m.value for m in metrics if m.name == "duration"]
        error_count = len([m for m in metrics if m.name == "error"])
        
        # Calculate statistics
        total_operations = len(durations)
        average_duration = statistics.mean(durations) if durations else 0
        min_duration = min(durations) if durations else 0
        max_duration = max(durations) if durations else 0
        
        # Calculate percentiles
        if durations:
            sorted_durations = sorted(durations)
            p95_duration = sorted_durations[int(0.95 * len(sorted_durations))]
            p99_duration = sorted_durations[int(0.99 * len(sorted_durations))]
        else:
            p95_duration = p99_duration = 0
        
        error_rate = (error_count / total_operations) if total_operations > 0 else 0
        throughput = total_operations / period.total_seconds() if period.total_seconds() > 0 else 0
        
        # Analyze memory usage
        memory_metrics = [m for m in metrics if m.name == "memory_usage"]
        memory_usage = {
            "average": statistics.mean([m.value for m in memory_metrics]) if memory_metrics else 0,
            "peak": max([m.value for m in memory_metrics]) if memory_metrics else 0
        }
        
        # Analyze database metrics
        db_query_metrics = [m for m in metrics if m.name == "database_queries"]
        database_metrics = {
            "total_queries": sum([m.value for m in db_query_metrics]),
            "average_queries_per_operation": statistics.mean([m.value for m in db_query_metrics]) if db_query_metrics else 0
        }
        
        # Generate recommendations and identify bottlenecks
        recommendations = self._generate_recommendations(
            average_duration, error_rate, throughput, memory_usage, database_metrics
        )
        bottlenecks = self._identify_bottlenecks(metrics)
        
        return ServicePerformanceReport(
            service_name=service,
            report_period=period,
            total_operations=total_operations,
            average_duration=average_duration,
            min_duration=min_duration,
            max_duration=max_duration,
            p95_duration=p95_duration,
            p99_duration=p99_duration,
            error_rate=error_rate,
            throughput=throughput,
            memory_usage=memory_usage,
            database_metrics=database_metrics,
            recommendations=recommendations,
            bottlenecks=bottlenecks
        )
    
    def _generate_recommendations(
        self,
        avg_duration: float,
        error_rate: float,
        throughput: float,
        memory_usage: Dict[str, float],
        db_metrics: Dict[str, Any]
    ) -> List[str]:
        """Generate performance recommendations."""
        recommendations = []
        
        # Duration recommendations
        if avg_duration > 5.0:  # 5 seconds
            recommendations.append("Consider optimizing slow operations - average duration is high")
        
        # Error rate recommendations
        if error_rate > 0.05:  # 5%
            recommendations.append("High error rate detected - review error handling and validation")
        
        # Throughput recommendations
        if throughput < 1.0:  # Less than 1 operation per second
            recommendations.append("Low throughput detected - consider batch processing or parallel execution")
        
        # Memory recommendations
        if memory_usage.get("peak", 0) > 1000:  # 1GB
            recommendations.append("High memory usage detected - consider memory optimization")
        
        # Database recommendations
        if db_metrics.get("average_queries_per_operation", 0) > 10:
            recommendations.append("High database query count - consider query optimization or caching")
        
        return recommendations
    
    def _identify_bottlenecks(self, metrics: List[PerformanceMetric]) -> List[str]:
        """Identify performance bottlenecks."""
        bottlenecks = []
        
        # Group metrics by operation
        operation_durations = defaultdict(list)
        for metric in metrics:
            if metric.name == "duration":
                operation_durations[metric.operation].append(metric.value)
        
        # Find slowest operations
        for operation, durations in operation_durations.items():
            avg_duration = statistics.mean(durations)
            if avg_duration > 2.0:  # 2 seconds
                bottlenecks.append(f"Slow operation: {operation} (avg: {avg_duration:.2f}s)")
        
        return bottlenecks


class PerformanceMonitor:
    """Main performance monitoring service."""
    
    def __init__(self, db: Session):
        self.db = db
        self.collector = PerformanceCollector()
        self.analyzer = PerformanceAnalyzer(self.collector)
        self._monitoring_active = False
        self._monitor_thread = None
    
    @contextmanager
    def monitor_operation(self, service: str, operation: str):
        """Context manager for monitoring an operation."""
        start_time = time.time()
        start_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
        
        try:
            yield
            # Record success
            duration = time.time() - start_time
            self.collector.record_metric(PerformanceMetric(
                name="duration",
                value=duration,
                unit="seconds",
                timestamp=datetime.now(),
                service=service,
                operation=operation
            ))
            
        except Exception as e:
            # Record error
            self.collector.record_metric(PerformanceMetric(
                name="error",
                value=1,
                unit="count",
                timestamp=datetime.now(),
                service=service,
                operation=operation,
                metadata={"error_type": type(e).__name__, "error_message": str(e)}
            ))
            raise
        finally:
            # Record memory usage
            end_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB
            memory_delta = end_memory - start_memory
            
            self.collector.record_metric(PerformanceMetric(
                name="memory_usage",
                value=memory_delta,
                unit="MB",
                timestamp=datetime.now(),
                service=service,
                operation=operation
            ))
    
    def start_monitoring(self, interval: int = 60):
        """Start background monitoring."""
        if self._monitoring_active:
            return
        
        self._monitoring_active = True
        self._monitor_thread = threading.Thread(
            target=self._background_monitor,
            args=(interval,),
            daemon=True
        )
        self._monitor_thread.start()
        logger.info("Performance monitoring started")
    
    def stop_monitoring(self):
        """Stop background monitoring."""
        self._monitoring_active = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=5)
        logger.info("Performance monitoring stopped")
    
    def _background_monitor(self, interval: int):
        """Background monitoring loop."""
        while self._monitoring_active:
            try:
                # Collect system metrics
                self._collect_system_metrics()
                
                # Clean old metrics
                self.collector.clear_old_metrics(timedelta(hours=24))
                
                time.sleep(interval)
            except Exception as e:
                logger.error(f"Error in background monitoring: {e}")
                time.sleep(interval)
    
    def _collect_system_metrics(self):
        """Collect system-level metrics."""
        try:
            # CPU usage
            cpu_percent = psutil.cpu_percent()
            self.collector.record_metric(PerformanceMetric(
                name="cpu_usage",
                value=cpu_percent,
                unit="percent",
                timestamp=datetime.now(),
                service="system",
                operation="monitoring"
            ))
            
            # Memory usage
            memory = psutil.virtual_memory()
            self.collector.record_metric(PerformanceMetric(
                name="memory_usage",
                value=memory.percent,
                unit="percent",
                timestamp=datetime.now(),
                service="system",
                operation="monitoring"
            ))
            
            # Database connection count
            try:
                result = self.db.execute(text("SELECT count(*) FROM pg_stat_activity"))
                connection_count = result.scalar()
                self.collector.record_metric(PerformanceMetric(
                    name="database_connections",
                    value=connection_count,
                    unit="count",
                    timestamp=datetime.now(),
                    service="database",
                    operation="monitoring"
                ))
            except Exception as e:
                logger.warning(f"Could not collect database metrics: {e}")
                
        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")
    
    def get_service_report(self, service: str, period: timedelta = timedelta(hours=1)) -> ServicePerformanceReport:
        """Get performance report for a service."""
        return self.analyzer.analyze_service_performance(service, period)
    
    def get_all_services_summary(self) -> Dict[str, Dict[str, Any]]:
        """Get summary for all monitored services."""
        services = set()
        for metric in self.collector.metrics:
            services.add(metric.service)
        
        summary = {}
        for service in services:
            if service == "system":  # Skip system metrics
                continue
            
            report = self.get_service_report(service)
            summary[service] = {
                "total_operations": report.total_operations,
                "average_duration": report.average_duration,
                "error_rate": report.error_rate,
                "throughput": report.throughput,
                "health_status": "healthy" if report.error_rate < 0.05 and report.average_duration < 2.0 else "degraded"
            }
        
        return summary
    
    def get_health_status(self) -> Dict[str, Any]:
        """Get overall system health status."""
        summary = self.get_all_services_summary()
        
        total_services = len(summary)
        healthy_services = len([s for s in summary.values() if s["health_status"] == "healthy"])
        
        overall_status = "healthy" if healthy_services == total_services else "degraded"
        if healthy_services < total_services * 0.5:
            overall_status = "unhealthy"
        
        return {
            "overall_status": overall_status,
            "total_services": total_services,
            "healthy_services": healthy_services,
            "degraded_services": total_services - healthy_services,
            "services": summary
        }
