"""
Core configuration settings for the FastAPI application.

This module contains all configuration settings including database connections,
authentication settings, and framework-specific configurations.
"""

import os
from typing import List, Optional
from pydantic import BaseSettings, validator


class Settings(BaseSettings):
    """Application settings."""
    
    # Application settings
    APP_NAME: str = "Cybersecurity Framework Management API"
    APP_VERSION: str = "1.0.0"
    DEBUG: bool = False
    
    # Database settings
    DATABASE_URL: str = os.getenv("DATABASE_URL", "sqlite:///./cybersecurity_frameworks.db")
    
    # Authentication settings
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key-change-in-production")
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 30
    
    # CORS settings
    ALLOWED_HOSTS: List[str] = [
        "http://localhost:3000",
        "http://localhost:8000",
        "http://localhost:8080",
        "https://api.cybersecurity-framework.com"
    ]
    
    # Rate limiting settings
    RATE_LIMIT_ENABLED: bool = True
    DEFAULT_RATE_LIMIT: str = "100/minute"
    STRICT_RATE_LIMIT: str = "20/minute"
    AUTH_RATE_LIMIT: str = "5/minute"
    
    # Cache settings
    CACHE_ENABLED: bool = True
    CACHE_TTL: int = 300  # 5 minutes
    REDIS_URL: Optional[str] = os.getenv("REDIS_URL")
    
    # File upload settings
    MAX_FILE_SIZE: int = 10 * 1024 * 1024  # 10MB
    ALLOWED_FILE_TYPES: List[str] = ["json", "csv", "xml"]
    UPLOAD_DIR: str = "./uploads"
    
    # Framework-specific settings
    ISF_DEFAULT_VERSION: str = "2020.1"
    NIST_CSF_DEFAULT_VERSION: str = "2.0"
    MITRE_ATTACK_DEFAULT_VERSION: str = "14.1"
    
    # Import/Export settings
    MAX_IMPORT_RECORDS: int = 10000
    EXPORT_CHUNK_SIZE: int = 1000
    ASYNC_IMPORT_THRESHOLD: int = 1000
    
    # Mapping algorithm settings
    MIN_CONFIDENCE_THRESHOLD: float = 0.5
    MIN_EFFECTIVENESS_THRESHOLD: float = 0.3
    SEMANTIC_SIMILARITY_WEIGHT: float = 0.4
    KEYWORD_OVERLAP_WEIGHT: float = 0.3
    EXPERT_VALIDATION_WEIGHT: float = 0.3
    
    # Search settings
    MAX_SEARCH_RESULTS: int = 1000
    DEFAULT_PAGE_SIZE: int = 20
    MAX_PAGE_SIZE: int = 100
    
    # Logging settings
    LOG_LEVEL: str = "INFO"
    LOG_FORMAT: str = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    LOG_FILE: Optional[str] = None
    
    # Performance settings
    ENABLE_QUERY_OPTIMIZATION: bool = True
    ENABLE_RESPONSE_COMPRESSION: bool = True
    ENABLE_CACHING: bool = True
    
    # Security settings
    ENABLE_HTTPS_REDIRECT: bool = False
    ENABLE_SECURITY_HEADERS: bool = True
    ENABLE_CSRF_PROTECTION: bool = True
    
    # External API settings
    MITRE_ATTACK_API_URL: str = "https://attack.mitre.org/api/v2"
    NIST_CSF_API_URL: str = "https://csrc.nist.gov/api/csf"
    
    # Validation settings
    ENABLE_STRICT_VALIDATION: bool = True
    VALIDATE_FRAMEWORK_REFERENCES: bool = True
    
    @validator('ALLOWED_HOSTS', pre=True)
    def parse_allowed_hosts(cls, v):
        if isinstance(v, str):
            return [host.strip() for host in v.split(',')]
        return v
    
    @validator('DATABASE_URL')
    def validate_database_url(cls, v):
        if not v:
            raise ValueError("DATABASE_URL is required")
        return v
    
    @validator('SECRET_KEY')
    def validate_secret_key(cls, v):
        if not v or v == "your-secret-key-change-in-production":
            raise ValueError("SECRET_KEY must be set to a secure value in production")
        return v
    
    @validator('MIN_CONFIDENCE_THRESHOLD', 'MIN_EFFECTIVENESS_THRESHOLD')
    def validate_thresholds(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError("Threshold values must be between 0.0 and 1.0")
        return v
    
    @validator('SEMANTIC_SIMILARITY_WEIGHT', 'KEYWORD_OVERLAP_WEIGHT', 'EXPERT_VALIDATION_WEIGHT')
    def validate_weights(cls, v):
        if not 0.0 <= v <= 1.0:
            raise ValueError("Weight values must be between 0.0 and 1.0")
        return v
    
    class Config:
        env_file = ".env"
        case_sensitive = True


# Create settings instance
settings = Settings()


# Framework-specific configurations
class ISFConfig:
    """ISF framework configuration."""
    
    SUPPORTED_VERSIONS = ["2020.1", "2018.1", "2016.1"]
    DEFAULT_VERSION = settings.ISF_DEFAULT_VERSION
    
    # Security areas configuration
    SECURITY_AREAS = [
        "SG",  # Security Governance
        "RM",  # Risk Management
        "LR",  # Legal and Regulatory
        "BM",  # Business Continuity Management
        "IS",  # Information Security
        "HR",  # Human Resources Security
        "PA",  # Physical and Environmental Security
        "SA",  # System Access
        "SD",  # System Development
        "SO",  # System Operations
        "CM",  # Communications
        "OP",  # Operations Security
        "AC",  # Access Control
        "SY",  # System Security
        "CR",  # Cryptography
        "SC",  # Security in Cloud Computing
        "MO",  # Mobile Computing
        "EM",  # Email Security
        "WS",  # Web Security
        "NW",  # Network Security
        "MA",  # Malware
        "IN",  # Incident Management
        "FO",  # Forensics
        "BC",  # Business Continuity
        "DR",  # Disaster Recovery
        "SU",  # Supplier Relationships
    ]
    
    # Control types
    CONTROL_TYPES = ["policy", "administrative", "technical"]
    
    # Maturity levels
    MATURITY_LEVELS = ["basic", "intermediate", "advanced"]


class NISTCSFConfig:
    """NIST CSF framework configuration."""
    
    SUPPORTED_VERSIONS = ["2.0", "1.1"]
    DEFAULT_VERSION = settings.NIST_CSF_DEFAULT_VERSION
    
    # Functions configuration
    FUNCTIONS = {
        "2.0": ["GV", "ID", "PR", "DE", "RS", "RC"],  # Added Govern function
        "1.1": ["ID", "PR", "DE", "RS", "RC"]
    }
    
    FUNCTION_NAMES = {
        "GV": "Govern",
        "ID": "Identify",
        "PR": "Protect", 
        "DE": "Detect",
        "RS": "Respond",
        "RC": "Recover"
    }
    
    # Version supersession
    VERSION_SUPERSESSION = {
        "2.0": "1.1"  # 2.0 supersedes 1.1
    }


class MITREConfig:
    """MITRE ATT&CK framework configuration."""
    
    SUPPORTED_VERSIONS = ["14.1", "13.1", "12.1"]
    DEFAULT_VERSION = settings.MITRE_ATTACK_DEFAULT_VERSION
    
    # Tactics
    TACTICS = [
        "initial-access",
        "execution", 
        "persistence",
        "privilege-escalation",
        "defense-evasion",
        "credential-access",
        "discovery",
        "lateral-movement",
        "collection",
        "command-and-control",
        "exfiltration",
        "impact"
    ]
    
    # Platforms
    PLATFORMS = [
        "Windows",
        "Linux", 
        "macOS",
        "Network",
        "PRE",
        "Azure AD",
        "Office 365",
        "SaaS",
        "IaaS",
        "Google Workspace",
        "Containers"
    ]


class MappingConfig:
    """Mapping algorithm configuration."""
    
    # Mapping types
    MAPPING_TYPES = [
        "mitigates",
        "detects", 
        "prevents",
        "responds",
        "recovers",
        "equivalent",
        "overlapping",
        "complementary",
        "related"
    ]
    
    # Quality thresholds
    HIGH_QUALITY_THRESHOLD = 0.8
    MEDIUM_QUALITY_THRESHOLD = 0.6
    LOW_QUALITY_THRESHOLD = 0.4
    
    # Effectiveness factors
    EFFECTIVENESS_FACTORS = [
        "implementation_complexity",
        "cost_effectiveness", 
        "technical_feasibility",
        "organizational_impact",
        "coverage_completeness"
    ]
    
    # Confidence factors
    CONFIDENCE_FACTORS = [
        "semantic_similarity",
        "keyword_overlap",
        "expert_validation",
        "implementation_evidence",
        "peer_review"
    ]


class ExportConfig:
    """Export configuration."""
    
    # Supported formats
    SUPPORTED_FORMATS = ["json", "csv", "xml", "stix"]
    
    # STIX configuration
    STIX_VERSION = "2.1"
    STIX_NAMESPACE = "https://api.cybersecurity-framework.com"
    
    # CSV configuration
    CSV_DELIMITER = ","
    CSV_QUOTE_CHAR = '"'
    CSV_ENCODING = "utf-8"
    
    # JSON configuration
    JSON_INDENT = 2
    JSON_ENSURE_ASCII = False


# Create configuration instances
isf_config = ISFConfig()
nist_csf_config = NISTCSFConfig()
mitre_config = MITREConfig()
mapping_config = MappingConfig()
export_config = ExportConfig()
