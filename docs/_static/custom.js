// Custom JavaScript for Cybersecurity Framework API Documentation

document.addEventListener('DOMContentLoaded', function() {
    
    // Initialize interactive features
    initializeInteractiveElements();
    initializeMermaidDiagrams();
    initializeCodeCopyButtons();
    initializeSearchEnhancements();
    initializeNavigationEnhancements();
    
});

/**
 * Initialize interactive elements
 */
function initializeInteractiveElements() {
    // Add click handlers for API endpoint cards
    const apiEndpoints = document.querySelectorAll('.api-endpoint');
    apiEndpoints.forEach(endpoint => {
        endpoint.addEventListener('click', function() {
            this.classList.toggle('expanded');
        });
    });
    
    // Add hover effects for grid cards
    const gridCards = document.querySelectorAll('.sd-card');
    gridCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-2px)';
            this.style.boxShadow = '0 4px 12px rgba(0, 0, 0, 0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0)';
            this.style.boxShadow = '';
        });
    });
}

/**
 * Initialize Mermaid diagrams with custom configuration
 */
function initializeMermaidDiagrams() {
    if (typeof mermaid !== 'undefined') {
        // Configure Mermaid for better interactivity
        mermaid.initialize({
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#1976d2',
                primaryTextColor: '#ffffff',
                primaryBorderColor: '#0d47a1',
                lineColor: '#424242',
                sectionBkgColor: '#e3f2fd',
                altSectionBkgColor: '#bbdefb',
                gridColor: '#9e9e9e',
                tertiaryColor: '#f5f5f5'
            },
            flowchart: {
                useMaxWidth: true,
                htmlLabels: true,
                curve: 'basis'
            },
            sequence: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                actorMargin: 50,
                width: 150,
                height: 65,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35,
                mirrorActors: true,
                bottomMarginAdj: 1,
                useMaxWidth: true,
                rightAngles: false,
                showSequenceNumbers: false
            },
            journey: {
                diagramMarginX: 50,
                diagramMarginY: 10,
                leftMargin: 150,
                width: 150,
                height: 50,
                boxMargin: 10,
                boxTextMargin: 5,
                noteMargin: 10,
                messageMargin: 35,
                bottomMarginAdj: 1,
                useMaxWidth: true
            }
        });
        
        // Add click handlers for Mermaid diagrams
        document.addEventListener('click', function(event) {
            if (event.target.closest('.mermaid')) {
                const diagram = event.target.closest('.mermaid');
                toggleDiagramFullscreen(diagram);
            }
        });
    }
}

/**
 * Toggle fullscreen mode for Mermaid diagrams
 */
function toggleDiagramFullscreen(diagram) {
    if (diagram.classList.contains('fullscreen')) {
        diagram.classList.remove('fullscreen');
        document.body.style.overflow = '';
    } else {
        diagram.classList.add('fullscreen');
        document.body.style.overflow = 'hidden';
    }
}

/**
 * Initialize enhanced code copy buttons
 */
function initializeCodeCopyButtons() {
    // Add copy buttons to code blocks
    const codeBlocks = document.querySelectorAll('.highlight');
    codeBlocks.forEach(block => {
        const copyButton = document.createElement('button');
        copyButton.className = 'copy-button';
        copyButton.innerHTML = '📋 Copy';
        copyButton.title = 'Copy code to clipboard';
        
        copyButton.addEventListener('click', function() {
            const code = block.querySelector('pre').textContent;
            navigator.clipboard.writeText(code).then(() => {
                copyButton.innerHTML = '✅ Copied!';
                copyButton.style.background = '#28a745';
                setTimeout(() => {
                    copyButton.innerHTML = '📋 Copy';
                    copyButton.style.background = '';
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy code: ', err);
                copyButton.innerHTML = '❌ Failed';
                setTimeout(() => {
                    copyButton.innerHTML = '📋 Copy';
                }, 2000);
            });
        });
        
        block.style.position = 'relative';
        block.appendChild(copyButton);
    });
}

/**
 * Initialize search enhancements
 */
function initializeSearchEnhancements() {
    const searchInput = document.querySelector('input[type="text"][name="q"]');
    if (searchInput) {
        // Add search suggestions
        searchInput.addEventListener('input', function() {
            const query = this.value.toLowerCase();
            if (query.length > 2) {
                showSearchSuggestions(query);
            } else {
                hideSearchSuggestions();
            }
        });
        
        // Add keyboard shortcuts
        document.addEventListener('keydown', function(event) {
            // Ctrl/Cmd + K to focus search
            if ((event.ctrlKey || event.metaKey) && event.key === 'k') {
                event.preventDefault();
                searchInput.focus();
            }
            
            // Escape to clear search
            if (event.key === 'Escape' && document.activeElement === searchInput) {
                searchInput.value = '';
                hideSearchSuggestions();
            }
        });
    }
}

/**
 * Show search suggestions
 */
function showSearchSuggestions(query) {
    const suggestions = [
        'API endpoints',
        'Authentication',
        'ISF framework',
        'NIST CSF',
        'Mapping suggestions',
        'BDD scenarios',
        'Error handling',
        'Rate limiting'
    ].filter(item => item.toLowerCase().includes(query));
    
    if (suggestions.length > 0) {
        let suggestionBox = document.querySelector('.search-suggestions');
        if (!suggestionBox) {
            suggestionBox = document.createElement('div');
            suggestionBox.className = 'search-suggestions';
            suggestionBox.style.cssText = `
                position: absolute;
                top: 100%;
                left: 0;
                right: 0;
                background: white;
                border: 1px solid #ddd;
                border-top: none;
                border-radius: 0 0 4px 4px;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
                z-index: 1000;
                max-height: 200px;
                overflow-y: auto;
            `;
            document.querySelector('.wy-side-nav-search').appendChild(suggestionBox);
        }
        
        suggestionBox.innerHTML = suggestions.map(suggestion => 
            `<div class="suggestion-item" style="padding: 8px 12px; cursor: pointer; border-bottom: 1px solid #eee;">
                ${suggestion}
            </div>`
        ).join('');
        
        // Add click handlers for suggestions
        suggestionBox.querySelectorAll('.suggestion-item').forEach(item => {
            item.addEventListener('click', function() {
                document.querySelector('input[type="text"][name="q"]').value = this.textContent;
                hideSearchSuggestions();
            });
        });
    }
}

/**
 * Hide search suggestions
 */
function hideSearchSuggestions() {
    const suggestionBox = document.querySelector('.search-suggestions');
    if (suggestionBox) {
        suggestionBox.remove();
    }
}

/**
 * Initialize navigation enhancements
 */
function initializeNavigationEnhancements() {
    // Add smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Add back to top button
    const backToTopButton = document.createElement('button');
    backToTopButton.innerHTML = '↑ Top';
    backToTopButton.className = 'back-to-top';
    backToTopButton.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background: #1976d2;
        color: white;
        border: none;
        border-radius: 50px;
        padding: 10px 15px;
        cursor: pointer;
        display: none;
        z-index: 1000;
        transition: all 0.3s ease;
    `;
    
    backToTopButton.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
    
    document.body.appendChild(backToTopButton);
    
    // Show/hide back to top button based on scroll position
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backToTopButton.style.display = 'block';
        } else {
            backToTopButton.style.display = 'none';
        }
    });
    
    // Add progress indicator
    const progressBar = document.createElement('div');
    progressBar.className = 'reading-progress';
    progressBar.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 0%;
        height: 3px;
        background: #1976d2;
        z-index: 1001;
        transition: width 0.3s ease;
    `;
    document.body.appendChild(progressBar);
    
    // Update progress indicator on scroll
    window.addEventListener('scroll', function() {
        const scrollTop = window.pageYOffset;
        const docHeight = document.body.scrollHeight - window.innerHeight;
        const scrollPercent = (scrollTop / docHeight) * 100;
        progressBar.style.width = scrollPercent + '%';
    });
}

/**
 * Initialize theme toggle functionality
 */
function initializeThemeToggle() {
    const themeToggle = document.createElement('button');
    themeToggle.innerHTML = '🌙';
    themeToggle.className = 'theme-toggle';
    themeToggle.title = 'Toggle dark mode';
    themeToggle.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: transparent;
        border: 2px solid #1976d2;
        border-radius: 50%;
        width: 40px;
        height: 40px;
        cursor: pointer;
        font-size: 16px;
        z-index: 1000;
        transition: all 0.3s ease;
    `;
    
    themeToggle.addEventListener('click', function() {
        document.body.classList.toggle('dark-theme');
        const isDark = document.body.classList.contains('dark-theme');
        this.innerHTML = isDark ? '☀️' : '🌙';
        localStorage.setItem('theme', isDark ? 'dark' : 'light');
    });
    
    // Load saved theme
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme === 'dark') {
        document.body.classList.add('dark-theme');
        themeToggle.innerHTML = '☀️';
    }
    
    document.body.appendChild(themeToggle);
}

/**
 * Initialize print functionality
 */
function initializePrintFunctionality() {
    const printButton = document.createElement('button');
    printButton.innerHTML = '🖨️ Print';
    printButton.className = 'print-button';
    printButton.style.cssText = `
        position: fixed;
        bottom: 80px;
        right: 20px;
        background: #28a745;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 8px 12px;
        cursor: pointer;
        font-size: 12px;
        z-index: 1000;
    `;
    
    printButton.addEventListener('click', function() {
        window.print();
    });
    
    document.body.appendChild(printButton);
}

/**
 * Initialize accessibility features
 */
function initializeAccessibilityFeatures() {
    // Add skip to content link
    const skipLink = document.createElement('a');
    skipLink.href = '#main-content';
    skipLink.textContent = 'Skip to main content';
    skipLink.className = 'skip-link';
    skipLink.style.cssText = `
        position: absolute;
        top: -40px;
        left: 6px;
        background: #000;
        color: #fff;
        padding: 8px;
        text-decoration: none;
        z-index: 1002;
        transition: top 0.3s;
    `;
    
    skipLink.addEventListener('focus', function() {
        this.style.top = '6px';
    });
    
    skipLink.addEventListener('blur', function() {
        this.style.top = '-40px';
    });
    
    document.body.insertBefore(skipLink, document.body.firstChild);
    
    // Add main content landmark
    const mainContent = document.querySelector('.wy-nav-content');
    if (mainContent) {
        mainContent.id = 'main-content';
        mainContent.setAttribute('role', 'main');
    }
}

// Initialize additional features when DOM is ready
document.addEventListener('DOMContentLoaded', function() {
    initializeThemeToggle();
    initializePrintFunctionality();
    initializeAccessibilityFeatures();
});

// Add CSS for fullscreen Mermaid diagrams
const style = document.createElement('style');
style.textContent = `
    .mermaid.fullscreen {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        background: rgba(255, 255, 255, 0.95);
        z-index: 9999;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;
    }
    
    .copy-button {
        position: absolute;
        top: 8px;
        right: 8px;
        background: #007bff;
        color: white;
        border: none;
        border-radius: 4px;
        padding: 4px 8px;
        font-size: 12px;
        cursor: pointer;
        opacity: 0;
        transition: opacity 0.3s ease;
    }
    
    .highlight:hover .copy-button {
        opacity: 1;
    }
    
    .dark-theme {
        filter: invert(1) hue-rotate(180deg);
    }
    
    .dark-theme img,
    .dark-theme .mermaid {
        filter: invert(1) hue-rotate(180deg);
    }
`;
document.head.appendChild(style);
