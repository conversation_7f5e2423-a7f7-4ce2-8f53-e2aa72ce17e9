/* Custom CSS for Cybersecurity Framework API Documentation */

/* API Endpoint Styling */
.api-endpoint {
    border: 1px solid #ddd;
    border-radius: 8px;
    margin: 1em 0;
    padding: 1em;
    background: #f8f9fa;
}

.api-endpoint .http-method {
    font-weight: bold;
    padding: 0.2em 0.5em;
    border-radius: 4px;
    margin-right: 0.5em;
    color: white;
    text-transform: uppercase;
}

.api-endpoint .http-method.get {
    background-color: #28a745;
}

.api-endpoint .http-method.post {
    background-color: #007bff;
}

.api-endpoint .http-method.put {
    background-color: #ffc107;
    color: #212529;
}

.api-endpoint .http-method.delete {
    background-color: #dc3545;
}

.api-endpoint .http-method.patch {
    background-color: #6f42c1;
}

.api-endpoint .api-path {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    background: #e9ecef;
    padding: 0.2em 0.5em;
    border-radius: 4px;
    font-size: 0.9em;
}

.api-endpoint .label {
    font-weight: bold;
    color: #495057;
}

.api-endpoint .status-code {
    background: #28a745;
    color: white;
    padding: 0.1em 0.3em;
    border-radius: 3px;
    font-size: 0.8em;
}

.api-endpoint .auth-required {
    background: #dc3545;
    color: white;
    padding: 0.1em 0.3em;
    border-radius: 3px;
    font-size: 0.8em;
}

.api-endpoint .user-role {
    background: #6f42c1;
    color: white;
    padding: 0.1em 0.3em;
    border-radius: 3px;
    font-size: 0.8em;
}

/* Mermaid Diagram Styling */
.mermaid {
    text-align: center;
    margin: 2em 0;
}

/* Code Block Enhancements */
.highlight {
    border-radius: 8px;
    overflow: hidden;
}

.highlight pre {
    padding: 1em;
    margin: 0;
}

/* Grid Card Enhancements */
.sd-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.sd-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Tab Styling */
.sphinx-tabs-tab {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-bottom: none;
    padding: 0.5em 1em;
    cursor: pointer;
    transition: background-color 0.2s;
}

.sphinx-tabs-tab:hover {
    background: #e9ecef;
}

.sphinx-tabs-tab[aria-selected="true"] {
    background: #007bff;
    color: white;
}

.sphinx-tabs-panel {
    border: 1px solid #dee2e6;
    padding: 1em;
    background: white;
}

/* Table Styling */
.wy-table-responsive table td,
.wy-table-responsive table th {
    white-space: normal;
}

.wy-table-responsive table th {
    background: #f8f9fa;
    font-weight: bold;
}

/* Admonition Styling */
.admonition {
    border-radius: 8px;
    border-left: 4px solid;
    margin: 1em 0;
    padding: 1em;
}

.admonition.note {
    border-left-color: #007bff;
    background: #e7f3ff;
}

.admonition.warning {
    border-left-color: #ffc107;
    background: #fff8e1;
}

.admonition.important {
    border-left-color: #dc3545;
    background: #ffebee;
}

.admonition.tip {
    border-left-color: #28a745;
    background: #e8f5e8;
}

/* Navigation Enhancements */
.wy-nav-side {
    background: #2c3e50;
}

.wy-menu-vertical a {
    color: #ecf0f1;
}

.wy-menu-vertical a:hover {
    background: #34495e;
    color: #3498db;
}

/* Search Box Styling */
.wy-side-nav-search {
    background: #1976d2;
}

.wy-side-nav-search input[type=text] {
    border-radius: 20px;
    border: none;
    padding: 0.5em 1em;
}

/* Footer Styling */
.rst-footer-buttons {
    margin-top: 2em;
    padding-top: 1em;
    border-top: 1px solid #dee2e6;
}

/* Responsive Design */
@media (max-width: 768px) {
    .api-endpoint .http-method,
    .api-endpoint .api-path {
        display: block;
        margin: 0.2em 0;
    }
    
    .mermaid {
        overflow-x: auto;
    }
}

/* Print Styles */
@media print {
    .wy-nav-side,
    .wy-nav-content-wrap .wy-nav-top,
    .rst-footer-buttons {
        display: none;
    }
    
    .wy-nav-content {
        margin-left: 0;
    }
    
    .mermaid {
        page-break-inside: avoid;
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .api-endpoint {
        background: #2d3748;
        border-color: #4a5568;
        color: #e2e8f0;
    }
    
    .api-endpoint .api-path {
        background: #4a5568;
        color: #e2e8f0;
    }
    
    .sphinx-tabs-panel {
        background: #2d3748;
        color: #e2e8f0;
        border-color: #4a5568;
    }
}

/* Animation for Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Syntax Highlighting Enhancements */
.highlight .hll { background-color: #ffffcc }
.highlight .c { color: #408080; font-style: italic } /* Comment */
.highlight .err { border: 1px solid #FF0000 } /* Error */
.highlight .k { color: #008000; font-weight: bold } /* Keyword */
.highlight .o { color: #666666 } /* Operator */
.highlight .ch { color: #408080; font-style: italic } /* Comment.Hashbang */
.highlight .cm { color: #408080; font-style: italic } /* Comment.Multiline */
.highlight .cp { color: #BC7A00 } /* Comment.Preproc */
.highlight .cpf { color: #408080; font-style: italic } /* Comment.PreprocFile */
.highlight .c1 { color: #408080; font-style: italic } /* Comment.Single */
.highlight .cs { color: #408080; font-style: italic } /* Comment.Special */
.highlight .gd { color: #A00000 } /* Generic.Deleted */
.highlight .ge { font-style: italic } /* Generic.Emph */
.highlight .gr { color: #FF0000 } /* Generic.Error */
.highlight .gh { color: #000080; font-weight: bold } /* Generic.Heading */
.highlight .gi { color: #00A000 } /* Generic.Inserted */
.highlight .go { color: #888888 } /* Generic.Output */
.highlight .gp { color: #000080; font-weight: bold } /* Generic.Prompt */
.highlight .gs { font-weight: bold } /* Generic.Strong */
.highlight .gu { color: #800080; font-weight: bold } /* Generic.Subheading */
.highlight .gt { color: #0044DD } /* Generic.Traceback */
