# Mermaid Workflow Diagrams

## Overview

This document contains interactive Mermaid diagrams that visualize the user journey flows, decision points, and system interactions for each user story in the Cybersecurity Framework Management API.

---

## Epic 1: Framework Data Management

### Workflow 1.1: ISF Framework Import Process

```mermaid
flowchart TD
    A[User Starts Import] --> B{Authentication Check}
    B -->|Failed| C[Return 401 Unauthorized]
    B -->|Success| D{Admin Role Check}
    D -->|Not Admin| E[Return 403 Forbidden]
    D -->|Admin| F[Receive Import Data]
    
    F --> G{Data Format Check}
    G -->|JSON| H[Parse JSON Structure]
    G -->|CSV| I[Parse CSV Structure]
    G -->|Invalid| J[Return Format Error]
    
    H --> K[Validate JSON Schema]
    I --> L[Validate CSV Headers]
    
    K --> M{Validation Result}
    L --> M
    M -->|Failed| N[Return Validation Errors]
    M -->|Success| O{File Size Check}
    
    O -->|Large File| P[Start Async Import]
    O -->|Small File| Q[Start Sync Import]
    
    P --> R[Create Background Task]
    R --> S[Return Task ID]
    S --> T[Process in Background]
    
    Q --> U[Begin Transaction]
    T --> U
    
    U --> V{Version Conflict Check}
    V -->|Conflict & No Replace| W[Return Conflict Error]
    V -->|No Conflict or Replace| X[Create ISF Version]
    
    X --> Y[Import Security Areas]
    Y --> Z[Import Controls]
    Z --> AA{Import Success}
    
    AA -->|Failed| BB[Rollback Transaction]
    BB --> CC[Return Error Details]
    AA -->|Success| DD[Commit Transaction]
    DD --> EE[Log Import Activity]
    EE --> FF[Return Success Response]
    
    style A fill:#e1f5fe
    style FF fill:#c8e6c9
    style C fill:#ffcdd2
    style E fill:#ffcdd2
    style J fill:#ffcdd2
    style N fill:#ffcdd2
    style W fill:#ffcdd2
    style CC fill:#ffcdd2
```

### Workflow 1.2: Framework Export Process

```mermaid
flowchart TD
    A[User Requests Export] --> B{Authentication Check}
    B -->|Failed| C[Return 401 Unauthorized]
    B -->|Success| D[Parse Export Parameters]
    
    D --> E{Format Validation}
    E -->|Invalid| F[Return Format Error]
    E -->|Valid| G[Apply Filters]
    
    G --> H{Data Size Check}
    H -->|Large Dataset| I[Use Streaming Export]
    H -->|Small Dataset| J[Use Direct Export]
    
    I --> K[Initialize Stream]
    K --> L[Stream Data Chunks]
    L --> M[Return Streaming Response]
    
    J --> N[Query Database]
    N --> O[Format Data]
    O --> P{Export Format}
    
    P -->|JSON| Q[Generate JSON]
    P -->|CSV| R[Generate CSV]
    P -->|XML| S[Generate XML]
    P -->|STIX| T[Generate STIX]
    
    Q --> U[Add Metadata]
    R --> U
    S --> U
    T --> U
    
    U --> V[Create Download Link]
    V --> W[Log Export Activity]
    W --> X[Return Export Response]
    
    style A fill:#e1f5fe
    style X fill:#c8e6c9
    style M fill:#c8e6c9
    style C fill:#ffcdd2
    style F fill:#ffcdd2
```

---

## Epic 2: Intelligent Mapping Operations

### Workflow 2.1: MITRE to ISF Mapping Suggestions

```mermaid
flowchart TD
    A[Analyst Requests Mappings] --> B{Authentication Check}
    B -->|Failed| C[Return 401 Unauthorized]
    B -->|Success| D[Validate Technique ID]
    
    D --> E{Technique Exists}
    E -->|No| F[Return Technique Not Found]
    E -->|Yes| G[Load Technique Data]
    
    G --> H[Load ISF Controls]
    H --> I[Initialize Mapping Algorithm]
    
    I --> J[Calculate Semantic Similarity]
    J --> K[Analyze Keyword Overlap]
    K --> L[Assess Effectiveness Factors]
    
    L --> M[Generate Suggestions]
    M --> N{Confidence Threshold}
    N -->|Below Threshold| O[Add Low Confidence Warning]
    N -->|Above Threshold| P[Rank by Confidence]
    
    O --> Q[Suggest Alternatives]
    P --> R[Apply User Filters]
    
    R --> S{Bulk Request}
    S -->|Yes| T[Process Multiple Techniques]
    S -->|No| U[Format Single Response]
    
    T --> V[Aggregate Results]
    V --> W[Generate Summary Stats]
    W --> X[Return Bulk Response]
    
    U --> Y[Add Implementation Guidance]
    Y --> Z[Return Mapping Suggestions]
    
    Q --> AA[Return Low Confidence Response]
    
    style A fill:#e1f5fe
    style Z fill:#c8e6c9
    style X fill:#c8e6c9
    style AA fill:#fff3e0
    style C fill:#ffcdd2
    style F fill:#ffcdd2
```

### Workflow 2.2: Mapping Validation and Quality Assessment

```mermaid
flowchart TD
    A[Architect Reviews Mappings] --> B{Authentication Check}
    B -->|Failed| C[Return 401 Unauthorized]
    B -->|Success| D[Load Pending Mappings]
    
    D --> E{Mappings Available}
    E -->|No| F[Return No Mappings Message]
    E -->|Yes| G[Display Mapping Details]
    
    G --> H[Show Confidence Scores]
    H --> I[Show Effectiveness Analysis]
    I --> J[Show Supporting Evidence]
    
    J --> K{Architect Decision}
    K -->|Approve| L[Update Mapping Status]
    K -->|Reject| M[Add Rejection Reason]
    K -->|Modify| N[Update Mapping Details]
    
    L --> O[Increase Confidence Score]
    M --> P[Decrease Confidence Score]
    N --> Q[Recalculate Scores]
    
    O --> R[Notify Original Analyst]
    P --> R
    Q --> R
    
    R --> S[Update Audit Trail]
    S --> T[Save Validation Results]
    T --> U{More Mappings}
    
    U -->|Yes| G
    U -->|No| V[Generate Validation Report]
    V --> W[Return Validation Summary]
    
    style A fill:#e1f5fe
    style W fill:#c8e6c9
    style F fill:#fff3e0
    style C fill:#ffcdd2
```

---

## Epic 3: Framework Migration and Version Management

### Workflow 3.1: NIST CSF 1.1 to 2.0 Migration

```mermaid
flowchart TD
    A[Admin Initiates Migration] --> B{Authentication Check}
    B -->|Failed| C[Return 401 Unauthorized]
    B -->|Success| D{Admin Role Check}
    D -->|Not Admin| E[Return 403 Forbidden]
    D -->|Admin| F[Validate Source Version]
    
    F --> G{Version 1.1 Exists}
    G -->|No| H[Return Source Not Found]
    G -->|Yes| I[Check Migration Compatibility]
    
    I --> J{Compatible}
    J -->|No| K[Return Compatibility Errors]
    J -->|Yes| L[Begin Migration Process]
    
    L --> M[Create Backup of 1.1 Data]
    M --> N[Initialize Migration Tracker]
    N --> O[Add New Govern Function]
    
    O --> P[Migrate Existing Functions]
    P --> Q[Update Subcategory IDs]
    Q --> R[Map ID Formats]
    
    R --> S{Mapping Conflicts}
    S -->|Yes| T[Identify Conflicts]
    S -->|No| U[Update Cross-References]
    
    T --> V[Generate Conflict Report]
    V --> W[Provide Resolution Options]
    W --> X{Manual Review Required}
    
    X -->|Yes| Y[Pause for Manual Review]
    X -->|No| Z[Auto-Resolve Conflicts]
    
    Y --> AA[Wait for Admin Input]
    AA --> BB[Apply Manual Resolutions]
    BB --> U
    Z --> U
    
    U --> CC[Validate Migration Results]
    CC --> DD{Validation Success}
    
    DD -->|Failed| EE[Rollback Migration]
    EE --> FF[Return Migration Errors]
    DD -->|Success| GG[Commit Migration]
    GG --> HH[Generate Migration Report]
    HH --> II[Return Migration Success]
    
    style A fill:#e1f5fe
    style II fill:#c8e6c9
    style C fill:#ffcdd2
    style E fill:#ffcdd2
    style H fill:#ffcdd2
    style K fill:#ffcdd2
    style FF fill:#ffcdd2
    style Y fill:#fff3e0
```

### Workflow 3.2: Framework Version Comparison

```mermaid
flowchart TD
    A[User Requests Comparison] --> B{Authentication Check}
    B -->|Failed| C[Return 401 Unauthorized]
    B -->|Success| D[Parse Version Parameters]
    
    D --> E{Both Versions Exist}
    E -->|No| F[Return Version Not Found]
    E -->|Yes| G[Load Source Version Data]
    
    G --> H[Load Target Version Data]
    H --> I[Initialize Comparison Engine]
    
    I --> J[Compare Functions]
    J --> K[Compare Categories]
    K --> L[Compare Subcategories]
    
    L --> M[Identify Added Items]
    M --> N[Identify Removed Items]
    N --> O[Identify Modified Items]
    O --> P[Identify Unchanged Items]
    
    P --> Q[Analyze Mapping Impact]
    Q --> R[Calculate Change Statistics]
    R --> S[Generate Change Summary]
    
    S --> T{Export Format}
    T -->|JSON| U[Format as JSON]
    T -->|Report| V[Generate HTML Report]
    T -->|CSV| W[Format as CSV]
    
    U --> X[Return Comparison Data]
    V --> X
    W --> X
    
    style A fill:#e1f5fe
    style X fill:#c8e6c9
    style C fill:#ffcdd2
    style F fill:#ffcdd2
```

---

## Epic 4: Advanced Analytics and Reporting

### Workflow 4.1: Coverage Analysis Generation

```mermaid
flowchart TD
    A[Architect Requests Analysis] --> B{Authentication Check}
    B -->|Failed| C[Return 401 Unauthorized]
    B -->|Success| D[Parse Analysis Parameters]
    
    D --> E{Analysis Scope}
    E -->|All Tactics| F[Load All MITRE Tactics]
    E -->|Specific Tactics| G[Load Selected Tactics]
    E -->|Threat Actor| H[Load Actor Techniques]
    
    F --> I[Load All Mappings]
    G --> I
    H --> I
    
    I --> J[Calculate Coverage by Tactic]
    J --> K[Identify Unmapped Techniques]
    K --> L[Identify Over-mapped Techniques]
    
    L --> M[Calculate Coverage Percentages]
    M --> N[Generate Gap Analysis]
    N --> O[Prioritize Recommendations]
    
    O --> P{Report Type}
    P -->|Executive| Q[Generate Executive Summary]
    P -->|Technical| R[Generate Technical Report]
    P -->|Dashboard| S[Generate Dashboard Data]
    
    Q --> T[Add Key Metrics]
    R --> U[Add Detailed Analysis]
    S --> V[Add Interactive Elements]
    
    T --> W[Format Executive Report]
    U --> X[Format Technical Report]
    V --> Y[Format Dashboard JSON]
    
    W --> Z[Return Analysis Results]
    X --> Z
    Y --> Z
    
    style A fill:#e1f5fe
    style Z fill:#c8e6c9
    style C fill:#ffcdd2
```

### Workflow 4.2: Effectiveness Analysis and Optimization

```mermaid
flowchart TD
    A[Analyst Requests Effectiveness Analysis] --> B{Authentication Check}
    B -->|Failed| C[Return 401 Unauthorized]
    B -->|Success| D[Load Current Mappings]
    
    D --> E[Initialize Effectiveness Engine]
    E --> F[Analyze Implementation Complexity]
    F --> G[Assess Cost-Effectiveness]
    G --> H[Evaluate Technical Feasibility]
    H --> I[Measure Organizational Impact]
    I --> J[Calculate Coverage Completeness]
    
    J --> K[Compute Weighted Scores]
    K --> L[Identify Low-Performing Mappings]
    L --> M[Generate Optimization Suggestions]
    
    M --> N{Analysis Depth}
    N -->|Summary| O[Create High-Level Overview]
    N -->|Detailed| P[Create Detailed Analysis]
    N -->|Actionable| Q[Create Action Plan]
    
    O --> R[Format Summary Report]
    P --> S[Format Detailed Report]
    Q --> T[Format Action Plan]
    
    R --> U[Add Visualization Data]
    S --> U
    T --> U
    
    U --> V[Return Effectiveness Analysis]
    
    style A fill:#e1f5fe
    style V fill:#c8e6c9
    style C fill:#ffcdd2
```

---

## Epic 5: Error Handling and Recovery

### Workflow 5.1: Import Failure Recovery

```mermaid
flowchart TD
    A[Import Process Starts] --> B[Begin Transaction]
    B --> C[Process Data Chunks]
    C --> D{Processing Error}
    
    D -->|No Error| E[Continue Processing]
    E --> F{More Data}
    F -->|Yes| C
    F -->|No| G[Commit Transaction]
    G --> H[Return Success]
    
    D -->|Error Detected| I[Stop Processing]
    I --> J[Rollback Transaction]
    J --> K[Preserve Valid Data]
    K --> L[Analyze Error Details]
    
    L --> M[Generate Error Report]
    M --> N[Suggest Fixes]
    N --> O{Error Type}
    
    O -->|Data Format| P[Provide Format Examples]
    O -->|Validation| Q[Highlight Invalid Fields]
    O -->|System| R[Log Technical Details]
    
    P --> S[Offer Resume Option]
    Q --> S
    R --> T[Escalate to Admin]
    
    S --> U[Return Recovery Options]
    T --> V[Return System Error]
    
    style A fill:#e1f5fe
    style H fill:#c8e6c9
    style U fill:#fff3e0
    style V fill:#ffcdd2
```

### Workflow 5.2: Concurrent Operation Handling

```mermaid
flowchart TD
    A[Multiple Users Active] --> B[User A Starts Operation]
    A --> C[User B Starts Operation]
    
    B --> D[Acquire Resource Lock]
    C --> E[Attempt Resource Lock]
    
    D --> F[Process User A Request]
    E --> G{Lock Available}
    
    G -->|No| H[Queue User B Request]
    G -->|Yes| I[Process User B Request]
    
    F --> J{Operation Type}
    J -->|Read| K[Allow Concurrent Read]
    J -->|Write| L[Serialize Write Operations]
    
    H --> M[Notify User B of Queue]
    M --> N[Wait for Lock Release]
    
    K --> O[Return Results to A]
    L --> P[Complete Write Operation]
    P --> Q[Release Lock]
    
    Q --> R[Process Queued Request]
    R --> S[Return Results to B]
    
    N --> T{Timeout Reached}
    T -->|No| R
    T -->|Yes| U[Return Timeout Error]
    
    I --> V[Process Concurrent Operation]
    V --> W[Return Results to B]
    
    style A fill:#e1f5fe
    style O fill:#c8e6c9
    style S fill:#c8e6c9
    style W fill:#c8e6c9
    style U fill:#ffcdd2
```

---

## User Journey Overview

### Complete User Journey: From Import to Analysis

```mermaid
journey
    title Security Analyst Complete Workflow
    section Framework Setup
      Login to System: 5: Analyst
      Import ISF Data: 4: Analyst
      Validate Import: 5: Analyst
    section Mapping Operations
      Select MITRE Technique: 5: Analyst
      Request Mapping Suggestions: 4: Analyst
      Review Suggestions: 3: Analyst
      Create Mappings: 4: Analyst
    section Quality Assurance
      Submit for Review: 4: Analyst
      Architect Reviews: 5: Architect
      Receive Feedback: 3: Analyst
      Update Mappings: 4: Analyst
    section Analysis & Reporting
      Generate Coverage Report: 5: Analyst
      Analyze Effectiveness: 4: Analyst
      Export Results: 5: Analyst
      Share with Team: 5: Analyst
```

---

## System State Diagrams

### Mapping Lifecycle State Machine

```mermaid
stateDiagram-v2
    [*] --> Draft: Create Mapping
    Draft --> PendingReview: Submit for Review
    Draft --> Draft: Edit Mapping
    PendingReview --> Approved: Architect Approves
    PendingReview --> Rejected: Architect Rejects
    PendingReview --> Draft: Return for Revision
    Rejected --> Draft: Revise and Resubmit
    Approved --> Active: Deploy to Production
    Active --> Deprecated: Mark as Deprecated
    Active --> Active: Update Mapping
    Deprecated --> [*]: Archive Mapping
    
    Draft: confidence < 0.7
    PendingReview: awaiting validation
    Approved: confidence >= 0.8
    Rejected: validation failed
    Active: in production use
    Deprecated: superseded
```

---

## Integration Flow Diagrams

### External System Integration

```mermaid
sequenceDiagram
    participant User
    participant API
    participant Database
    participant MITRE_API
    participant NIST_API
    participant Cache
    
    User->>API: Request Mapping Suggestions
    API->>Cache: Check for Cached Data
    Cache-->>API: Cache Miss
    
    API->>MITRE_API: Fetch Technique Details
    MITRE_API-->>API: Technique Data
    
    API->>Database: Load Framework Controls
    Database-->>API: Control Data
    
    API->>API: Run Mapping Algorithm
    API->>Cache: Store Results
    API-->>User: Return Suggestions
    
    Note over User,Cache: Subsequent requests use cached data
    
    User->>API: Request Same Technique
    API->>Cache: Check Cache
    Cache-->>API: Cache Hit
    API-->>User: Return Cached Results
```

This comprehensive set of Mermaid diagrams provides visual representations of all major user workflows, system interactions, and decision points in the Cybersecurity Framework Management API.
