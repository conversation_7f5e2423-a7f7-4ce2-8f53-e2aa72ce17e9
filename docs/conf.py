# Configuration file for the Sphinx documentation builder.
#
# For the full list of built-in configuration values, see the documentation:
# https://www.sphinx-doc.org/en/master/usage/configuration.html

# -- Project information -----------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#project-information

project = 'Cybersecurity Framework Management API'
copyright = '2024, Security Team'
author = 'Security Team'
release = '1.0.0'

# -- General configuration ---------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#general-configuration

extensions = [
    'sphinx.ext.autodoc',
    'sphinx.ext.viewcode',
    'sphinx.ext.napoleon',
    'sphinx.ext.intersphinx',
    'sphinx.ext.todo',
    'sphinx.ext.coverage',
    'sphinx.ext.ifconfig',
    'sphinx.ext.githubpages',
    'sphinxcontrib.mermaid',
    'myst_parser',
    'sphinx_copybutton',
    'sphinx_tabs.tabs',
    'sphinx_design',
]

# Mermaid configuration
mermaid_version = "10.6.1"
mermaid_init_js = """
mermaid.initialize({
    startOnLoad: true,
    theme: 'default',
    themeVariables: {
        primaryColor: '#1976d2',
        primaryTextColor: '#ffffff',
        primaryBorderColor: '#0d47a1',
        lineColor: '#424242',
        sectionBkgColor: '#e3f2fd',
        altSectionBkgColor: '#bbdefb',
        gridColor: '#9e9e9e',
        tertiaryColor: '#f5f5f5'
    },
    flowchart: {
        useMaxWidth: true,
        htmlLabels: true,
        curve: 'basis'
    },
    sequence: {
        diagramMarginX: 50,
        diagramMarginY: 10,
        actorMargin: 50,
        width: 150,
        height: 65,
        boxMargin: 10,
        boxTextMargin: 5,
        noteMargin: 10,
        messageMargin: 35,
        mirrorActors: true,
        bottomMarginAdj: 1,
        useMaxWidth: true,
        rightAngles: false,
        showSequenceNumbers: false
    },
    journey: {
        diagramMarginX: 50,
        diagramMarginY: 10,
        leftMargin: 150,
        width: 150,
        height: 50,
        boxMargin: 10,
        boxTextMargin: 5,
        noteMargin: 10,
        messageMargin: 35,
        bottomMarginAdj: 1,
        useMaxWidth: true
    }
});
"""

# MyST Parser configuration
myst_enable_extensions = [
    "amsmath",
    "colon_fence",
    "deflist",
    "dollarmath",
    "fieldlist",
    "html_admonition",
    "html_image",
    "linkify",
    "replacements",
    "smartquotes",
    "strikethrough",
    "substitution",
    "tasklist",
]

# Source file suffixes
source_suffix = {
    '.rst': None,
    '.md': 'myst_parser',
}

templates_path = ['_templates']
exclude_patterns = ['_build', 'Thumbs.db', '.DS_Store']

# -- Options for HTML output -------------------------------------------------
# https://www.sphinx-doc.org/en/master/usage/configuration.html#options-for-html-output

html_theme = 'sphinx_rtd_theme'
html_static_path = ['_static']

# Theme options
html_theme_options = {
    'analytics_id': '',
    'analytics_anonymize_ip': False,
    'logo_only': False,
    'display_version': True,
    'prev_next_buttons_location': 'bottom',
    'style_external_links': False,
    'vcs_pageview_mode': '',
    'style_nav_header_background': '#1976d2',
    'collapse_navigation': False,
    'sticky_navigation': True,
    'navigation_depth': 4,
    'includehidden': True,
    'titles_only': False
}

# Custom CSS
html_css_files = [
    'custom.css',
]

# Custom JavaScript
html_js_files = [
    'custom.js',
]

# Logo and favicon
html_logo = '_static/logo.png'
html_favicon = '_static/favicon.ico'

# -- Extension configuration -------------------------------------------------

# Napoleon settings
napoleon_google_docstring = True
napoleon_numpy_docstring = True
napoleon_include_init_with_doc = False
napoleon_include_private_with_doc = False
napoleon_include_special_with_doc = True
napoleon_use_admonition_for_examples = False
napoleon_use_admonition_for_notes = False
napoleon_use_admonition_for_references = False
napoleon_use_ivar = False
napoleon_use_param = True
napoleon_use_rtype = True
napoleon_preprocess_types = False
napoleon_type_aliases = None
napoleon_attr_annotations = True

# Autodoc settings
autodoc_default_options = {
    'members': True,
    'member-order': 'bysource',
    'special-members': '__init__',
    'undoc-members': True,
    'exclude-members': '__weakref__'
}

# Intersphinx mapping
intersphinx_mapping = {
    'python': ('https://docs.python.org/3/', None),
    'fastapi': ('https://fastapi.tiangolo.com/', None),
    'sqlalchemy': ('https://docs.sqlalchemy.org/en/20/', None),
    'pydantic': ('https://docs.pydantic.dev/latest/', None),
}

# Todo extension
todo_include_todos = True

# Copy button configuration
copybutton_prompt_text = r">>> |\.\.\. |\$ |In \[\d*\]: | {2,5}\.\.\.: | {5,8}: "
copybutton_prompt_is_regexp = True
copybutton_only_copy_prompt_lines = True
copybutton_remove_prompts = True

# -- Custom configuration ---------------------------------------------------

# Add custom roles
rst_prolog = """
.. role:: api-endpoint
.. role:: http-method
.. role:: status-code
.. role:: user-role
"""

# HTML context for templates
html_context = {
    'display_github': True,
    'github_user': 'your-org',
    'github_repo': 'cybersecurity-framework-api',
    'github_version': 'main',
    'conf_py_path': '/docs/',
}

# -- Custom directives ------------------------------------------------------

def setup(app):
    """Custom Sphinx setup function."""
    app.add_css_file('custom.css')
    app.add_js_file('custom.js')
    
    # Add custom directives
    from sphinx.util.docutils import docutils_namespace
    with docutils_namespace():
        # Custom directive for API endpoints
        from docutils.parsers.rst import directives
        from docutils import nodes
        from sphinx.util.docutils import SphinxDirective
        
        class APIEndpointDirective(SphinxDirective):
            """Custom directive for documenting API endpoints."""
            has_content = True
            required_arguments = 2  # method and path
            optional_arguments = 0
            option_spec = {
                'status': directives.unchanged,
                'auth': directives.unchanged,
                'role': directives.unchanged,
            }
            
            def run(self):
                method = self.arguments[0].upper()
                path = self.arguments[1]
                
                # Create container
                container = nodes.container()
                container['classes'] = ['api-endpoint']
                
                # Add method and path
                title = nodes.paragraph()
                title += nodes.inline(text=method, classes=['http-method', method.lower()])
                title += nodes.inline(text=' ', classes=['spacer'])
                title += nodes.inline(text=path, classes=['api-path'])
                container += title
                
                # Add options
                if 'status' in self.options:
                    status_para = nodes.paragraph()
                    status_para += nodes.inline(text='Status: ', classes=['label'])
                    status_para += nodes.inline(text=self.options['status'], classes=['status-code'])
                    container += status_para
                
                if 'auth' in self.options:
                    auth_para = nodes.paragraph()
                    auth_para += nodes.inline(text='Authentication: ', classes=['label'])
                    auth_para += nodes.inline(text=self.options['auth'], classes=['auth-required'])
                    container += auth_para
                
                if 'role' in self.options:
                    role_para = nodes.paragraph()
                    role_para += nodes.inline(text='Required Role: ', classes=['label'])
                    role_para += nodes.inline(text=self.options['role'], classes=['user-role'])
                    container += role_para
                
                # Add content
                if self.content:
                    content_node = nodes.container()
                    content_node['classes'] = ['api-content']
                    self.state.nested_parse(self.content, self.content_offset, content_node)
                    container += content_node
                
                return [container]
        
        app.add_directive('api-endpoint', APIEndpointDirective)
    
    return {
        'version': '1.0',
        'parallel_read_safe': True,
        'parallel_write_safe': True,
    }
