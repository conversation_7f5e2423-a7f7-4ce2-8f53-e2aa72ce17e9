# Sphinx Documentation Requirements

# Core Sphinx
sphinx>=5.0.0
sphinx-rtd-theme>=1.0.0

# Mermaid diagram support
sphinxcontrib-mermaid>=0.7.1

# Markdown support
myst-parser>=0.18.0

# Enhanced features
sphinx-copybutton>=0.5.0
sphinx-tabs>=3.4.0
sphinx-design>=0.3.0
sphinx-autobuild>=2021.3.14

# Deployment
ghp-import>=2.1.0

# Additional extensions
sphinx-external-toc>=0.3.0
sphinx-togglebutton>=0.3.0
sphinx-inline-tabs>=2022.1.2b11

# Documentation testing
doc8>=0.11.0
rstcheck>=6.0.0

# Image optimization
Pillow>=9.0.0

# Spell checking
sphinxcontrib-spelling>=7.6.0
pyenchant>=3.2.0

# API documentation generation
sphinx-autoapi>=2.0.0

# Performance monitoring
sphinx-build-info>=0.1.0

# Additional themes (optional)
furo>=2022.6.21
sphinx-book-theme>=0.3.3
pydata-sphinx-theme>=0.9.0

# Development tools
pre-commit>=2.20.0
black>=22.0.0
isort>=5.10.0
flake8>=5.0.0

# Documentation analytics
sphinx-analytics>=0.2.0

# Enhanced search
sphinx-search>=0.1.0

# PDF generation
rst2pdf>=0.99
weasyprint>=56.0

# Live reload for development
livereload>=2.6.3

# Git integration
gitpython>=3.1.0

# YAML support for configuration
PyYAML>=6.0

# JSON schema validation
jsonschema>=4.0.0

# HTTP client for external API documentation
requests>=2.28.0

# Template engine
Jinja2>=3.1.0

# Markdown extensions
markdown-it-py>=2.1.0
mdit-py-plugins>=0.3.0

# Code highlighting
Pygments>=2.12.0

# Documentation linting
doc8>=0.11.0
restructuredtext-lint>=1.4.0

# Performance profiling
memory-profiler>=0.60.0
psutil>=5.9.0

# Internationalization
sphinx-intl>=2.0.0
Babel>=2.10.0

# Version management
packaging>=21.0
setuptools-scm>=7.0.0

# Security scanning
safety>=2.0.0
bandit>=1.7.0

# Code quality
mypy>=0.971
pylint>=2.14.0

# Testing framework
pytest>=7.0.0
pytest-cov>=3.0.0
pytest-xdist>=2.5.0

# Documentation coverage
interrogate>=1.5.0

# Dependency management
pip-tools>=6.8.0
pipdeptree>=2.2.0

# Environment management
python-dotenv>=0.20.0

# Logging
structlog>=22.1.0
colorlog>=6.6.0

# Configuration management
dynaconf>=3.1.0
click>=8.1.0

# File watching
watchdog>=2.1.0

# Network utilities
urllib3>=1.26.0
certifi>=2022.6.15

# Data validation
pydantic>=1.9.0
marshmallow>=3.17.0

# Caching
diskcache>=5.4.0
redis>=4.3.0

# Async support
aiohttp>=3.8.0
asyncio-mqtt>=0.11.0

# Database utilities (for documentation examples)
SQLAlchemy>=1.4.0
alembic>=1.8.0

# API documentation
fastapi>=0.78.0
uvicorn>=0.18.0

# Authentication examples
PyJWT>=2.4.0
passlib>=1.7.0

# Cryptography
cryptography>=37.0.0

# Date/time utilities
python-dateutil>=2.8.0
pytz>=2022.1

# String utilities
python-slugify>=6.1.0
Unidecode>=1.3.0

# File format support
openpyxl>=3.0.0
python-docx>=0.8.0
PyPDF2>=2.10.0

# Image processing
Pillow>=9.2.0
matplotlib>=3.5.0

# Data visualization for documentation
plotly>=5.9.0
seaborn>=0.11.0

# Scientific computing (for examples)
numpy>=1.23.0
pandas>=1.4.0

# Machine learning (for algorithm documentation)
scikit-learn>=1.1.0
scipy>=1.8.0

# Natural language processing (for semantic analysis examples)
nltk>=3.7.0
spacy>=3.4.0

# Web scraping (for external data examples)
beautifulsoup4>=4.11.0
lxml>=4.9.0

# HTTP mocking for testing
responses>=0.21.0
httpretty>=1.1.0

# Fixtures and test data
factory-boy>=3.2.0
faker>=13.15.0

# Performance benchmarking
pytest-benchmark>=3.4.0
locust>=2.10.0

# Documentation deployment
sphinx-external-toc>=0.3.0
sphinx-multitoc-numbering>=0.1.0

# Advanced formatting
sphinx-panels>=0.6.0
sphinx-prompt>=1.5.0
sphinx-substitution-extensions>=2022.2.16

# Code execution in docs
jupyter-sphinx>=0.4.0
nbsphinx>=0.8.0

# API reference generation
sphinx-jsonschema>=1.17.0
sphinx-openapi>=1.0.0

# Documentation versioning
sphinx-versions>=1.1.0
mike>=1.1.0

# Social media integration
sphinx-social-cards>=0.1.0

# SEO optimization
sphinx-sitemap>=2.2.0
sphinx-seo>=1.0.0

# Accessibility
sphinx-a11y>=1.0.0

# Documentation analytics
sphinx-plausible>=0.1.0
sphinx-google-analytics>=0.1.0
