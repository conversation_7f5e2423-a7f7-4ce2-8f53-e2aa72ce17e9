# 📚 Cybersecurity Framework API Documentation

Welcome to the comprehensive documentation for the Cybersecurity Framework Management API. This documentation provides detailed user guides, API references, and interactive workflows with Mermaid diagrams.

## 🎯 Documentation Overview

This documentation includes:

- **📖 User Guide** - Comprehensive BDD scenarios with interactive Mermaid diagrams
- **🔧 API Reference** - Complete endpoint documentation with examples
- **👩‍💻 Developer Guide** - Technical implementation details and architecture
- **🧪 Testing Guide** - BDD scenarios and testing methodologies
- **🚀 Quick Start** - Get up and running quickly

## 🏗️ Documentation Structure

```
docs/
├── index.rst                          # Main documentation index
├── user-guide.rst                     # Comprehensive user guide with Mermaid diagrams
├── conf.py                           # Sphinx configuration
├── Makefile                          # Build automation
├── requirements.txt                  # Documentation dependencies
├── user-stories/                    # BDD scenarios and workflows
│   ├── comprehensive_bdd_flows.md   # Main user stories
│   ├── mermaid_workflows.md         # Mermaid diagram definitions
│   └── advanced_bdd_scenarios.md    # Edge cases and error handling
├── _static/                         # Static assets
│   ├── custom.css                   # Custom styling
│   ├── custom.js                    # Interactive features
│   ├── logo.png                     # Documentation logo
│   └── favicon.ico                  # Site favicon
└── _templates/                      # Custom templates
```

## 🚀 Quick Start

### Prerequisites

- Python 3.8+
- pip or conda package manager

### Installation

1. **Clone the repository:**
   ```bash
   git clone <repository-url>
   cd cybersecurity-framework-api/docs
   ```

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Build the documentation:**
   ```bash
   make html
   ```

4. **Serve locally:**
   ```bash
   make serve
   ```

5. **Open in browser:**
   Navigate to `http://localhost:8080`

## 🔧 Building Documentation

### Available Make Targets

| Command | Description |
|---------|-------------|
| `make html` | Build HTML documentation |
| `make livehtml` | Build with live reload (auto-refresh) |
| `make pdf` | Build PDF documentation |
| `make epub` | Build EPUB documentation |
| `make clean` | Clean build directory |
| `make check` | Check for issues (links, spelling) |
| `make serve` | Serve documentation locally |
| `make all` | Build all formats |

### Development Workflow

For active development with auto-reload:

```bash
# Start live reload server
make livehtml

# In another terminal, edit files
# Documentation will auto-rebuild and refresh
```

## 📊 Features

### 🎨 Interactive Mermaid Diagrams

The documentation includes comprehensive Mermaid diagrams for:

- **User Journey Flows** - Complete workflows from start to finish
- **API Interaction Diagrams** - Request/response flows
- **System Architecture** - Component relationships
- **Error Handling Flows** - Exception and recovery scenarios
- **State Machines** - Mapping lifecycle and status transitions

Example Mermaid diagram:

```mermaid
flowchart TD
    A[User Starts Import] --> B{Authentication Check}
    B -->|Failed| C[Return 401 Unauthorized]
    B -->|Success| D[Process Import Data]
    D --> E[Return Success Response]
```

### 📋 BDD Scenarios

Comprehensive Behavior-Driven Development scenarios covering:

- **Framework Management** - Import, export, version management
- **Intelligent Mapping** - AI-powered suggestions and validation
- **Analytics & Reporting** - Coverage analysis and effectiveness scoring
- **Error Handling** - Edge cases and recovery scenarios
- **Performance Testing** - Load testing and scalability scenarios

### 🎯 User-Centric Design

Documentation organized by user personas:

- **🔍 Security Analyst** - Threat analysis and mapping workflows
- **⚙️ Framework Administrator** - System management and configuration
- **📊 Compliance Officer** - Reporting and audit workflows
- **🏗️ Security Architect** - Design validation and expert review

### 🔧 Interactive Features

- **Copy Code Buttons** - One-click code copying
- **Live Search** - Real-time search with suggestions
- **Dark Mode Toggle** - User preference support
- **Progress Indicator** - Reading progress tracking
- **Responsive Design** - Mobile and tablet friendly
