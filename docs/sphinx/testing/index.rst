Testing Framework
=================

.. toctree::
   :maxdepth: 2
   :caption: Testing Topics:

   unit_testing
   integration_testing
   performance_testing
   test_automation

Overview
--------

The RegressionRigor platform employs a comprehensive testing strategy that ensures reliability, performance, and security across all components. Our testing framework supports multiple testing methodologies and provides tools for automated test generation, execution, and reporting.

.. admonition:: Quality Through Testing
   :class: note

   Comprehensive testing is fundamental to maintaining the reliability and security of the RegressionRigor platform. Every feature is thoroughly tested before deployment.

Testing Philosophy
-----------------

Our testing approach is built on several key principles:

🎯 **Test-Driven Development**
   Tests are written before or alongside code to ensure comprehensive coverage and clear requirements.

🔄 **Continuous Testing**
   Automated testing runs continuously throughout the development lifecycle, providing immediate feedback.

📊 **Comprehensive Coverage**
   We aim for high test coverage across unit, integration, and end-to-end tests.

⚡ **Performance Validation**
   Performance tests ensure the platform meets speed and scalability requirements.

🛡️ **Security Testing**
   Security tests validate that security controls work as expected and identify vulnerabilities.

Testing Strategy
---------------

Multi-Layer Testing Approach
~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. mermaid::

   graph TD
       A[Unit Tests] --> B[Integration Tests]
       B --> C[System Tests]
       C --> D[Performance Tests]
       D --> E[Security Tests]
       E --> F[User Acceptance Tests]
       
       A --> G[Fast Feedback]
       B --> H[Component Integration]
       C --> I[End-to-End Validation]
       D --> J[Performance Validation]
       E --> K[Security Validation]
       F --> L[User Experience]

**Testing Pyramid:**

1. **Unit Tests (70%)**
   * Fast, isolated tests for individual functions and classes
   * Mock external dependencies
   * High coverage of business logic

2. **Integration Tests (20%)**
   * Test component interactions
   * Database integration
   * API endpoint testing

3. **End-to-End Tests (10%)**
   * Full user workflow testing
   * Browser automation
   * Complete system validation

Testing Types
------------

Unit Testing
~~~~~~~~~~~

**Framework:** pytest with extensive fixture support

**Coverage:** Individual functions, classes, and modules

**Characteristics:**
   * Fast execution (< 1 second per test)
   * Isolated from external dependencies
   * High code coverage (target: 95%+)
   * Comprehensive edge case testing

**Example Unit Test:**

.. code-block:: python

   import pytest
   from api.models.campaign import Campaign
   from api.exceptions import ValidationError

   class TestCampaign:
       def test_campaign_creation_valid_data(self):
           """Test campaign creation with valid data."""
           campaign = Campaign(
               name="Test Campaign",
               description="Test Description",
               user_id=1
           )
           assert campaign.name == "Test Campaign"
           assert campaign.is_valid()

       def test_campaign_creation_invalid_name(self):
           """Test campaign creation with invalid name."""
           with pytest.raises(ValidationError):
               Campaign(name="", description="Test", user_id=1)

Integration Testing
~~~~~~~~~~~~~~~~~

**Framework:** pytest with database fixtures

**Coverage:** Component interactions, API endpoints, database operations

**Characteristics:**
   * Medium execution time (1-10 seconds per test)
   * Real database interactions (test database)
   * API endpoint validation
   * Authentication and authorization testing

**Example Integration Test:**

.. code-block:: python

   import pytest
   from fastapi.testclient import TestClient
   from api.main import app

   client = TestClient(app)

   class TestCampaignAPI:
       def test_create_campaign_authenticated(self, auth_headers):
           """Test campaign creation via API with authentication."""
           response = client.post(
               "/api/campaigns/",
               json={
                   "name": "API Test Campaign",
                   "description": "Created via API"
               },
               headers=auth_headers
           )
           assert response.status_code == 201
           data = response.json()
           assert data["name"] == "API Test Campaign"

Performance Testing
~~~~~~~~~~~~~~~~~

**Framework:** pytest-benchmark with custom performance utilities

**Coverage:** Critical performance paths, load testing, stress testing

**Characteristics:**
   * Longer execution time (10+ seconds per test)
   * Resource usage monitoring
   * Scalability validation
   * Performance regression detection

**Example Performance Test:**

.. code-block:: python

   import pytest
   from performance_utils import measure_time, measure_memory

   class TestPerformance:
       @measure_time(max_duration=0.5)
       def test_campaign_list_performance(self, db_session):
           """Test that campaign listing completes within 500ms."""
           # Create test data
           campaigns = [Campaign(name=f"Campaign {i}") for i in range(100)]
           db_session.add_all(campaigns)
           db_session.commit()
           
           # Test performance
           result = get_user_campaigns(user_id=1)
           assert len(result) <= 100

Security Testing
~~~~~~~~~~~~~~

**Framework:** Custom security testing utilities with pytest

**Coverage:** Authentication, authorization, input validation, security controls

**Characteristics:**
   * Security vulnerability scanning
   * Penetration testing automation
   * Compliance validation
   * Security regression testing

**Example Security Test:**

.. code-block:: python

   import pytest
   from security_utils import test_sql_injection, test_xss_protection

   class TestSecurity:
       def test_sql_injection_protection(self):
           """Test that API endpoints are protected against SQL injection."""
           malicious_input = "'; DROP TABLE users; --"
           response = client.post(
               "/api/campaigns/",
               json={"name": malicious_input},
               headers=auth_headers
           )
           # Should not cause database error
           assert response.status_code in [400, 422]  # Validation error

Test Infrastructure
------------------

Test Environment Setup
~~~~~~~~~~~~~~~~~~~~~

**Database Configuration:**

.. code-block:: python
   :caption: conftest.py

   import pytest
   from sqlalchemy import create_engine
   from sqlalchemy.orm import sessionmaker
   from api.database import Base

   @pytest.fixture(scope="session")
   def test_engine():
       """Create test database engine."""
       engine = create_engine("postgresql://test:test@localhost/test_db")
       Base.metadata.create_all(engine)
       yield engine
       Base.metadata.drop_all(engine)

   @pytest.fixture
   def db_session(test_engine):
       """Create database session for each test."""
       Session = sessionmaker(bind=test_engine)
       session = Session()
       try:
           yield session
       finally:
           session.rollback()
           session.close()

**Authentication Fixtures:**

.. code-block:: python

   @pytest.fixture
   def test_user(db_session):
       """Create test user."""
       user = User(
           username="testuser",
           email="<EMAIL>",
           role="analyst"
       )
       db_session.add(user)
       db_session.commit()
       return user

   @pytest.fixture
   def auth_headers(test_user):
       """Create authentication headers."""
       token = create_access_token(data={"sub": test_user.username})
       return {"Authorization": f"Bearer {token}"}

Test Data Management
~~~~~~~~~~~~~~~~~~

**Fixture Factories:**

.. code-block:: python

   import factory
   from api.models import User, Campaign, Assessment

   class UserFactory(factory.alchemy.SQLAlchemyModelFactory):
       class Meta:
           model = User
           sqlalchemy_session_persistence = "commit"

       username = factory.Sequence(lambda n: f"user{n}")
       email = factory.LazyAttribute(lambda obj: f"{obj.username}@example.com")
       role = "analyst"

   class CampaignFactory(factory.alchemy.SQLAlchemyModelFactory):
       class Meta:
           model = Campaign
           sqlalchemy_session_persistence = "commit"

       name = factory.Faker("sentence", nb_words=3)
       description = factory.Faker("text")
       user = factory.SubFactory(UserFactory)

Test Execution
-------------

Local Testing
~~~~~~~~~~~~

.. code-block:: bash

   # Run all tests
   pytest

   # Run specific test categories
   pytest tests/unit/
   pytest tests/integration/
   pytest tests/performance/

   # Run with coverage
   pytest --cov=api --cov-report=html

   # Run performance tests only
   pytest -m performance

   # Run tests in parallel
   pytest -n auto

Continuous Integration
~~~~~~~~~~~~~~~~~~~~

**GitHub Actions Configuration:**

.. code-block:: yaml

   name: Test Suite
   on: [push, pull_request]

   jobs:
     test:
       runs-on: ubuntu-latest
       services:
         postgres:
           image: postgres:13
           env:
             POSTGRES_PASSWORD: test
             POSTGRES_DB: test_db
           options: >-
             --health-cmd pg_isready
             --health-interval 10s
             --health-timeout 5s
             --health-retries 5

       steps:
         - uses: actions/checkout@v3
         
         - name: Set up Python
           uses: actions/setup-python@v4
           with:
             python-version: '3.11'
             
         - name: Install dependencies
           run: |
             pip install -r requirements.txt
             pip install -r requirements-test.txt
             
         - name: Run tests
           run: |
             pytest tests/ --cov=api --cov-report=xml
             
         - name: Upload coverage
           uses: codecov/codecov-action@v3

Test Reporting
-------------

Coverage Reports
~~~~~~~~~~~~~~

.. code-block:: bash

   # Generate HTML coverage report
   pytest --cov=api --cov-report=html

   # Generate XML coverage report for CI
   pytest --cov=api --cov-report=xml

   # Check coverage thresholds
   pytest --cov=api --cov-fail-under=90

Performance Reports
~~~~~~~~~~~~~~~~~

.. code-block:: bash

   # Generate performance benchmark report
   pytest tests/performance/ --benchmark-json=benchmark.json

   # Compare performance with baseline
   pytest tests/performance/ --benchmark-compare=baseline.json

Quality Metrics
--------------

Test Quality Dashboard
~~~~~~~~~~~~~~~~~~~~

.. list-table:: Testing Metrics
   :header-rows: 1
   :widths: 25 25 25 25

   * - Metric
     - Current
     - Target
     - Status
   * - Test Coverage
     - 94%
     - 95%
     - 🟡 Close
   * - Test Count
     - 1,247
     - 1,500
     - 🟢 Good
   * - Test Speed
     - 2.1 min
     - < 3 min
     - 🟢 Good
   * - Flaky Tests
     - 2
     - 0
     - 🟡 Improving

Best Practices
--------------

Test Writing Guidelines
~~~~~~~~~~~~~~~~~~~~~

* **Clear Test Names**: Use descriptive names that explain the test scenario
* **Single Responsibility**: Each test should verify one specific behavior
* **Independent Tests**: Tests should not depend on each other
* **Comprehensive Assertions**: Verify all relevant aspects of the behavior

Test Maintenance
~~~~~~~~~~~~~~~

* **Regular Review**: Regularly review and update tests
* **Refactor Tests**: Keep test code clean and maintainable
* **Remove Obsolete Tests**: Remove tests for deprecated functionality
* **Update Test Data**: Keep test data relevant and realistic

Getting Help
-----------

* Review detailed testing guides in the individual testing topic sections
* Check the :doc:`../tools/testing_tools` for testing utilities
* See the :doc:`../reference/index` for troubleshooting
* Join our testing community for best practices and support
