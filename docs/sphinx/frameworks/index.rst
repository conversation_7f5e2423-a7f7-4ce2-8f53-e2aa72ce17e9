==========================
Cybersecurity Frameworks
==========================

This section provides comprehensive documentation for all implemented cybersecurity frameworks in the Regression Rigor system. Our platform supports multiple industry-standard frameworks with advanced cross-framework mapping and analytics capabilities.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
========

The Regression Rigor platform implements a comprehensive multi-framework cybersecurity management system that supports:

* **Framework Management**: Complete CRUD operations for all framework elements
* **Cross-Framework Mapping**: Intelligent mapping between frameworks with confidence scoring
* **Analytics & Reporting**: Advanced analytics, gap analysis, and compliance tracking
* **Import/Export**: Official framework data import and multi-format export capabilities
* **Assessment Tools**: Compliance assessment and maturity scoring across frameworks

Supported Frameworks
====================

Our platform currently supports the following major cybersecurity frameworks:

.. mermaid::

   graph TB
       subgraph "Cybersecurity Frameworks"
           ISF["🏛️ ISF 2022<br/>Information Security Framework<br/>14 Security Areas<br/>56+ Controls"]
           NIST["🆕 NIST CSF 2.0<br/>Cybersecurity Framework<br/>6 Functions<br/>108+ Subcategories"]
           ISO["📋 ISO/IEC 27001:2022<br/>Information Security Management<br/>5 Domains<br/>6+ Controls"]
           CIS["🛡️ CIS Controls v8<br/>Critical Security Controls<br/>18 Controls<br/>153 Safeguards"]
       end
       
       subgraph "Cross-Framework Capabilities"
           MAPPING["🔗 Framework Mapping<br/>Confidence Scoring<br/>Validation Workflow"]
           ANALYTICS["📊 Analytics Engine<br/>Gap Analysis<br/>Compliance Tracking"]
           ASSESSMENT["📈 Assessment Tools<br/>Maturity Scoring<br/>Risk Assessment"]
       end
       
       ISF --> MAPPING
       NIST --> MAPPING
       ISO --> MAPPING
       CIS --> MAPPING
       
       MAPPING --> ANALYTICS
       MAPPING --> ASSESSMENT
       
       style ISF fill:#e1f5fe
       style NIST fill:#f3e5f5
       style ISO fill:#e8f5e8
       style CIS fill:#fff3e0
       style MAPPING fill:#fce4ec
       style ANALYTICS fill:#f1f8e9
       style ASSESSMENT fill:#e3f2fd

Framework Architecture
======================

The framework system is built on a flexible, extensible architecture that supports:

.. mermaid::

   graph LR
       subgraph "Data Layer"
           DB[(Database)]
           MODELS[SQLAlchemy Models]
           SCHEMAS[Pydantic Schemas]
       end
       
       subgraph "Service Layer"
           IMPORT[Import Services]
           EXPORT[Export Services]
           MAPPING_SVC[Mapping Services]
           ANALYTICS_SVC[Analytics Services]
       end
       
       subgraph "API Layer"
           REST[REST APIs]
           AUTH[Authentication]
           VALIDATION[Validation]
       end
       
       subgraph "Client Layer"
           WEB[Web Interface]
           CLI[CLI Tools]
           INTEGRATIONS[Integrations]
       end
       
       DB --> MODELS
       MODELS --> SCHEMAS
       SCHEMAS --> IMPORT
       SCHEMAS --> EXPORT
       SCHEMAS --> MAPPING_SVC
       SCHEMAS --> ANALYTICS_SVC
       
       IMPORT --> REST
       EXPORT --> REST
       MAPPING_SVC --> REST
       ANALYTICS_SVC --> REST
       
       AUTH --> REST
       VALIDATION --> REST
       
       REST --> WEB
       REST --> CLI
       REST --> INTEGRATIONS

Key Features
============

Framework Management
--------------------

* **Version Control**: Support for multiple framework versions with migration capabilities
* **Hierarchical Structure**: Proper representation of framework hierarchies (functions → categories → controls)
* **Metadata Management**: Comprehensive metadata including descriptions, guidance, and references
* **Soft Deletion**: Safe deletion with audit trails and recovery capabilities

Cross-Framework Mapping
-----------------------

* **Intelligent Mapping**: AI-assisted mapping suggestions between framework elements
* **Confidence Scoring**: Quantitative confidence levels for mapping relationships
* **Validation Workflow**: Peer review and validation process for mapping accuracy
* **Bulk Operations**: Efficient bulk mapping creation and management

Analytics & Reporting
---------------------

* **Gap Analysis**: Identify implementation gaps across frameworks
* **Compliance Tracking**: Monitor compliance status and trends over time
* **Maturity Assessment**: Evaluate organizational security maturity
* **Custom Reports**: Generate tailored reports for different stakeholders

Data Quality & Integrity
------------------------

* **Official Sources**: Import data from official framework publications
* **Validation Rules**: Comprehensive data validation and integrity checks
* **Audit Trails**: Complete audit logging for all framework operations
* **Backup & Recovery**: Robust backup and recovery mechanisms

Getting Started
===============

To begin working with frameworks in Regression Rigor:

1. **Choose Your Framework**: Select the framework(s) most relevant to your organization
2. **Import Official Data**: Use our import services to load official framework data
3. **Configure Mappings**: Set up cross-framework mappings for comprehensive coverage
4. **Run Assessments**: Conduct compliance assessments and gap analysis
5. **Generate Reports**: Create reports for stakeholders and auditors

Framework Documentation
=======================

.. toctree::
   :maxdepth: 2
   :caption: Framework Guides

   isf/index
   nist_csf_2/index
   iso_27001/index
   cis_controls/index
   mapping/index
   analytics/index

Quick Reference
===============

.. list-table:: Framework Quick Reference
   :header-rows: 1
   :widths: 20 15 15 15 35

   * - Framework
     - Version
     - Elements
     - Focus Area
     - Best For
   * - ISF
     - 2022
     - 14 Areas, 56+ Controls
     - Comprehensive Security
     - Large enterprises, Financial services
   * - NIST CSF 2.0
     - 2.0
     - 6 Functions, 108+ Subcategories
     - Risk Management
     - All organizations, Critical infrastructure
   * - ISO/IEC 27001
     - 2022
     - 5 Domains, 6+ Controls
     - ISMS Certification
     - Global organizations, Certification required
   * - CIS Controls
     - v8
     - 18 Controls, 153 Safeguards
     - Practical Implementation
     - SMEs, Implementation groups

API Endpoints
=============

Each framework provides a consistent set of API endpoints:

.. code-block:: text

   GET    /api/v1/{framework}/versions          # List framework versions
   GET    /api/v1/{framework}/versions/current  # Get current version
   GET    /api/v1/{framework}/controls          # List controls/elements
   POST   /api/v1/{framework}/search            # Search framework elements
   POST   /api/v1/{framework}/export            # Export framework data
   POST   /api/v1/{framework}/import            # Import framework data

Where ``{framework}`` can be:
- ``isf`` - Information Security Framework
- ``nist-csf-2`` - NIST Cybersecurity Framework 2.0
- ``iso-27001`` - ISO/IEC 27001
- ``cis-controls`` - CIS Critical Security Controls

Next Steps
==========

* :doc:`isf/index` - Learn about the Information Security Framework
* :doc:`nist_csf_2/index` - Explore NIST Cybersecurity Framework 2.0
* :doc:`iso_27001/index` - Understand ISO/IEC 27001 implementation
* :doc:`cis_controls/index` - Discover CIS Critical Security Controls
* :doc:`mapping/index` - Master cross-framework mapping
* :doc:`analytics/index` - Leverage framework analytics
