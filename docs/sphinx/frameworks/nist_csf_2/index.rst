=============================================
NIST Cybersecurity Framework 2.0 (CSF 2.0)
=============================================

The NIST Cybersecurity Framework 2.0 is a voluntary framework that provides a policy framework of computer security guidance for how private sector organizations can assess and improve their ability to prevent, detect, and respond to cyber attacks.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
========

NIST CSF 2.0, released in February 2024, represents a significant evolution of the original framework with enhanced governance capabilities and expanded scope for all organizations.

.. mermaid::

   graph TB
       subgraph "NIST CSF 2.0 Framework Structure"
           VERSION["📋 NIST CSF Version 2.0<br/>Released: February 2024<br/>Status: Current"]
           
           subgraph "6 Core Functions"
               GV["🏛️ GOVERN (GV)<br/>NEW in 2.0<br/>Organizational Context<br/>Risk Management Strategy"]
               ID["🔍 IDENTIFY (ID)<br/>Asset Management<br/>Risk Assessment<br/>Governance"]
               PR["🛡️ PROTECT (PR)<br/>Access Control<br/>Data Security<br/>Protective Technology"]
               DE["🚨 DETECT (DE)<br/>Anomalies & Events<br/>Security Monitoring<br/>Detection Processes"]
               RS["🚑 RESPOND (RS)<br/>Response Planning<br/>Communications<br/>Analysis & Mitigation"]
               RC["🔄 RECOVER (RC)<br/>Recovery Planning<br/>Improvements<br/>Communications"]
           end
           
           subgraph "108+ Subcategories"
               SUBCATS["🎯 Detailed Subcategories<br/>Implementation Guidance<br/>Informative References<br/>Examples"]
           end
       end
       
       VERSION --> GV
       VERSION --> ID
       VERSION --> PR
       VERSION --> DE
       VERSION --> RS
       VERSION --> RC
       
       GV --> SUBCATS
       ID --> SUBCATS
       PR --> SUBCATS
       DE --> SUBCATS
       RS --> SUBCATS
       RC --> SUBCATS
       
       style VERSION fill:#f3e5f5
       style GV fill:#4caf50
       style ID fill:#2196f3
       style PR fill:#ff9800
       style DE fill:#f44336
       style RS fill:#9c27b0
       style RC fill:#607d8b
       style SUBCATS fill:#e1f5fe

What's New in CSF 2.0
======================

Key Enhancements
----------------

.. mermaid::

   graph LR
       subgraph "CSF 1.1 (2018)"
           OLD_FUNC["5 Functions<br/>Identify, Protect, Detect<br/>Respond, Recover"]
           OLD_FOCUS["Critical Infrastructure<br/>Focus"]
           OLD_GOVERN["Governance embedded<br/>in other functions"]
       end
       
       subgraph "CSF 2.0 (2024)"
           NEW_FUNC["6 Functions<br/>+ NEW Govern Function"]
           NEW_FOCUS["All Organizations<br/>Universal Applicability"]
           NEW_GOVERN["Dedicated Governance<br/>Function (GV)"]
           NEW_FEATURES["Enhanced Guidance<br/>Supply Chain Focus<br/>Implementation Examples"]
       end
       
       OLD_FUNC -.->|Evolution| NEW_FUNC
       OLD_FOCUS -.->|Expansion| NEW_FOCUS
       OLD_GOVERN -.->|Enhancement| NEW_GOVERN
       NEW_FUNC --> NEW_FEATURES
       
       style OLD_FUNC fill:#ffecb3
       style OLD_FOCUS fill:#ffecb3
       style OLD_GOVERN fill:#ffecb3
       style NEW_FUNC fill:#c8e6c9
       style NEW_FOCUS fill:#c8e6c9
       style NEW_GOVERN fill:#c8e6c9
       style NEW_FEATURES fill:#e1f5fe

**Major Improvements:**

1. **New GOVERN Function**: Dedicated governance and risk management strategy
2. **Universal Applicability**: Expanded beyond critical infrastructure to all organizations
3. **Enhanced Guidance**: More detailed implementation guidance and examples
4. **Supply Chain Focus**: Increased emphasis on supply chain risk management
5. **Organizational Profiles**: Enhanced profile capabilities for customization

Core Functions Deep Dive
=========================

The NIST CSF 2.0 organizes cybersecurity activities into six core functions:

.. mermaid::

   graph TD
       subgraph "GOVERN (GV) - NEW"
           GV_GV["GV.GV: Governance<br/>Strategy & Policy"]
           GV_RM["GV.RM: Risk Management<br/>Strategy"]
           GV_RR["GV.RR: Roles & Responsibilities<br/>Accountability"]
           GV_PO["GV.PO: Policy<br/>Organizational Policies"]
           GV_OV["GV.OV: Oversight<br/>Governance Oversight"]
           GV_SC["GV.SC: Supply Chain<br/>Risk Management"]
       end
       
       subgraph "IDENTIFY (ID)"
           ID_AM["ID.AM: Asset Management"]
           ID_BE["ID.BE: Business Environment"]
           ID_GV["ID.GV: Governance"]
           ID_RA["ID.RA: Risk Assessment"]
           ID_RM["ID.RM: Risk Management Strategy"]
           ID_SC["ID.SC: Supply Chain"]
       end
       
       subgraph "PROTECT (PR)"
           PR_AC["PR.AC: Identity Management<br/>& Access Control"]
           PR_AT["PR.AT: Awareness & Training"]
           PR_DS["PR.DS: Data Security"]
           PR_IP["PR.IP: Information Protection<br/>Processes & Procedures"]
           PR_MA["PR.MA: Maintenance"]
           PR_PT["PR.PT: Protective Technology"]
       end
       
       style GV_GV fill:#4caf50
       style GV_RM fill:#4caf50
       style GV_RR fill:#4caf50
       style ID_AM fill:#2196f3
       style ID_BE fill:#2196f3
       style PR_AC fill:#ff9800
       style PR_DS fill:#ff9800

**1. GOVERN (GV) - The New Function**

The GOVERN function is the cornerstone of CSF 2.0, establishing the foundation for all other cybersecurity activities:

* **GV.GV**: Organizational cybersecurity strategy
* **GV.RM**: Risk management strategy and processes
* **GV.RR**: Roles, responsibilities, and authorities
* **GV.PO**: Policies, procedures, and processes
* **GV.OV**: Oversight and governance mechanisms
* **GV.SC**: Supply chain risk management

**2. IDENTIFY (ID)**

Understanding organizational context and cybersecurity risks:

* **ID.AM**: Physical devices, software, data, and personnel assets
* **ID.BE**: Business context, mission, and stakeholder expectations
* **ID.GV**: Governance policies and legal/regulatory requirements
* **ID.RA**: Risk identification and analysis processes
* **ID.RM**: Risk management processes and decisions
* **ID.SC**: Supply chain partners and dependencies

Implementation Tiers
====================

NIST CSF 2.0 maintains the four implementation tiers that describe the degree of rigor in cybersecurity risk management:

.. mermaid::

   graph TB
       subgraph "Implementation Tiers"
           TIER1["🔴 Tier 1: Partial<br/>Ad hoc, reactive<br/>Limited awareness<br/>Risk management irregular"]
           TIER2["🟡 Tier 2: Risk Informed<br/>Risk management practices<br/>approved by management<br/>but may not be established<br/>as organizational policy"]
           TIER3["🟢 Tier 3: Repeatable<br/>Risk management practices<br/>formally approved and<br/>expressed as policy<br/>Regular updates"]
           TIER4["🔵 Tier 4: Adaptive<br/>Agile and risk-informed<br/>Continuous improvement<br/>Advanced cybersecurity<br/>Lessons learned incorporated"]
       end
       
       TIER1 -.->|Improvement| TIER2
       TIER2 -.->|Maturation| TIER3
       TIER3 -.->|Optimization| TIER4
       
       style TIER1 fill:#ffcdd2
       style TIER2 fill:#fff3e0
       style TIER3 fill:#e8f5e8
       style TIER4 fill:#e3f2fd

Organizational Profiles
=======================

CSF 2.0 enhances the profile concept for better organizational customization:

.. mermaid::

   graph LR
       subgraph "Profile Development Process"
           CURRENT["📊 Current Profile<br/>Current State<br/>Assessment"]
           TARGET["🎯 Target Profile<br/>Desired State<br/>Goals"]
           GAP["📈 Gap Analysis<br/>Identify Differences<br/>Prioritize Actions"]
           PLAN["📋 Action Plan<br/>Implementation<br/>Roadmap"]
       end
       
       CURRENT --> GAP
       TARGET --> GAP
       GAP --> PLAN
       PLAN -.->|Continuous Improvement| CURRENT
       
       subgraph "Profile Components"
           FUNCTIONS["Functions & Categories<br/>Selected for Organization"]
           SUBCATS["Subcategories<br/>Relevant to Context"]
           REFERENCES["Informative References<br/>Standards & Guidelines"]
           TIERS["Implementation Tiers<br/>Maturity Levels"]
       end
       
       CURRENT --> FUNCTIONS
       TARGET --> SUBCATS
       GAP --> REFERENCES
       PLAN --> TIERS

API Reference
=============

NIST CSF 2.0 Framework Endpoints
--------------------------------

**Version Management**

.. code-block:: http

   GET /api/v1/nist-csf-2/versions
   GET /api/v1/nist-csf-2/versions/current
   POST /api/v1/nist-csf-2/versions

**Functions and Categories**

.. code-block:: http

   GET /api/v1/nist-csf-2/functions
   GET /api/v1/nist-csf-2/categories
   GET /api/v1/nist-csf-2/categories/{id}

**Subcategories**

.. code-block:: http

   GET /api/v1/nist-csf-2/subcategories
   GET /api/v1/nist-csf-2/subcategories/{id}
   PUT /api/v1/nist-csf-2/subcategories/{id}
   POST /api/v1/nist-csf-2/subcategories/search

**Profiles and Assessments**

.. code-block:: http

   GET /api/v1/nist-csf-2/profiles
   POST /api/v1/nist-csf-2/profiles
   GET /api/v1/nist-csf-2/assessments
   POST /api/v1/nist-csf-2/assessments

Example API Usage
-----------------

**Get GOVERN Function Details**

.. code-block:: python

   import requests
   
   response = requests.get('/api/v1/nist-csf-2/functions?function_id=GV')
   govern_function = response.json()
   
   print(f"Function: {govern_function['name']}")
   print(f"Description: {govern_function['description']}")
   print(f"Categories: {len(govern_function['categories'])}")

**Search Subcategories**

.. code-block:: python

   search_request = {
       "query": "supply chain",
       "function": "GV",
       "include_examples": True
   }
   
   response = requests.post('/api/v1/nist-csf-2/subcategories/search', json=search_request)
   subcategories = response.json()
   
   for subcat in subcategories['results']:
       print(f"Subcategory: {subcat['subcategory_id']} - {subcat['outcome']}")

**Create Organizational Profile**

.. code-block:: python

   profile_request = {
       "name": "Financial Services Profile",
       "description": "NIST CSF profile for financial services organization",
       "organization_context": {
           "industry": "financial_services",
           "size": "large",
           "risk_tolerance": "low"
       },
       "selected_subcategories": [
           "GV.GV-01", "GV.RM-01", "ID.AM-01", "PR.AC-01"
       ],
       "implementation_tiers": {
           "current": 2,
           "target": 3
       }
   }
   
   response = requests.post('/api/v1/nist-csf-2/profiles', json=profile_request)
   profile = response.json()

Data Model
==========

The NIST CSF 2.0 implementation uses a hierarchical structure:

.. mermaid::

   erDiagram
       NISTCSFVersion ||--o{ NISTCSFFunction : contains
       NISTCSFFunction ||--o{ NISTCSFCategory : contains
       NISTCSFCategory ||--o{ NISTCSFSubcategory : contains
       NISTCSFSubcategory ||--o{ NISTCSFImplementationExample : has
       NISTCSFVersion ||--o{ NISTCSFProfile : uses
       NISTCSFProfile ||--o{ NISTCSFAssessment : assessed_by
       
       NISTCSFVersion {
           int id PK
           string version
           string release_date
           text description
           boolean is_current
           int total_functions
           int total_subcategories
       }
       
       NISTCSFFunction {
           int id PK
           string function_id
           string name
           text description
           int version_id FK
           int order_index
           string function_type
       }
       
       NISTCSFCategory {
           int id PK
           string category_id
           string name
           text description
           int function_id FK
           int version_id FK
           int order_index
       }
       
       NISTCSFSubcategory {
           int id PK
           string subcategory_id
           text outcome
           text implementation_guidance
           int category_id FK
           int version_id FK
           json informative_references
       }

Implementation Guide
====================

Getting Started with NIST CSF 2.0
----------------------------------

.. mermaid::

   graph TD
       START([Start CSF 2.0 Implementation])
       
       STEP1[1. Prioritize and Scope<br/>Define business objectives<br/>Identify systems and assets]
       STEP2[2. Orient<br/>Understand organizational context<br/>Identify threats and vulnerabilities]
       STEP3[3. Create Current Profile<br/>Document current cybersecurity<br/>practices and outcomes]
       STEP4[4. Conduct Risk Assessment<br/>Analyze current state<br/>Identify gaps and risks]
       STEP5[5. Create Target Profile<br/>Define desired cybersecurity<br/>outcomes and priorities]
       STEP6[6. Determine, Analyze, Prioritize Gaps<br/>Compare current and target<br/>Prioritize improvement actions]
       STEP7[7. Implement Action Plan<br/>Execute prioritized actions<br/>Monitor progress]
       
       START --> STEP1
       STEP1 --> STEP2
       STEP2 --> STEP3
       STEP3 --> STEP4
       STEP4 --> STEP5
       STEP5 --> STEP6
       STEP6 --> STEP7
       STEP7 -.->|Continuous Improvement| STEP3
       
       style START fill:#4caf50
       style STEP1 fill:#2196f3
       style STEP2 fill:#2196f3
       style STEP3 fill:#ff9800
       style STEP4 fill:#ff9800
       style STEP5 fill:#9c27b0
       style STEP6 fill:#9c27b0
       style STEP7 fill:#f44336

Best Practices
==============

Implementation Success Factors
------------------------------

1. **Executive Leadership**: Ensure strong executive sponsorship and commitment
2. **Cross-Functional Teams**: Include representatives from all business units
3. **Risk-Based Approach**: Focus on highest priority risks and business objectives
4. **Iterative Implementation**: Start small and expand gradually
5. **Continuous Improvement**: Regularly review and update profiles and practices

Common Implementation Challenges
-------------------------------

* **Scope Creep**: Trying to implement everything at once
* **Lack of Context**: Not tailoring the framework to organizational needs
* **Resource Constraints**: Insufficient resources for implementation
* **Change Resistance**: Organizational resistance to new processes
* **Measurement Difficulties**: Challenges in measuring cybersecurity improvements

Integration with Other Frameworks
==================================

NIST CSF 2.0 is designed to complement other cybersecurity frameworks:

.. mermaid::

   graph LR
       CSF[NIST CSF 2.0]
       
       subgraph "Framework Integration"
           ISF[ISF 2022<br/>Governance Alignment]
           ISO[ISO 27001<br/>Management System]
           CIS[CIS Controls<br/>Technical Implementation]
           NIST800[NIST 800-53<br/>Control Catalog]
       end
       
       subgraph "Industry Standards"
           COBIT[COBIT<br/>IT Governance]
           ITIL[ITIL<br/>Service Management]
           SOC[SOC 2<br/>Trust Services]
       end
       
       CSF -.->|Strategic Framework| ISF
       CSF -.->|Certification Path| ISO
       CSF -.->|Tactical Controls| CIS
       CSF -.->|Control Details| NIST800
       
       CSF -.->|IT Governance| COBIT
       CSF -.->|Operations| ITIL
       CSF -.->|Compliance| SOC
       
       style CSF fill:#f3e5f5
       style ISF fill:#e1f5fe
       style ISO fill:#e8f5e8
       style CIS fill:#fff3e0

Next Steps
==========

* :doc:`../isf/index` - Compare with ISF framework
* :doc:`../iso_27001/index` - Explore ISO 27001 integration
* :doc:`../cis_controls/index` - Learn about CIS Controls alignment
* :doc:`../mapping/index` - Master cross-framework mapping
* :doc:`../analytics/index` - Use NIST CSF analytics

Resources
=========

* `NIST CSF 2.0 Official Site <https://www.nist.gov/cyberframework>`_
* `CSF 2.0 Core Document <https://nvlpubs.nist.gov/nistpubs/CSWP/NIST.CSWP.29.pdf>`_
* `Implementation Examples <https://www.nist.gov/cyberframework/implementation-examples>`_
* :doc:`../../api/index` - Complete API documentation
* :doc:`../../user_guide/index` - User guide and tutorials
