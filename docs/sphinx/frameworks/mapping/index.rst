===============================
Cross-Framework Mapping
===============================

Cross-framework mapping is a critical capability that enables organizations to understand relationships between different cybersecurity frameworks, identify overlaps, and optimize their security investments across multiple compliance requirements.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
========

The Regression Rigor platform provides advanced cross-framework mapping capabilities that help organizations navigate the complex landscape of cybersecurity frameworks with intelligent mapping, confidence scoring, and validation workflows.

.. mermaid::

   graph TB
       subgraph "Framework Ecosystem"
           ISF["🏛️ ISF 2022<br/>14 Security Areas<br/>56+ Controls"]
           NIST["🆕 NIST CSF 2.0<br/>6 Functions<br/>108+ Subcategories"]
           ISO["📋 ISO/IEC 27001<br/>5 Domains<br/>93 Controls"]
           CIS["🛡️ CIS Controls v8<br/>18 Controls<br/>153 Safeguards"]
       end
       
       subgraph "Mapping Engine"
           MAPPING["🔗 Intelligent Mapping<br/>Confidence Scoring<br/>Validation Workflow"]
           ANALYTICS["📊 Mapping Analytics<br/>Coverage Analysis<br/>Gap Identification"]
           VALIDATION["✅ Validation System<br/>Peer Review<br/>Quality Assurance"]
       end
       
       subgraph "Business Value"
           COMPLIANCE["📋 Multi-Framework Compliance<br/>Unified View<br/>Reduced Duplication"]
           OPTIMIZATION["⚡ Resource Optimization<br/>Efficient Implementation<br/>Cost Reduction"]
           REPORTING["📈 Comprehensive Reporting<br/>Stakeholder Communication<br/>Audit Support"]
       end
       
       ISF --> MAPPING
       NIST --> MAPPING
       ISO --> MAPPING
       CIS --> MAPPING
       
       MAPPING --> ANALYTICS
       MAPPING --> VALIDATION
       
       ANALYTICS --> COMPLIANCE
       VALIDATION --> OPTIMIZATION
       MAPPING --> REPORTING
       
       style MAPPING fill:#e1f5fe
       style ANALYTICS fill:#f3e5f5
       style VALIDATION fill:#e8f5e8
       style COMPLIANCE fill:#fff3e0
       style OPTIMIZATION fill:#fce4ec
       style REPORTING fill:#f1f8e9

Mapping Types
=============

The platform supports different types of mappings based on the relationship strength between framework elements:

.. mermaid::

   graph LR
       subgraph "Mapping Types"
           DIRECT["🎯 Direct Mapping<br/>1:1 Relationship<br/>Same objective<br/>High confidence"]
           PARTIAL["🔄 Partial Mapping<br/>Overlapping scope<br/>Similar objectives<br/>Medium confidence"]
           RELATED["🔗 Related Mapping<br/>Complementary<br/>Supporting relationship<br/>Lower confidence"]
           NONE["❌ No Mapping<br/>No relationship<br/>Different scope<br/>Not applicable"]
       end
       
       subgraph "Confidence Levels"
           HIGH["🟢 High (0.8-1.0)<br/>Strong relationship<br/>Clear alignment<br/>Validated"]
           MEDIUM["🟡 Medium (0.6-0.8)<br/>Good relationship<br/>Some differences<br/>Review needed"]
           LOW["🔴 Low (0.0-0.6)<br/>Weak relationship<br/>Significant differences<br/>Requires validation"]
       end
       
       DIRECT --> HIGH
       PARTIAL --> MEDIUM
       RELATED --> LOW
       NONE --> LOW
       
       style DIRECT fill:#c8e6c9
       style PARTIAL fill:#fff9c4
       style RELATED fill:#ffecb3
       style NONE fill:#ffcdd2
       style HIGH fill:#c8e6c9
       style MEDIUM fill:#fff9c4
       style LOW fill:#ffcdd2

Mapping Methodology
===================

Our mapping methodology follows a systematic approach to ensure accuracy and consistency:

.. mermaid::

   graph TD
       START([Start Mapping Process])
       
       ANALYZE[Analysis Phase<br/>• Framework structure analysis<br/>• Control objective comparison<br/>• Scope assessment]
       
       MAP[Mapping Phase<br/>• Identify relationships<br/>• Assign confidence scores<br/>• Document rationale]
       
       VALIDATE[Validation Phase<br/>• Peer review<br/>• Expert validation<br/>• Quality assurance]
       
       PUBLISH[Publication Phase<br/>• Approve mappings<br/>• Update mapping sets<br/>• Notify stakeholders]
       
       MAINTAIN[Maintenance Phase<br/>• Monitor accuracy<br/>• Update as needed<br/>• Continuous improvement]
       
       START --> ANALYZE
       ANALYZE --> MAP
       MAP --> VALIDATE
       VALIDATE --> PUBLISH
       PUBLISH --> MAINTAIN
       MAINTAIN -.->|Continuous Cycle| ANALYZE
       
       subgraph "Quality Gates"
           QG1["📋 Completeness Check<br/>All elements mapped<br/>Documentation complete"]
           QG2["🔍 Accuracy Review<br/>Mapping correctness<br/>Confidence validation"]
           QG3["✅ Final Approval<br/>Stakeholder sign-off<br/>Publication ready"]
       end
       
       ANALYZE --> QG1
       MAP --> QG2
       VALIDATE --> QG3
       
       style START fill:#4caf50
       style ANALYZE fill:#2196f3
       style MAP fill:#ff9800
       style VALIDATE fill:#9c27b0
       style PUBLISH fill:#f44336
       style MAINTAIN fill:#607d8b

Mapping Sets
============

Mappings are organized into mapping sets for better management and organization:

.. mermaid::

   graph TB
       subgraph "Official Mapping Sets"
           SET1["📋 ISF ↔ NIST CSF 2.0<br/>Strategic Alignment<br/>Risk Management Focus<br/>13 Mappings"]
           SET2["📋 ISF ↔ ISO 27001<br/>Governance Integration<br/>ISMS Alignment<br/>15 Mappings"]
           SET3["📋 NIST CSF ↔ ISO 27001<br/>Framework Harmonization<br/>Control Alignment<br/>18 Mappings"]
           SET4["📋 CIS Controls ↔ NIST CSF<br/>Tactical Implementation<br/>Technical Controls<br/>25 Mappings"]
           SET5["📋 CIS Controls ↔ ISO 27001<br/>Technical Implementation<br/>Control Mapping<br/>20 Mappings"]
       end
       
       subgraph "Custom Mapping Sets"
           CUSTOM1["🔧 Organization-Specific<br/>Tailored mappings<br/>Local requirements<br/>Custom controls"]
           CUSTOM2["🏭 Industry-Specific<br/>Sector requirements<br/>Regulatory alignment<br/>Best practices"]
           CUSTOM3["🌍 Regional Mappings<br/>Local regulations<br/>Cultural considerations<br/>Language support"]
       end
       
       subgraph "Mapping Attributes"
           CONFIDENCE["📊 Confidence Score<br/>0.0 - 1.0 scale<br/>Quality indicator"]
           VALIDATION["✅ Validation Status<br/>Validated/Pending<br/>Quality assurance"]
           RATIONALE["📝 Rationale<br/>Mapping justification<br/>Expert reasoning"]
       end
       
       SET1 --> CONFIDENCE
       SET2 --> VALIDATION
       SET3 --> RATIONALE
       
       style SET1 fill:#e1f5fe
       style SET2 fill:#e8f5e8
       style SET3 fill:#fff3e0
       style CUSTOM1 fill:#f3e5f5
       style CUSTOM2 fill:#fce4ec
       style CUSTOM3 fill:#f1f8e9

Coverage Matrix
===============

The coverage matrix provides a visual representation of mapping relationships across frameworks:

.. mermaid::

   graph TB
       subgraph "Framework Coverage Matrix"
           MATRIX["
           📊 Coverage Matrix
           
           Source → Target    | ISF  | NIST | ISO  | CIS
           -------------------|------|------|------|-----
           ISF               | 100% | 85%  | 90%  | 70%
           NIST CSF 2.0      | 80%  | 100% | 88%  | 75%
           ISO 27001         | 85%  | 82%  | 100% | 65%
           CIS Controls      | 65%  | 78%  | 70%  | 100%
           "]
       end
       
       subgraph "Coverage Indicators"
           HIGH_COV["🟢 High Coverage (80%+)<br/>Strong alignment<br/>Comprehensive mapping"]
           MED_COV["🟡 Medium Coverage (60-80%)<br/>Good alignment<br/>Some gaps"]
           LOW_COV["🔴 Low Coverage (<60%)<br/>Limited alignment<br/>Significant gaps"]
       end
       
       subgraph "Gap Analysis"
           GAPS["📈 Identified Gaps<br/>• Physical security controls<br/>• Supply chain security<br/>• Privacy-specific controls<br/>• Emerging technology risks"]
       end
       
       MATRIX --> HIGH_COV
       MATRIX --> MED_COV
       MATRIX --> LOW_COV
       MATRIX --> GAPS
       
       style MATRIX fill:#f8f9fa
       style HIGH_COV fill:#c8e6c9
       style MED_COV fill:#fff9c4
       style LOW_COV fill:#ffcdd2
       style GAPS fill:#e1f5fe

API Reference
=============

Cross-Framework Mapping Endpoints
---------------------------------

**Mapping Management**

.. code-block:: http

   GET /api/v1/mappings
   POST /api/v1/mappings
   GET /api/v1/mappings/{id}
   PUT /api/v1/mappings/{id}
   DELETE /api/v1/mappings/{id}

**Mapping Sets**

.. code-block:: http

   GET /api/v1/mapping-sets
   POST /api/v1/mapping-sets
   GET /api/v1/mapping-sets/{id}
   GET /api/v1/mapping-sets/{id}/mappings

**Search and Analysis**

.. code-block:: http

   POST /api/v1/mappings/search
   GET /api/v1/mappings/coverage-matrix
   GET /api/v1/mappings/gap-analysis
   POST /api/v1/mappings/validate

**Bulk Operations**

.. code-block:: http

   POST /api/v1/mappings/bulk-create
   POST /api/v1/mappings/bulk-validate
   POST /api/v1/mappings/export
   POST /api/v1/mappings/import

Example API Usage
-----------------

**Create Framework Mapping**

.. code-block:: python

   import requests
   
   mapping_request = {
       "source_framework": "ISF",
       "source_control_id": "ISF.01.01",
       "target_framework": "NIST_CSF_2",
       "target_control_id": "GV.GV-01",
       "mapping_type": "direct",
       "confidence_level": "high",
       "confidence_score": 0.9,
       "description": "Both controls focus on establishing organizational cybersecurity strategy",
       "rationale": "Direct alignment between ISF policy requirements and NIST CSF governance",
       "mapping_set_id": 1
   }
   
   response = requests.post('/api/v1/mappings', json=mapping_request)
   mapping = response.json()

**Search Mappings**

.. code-block:: python

   search_request = {
       "source_framework": "CIS_CONTROLS",
       "target_framework": "ISO_27001",
       "confidence_level": "high",
       "validation_status": "validated",
       "include_rationale": True
   }
   
   response = requests.post('/api/v1/mappings/search', json=search_request)
   mappings = response.json()
   
   for mapping in mappings['results']:
       print(f"Mapping: {mapping['source_control_id']} → {mapping['target_control_id']}")
       print(f"Confidence: {mapping['confidence_score']}")

**Get Coverage Matrix**

.. code-block:: python

   response = requests.get('/api/v1/mappings/coverage-matrix')
   matrix = response.json()
   
   for source_framework, targets in matrix['coverage'].items():
       print(f"\n{source_framework} Coverage:")
       for target_framework, coverage in targets.items():
           print(f"  → {target_framework}: {coverage['coverage_percentage']}%")

**Validate Mappings**

.. code-block:: python

   validation_request = {
       "mapping_ids": [1, 2, 3, 4, 5],
       "validator": "security_expert",
       "validation_criteria": {
           "accuracy": True,
           "completeness": True,
           "consistency": True
       },
       "comments": "Reviewed mappings for accuracy and completeness"
   }
   
   response = requests.post('/api/v1/mappings/validate', json=validation_request)
   validation_result = response.json()

Data Model
==========

The mapping system uses a comprehensive data model to capture all aspects of framework relationships:

.. mermaid::

   erDiagram
       MappingSet ||--o{ FrameworkMapping : contains
       FrameworkMapping ||--o{ MappingValidation : validated_by
       FrameworkMapping ||--o{ MappingComment : has
       
       MappingSet {
           int id PK
           string name
           text description
           string source_framework
           string target_framework
           string version
           boolean is_official
           boolean is_published
           int total_mappings
           int validated_mappings
           float average_confidence
           datetime created_at
           datetime updated_at
       }
       
       FrameworkMapping {
           int id PK
           string source_framework
           string source_control_id
           string target_framework
           string target_control_id
           string mapping_type
           string confidence_level
           float confidence_score
           text description
           text rationale
           text notes
           boolean is_validated
           string validated_by
           datetime validated_at
           int mapping_set_id FK
           json mapping_data
           datetime created_at
           datetime updated_at
       }
       
       MappingValidation {
           int id PK
           int mapping_id FK
           string validator
           string validation_status
           text comments
           json validation_criteria
           datetime validation_date
       }

Mapping Quality Assurance
=========================

Quality assurance is critical for maintaining accurate and reliable mappings:

.. mermaid::

   graph TD
       subgraph "Quality Assurance Process"
           CREATE[Create Mapping<br/>Initial mapping creation<br/>Basic validation]
           REVIEW[Peer Review<br/>Expert review<br/>Accuracy check]
           VALIDATE[Formal Validation<br/>Quality assurance<br/>Final approval]
           MONITOR[Ongoing Monitoring<br/>Usage tracking<br/>Feedback collection]
       end
       
       subgraph "Quality Metrics"
           ACCURACY["📊 Accuracy<br/>Mapping correctness<br/>Expert validation"]
           COMPLETENESS["📋 Completeness<br/>Coverage assessment<br/>Gap identification"]
           CONSISTENCY["🔄 Consistency<br/>Uniform approach<br/>Standard methodology"]
           USABILITY["👥 Usability<br/>User feedback<br/>Practical application"]
       end
       
       subgraph "Quality Gates"
           QG1["Gate 1: Creation<br/>Basic validation<br/>Completeness check"]
           QG2["Gate 2: Review<br/>Peer validation<br/>Accuracy verification"]
           QG3["Gate 3: Approval<br/>Final validation<br/>Publication ready"]
       end
       
       CREATE --> QG1 --> REVIEW
       REVIEW --> QG2 --> VALIDATE
       VALIDATE --> QG3 --> MONITOR
       
       QG1 --> ACCURACY
       QG2 --> COMPLETENESS
       QG3 --> CONSISTENCY
       MONITOR --> USABILITY
       
       style CREATE fill:#e3f2fd
       style REVIEW fill:#e8f5e8
       style VALIDATE fill:#fff3e0
       style MONITOR fill:#f3e5f5

Best Practices
==============

Mapping Development Guidelines
-----------------------------

1. **Understand Framework Context**: Thoroughly understand both source and target frameworks
2. **Use Consistent Methodology**: Apply standardized mapping criteria across all mappings
3. **Document Rationale**: Provide clear justification for each mapping decision
4. **Validate Regularly**: Implement regular validation cycles to maintain accuracy
5. **Engage Experts**: Involve subject matter experts in mapping development and validation

Common Mapping Challenges
-------------------------

* **Scope Differences**: Frameworks may have different scopes and objectives
* **Granularity Mismatches**: Controls may be at different levels of detail
* **Terminology Variations**: Different frameworks use different terminology
* **Context Dependencies**: Mappings may vary based on organizational context
* **Evolution Over Time**: Frameworks evolve, requiring mapping updates

Quality Indicators
------------------

* **High Confidence Scores**: Mappings with confidence scores above 0.8
* **Expert Validation**: Mappings validated by recognized subject matter experts
* **Peer Review**: Mappings reviewed by multiple independent experts
* **Usage Feedback**: Positive feedback from organizations using the mappings
* **Regular Updates**: Mappings updated to reflect framework changes

Use Cases
=========

Cross-framework mapping supports various organizational use cases:

.. mermaid::

   graph LR
       subgraph "Compliance Use Cases"
           MULTI["📋 Multi-Framework Compliance<br/>Unified compliance view<br/>Reduced duplication"]
           AUDIT["🔍 Audit Preparation<br/>Evidence mapping<br/>Control alignment"]
           REPORTING["📈 Stakeholder Reporting<br/>Executive dashboards<br/>Board reporting"]
       end
       
       subgraph "Implementation Use Cases"
           PLANNING["📅 Implementation Planning<br/>Resource optimization<br/>Priority setting"]
           GAP["📊 Gap Analysis<br/>Coverage assessment<br/>Risk identification"]
           MIGRATION["🔄 Framework Migration<br/>Transition planning<br/>Legacy mapping"]
       end
       
       subgraph "Optimization Use Cases"
           RESOURCE["⚡ Resource Optimization<br/>Effort reduction<br/>Cost savings"]
           INTEGRATION["🔗 Tool Integration<br/>System alignment<br/>Process optimization"]
           TRAINING["🎓 Training & Education<br/>Framework comparison<br/>Knowledge transfer"]
       end
       
       style MULTI fill:#e1f5fe
       style AUDIT fill:#e8f5e8
       style REPORTING fill:#fff3e0
       style PLANNING fill:#f3e5f5
       style GAP fill:#fce4ec
       style MIGRATION fill:#f1f8e9

Next Steps
==========

* :doc:`../analytics/index` - Explore framework analytics capabilities
* :doc:`../isf/index` - Learn about ISF framework implementation
* :doc:`../nist_csf_2/index` - Understand NIST CSF 2.0 features
* :doc:`../iso_27001/index` - Discover ISO 27001 capabilities
* :doc:`../cis_controls/index` - Explore CIS Controls implementation

Resources
=========

* :doc:`../../api/index` - Complete API documentation
* :doc:`../../user_guide/index` - User guide and tutorials
* `Framework Mapping Best Practices <#>`_ - Industry best practices guide
* `Mapping Validation Guidelines <#>`_ - Quality assurance guidelines
