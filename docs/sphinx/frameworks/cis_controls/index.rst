==========================================
CIS Critical Security Controls v8
==========================================

The CIS Critical Security Controls v8 is a prioritized set of actions for cyber defense that provides specific and actionable ways to stop today's most pervasive and dangerous attacks. The Controls are developed by a community of IT experts who apply a real-world approach to cyber security.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
========

CIS Controls v8, released in May 2021, represents the latest evolution of the world's most widely adopted cybersecurity framework, providing a prioritized, asset-based approach to cybersecurity.

.. mermaid::

   graph TB
       subgraph "CIS Controls v8 Framework"
           VERSION["📋 CIS Controls Version 8<br/>Released: May 2021<br/>Status: Current"]
           
           subgraph "18 Core Controls"
               C1["1️⃣ Inventory and Control of Enterprise Assets"]
               C2["2️⃣ Inventory and Control of Software Assets"]
               C3["3️⃣ Data Protection"]
               C4["4️⃣ Secure Configuration of Enterprise Assets"]
               C5["5️⃣ Account Management"]
               C6["6️⃣ Access Control Management"]
               CPLUS["+ 12 Additional Controls<br/>Network Security, Malware Defense<br/>Data Recovery, Incident Response<br/>Penetration Testing, etc."]
           end
           
           subgraph "153 Safeguards"
               IG1["🟢 IG1: 56 Safeguards<br/>Essential Cyber Hygiene<br/>Small Organizations"]
               IG2["🟡 IG2: 130 Safeguards<br/>Enhanced Security<br/>Medium Organizations"]
               IG3["🔴 IG3: 153 Safeguards<br/>Advanced Security<br/>Large Organizations"]
           end
           
           subgraph "Asset-Based Approach"
               DEVICES["💻 Devices"]
               NETWORK["🌐 Network"]
               DATA["📊 Data"]
               SOFTWARE["💿 Software"]
               USERS["👥 Users"]
           end
       end
       
       VERSION --> C1
       VERSION --> C2
       VERSION --> C3
       VERSION --> C4
       VERSION --> C5
       VERSION --> C6
       VERSION --> CPLUS
       
       C1 --> IG1
       C2 --> IG2
       C3 --> IG3
       
       C1 --> DEVICES
       C2 --> SOFTWARE
       C3 --> DATA
       C5 --> USERS
       C6 --> NETWORK
       
       style VERSION fill:#fff3e0
       style C1 fill:#e3f2fd
       style C2 fill:#e3f2fd
       style C3 fill:#e3f2fd
       style IG1 fill:#c8e6c9
       style IG2 fill:#fff9c4
       style IG3 fill:#ffcdd2

What's New in CIS Controls v8
=============================

Key Enhancements from v7.1
---------------------------

.. mermaid::

   graph LR
       subgraph "CIS Controls v7.1"
           OLD_CONTROLS["20 Controls<br/>171 Sub-Controls"]
           OLD_IG["3 Implementation Groups<br/>Basic, Foundational, Organizational"]
           OLD_ASSETS["Asset Focus<br/>Limited Asset Types"]
       end
       
       subgraph "CIS Controls v8"
           NEW_CONTROLS["18 Controls<br/>153 Safeguards"]
           NEW_IG["3 Implementation Groups<br/>IG1, IG2, IG3<br/>Enhanced Guidance"]
           NEW_ASSETS["5 Asset Types<br/>Devices, Network, Data<br/>Software, Users"]
           NEW_FEATURES["Enhanced Automation<br/>Cloud Security<br/>Supply Chain Focus"]
       end
       
       OLD_CONTROLS -.->|Streamlined| NEW_CONTROLS
       OLD_IG -.->|Enhanced| NEW_IG
       OLD_ASSETS -.->|Expanded| NEW_ASSETS
       NEW_CONTROLS --> NEW_FEATURES
       
       style OLD_CONTROLS fill:#ffecb3
       style OLD_IG fill:#ffecb3
       style OLD_ASSETS fill:#ffecb3
       style NEW_CONTROLS fill:#c8e6c9
       style NEW_IG fill:#c8e6c9
       style NEW_ASSETS fill:#c8e6c9
       style NEW_FEATURES fill:#e1f5fe

**Major Improvements:**

1. **Streamlined Structure**: Reduced from 20 to 18 controls for better focus
2. **Asset-Based Organization**: Clear mapping to five asset types
3. **Enhanced Implementation Groups**: Better guidance for organizational maturity
4. **Cloud Security**: Improved coverage of cloud computing environments
5. **Supply Chain Security**: Enhanced focus on third-party risk management
6. **Automation Emphasis**: Greater focus on automation and tooling

Implementation Groups (IG)
==========================

CIS Controls v8 organizes safeguards into three Implementation Groups based on organizational size and security maturity:

.. mermaid::

   graph TD
       subgraph "Implementation Group 1 (IG1)"
           IG1_DESC["🟢 Essential Cyber Hygiene<br/>56 Safeguards<br/>Small Organizations<br/>Limited Resources"]
           IG1_ORGS["Target Organizations:<br/>• Small businesses<br/>• Limited IT staff<br/>• Basic security needs<br/>• Resource constraints"]
           IG1_FOCUS["Focus Areas:<br/>• Asset inventory<br/>• Basic access control<br/>• Secure configuration<br/>• Awareness training"]
       end
       
       subgraph "Implementation Group 2 (IG2)"
           IG2_DESC["🟡 Enhanced Security<br/>130 Safeguards<br/>Medium Organizations<br/>Dedicated IT Resources"]
           IG2_ORGS["Target Organizations:<br/>• Medium enterprises<br/>• Dedicated IT staff<br/>• Moderate risk tolerance<br/>• Growing security needs"]
           IG2_FOCUS["Focus Areas:<br/>• Enhanced monitoring<br/>• Incident response<br/>• Vulnerability management<br/>• Security awareness"]
       end
       
       subgraph "Implementation Group 3 (IG3)"
           IG3_DESC["🔴 Advanced Security<br/>153 Safeguards<br/>Large Organizations<br/>Mature Security Programs"]
           IG3_ORGS["Target Organizations:<br/>• Large enterprises<br/>• High-risk environments<br/>• Regulatory requirements<br/>• Advanced threats"]
           IG3_FOCUS["Focus Areas:<br/>• Advanced threat detection<br/>• Threat hunting<br/>• Forensics capabilities<br/>• Continuous monitoring"]
       end
       
       IG1_DESC --> IG1_ORGS --> IG1_FOCUS
       IG2_DESC --> IG2_ORGS --> IG2_FOCUS
       IG3_DESC --> IG3_ORGS --> IG3_FOCUS
       
       IG1_FOCUS -.->|Builds Upon| IG2_FOCUS
       IG2_FOCUS -.->|Builds Upon| IG3_FOCUS
       
       style IG1_DESC fill:#c8e6c9
       style IG2_DESC fill:#fff9c4
       style IG3_DESC fill:#ffcdd2

Asset Types
===========

CIS Controls v8 organizes security around five core asset types:

.. mermaid::

   graph TB
       subgraph "Five Asset Types"
           DEVICES["💻 Devices<br/>End-user devices, servers<br/>Network devices, IoT devices<br/>Mobile devices"]
           NETWORK["🌐 Network<br/>Network infrastructure<br/>Network segments<br/>Network protocols"]
           DATA["📊 Data<br/>Sensitive data<br/>Intellectual property<br/>Personal information"]
           SOFTWARE["💿 Software<br/>Operating systems<br/>Applications<br/>Firmware"]
           USERS["👥 Users<br/>Employees<br/>Contractors<br/>Service accounts"]
       end
       
       subgraph "Security Functions"
           IDENTIFY["🔍 Identify<br/>Asset discovery<br/>Vulnerability assessment<br/>Risk identification"]
           PROTECT["🛡️ Protect<br/>Access control<br/>Secure configuration<br/>Data protection"]
           DETECT["🚨 Detect<br/>Monitoring<br/>Anomaly detection<br/>Threat detection"]
           RESPOND["🚑 Respond<br/>Incident response<br/>Containment<br/>Recovery"]
           RECOVER["🔄 Recover<br/>Backup restoration<br/>Business continuity<br/>Lessons learned"]
       end
       
       DEVICES --> IDENTIFY
       NETWORK --> PROTECT
       DATA --> DETECT
       SOFTWARE --> RESPOND
       USERS --> RECOVER
       
       style DEVICES fill:#e3f2fd
       style NETWORK fill:#e8f5e8
       style DATA fill:#fff3e0
       style SOFTWARE fill:#f3e5f5
       style USERS fill:#fce4ec

Core Controls Deep Dive
=======================

The 18 CIS Controls are organized into foundational, advanced, and organizational categories:

.. mermaid::

   graph TB
       subgraph "Foundational Controls (1-6)"
           C1["Control 1<br/>Enterprise Asset Inventory<br/>Know what you have"]
           C2["Control 2<br/>Software Asset Inventory<br/>Know what's running"]
           C3["Control 3<br/>Data Protection<br/>Protect sensitive data"]
           C4["Control 4<br/>Secure Configuration<br/>Harden systems"]
           C5["Control 5<br/>Account Management<br/>Control user accounts"]
           C6["Control 6<br/>Access Control<br/>Limit access rights"]
       end
       
       subgraph "Advanced Controls (7-12)"
           C7["Control 7<br/>Continuous Vulnerability Management"]
           C8["Control 8<br/>Audit Log Management"]
           C9["Control 9<br/>Email & Web Browser Protections"]
           C10["Control 10<br/>Malware Defenses"]
           C11["Control 11<br/>Data Recovery"]
           C12["Control 12<br/>Network Infrastructure Management"]
       end
       
       subgraph "Organizational Controls (13-18)"
           C13["Control 13<br/>Network Monitoring & Defense"]
           C14["Control 14<br/>Security Awareness & Skills Training"]
           C15["Control 15<br/>Service Provider Management"]
           C16["Control 16<br/>Application Software Security"]
           C17["Control 17<br/>Incident Response Management"]
           C18["Control 18<br/>Penetration Testing"]
       end
       
       C1 --> C2 --> C3 --> C4 --> C5 --> C6
       C6 -.-> C7 --> C8 --> C9 --> C10 --> C11 --> C12
       C12 -.-> C13 --> C14 --> C15 --> C16 --> C17 --> C18
       
       style C1 fill:#e3f2fd
       style C2 fill:#e3f2fd
       style C3 fill:#e3f2fd
       style C4 fill:#e3f2fd
       style C5 fill:#e3f2fd
       style C6 fill:#e3f2fd
       style C7 fill:#e8f5e8
       style C8 fill:#e8f5e8
       style C13 fill:#fff3e0
       style C14 fill:#fff3e0

Implementation Workflow
=======================

CIS Controls implementation follows a prioritized, risk-based approach:

.. mermaid::

   graph TD
       START([Start CIS Controls Implementation])
       
       ASSESS[Assessment Phase<br/>Current state analysis<br/>Asset inventory<br/>Risk assessment]
       PRIORITIZE[Prioritization Phase<br/>Select Implementation Group<br/>Prioritize controls<br/>Resource planning]
       IMPLEMENT[Implementation Phase<br/>Deploy safeguards<br/>Configure tools<br/>Train personnel]
       MEASURE[Measurement Phase<br/>Monitor effectiveness<br/>Collect metrics<br/>Assess compliance]
       IMPROVE[Improvement Phase<br/>Analyze results<br/>Identify gaps<br/>Continuous improvement]
       
       START --> ASSESS
       ASSESS --> PRIORITIZE
       PRIORITIZE --> IMPLEMENT
       IMPLEMENT --> MEASURE
       MEASURE --> IMPROVE
       IMPROVE -.->|Continuous Cycle| ASSESS
       
       subgraph "IG1 Foundation"
           IG1_CONTROLS["Controls 1-6<br/>Basic Safeguards<br/>Essential Hygiene"]
       end
       
       subgraph "IG2 Enhancement"
           IG2_CONTROLS["Controls 7-12<br/>Enhanced Monitoring<br/>Advanced Protection"]
       end
       
       subgraph "IG3 Maturity"
           IG3_CONTROLS["Controls 13-18<br/>Organizational Controls<br/>Advanced Capabilities"]
       end
       
       PRIORITIZE --> IG1_CONTROLS
       IG1_CONTROLS --> IG2_CONTROLS
       IG2_CONTROLS --> IG3_CONTROLS
       
       style START fill:#4caf50
       style ASSESS fill:#2196f3
       style PRIORITIZE fill:#ff9800
       style IMPLEMENT fill:#9c27b0
       style MEASURE fill:#f44336
       style IMPROVE fill:#607d8b

API Reference
=============

CIS Controls Framework Endpoints
-------------------------------

**Version Management**

.. code-block:: http

   GET /api/v1/cis-controls/versions
   GET /api/v1/cis-controls/versions/current
   POST /api/v1/cis-controls/versions

**Controls and Safeguards**

.. code-block:: http

   GET /api/v1/cis-controls/controls
   GET /api/v1/cis-controls/safeguards
   GET /api/v1/cis-controls/safeguards/{id}
   PUT /api/v1/cis-controls/safeguards/{id}

**Implementation Groups**

.. code-block:: http

   GET /api/v1/cis-controls/implementation-groups
   GET /api/v1/cis-controls/implementation-groups/{ig}
   GET /api/v1/cis-controls/safeguards?implementation_group=IG1

**Search and Filtering**

.. code-block:: http

   POST /api/v1/cis-controls/search
   GET /api/v1/cis-controls/safeguards?asset_type=devices
   GET /api/v1/cis-controls/safeguards?security_function=protect

Example API Usage
-----------------

**Get IG1 Safeguards**

.. code-block:: python

   import requests
   
   response = requests.get('/api/v1/cis-controls/safeguards?implementation_group=IG1')
   ig1_safeguards = response.json()
   
   print(f"IG1 Safeguards: {len(ig1_safeguards['safeguards'])}")
   for safeguard in ig1_safeguards['safeguards']:
       print(f"Safeguard: {safeguard['safeguard_id']} - {safeguard['title']}")

**Search by Asset Type**

.. code-block:: python

   search_request = {
       "asset_type": "devices",
       "implementation_group": "IG2",
       "include_examples": True
   }
   
   response = requests.post('/api/v1/cis-controls/search', json=search_request)
   results = response.json()
   
   for safeguard in results['results']:
       print(f"Control {safeguard['control_id']}: {safeguard['title']}")

**Create Assessment**

.. code-block:: python

   assessment_request = {
       "name": "Q1 2024 CIS Controls Assessment",
       "target_implementation_group": "IG2",
       "assessment_scope": "Controls 1-12",
       "assessor": "Security Team",
       "assessment_type": "self"
   }
   
   response = requests.post('/api/v1/cis-controls/assessments', json=assessment_request)
   assessment = response.json()

Data Model
==========

The CIS Controls implementation uses a hierarchical structure:

.. mermaid::

   erDiagram
       CISControlsVersion ||--o{ CISControl : contains
       CISControl ||--o{ CISSafeguard : contains
       CISSafeguard ||--o{ CISImplementationExample : has
       CISControlsVersion ||--o{ CISImplementationGroup : defines
       CISControlsVersion ||--o{ CISAssessment : assessed_by
       
       CISControlsVersion {
           int id PK
           string version
           string release_date
           text description
           boolean is_current
           int total_controls
           int total_safeguards
           json implementation_groups
       }
       
       CISControl {
           int id PK
           string control_id
           string title
           text description
           int version_id FK
           int order_index
           string control_type
           json asset_types
           json security_functions
           int total_safeguards
           int ig1_safeguards
           int ig2_safeguards
           int ig3_safeguards
       }
       
       CISSafeguard {
           int id PK
           string safeguard_id
           string title
           text description
           int control_id FK
           int version_id FK
           boolean implementation_group_1
           boolean implementation_group_2
           boolean implementation_group_3
           json asset_types
           text implementation_guidance
       }

Best Practices
==============

Implementation Success Factors
------------------------------

1. **Start with IG1**: Begin with essential cyber hygiene before advancing
2. **Asset-First Approach**: Understand your assets before implementing controls
3. **Automation Focus**: Leverage automation tools for scalability and consistency
4. **Measurement-Driven**: Establish metrics to measure control effectiveness
5. **Continuous Improvement**: Regularly assess and improve implementation

Common Implementation Challenges
-------------------------------

* **Scope Creep**: Trying to implement too many controls simultaneously
* **Tool Proliferation**: Deploying too many security tools without integration
* **Insufficient Automation**: Manual processes that don't scale
* **Lack of Metrics**: No measurement of control effectiveness
* **Resource Constraints**: Insufficient staffing or budget for implementation

IG Progression Strategy
----------------------

* **IG1 First**: Establish foundational controls before advancing
* **Measure Effectiveness**: Ensure IG1 controls are working before IG2
* **Resource Assessment**: Verify adequate resources for next IG level
* **Risk-Based Prioritization**: Focus on highest-risk areas first
* **Stakeholder Buy-in**: Ensure organizational support for advancement

Integration with Other Frameworks
==================================

CIS Controls integrate well with other cybersecurity frameworks:

.. mermaid::

   graph LR
       CIS[CIS Controls v8]
       
       subgraph "Strategic Frameworks"
           NIST[NIST CSF 2.0<br/>Strategic Alignment]
           ISF[ISF 2022<br/>Governance Integration]
           ISO[ISO 27001<br/>Management System]
       end
       
       subgraph "Technical Standards"
           NIST800[NIST 800-53<br/>Control Catalog]
           SANS[SANS Top 20<br/>Critical Controls]
           OWASP[OWASP Top 10<br/>Application Security]
       end
       
       subgraph "Compliance Requirements"
           PCI[PCI DSS<br/>Payment Security]
           HIPAA[HIPAA<br/>Healthcare Security]
           SOC2[SOC 2<br/>Trust Services]
       end
       
       CIS -.->|Tactical Implementation| NIST
       CIS -.->|Operational Controls| ISF
       CIS -.->|Technical Controls| ISO
       
       CIS -.->|Control Details| NIST800
       CIS -.->|Priority Alignment| SANS
       CIS -.->|Application Security| OWASP
       
       CIS -.->|Payment Controls| PCI
       CIS -.->|Healthcare Controls| HIPAA
       CIS -.->|Trust Services| SOC2
       
       style CIS fill:#fff3e0

Next Steps
==========

* :doc:`../isf/index` - Compare with ISF framework
* :doc:`../nist_csf_2/index` - Explore NIST CSF integration
* :doc:`../iso_27001/index` - Learn about ISO 27001 alignment
* :doc:`../mapping/index` - Master cross-framework mapping
* :doc:`../analytics/index` - Use CIS Controls analytics

Resources
=========

* `CIS Controls v8 Official Site <https://www.cisecurity.org/controls/v8>`_
* `CIS Controls Implementation Guide <https://www.cisecurity.org/controls/implementation-guide>`_
* `CIS Community <https://www.cisecurity.org/cis-community>`_
* :doc:`../../api/index` - Complete API documentation
* :doc:`../../user_guide/index` - User guide and tutorials
