=======================================
ISO/IEC 27001:2022 Framework
=======================================

ISO/IEC 27001:2022 is the international standard for Information Security Management Systems (ISMS). It provides a systematic approach to managing sensitive company information and ensuring its security through risk management processes.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
========

ISO/IEC 27001:2022 is the latest version of the world's most widely recognized information security standard, providing a framework for establishing, implementing, maintaining, and continually improving an ISMS.

.. mermaid::

   graph TB
       subgraph "ISO/IEC 27001:2022 Framework"
           VERSION["📋 ISO/IEC 27001:2022<br/>Released: October 2022<br/>Status: Current"]
           
           subgraph "ISMS Core Components"
               CONTEXT["🎯 Context of Organization<br/>Understanding needs<br/>Scope definition"]
               LEADERSHIP["👑 Leadership<br/>Management commitment<br/>Policy & roles"]
               PLANNING["📋 Planning<br/>Risk assessment<br/>Risk treatment"]
               SUPPORT["🤝 Support<br/>Resources & competence<br/>Communication"]
               OPERATION["⚙️ Operation<br/>Risk treatment<br/>Implementation"]
               EVALUATION["📊 Performance Evaluation<br/>Monitoring & measurement<br/>Internal audit"]
               IMPROVEMENT["🔄 Improvement<br/>Nonconformity<br/>Continual improvement"]
           end
           
           subgraph "Annex A Controls"
               A5["A.5 Information Security Policies"]
               A6["A.6 Organization of Information Security"]
               A7["A.7 Human Resource Security"]
               A8["A.8 Asset Management"]
               A9["A.9 Access Control"]
               APLUS["+ 9 Additional Domains<br/>93 Total Controls"]
           end
       end
       
       VERSION --> CONTEXT
       VERSION --> LEADERSHIP
       VERSION --> PLANNING
       VERSION --> SUPPORT
       VERSION --> OPERATION
       VERSION --> EVALUATION
       VERSION --> IMPROVEMENT
       
       PLANNING --> A5
       OPERATION --> A6
       OPERATION --> A7
       OPERATION --> A8
       OPERATION --> A9
       OPERATION --> APLUS
       
       style VERSION fill:#e8f5e8
       style CONTEXT fill:#e3f2fd
       style LEADERSHIP fill:#f3e5f5
       style PLANNING fill:#fff3e0
       style SUPPORT fill:#e8f5e8
       style OPERATION fill:#ffecb3
       style EVALUATION fill:#f1f8e9
       style IMPROVEMENT fill:#fce4ec

What's New in ISO 27001:2022
=============================

Key Changes from 2013 Version
-----------------------------

.. mermaid::

   graph LR
       subgraph "ISO 27001:2013"
           OLD_CONTROLS["114 Controls<br/>14 Domains"]
           OLD_STRUCTURE["Annex A Structure<br/>Traditional Categories"]
           OLD_THEMES["Limited Thematic<br/>Organization"]
       end
       
       subgraph "ISO 27001:2022"
           NEW_CONTROLS["93 Controls<br/>4 Domains"]
           NEW_STRUCTURE["Reorganized Structure<br/>Thematic Grouping"]
           NEW_THEMES["People, Physical,<br/>Technological, Organizational"]
           NEW_FEATURES["Enhanced Guidance<br/>Cloud Computing<br/>Supply Chain"]
       end
       
       OLD_CONTROLS -.->|Consolidation| NEW_CONTROLS
       OLD_STRUCTURE -.->|Reorganization| NEW_STRUCTURE
       OLD_THEMES -.->|Enhancement| NEW_THEMES
       NEW_STRUCTURE --> NEW_FEATURES
       
       style OLD_CONTROLS fill:#ffecb3
       style OLD_STRUCTURE fill:#ffecb3
       style OLD_THEMES fill:#ffecb3
       style NEW_CONTROLS fill:#c8e6c9
       style NEW_STRUCTURE fill:#c8e6c9
       style NEW_THEMES fill:#c8e6c9
       style NEW_FEATURES fill:#e1f5fe

**Major Improvements:**

1. **Streamlined Controls**: Reduced from 114 to 93 controls through consolidation
2. **Thematic Organization**: Controls organized by themes (People, Physical, Technological, Organizational)
3. **Enhanced Cloud Guidance**: Better coverage of cloud computing security
4. **Supply Chain Focus**: Increased emphasis on supplier relationships
5. **Threat Intelligence**: New controls for threat intelligence and vulnerability management

ISMS Structure (Plan-Do-Check-Act)
===================================

ISO 27001 follows the Plan-Do-Check-Act (PDCA) cycle for continuous improvement:

.. mermaid::

   graph TD
       subgraph "PDCA Cycle for ISMS"
           PLAN["📋 PLAN<br/>• Establish ISMS<br/>• Risk assessment<br/>• Risk treatment<br/>• SoA creation"]
           DO["⚙️ DO<br/>• Implement controls<br/>• Execute processes<br/>• Training & awareness<br/>• Document procedures"]
           CHECK["📊 CHECK<br/>• Monitor & measure<br/>• Internal audits<br/>• Management review<br/>• Performance evaluation"]
           ACT["🔄 ACT<br/>• Corrective actions<br/>• Continual improvement<br/>• Update risk assessment<br/>• Revise controls"]
       end
       
       PLAN --> DO
       DO --> CHECK
       CHECK --> ACT
       ACT --> PLAN
       
       subgraph "External Context"
           STAKEHOLDERS["👥 Interested Parties<br/>Customers, Regulators<br/>Partners, Employees"]
           REQUIREMENTS["📜 Requirements<br/>Legal, Regulatory<br/>Contractual, Business"]
           RISKS["⚠️ Risks & Opportunities<br/>Information Security<br/>Business Continuity"]
       end
       
       STAKEHOLDERS --> PLAN
       REQUIREMENTS --> PLAN
       RISKS --> PLAN
       
       style PLAN fill:#2196f3
       style DO fill:#4caf50
       style CHECK fill:#ff9800
       style ACT fill:#9c27b0

Annex A Control Domains
=======================

ISO 27001:2022 organizes controls into four thematic domains:

.. mermaid::

   graph TB
       subgraph "Organizational Controls"
           A5["A.5 Information Security Policies<br/>5.1 Information security policy<br/>5.2 Information security roles<br/>5.3 Segregation of duties"]
           A6["A.6 Organization of Information Security<br/>6.1 Internal organization<br/>6.2 Mobile devices<br/>6.3 Teleworking"]
           A7["A.7 Human Resource Security<br/>7.1 Prior to employment<br/>7.2 During employment<br/>7.3 Termination"]
           A8["A.8 Asset Management<br/>8.1 Responsibility for assets<br/>8.2 Information classification<br/>8.3 Media handling"]
       end
       
       subgraph "People Controls"
           A9["A.9 Access Control<br/>9.1 Business requirements<br/>9.2 User access management<br/>9.3 User responsibilities<br/>9.4 System access control"]
           A10["A.10 Cryptography<br/>10.1 Cryptographic controls"]
           A11["A.11 Physical Security<br/>11.1 Secure areas<br/>11.2 Equipment"]
       end
       
       subgraph "Physical Controls"
           A12["A.12 Operations Security<br/>12.1 Operational procedures<br/>12.2 Protection from malware<br/>12.3 Backup<br/>12.4 Logging & monitoring<br/>12.5 Control of operational software<br/>12.6 Technical vulnerability management<br/>12.7 Information systems audit"]
           A13["A.13 Communications Security<br/>13.1 Network security management<br/>13.2 Information transfer"]
       end
       
       subgraph "Technological Controls"
           A14["A.14 System Acquisition<br/>14.1 Security requirements<br/>14.2 Security in development<br/>14.3 Test data"]
           A15["A.15 Supplier Relationships<br/>15.1 Information security in supplier relationships<br/>15.2 Supplier service delivery management"]
           A16["A.16 Information Security Incident Management<br/>16.1 Management of incidents"]
           A17["A.17 Business Continuity<br/>17.1 Information security continuity<br/>17.2 Redundancies"]
           A18["A.18 Compliance<br/>18.1 Compliance with legal requirements<br/>18.2 Information security reviews"]
       end
       
       style A5 fill:#e8f5e8
       style A6 fill:#e8f5e8
       style A7 fill:#e8f5e8
       style A8 fill:#e8f5e8
       style A9 fill:#e3f2fd
       style A10 fill:#e3f2fd
       style A11 fill:#fff3e0
       style A12 fill:#f3e5f5
       style A13 fill:#f3e5f5
       style A14 fill:#fce4ec
       style A15 fill:#fce4ec
       style A16 fill:#fce4ec
       style A17 fill:#fce4ec
       style A18 fill:#fce4ec

Certification Process
=====================

The ISO 27001 certification process follows a structured approach:

.. mermaid::

   graph TD
       START([Start Certification Journey])
       
       PHASE1[Phase 1: Gap Analysis<br/>Current state assessment<br/>Identify gaps<br/>Create implementation plan]
       PHASE2[Phase 2: ISMS Implementation<br/>Develop policies & procedures<br/>Implement controls<br/>Train personnel]
       PHASE3[Phase 3: Internal Audit<br/>Conduct internal audits<br/>Management review<br/>Corrective actions]
       PHASE4[Phase 4: Certification Audit<br/>Stage 1: Documentation review<br/>Stage 2: Implementation audit<br/>Certification decision]
       PHASE5[Phase 5: Surveillance<br/>Annual surveillance audits<br/>Continuous improvement<br/>3-year recertification]
       
       START --> PHASE1
       PHASE1 --> PHASE2
       PHASE2 --> PHASE3
       PHASE3 --> PHASE4
       PHASE4 --> PHASE5
       PHASE5 -.->|Continuous Cycle| PHASE3
       
       subgraph "Certification Body Activities"
           STAGE1["📋 Stage 1 Audit<br/>Documentation review<br/>Readiness assessment"]
           STAGE2["🔍 Stage 2 Audit<br/>On-site implementation<br/>Evidence verification"]
           DECISION["✅ Certification Decision<br/>Certificate issuance<br/>Scope definition"]
       end
       
       PHASE4 --> STAGE1
       STAGE1 --> STAGE2
       STAGE2 --> DECISION
       
       style START fill:#4caf50
       style PHASE1 fill:#2196f3
       style PHASE2 fill:#ff9800
       style PHASE3 fill:#9c27b0
       style PHASE4 fill:#f44336
       style PHASE5 fill:#607d8b

API Reference
=============

ISO 27001 Framework Endpoints
-----------------------------

**Version Management**

.. code-block:: http

   GET /api/v1/iso-27001/versions
   GET /api/v1/iso-27001/versions/current
   POST /api/v1/iso-27001/versions

**Domains and Controls**

.. code-block:: http

   GET /api/v1/iso-27001/domains
   GET /api/v1/iso-27001/controls
   GET /api/v1/iso-27001/controls/{id}
   PUT /api/v1/iso-27001/controls/{id}

**ISMS Management**

.. code-block:: http

   GET /api/v1/iso-27001/isms
   POST /api/v1/iso-27001/isms
   GET /api/v1/iso-27001/isms/{id}
   PUT /api/v1/iso-27001/isms/{id}

**Assessments and Audits**

.. code-block:: http

   GET /api/v1/iso-27001/assessments
   POST /api/v1/iso-27001/assessments
   GET /api/v1/iso-27001/assessments/{id}/report

Example API Usage
-----------------

**Get ISO 27001 Controls by Domain**

.. code-block:: python

   import requests
   
   response = requests.get('/api/v1/iso-27001/controls?domain=A.9')
   access_controls = response.json()
   
   for control in access_controls['controls']:
       print(f"Control: {control['control_id']} - {control['name']}")
       print(f"Objective: {control['objective']}")

**Create ISMS Instance**

.. code-block:: python

   isms_request = {
       "name": "Corporate ISMS",
       "description": "Information Security Management System for corporate environment",
       "scope": "All information systems and processes within corporate headquarters",
       "organization_context": {
           "industry": "financial_services",
           "size": "large",
           "locations": ["headquarters", "branch_offices"]
       },
       "risk_appetite": "low",
       "certification_target": True
   }
   
   response = requests.post('/api/v1/iso-27001/isms', json=isms_request)
   isms = response.json()

**Conduct Compliance Assessment**

.. code-block:: python

   assessment_request = {
       "name": "Q1 2024 Compliance Assessment",
       "assessment_type": "internal",
       "scope": "All Annex A controls",
       "assessor": "Internal Audit Team",
       "assessment_criteria": {
           "evidence_required": True,
           "implementation_level": "full",
           "effectiveness_review": True
       }
   }
   
   response = requests.post('/api/v1/iso-27001/assessments', json=assessment_request)
   assessment = response.json()

Data Model
==========

The ISO 27001 implementation follows the standard's structure:

.. mermaid::

   erDiagram
       ISO27001Version ||--o{ ISO27001Domain : contains
       ISO27001Domain ||--o{ ISO27001Control : contains
       ISO27001Control ||--o{ ISO27001ImplementationExample : has
       ISO27001Version ||--o{ ISO27001ISMS : implements
       ISO27001ISMS ||--o{ ISO27001Assessment : assessed_by
       ISO27001Assessment ||--o{ ISO27001Finding : contains
       
       ISO27001Version {
           int id PK
           string version
           string release_date
           text description
           boolean is_current
           string certification_body
       }
       
       ISO27001Domain {
           int id PK
           string domain_id
           string name
           text description
           int version_id FK
           int order_index
           int control_count
       }
       
       ISO27001Control {
           int id PK
           string control_id
           string name
           text description
           text objective
           text implementation_guidance
           int domain_id FK
           int version_id FK
           string control_type
       }
       
       ISO27001ISMS {
           int id PK
           string name
           text description
           text scope
           json organization_context
           string risk_appetite
           boolean certification_target
           string implementation_status
           string certification_status
       }

Implementation Guide
====================

ISMS Implementation Roadmap
---------------------------

.. mermaid::

   graph TD
       subgraph "Phase 1: Foundation (Months 1-3)"
           P1_1["Define ISMS Scope<br/>Identify boundaries<br/>Document context"]
           P1_2["Leadership Commitment<br/>Secure management support<br/>Assign responsibilities"]
           P1_3["Initial Risk Assessment<br/>Identify assets<br/>Assess threats & vulnerabilities"]
       end
       
       subgraph "Phase 2: Planning (Months 4-6)"
           P2_1["Develop Policies<br/>Information security policy<br/>Supporting procedures"]
           P2_2["Risk Treatment Plan<br/>Select controls<br/>Create SoA"]
           P2_3["Resource Planning<br/>Allocate resources<br/>Training plan"]
       end
       
       subgraph "Phase 3: Implementation (Months 7-12)"
           P3_1["Deploy Controls<br/>Implement selected controls<br/>Document procedures"]
           P3_2["Training & Awareness<br/>Staff training<br/>Awareness programs"]
           P3_3["Operational Processes<br/>Incident management<br/>Change management"]
       end
       
       subgraph "Phase 4: Monitoring (Months 13-15)"
           P4_1["Internal Audits<br/>Audit program<br/>Corrective actions"]
           P4_2["Management Review<br/>Performance evaluation<br/>Improvement planning"]
           P4_3["Certification Prep<br/>External audit<br/>Certification"]
       end
       
       P1_1 --> P1_2 --> P1_3
       P1_3 --> P2_1 --> P2_2 --> P2_3
       P2_3 --> P3_1 --> P3_2 --> P3_3
       P3_3 --> P4_1 --> P4_2 --> P4_3
       
       style P1_1 fill:#e3f2fd
       style P2_1 fill:#e8f5e8
       style P3_1 fill:#fff3e0
       style P4_1 fill:#f3e5f5

Best Practices
==============

Success Factors
---------------

1. **Top Management Commitment**: Ensure visible leadership support and resource allocation
2. **Risk-Based Approach**: Focus on business-relevant risks and proportionate controls
3. **Stakeholder Engagement**: Involve all relevant stakeholders in ISMS development
4. **Continuous Improvement**: Establish ongoing monitoring and improvement processes
5. **Documentation Balance**: Maintain necessary documentation without over-documenting

Common Pitfalls
---------------

* **Over-Documentation**: Creating excessive documentation that becomes difficult to maintain
* **Checkbox Mentality**: Implementing controls without understanding their purpose
* **Insufficient Risk Assessment**: Inadequate risk identification and analysis
* **Poor Change Management**: Failing to manage organizational change effectively
* **Lack of Integration**: Treating ISMS as separate from business processes

Certification Readiness
-----------------------

* **Documentation Complete**: All required policies, procedures, and records in place
* **Controls Implemented**: All selected controls fully operational with evidence
* **Training Completed**: All personnel trained on their ISMS responsibilities
* **Audits Conducted**: Internal audits completed with corrective actions addressed
* **Management Review**: Management review conducted with improvement actions

Integration with Other Frameworks
==================================

ISO 27001 integrates well with other management systems and frameworks:

.. mermaid::

   graph LR
       ISO27001[ISO 27001<br/>ISMS]
       
       subgraph "ISO Management Systems"
           ISO9001[ISO 9001<br/>Quality Management]
           ISO14001[ISO 14001<br/>Environmental Management]
           ISO45001[ISO 45001<br/>Occupational Health & Safety]
       end
       
       subgraph "Cybersecurity Frameworks"
           NIST[NIST CSF 2.0<br/>Risk Framework]
           ISF[ISF 2022<br/>Comprehensive Security]
           CIS[CIS Controls<br/>Technical Controls]
       end
       
       subgraph "Compliance Standards"
           SOC2[SOC 2<br/>Trust Services]
           GDPR[GDPR<br/>Data Protection]
           PCI[PCI DSS<br/>Payment Security]
       end
       
       ISO27001 -.->|Integrated Management| ISO9001
       ISO27001 -.->|Environmental Security| ISO14001
       ISO27001 -.->|Physical Security| ISO45001
       
       ISO27001 -.->|Risk Management| NIST
       ISO27001 -.->|Governance Alignment| ISF
       ISO27001 -.->|Technical Implementation| CIS
       
       ISO27001 -.->|Trust Services| SOC2
       ISO27001 -.->|Privacy Controls| GDPR
       ISO27001 -.->|Payment Security| PCI
       
       style ISO27001 fill:#e8f5e8

Next Steps
==========

* :doc:`../isf/index` - Compare with ISF framework
* :doc:`../nist_csf_2/index` - Explore NIST CSF integration
* :doc:`../cis_controls/index` - Learn about CIS Controls alignment
* :doc:`../mapping/index` - Master cross-framework mapping
* :doc:`../analytics/index` - Use ISO 27001 analytics

Resources
=========

* `ISO 27001:2022 Official Standard <https://www.iso.org/standard/27001.html>`_
* `ISO/IEC 27002:2022 Implementation Guidance <https://www.iso.org/standard/75652.html>`_
* `ISO 27001 Certification Bodies <https://www.iso.org/certification.html>`_
* :doc:`../../api/index` - Complete API documentation
* :doc:`../../user_guide/index` - User guide and tutorials
