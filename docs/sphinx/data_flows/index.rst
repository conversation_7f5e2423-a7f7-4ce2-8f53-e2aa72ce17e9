===============================
Data Flows & System Architecture
===============================

This section provides comprehensive documentation of data flows, system architecture, and technical functionality from a data perspective. It covers how information moves through the system, data transformations, and the technical implementation of cybersecurity framework management.

.. contents:: Table of Contents
   :local:
   :depth: 2

Overview
========

The Regression Rigor platform implements a sophisticated data architecture that supports multiple cybersecurity frameworks with advanced search, linking, and analytics capabilities. The system processes framework data through multiple layers of transformation, enrichment, and analysis.

.. mermaid::

   graph TB
       subgraph "Data Sources"
           ISF_DATA["📋 ISF 2022 Data<br/>14 Security Areas<br/>56+ Controls"]
           NIST_DATA["🆕 NIST CSF 2.0 Data<br/>6 Functions<br/>108+ Subcategories"]
           ISO_DATA["📜 ISO 27001:2022 Data<br/>5 Domains<br/>93 Controls"]
           CIS_DATA["🛡️ CIS Controls v8 Data<br/>18 Controls<br/>153 Safeguards"]
       end
       
       subgraph "Data Ingestion Layer"
           IMPORT_SERVICE["🔄 Import Services<br/>Validation & Transformation<br/>Hierarchical Processing"]
           VALIDATION["✅ Data Validation<br/>Schema Compliance<br/>Integrity Checks"]
           TRANSFORMATION["🔄 Data Transformation<br/>Normalization<br/>Enrichment"]
       end
       
       subgraph "Core Data Layer"
           FRAMEWORK_DB["🗄️ Framework Database<br/>PostgreSQL<br/>Hierarchical Storage"]
           SEARCH_INDEX["🔍 Search Index<br/>Full-text Vectors<br/>Semantic Metadata"]
           MAPPING_DB["🔗 Mapping Database<br/>Cross-framework Relations<br/>Confidence Scoring"]
       end
       
       subgraph "Processing Layer"
           ANALYTICS_ENGINE["📊 Analytics Engine<br/>Gap Analysis<br/>Compliance Tracking"]
           ML_ENGINE["🤖 ML Engine<br/>Relationship Discovery<br/>Link Suggestions"]
           SEARCH_ENGINE["🔍 Search Engine<br/>Multi-modal Search<br/>Ranking Algorithms"]
       end
       
       subgraph "API Layer"
           REST_API["🌐 REST API<br/>Framework Endpoints<br/>Search & Analytics"]
           WEBSOCKET["⚡ WebSocket API<br/>Real-time Updates<br/>Live Analytics"]
       end
       
       subgraph "User Interface Layer"
           WEB_UI["💻 Web Interface<br/>Framework Management<br/>Search & Analytics"]
           MOBILE_UI["📱 Mobile Interface<br/>Dashboard Views<br/>Quick Access"]
       end
       
       ISF_DATA --> IMPORT_SERVICE
       NIST_DATA --> IMPORT_SERVICE
       ISO_DATA --> IMPORT_SERVICE
       CIS_DATA --> IMPORT_SERVICE
       
       IMPORT_SERVICE --> VALIDATION
       VALIDATION --> TRANSFORMATION
       TRANSFORMATION --> FRAMEWORK_DB
       TRANSFORMATION --> SEARCH_INDEX
       TRANSFORMATION --> MAPPING_DB
       
       FRAMEWORK_DB --> ANALYTICS_ENGINE
       SEARCH_INDEX --> SEARCH_ENGINE
       MAPPING_DB --> ML_ENGINE
       
       ANALYTICS_ENGINE --> REST_API
       ML_ENGINE --> REST_API
       SEARCH_ENGINE --> REST_API
       
       REST_API --> WEB_UI
       REST_API --> MOBILE_UI
       WEBSOCKET --> WEB_UI
       
       style ISF_DATA fill:#e1f5fe
       style NIST_DATA fill:#e8f5e8
       style ISO_DATA fill:#fff3e0
       style CIS_DATA fill:#f3e5f5
       style ANALYTICS_ENGINE fill:#fce4ec
       style ML_ENGINE fill:#f1f8e9

Data Ingestion Flows
====================

Framework Data Import Process
-----------------------------

The system supports importing official framework data through a standardized pipeline that ensures data quality and consistency.

.. mermaid::

   graph TD
       START([Framework Data Import Request])
       
       VALIDATE_SOURCE[Validate Data Source<br/>• Official source verification<br/>• Format validation<br/>• Version compatibility]
       
       EXTRACT_DATA[Extract Framework Data<br/>• Parse structured data<br/>• Extract hierarchical relationships<br/>• Identify metadata]
       
       VALIDATE_SCHEMA[Schema Validation<br/>• Framework-specific validation<br/>• Required field checks<br/>• Data type validation]
       
       TRANSFORM_DATA[Data Transformation<br/>• Normalize field names<br/>• Standardize formats<br/>• Enrich with metadata]
       
       CREATE_HIERARCHY[Create Hierarchical Structure<br/>• Version management<br/>• Parent-child relationships<br/>• Order preservation]
       
       UPDATE_SEARCH[Update Search Index<br/>• Generate search vectors<br/>• Extract keywords<br/>• Calculate quality scores]
       
       GENERATE_MAPPINGS[Generate Initial Mappings<br/>• Cross-framework analysis<br/>• Similarity calculations<br/>• Confidence scoring]
       
       COMMIT_TRANSACTION[Commit Transaction<br/>• Database persistence<br/>• Audit logging<br/>• Statistics generation]
       
       NOTIFY_COMPLETION[Notify Completion<br/>• Import statistics<br/>• Success/failure status<br/>• Next steps guidance]
       
       START --> VALIDATE_SOURCE
       VALIDATE_SOURCE --> EXTRACT_DATA
       EXTRACT_DATA --> VALIDATE_SCHEMA
       VALIDATE_SCHEMA --> TRANSFORM_DATA
       TRANSFORM_DATA --> CREATE_HIERARCHY
       CREATE_HIERARCHY --> UPDATE_SEARCH
       UPDATE_SEARCH --> GENERATE_MAPPINGS
       GENERATE_MAPPINGS --> COMMIT_TRANSACTION
       COMMIT_TRANSACTION --> NOTIFY_COMPLETION
       
       style START fill:#4caf50
       style VALIDATE_SOURCE fill:#2196f3
       style EXTRACT_DATA fill:#ff9800
       style VALIDATE_SCHEMA fill:#9c27b0
       style TRANSFORM_DATA fill:#f44336
       style CREATE_HIERARCHY fill:#607d8b
       style UPDATE_SEARCH fill:#795548
       style GENERATE_MAPPINGS fill:#e91e63
       style COMMIT_TRANSACTION fill:#009688
       style NOTIFY_COMPLETION fill:#8bc34a

Data Transformation Pipeline
----------------------------

Each framework undergoes specific transformations to ensure consistency and searchability:

**ISF Data Transformation**:

.. code-block:: python

   # ISF Raw Data → Normalized Structure
   {
     "security_area": "Security Governance",
     "control_id": "ISF.01.01",
     "title": "Information Security Policy",
     "description": "Establish and maintain information security policy",
     "implementation_guidance": "...",
     "related_controls": ["ISF.01.02", "ISF.02.01"]
   }
   
   # Transformed to:
   {
     "framework": "ISF",
     "element_type": "control",
     "element_id": "ISF.01.01",
     "title": "Information Security Policy",
     "description": "Establish and maintain information security policy",
     "category": "Security Governance",
     "search_vector": "tsvector representation",
     "keywords": ["security", "policy", "governance"],
     "quality_score": 0.9,
     "popularity_score": 0.8
   }

**NIST CSF 2.0 Data Transformation**:

.. code-block:: python

   # NIST CSF Raw Data → Hierarchical Structure
   {
     "function": "Govern",
     "category": "GV.GV",
     "subcategory_id": "GV.GV-01",
     "outcome": "Organizational cybersecurity strategy",
     "implementation_guidance": "...",
     "informative_references": [...]
   }
   
   # Transformed to hierarchical model with relationships:
   NISTCSFFunction(name="Govern") →
     NISTCSFCategory(name="Governance") →
       NISTCSFSubcategory(outcome="Organizational cybersecurity strategy")

Search Index Generation
=======================

The search index is a critical component that enables unified search across all frameworks.

.. mermaid::

   graph LR
       subgraph "Framework Data Sources"
           ISF_CTRL["ISF Controls"]
           NIST_SUB["NIST Subcategories"]
           ISO_CTRL["ISO Controls"]
           CIS_SAFE["CIS Safeguards"]
       end
       
       subgraph "Search Index Processing"
           EXTRACT["🔤 Text Extraction<br/>Title, Description<br/>Implementation Guidance"]
           TOKENIZE["🔍 Tokenization<br/>Keyword Extraction<br/>Stop Word Removal"]
           VECTOR["📊 Vector Generation<br/>PostgreSQL TSVECTOR<br/>Weighted Ranking"]
           ENRICH["✨ Metadata Enrichment<br/>Quality Scoring<br/>Popularity Metrics"]
       end
       
       subgraph "Search Capabilities"
           FULLTEXT["📝 Full-text Search<br/>PostgreSQL FTS<br/>Ranking Functions"]
           SEMANTIC["🧠 Semantic Search<br/>Similarity Analysis<br/>Context Understanding"]
           FACETED["🏷️ Faceted Search<br/>Framework Filtering<br/>Category Grouping"]
       end
       
       ISF_CTRL --> EXTRACT
       NIST_SUB --> EXTRACT
       ISO_CTRL --> EXTRACT
       CIS_SAFE --> EXTRACT
       
       EXTRACT --> TOKENIZE
       TOKENIZE --> VECTOR
       VECTOR --> ENRICH
       
       ENRICH --> FULLTEXT
       ENRICH --> SEMANTIC
       ENRICH --> FACETED
       
       style EXTRACT fill:#e3f2fd
       style TOKENIZE fill:#e8f5e8
       style VECTOR fill:#fff3e0
       style ENRICH fill:#f3e5f5

Cross-Framework Mapping Flows
=============================

Intelligent Mapping Discovery
-----------------------------

The system uses machine learning algorithms to discover relationships between framework elements.

.. mermaid::

   graph TD
       subgraph "Input Analysis"
           SOURCE_ITEM["📋 Source Framework Item<br/>Title, Description, Category<br/>Implementation Guidance"]
           TARGET_ITEMS["📋 Target Framework Items<br/>All elements from target framework<br/>Metadata and content"]
       end
       
       subgraph "Similarity Analysis"
           SEMANTIC_SIM["🧠 Semantic Similarity<br/>Text analysis<br/>Keyword matching<br/>Jaccard similarity"]
           CONTEXT_SIM["🎯 Context Similarity<br/>Category alignment<br/>Tag overlap<br/>Quality correlation"]
           STRUCT_SIM["🏗️ Structural Similarity<br/>Element type matching<br/>Framework compatibility<br/>Hierarchy position"]
       end
       
       subgraph "Confidence Calculation"
           WEIGHT_CALC["⚖️ Weighted Calculation<br/>Semantic: 40%<br/>Context: 30%<br/>Structural: 20%<br/>Quality: 10%"]
           THRESHOLD["🎯 Threshold Filtering<br/>Minimum confidence: 0.3<br/>Quality gates<br/>Validation rules"]
       end
       
       subgraph "Relationship Creation"
           REL_TYPE["🔗 Relationship Type<br/>Equivalent (>0.8)<br/>Implements (>0.6)<br/>Supports (>0.5)<br/>Related (>0.3)"]
           EVIDENCE["📝 Evidence Collection<br/>Similarity scores<br/>Rationale generation<br/>Supporting data"]
           VALIDATION["✅ Validation Workflow<br/>Expert review<br/>Peer validation<br/>Quality assurance"]
       end
       
       SOURCE_ITEM --> SEMANTIC_SIM
       TARGET_ITEMS --> SEMANTIC_SIM
       SOURCE_ITEM --> CONTEXT_SIM
       TARGET_ITEMS --> CONTEXT_SIM
       SOURCE_ITEM --> STRUCT_SIM
       TARGET_ITEMS --> STRUCT_SIM
       
       SEMANTIC_SIM --> WEIGHT_CALC
       CONTEXT_SIM --> WEIGHT_CALC
       STRUCT_SIM --> WEIGHT_CALC
       
       WEIGHT_CALC --> THRESHOLD
       THRESHOLD --> REL_TYPE
       REL_TYPE --> EVIDENCE
       EVIDENCE --> VALIDATION
       
       style SOURCE_ITEM fill:#e1f5fe
       style SEMANTIC_SIM fill:#e8f5e8
       style CONTEXT_SIM fill:#fff3e0
       style STRUCT_SIM fill:#f3e5f5
       style WEIGHT_CALC fill:#fce4ec
       style REL_TYPE fill:#f1f8e9

Analytics Data Processing
=========================

Gap Analysis Data Flow
----------------------

The gap analysis process combines current state assessment with target state definition to identify implementation gaps.

.. mermaid::

   graph TB
       subgraph "Current State Assessment"
           CURRENT_IMPL["📊 Current Implementation<br/>Framework compliance scores<br/>Control implementation status<br/>Assessment results"]
           CURRENT_METRICS["📈 Current Metrics<br/>Compliance percentages<br/>Maturity levels<br/>Risk indicators"]
       end
       
       subgraph "Target State Definition"
           TARGET_REQ["🎯 Target Requirements<br/>Business objectives<br/>Regulatory requirements<br/>Risk tolerance"]
           TARGET_METRICS["📊 Target Metrics<br/>Desired compliance levels<br/>Target maturity<br/>Risk thresholds"]
       end
       
       subgraph "Gap Analysis Engine"
           GAP_CALC["🔍 Gap Calculation<br/>Current vs Target<br/>Variance analysis<br/>Priority scoring"]
           RISK_ASSESS["⚠️ Risk Assessment<br/>Gap impact analysis<br/>Business risk scoring<br/>Urgency calculation"]
           ROADMAP_GEN["🗺️ Roadmap Generation<br/>Implementation phases<br/>Resource requirements<br/>Timeline estimation"]
       end
       
       subgraph "Output Generation"
           GAP_REPORT["📋 Gap Analysis Report<br/>Identified gaps<br/>Priority recommendations<br/>Implementation roadmap"]
           DASHBOARD["📊 Analytics Dashboard<br/>Visual gap representation<br/>Progress tracking<br/>KPI monitoring"]
       end
       
       CURRENT_IMPL --> CURRENT_METRICS
       TARGET_REQ --> TARGET_METRICS
       
       CURRENT_METRICS --> GAP_CALC
       TARGET_METRICS --> GAP_CALC
       
       GAP_CALC --> RISK_ASSESS
       RISK_ASSESS --> ROADMAP_GEN
       
       ROADMAP_GEN --> GAP_REPORT
       ROADMAP_GEN --> DASHBOARD
       
       style CURRENT_IMPL fill:#e3f2fd
       style TARGET_REQ fill:#e8f5e8
       style GAP_CALC fill:#fff3e0
       style RISK_ASSESS fill:#f3e5f5
       style ROADMAP_GEN fill:#fce4ec
       style GAP_REPORT fill:#f1f8e9

Real-Time Analytics Processing
-----------------------------

The system provides real-time analytics through event-driven processing and caching strategies.

.. code-block:: python

   # Real-time Analytics Data Flow
   
   # 1. Event Capture
   framework_event = {
       "event_type": "assessment_completed",
       "framework": "NIST_CSF_2",
       "organization_id": "org_123",
       "assessment_id": "assess_456",
       "compliance_score": 0.78,
       "timestamp": "2024-01-15T10:30:00Z"
   }
   
   # 2. Event Processing
   analytics_processor.process_event(framework_event)
   
   # 3. Cache Update
   cache.update_compliance_metrics(
       organization_id="org_123",
       framework="NIST_CSF_2",
       new_score=0.78
   )
   
   # 4. Dashboard Notification
   websocket.broadcast_update({
       "type": "compliance_update",
       "organization_id": "org_123",
       "framework": "NIST_CSF_2",
       "new_score": 0.78,
       "trend": "improving"
   })

Data Security & Compliance
===========================

Data Protection Measures
------------------------

The system implements comprehensive data protection measures to ensure security and compliance.

.. mermaid::

   graph LR
       subgraph "Data at Rest"
           ENCRYPTION["🔐 Database Encryption<br/>AES-256 encryption<br/>Encrypted backups<br/>Key management"]
           ACCESS_CTRL["🔑 Access Control<br/>Role-based permissions<br/>Organizational scoping<br/>Audit logging"]
       end
       
       subgraph "Data in Transit"
           TLS["🔒 TLS Encryption<br/>HTTPS/WSS protocols<br/>Certificate management<br/>Perfect forward secrecy"]
           API_SEC["🛡️ API Security<br/>Authentication tokens<br/>Rate limiting<br/>Input validation"]
       end
       
       subgraph "Data Processing"
           VALIDATION["✅ Input Validation<br/>Schema validation<br/>Sanitization<br/>Type checking"]
           AUDIT["📝 Audit Logging<br/>All data operations<br/>User actions<br/>System events"]
       end
       
       subgraph "Compliance"
           GDPR["🇪🇺 GDPR Compliance<br/>Data portability<br/>Right to erasure<br/>Consent management"]
           SOC2["🏛️ SOC 2 Type II<br/>Security controls<br/>Availability<br/>Confidentiality"]
       end
       
       ENCRYPTION --> TLS
       ACCESS_CTRL --> API_SEC
       TLS --> VALIDATION
       API_SEC --> AUDIT
       VALIDATION --> GDPR
       AUDIT --> SOC2
       
       style ENCRYPTION fill:#e3f2fd
       style TLS fill:#e8f5e8
       style VALIDATION fill:#fff3e0
       style GDPR fill:#f3e5f5

Performance Optimization
========================

Database Optimization Strategies
--------------------------------

The system employs multiple optimization strategies to ensure high performance at scale.

.. code-block:: sql

   -- Framework-specific indexes for optimal query performance
   CREATE INDEX CONCURRENTLY idx_isf_controls_search 
   ON isf_controls USING gin(to_tsvector('english', title || ' ' || description));
   
   CREATE INDEX CONCURRENTLY idx_nist_csf_hierarchy 
   ON nist_csf_subcategories(function_id, category_id, order_index);
   
   CREATE INDEX CONCURRENTLY idx_search_index_framework_type 
   ON search_index(framework, element_type, quality_score DESC);
   
   CREATE INDEX CONCURRENTLY idx_framework_mappings_confidence 
   ON framework_mappings(confidence_score DESC, is_validated, created_at DESC);
   
   -- Partial indexes for active records
   CREATE INDEX CONCURRENTLY idx_active_assessments 
   ON assessments(framework, status, created_at DESC) 
   WHERE deleted_at IS NULL;

Caching Architecture
-------------------

.. mermaid::

   graph TB
       subgraph "Application Layer"
           API["🌐 API Endpoints"]
           SERVICES["⚙️ Business Services"]
       end
       
       subgraph "Caching Layer"
           REDIS["📦 Redis Cache<br/>Session data<br/>Frequently accessed data<br/>Real-time analytics"]
           MEMCACHED["💾 Memcached<br/>Query result caching<br/>Framework metadata<br/>Search facets"]
       end
       
       subgraph "Database Layer"
           POSTGRES["🗄️ PostgreSQL<br/>Framework data<br/>Search indexes<br/>Audit logs"]
           REPLICA["📖 Read Replicas<br/>Analytics queries<br/>Reporting<br/>Search operations"]
       end
       
       API --> REDIS
       API --> MEMCACHED
       SERVICES --> REDIS
       SERVICES --> MEMCACHED
       
       REDIS --> POSTGRES
       MEMCACHED --> REPLICA
       
       style REDIS fill:#e3f2fd
       style MEMCACHED fill:#e8f5e8
       style POSTGRES fill:#fff3e0
       style REPLICA fill:#f3e5f5

Next Steps
==========

* :doc:`../user_guide/index` - User-focused documentation and workflows
* :doc:`../frameworks/index` - Framework-specific implementation details
* :doc:`../api/index` - Complete API documentation
* :doc:`../security/index` - Security implementation guidelines

Resources
=========

* `Data Architecture Guide <#>`_ - Detailed technical architecture
* `Performance Tuning Guide <#>`_ - Optimization best practices
* `Security Implementation Guide <#>`_ - Security configuration details
* `Monitoring & Observability Guide <#>`_ - System monitoring setup
