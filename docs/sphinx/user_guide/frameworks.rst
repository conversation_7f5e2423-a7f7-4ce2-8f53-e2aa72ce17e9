=============================
Working with Frameworks
=============================

This guide provides step-by-step instructions for working with cybersecurity frameworks in the Regression Rigor platform. Learn how to import framework data, create mappings, conduct assessments, and generate reports.

.. contents:: Table of Contents
   :local:
   :depth: 2

Getting Started with Frameworks
===============================

The Regression Rigor platform supports multiple cybersecurity frameworks with comprehensive management capabilities.

Framework Selection Workflow
-----------------------------

.. mermaid::

   graph TD
       START([Start Framework Selection])
       
       ASSESS[Assess Organization Needs<br/>• Industry requirements<br/>• Regulatory compliance<br/>• Business objectives<br/>• Risk tolerance]
       
       EVALUATE[Evaluate Framework Options<br/>• ISF: Comprehensive governance<br/>• NIST CSF 2.0: Risk management<br/>• ISO 27001: ISMS certification<br/>• CIS Controls: Practical implementation]
       
       SELECT[Select Primary Framework<br/>• Choose main framework<br/>• Identify complementary frameworks<br/>• Plan integration approach]
       
       IMPLEMENT[Implement Framework<br/>• Import official data<br/>• Configure organization context<br/>• Set up assessments<br/>• Create mappings]
       
       OPTIMIZE[Optimize Implementation<br/>• Monitor compliance<br/>• Analyze gaps<br/>• Improve processes<br/>• Expand coverage]
       
       START --> ASSESS
       ASSESS --> EVALUATE
       EVALUATE --> SELECT
       SELECT --> IMPLEMENT
       IMPLEMENT --> OPTIMIZE
       OPTIMIZE -.->|Continuous Improvement| ASSESS
       
       style START fill:#4caf50
       style ASSESS fill:#2196f3
       style EVALUATE fill:#ff9800
       style SELECT fill:#9c27b0
       style IMPLEMENT fill:#f44336
       style OPTIMIZE fill:#607d8b

Importing Framework Data
========================

Step 1: Access Framework Import
-------------------------------

1. Navigate to **Frameworks** → **Data Management** → **Import**
2. Select the framework you want to import
3. Choose the version (current version recommended)

Step 2: Import Official Data
----------------------------

.. code-block:: bash

   # Using the API
   curl -X POST /api/v1/isf/import/isf-2022 \
     -H "Authorization: Bearer YOUR_TOKEN" \
     -H "Content-Type: application/json"

.. code-block:: python

   # Using Python
   import requests
   
   response = requests.post(
       '/api/v1/nist-csf-2/import/nist-csf-2-0',
       headers={'Authorization': 'Bearer YOUR_TOKEN'}
   )
   
   if response.status_code == 200:
       result = response.json()
       print(f"Imported {result['statistics']['total_records']} records")

Step 3: Verify Import Results
-----------------------------

After import, verify the data:

1. Check import statistics
2. Review framework structure
3. Validate control content
4. Confirm version information

.. mermaid::

   graph LR
       subgraph "Import Process"
           DOWNLOAD["📥 Download Data<br/>Official sources<br/>Validated content"]
           VALIDATE["✅ Validate Structure<br/>Schema validation<br/>Data integrity"]
           IMPORT["💾 Import to Database<br/>Transactional import<br/>Error handling"]
           VERIFY["🔍 Verify Results<br/>Statistics review<br/>Quality check"]
       end
       
       DOWNLOAD --> VALIDATE
       VALIDATE --> IMPORT
       IMPORT --> VERIFY
       
       style DOWNLOAD fill:#e3f2fd
       style VALIDATE fill:#e8f5e8
       style IMPORT fill:#fff3e0
       style VERIFY fill:#f3e5f5

Creating Cross-Framework Mappings
=================================

Step 1: Plan Your Mapping Strategy
----------------------------------

Before creating mappings, consider:

* **Scope**: Which frameworks to map
* **Purpose**: Compliance, optimization, or analysis
* **Quality**: Desired confidence levels
* **Maintenance**: Update and validation processes

Step 2: Create Mapping Set
--------------------------

.. code-block:: python

   mapping_set_request = {
       "name": "ISF to NIST CSF 2.0 Mappings",
       "description": "Strategic alignment between ISF and NIST CSF",
       "source_framework": "ISF",
       "target_framework": "NIST_CSF_2",
       "version": "1.0",
       "is_official": False
   }
   
   response = requests.post('/api/v1/mapping-sets', json=mapping_set_request)
   mapping_set = response.json()

Step 3: Create Individual Mappings
----------------------------------

.. code-block:: python

   mapping_request = {
       "source_framework": "ISF",
       "source_control_id": "ISF.01.01",
       "target_framework": "NIST_CSF_2",
       "target_control_id": "GV.GV-01",
       "mapping_type": "direct",
       "confidence_level": "high",
       "confidence_score": 0.9,
       "description": "Both controls establish organizational cybersecurity strategy",
       "rationale": "Direct alignment between policy requirements",
       "mapping_set_id": mapping_set['id']
   }
   
   response = requests.post('/api/v1/mappings', json=mapping_request)

Step 4: Validate Mappings
-------------------------

.. code-block:: python

   validation_request = {
       "mapping_ids": [1, 2, 3],
       "validator": "security_expert",
       "validation_criteria": {
           "accuracy": True,
           "completeness": True,
           "consistency": True
       }
   }
   
   response = requests.post('/api/v1/mappings/validate', json=validation_request)

Conducting Framework Assessments
================================

Step 1: Create Assessment
------------------------

.. code-block:: python

   assessment_request = {
       "name": "Q1 2024 ISO 27001 Assessment",
       "framework": "ISO_27001",
       "assessment_type": "internal",
       "scope": "All Annex A controls",
       "assessor": "Internal Audit Team",
       "target_completion": "2024-03-31"
   }
   
   response = requests.post('/api/v1/iso-27001/assessments', json=assessment_request)

Step 2: Conduct Control Evaluation
----------------------------------

For each control in scope:

1. **Review Requirements**: Understand control objectives
2. **Gather Evidence**: Collect implementation evidence
3. **Assess Effectiveness**: Evaluate control effectiveness
4. **Document Findings**: Record assessment results

.. mermaid::

   graph TD
       subgraph "Assessment Process"
           PLAN[Assessment Planning<br/>• Define scope<br/>• Allocate resources<br/>• Set timeline]
           EXECUTE[Execute Assessment<br/>• Review controls<br/>• Gather evidence<br/>• Interview stakeholders]
           ANALYZE[Analyze Results<br/>• Evaluate effectiveness<br/>• Identify gaps<br/>• Calculate scores]
           REPORT[Generate Reports<br/>• Document findings<br/>• Provide recommendations<br/>• Create action plans]
       end
       
       PLAN --> EXECUTE
       EXECUTE --> ANALYZE
       ANALYZE --> REPORT
       
       subgraph "Assessment Criteria"
           IMPLEMENTED["✅ Implemented<br/>Control is in place<br/>Evidence available"]
           PARTIAL["🔄 Partially Implemented<br/>Some elements missing<br/>Improvement needed"]
           NOT_IMPL["❌ Not Implemented<br/>Control not in place<br/>Action required"]
       end
       
       EXECUTE --> IMPLEMENTED
       EXECUTE --> PARTIAL
       EXECUTE --> NOT_IMPL
       
       style PLAN fill:#e3f2fd
       style EXECUTE fill:#e8f5e8
       style ANALYZE fill:#fff3e0
       style REPORT fill:#f3e5f5

Step 3: Generate Assessment Report
---------------------------------

.. code-block:: python

   report_request = {
       "assessment_id": assessment['id'],
       "report_type": "comprehensive",
       "include_recommendations": True,
       "include_action_plan": True,
       "format": "pdf"
   }
   
   response = requests.post('/api/v1/assessments/reports', json=report_request)

Using Framework Analytics
=========================

Step 1: Access Analytics Dashboard
----------------------------------

Navigate to **Analytics** → **Framework Overview** to access:

* Real-time framework statistics
* Implementation progress tracking
* Compliance status indicators
* Trend analysis and forecasting

Step 2: Perform Gap Analysis
----------------------------

.. code-block:: python

   gap_analysis_request = {
       "target_frameworks": ["ISF", "NIST_CSF_2", "ISO_27001"],
       "current_implementation": {
           "ISF": 78,
           "NIST_CSF_2": 82,
           "ISO_27001": 75
       },
       "organization_context": {
           "industry": "financial_services",
           "size": "large"
       }
   }
   
   response = requests.post('/api/v1/analytics/gap-analysis', json=gap_analysis_request)
   gap_analysis = response.json()

Step 3: Generate Compliance Reports
-----------------------------------

.. code-block:: python

   compliance_request = {
       "frameworks": ["ISF", "NIST_CSF_2", "ISO_27001"],
       "report_period": "Q1_2024",
       "include_trends": True,
       "include_recommendations": True
   }
   
   response = requests.post('/api/v1/analytics/compliance-report', json=compliance_request)

Best Practices
==============

Framework Implementation
------------------------

1. **Start Small**: Begin with one framework before expanding
2. **Understand Context**: Align framework selection with business needs
3. **Engage Stakeholders**: Involve relevant teams in implementation
4. **Document Decisions**: Maintain clear documentation of choices
5. **Plan for Maintenance**: Establish ongoing maintenance processes

Mapping Quality
---------------

1. **Use Consistent Methodology**: Apply standardized mapping criteria
2. **Document Rationale**: Provide clear justification for mappings
3. **Validate Regularly**: Implement regular validation cycles
4. **Engage Experts**: Involve subject matter experts in validation
5. **Monitor Usage**: Track mapping usage and effectiveness

Assessment Excellence
--------------------

1. **Define Clear Scope**: Establish assessment boundaries upfront
2. **Use Multiple Sources**: Gather evidence from various sources
3. **Apply Consistent Criteria**: Use standardized assessment criteria
4. **Document Thoroughly**: Maintain comprehensive assessment records
5. **Follow Up**: Implement action plans and track progress

Common Pitfalls
===============

Framework Selection
------------------

* **Over-Engineering**: Selecting overly complex frameworks for organizational maturity
* **Scope Creep**: Trying to implement too many frameworks simultaneously
* **Lack of Alignment**: Choosing frameworks that don't align with business objectives
* **Insufficient Resources**: Underestimating resource requirements

Mapping Challenges
-----------------

* **Inconsistent Methodology**: Using different approaches for different mappings
* **Poor Documentation**: Inadequate rationale and justification
* **Lack of Validation**: Insufficient quality assurance processes
* **Static Mappings**: Failing to update mappings as frameworks evolve

Assessment Issues
----------------

* **Inadequate Preparation**: Insufficient planning and resource allocation
* **Inconsistent Evaluation**: Using different criteria across assessments
* **Poor Evidence**: Relying on inadequate or outdated evidence
* **No Follow-Up**: Failing to implement recommendations and track progress

Troubleshooting
===============

Common Issues and Solutions
--------------------------

**Import Failures**

* Check network connectivity
* Verify authentication credentials
* Review import logs for errors
* Contact support if issues persist

**Mapping Conflicts**

* Review mapping methodology
* Check for duplicate mappings
* Validate source and target controls
* Engage experts for resolution

**Assessment Errors**

* Verify assessment scope
* Check control availability
* Review evidence requirements
* Validate assessment criteria

**Performance Issues**

* Check system resources
* Review query complexity
* Optimize database indexes
* Consider data archiving

Next Steps
==========

* :doc:`../frameworks/index` - Detailed framework documentation
* :doc:`../api/index` - Complete API reference
* :doc:`../admin_guide/index` - Administrative procedures
* :doc:`../security/index` - Security implementation guidelines

Resources
=========

* `Framework Implementation Guide <#>`_ - Detailed implementation procedures
* `Mapping Best Practices <#>`_ - Quality assurance guidelines
* `Assessment Methodology <#>`_ - Standardized assessment procedures
* `Analytics User Guide <#>`_ - Advanced analytics capabilities
