# Makefile for Sphinx documentation

# You can set these variables from the command line, and also
# from the environment for the first two.
SPHINXOPTS    ?=
SPHINXBUILD  ?= sphinx-build
SOURCEDIR    = .
BUILDDIR     = _build

# Put it first so that "make" without argument is like "make help".
help:
	@$(SPHINXBUILD) -M help "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)

.PHONY: help Makefile

# Custom targets for enhanced documentation building

# Install dependencies
install:
	pip install -r requirements.txt

# Clean build directory
clean:
	rm -rf $(BUILDDIR)/*
	@echo "Build directory cleaned."

# Build HTML documentation
html:
	@echo "Building HTML documentation..."
	$(SPHINXBUILD) -b html $(SOURCEDIR) $(BUILDDIR)/html $(SPHINXOPTS)
	@echo "HTML documentation built in $(BUILDDIR)/html/"

# Build HTML documentation with live reload
livehtml:
	@echo "Starting live HTML build with auto-reload..."
	sphinx-autobuild $(SOURCEDIR) $(BUILDDIR)/html $(SPHINXOPTS) --host 0.0.0.0 --port 8000

# Build PDF documentation
pdf:
	@echo "Building PDF documentation..."
	$(SPHINXBUILD) -b latex $(SOURCEDIR) $(BUILDDIR)/latex $(SPHINXOPTS)
	cd $(BUILDDIR)/latex && make all-pdf
	@echo "PDF documentation built in $(BUILDDIR)/latex/"

# Build EPUB documentation
epub:
	@echo "Building EPUB documentation..."
	$(SPHINXBUILD) -b epub $(SOURCEDIR) $(BUILDDIR)/epub $(SPHINXOPTS)
	@echo "EPUB documentation built in $(BUILDDIR)/epub/"

# Check documentation for issues
check:
	@echo "Checking documentation for issues..."
	$(SPHINXBUILD) -b linkcheck $(SOURCEDIR) $(BUILDDIR)/linkcheck $(SPHINXOPTS)
	$(SPHINXBUILD) -b spelling $(SOURCEDIR) $(BUILDDIR)/spelling $(SPHINXOPTS)
	@echo "Documentation check complete."

# Build all formats
all: clean html pdf epub
	@echo "All documentation formats built successfully."

# Serve documentation locally
serve:
	@echo "Serving documentation at http://localhost:8080"
	cd $(BUILDDIR)/html && python -m http.server 8080

# Deploy to GitHub Pages
deploy-github:
	@echo "Deploying to GitHub Pages..."
	ghp-import -n -p -f $(BUILDDIR)/html

# Generate API documentation from source code
apidoc:
	@echo "Generating API documentation from source code..."
	sphinx-apidoc -o api ../api --force --module-first
	@echo "API documentation generated."

# Validate Mermaid diagrams
validate-mermaid:
	@echo "Validating Mermaid diagrams..."
	@find . -name "*.rst" -o -name "*.md" | xargs grep -l "mermaid::" | while read file; do \
		echo "Checking $$file..."; \
		# Add mermaid validation logic here if needed \
	done
	@echo "Mermaid diagram validation complete."

# Check for broken internal links
checklinks:
	@echo "Checking for broken internal links..."
	$(SPHINXBUILD) -b linkcheck $(SOURCEDIR) $(BUILDDIR)/linkcheck $(SPHINXOPTS)

# Spell check documentation
spellcheck:
	@echo "Running spell check..."
	$(SPHINXBUILD) -b spelling $(SOURCEDIR) $(BUILDDIR)/spelling $(SPHINXOPTS)

# Generate documentation statistics
stats:
	@echo "Generating documentation statistics..."
	@echo "=== Documentation Statistics ==="
	@echo "Total RST files: $$(find . -name '*.rst' | wc -l)"
	@echo "Total MD files: $$(find . -name '*.md' | wc -l)"
	@echo "Total lines: $$(find . -name '*.rst' -o -name '*.md' | xargs wc -l | tail -1)"
	@echo "Mermaid diagrams: $$(find . -name '*.rst' -o -name '*.md' | xargs grep -c 'mermaid::' | awk -F: '{sum += $$2} END {print sum}')"
	@echo "Code blocks: $$(find . -name '*.rst' -o -name '*.md' | xargs grep -c 'code-block::' | awk -F: '{sum += $$2} END {print sum}')"

# Watch for changes and rebuild
watch:
	@echo "Watching for changes and rebuilding..."
	while inotifywait -e modify,create,delete -r $(SOURCEDIR); do \
		make html; \
	done

# Create a new documentation page template
new-page:
	@read -p "Enter page name (without extension): " page; \
	echo "Creating new page: $$page.rst"; \
	cat > $$page.rst << 'EOF'; \
	$$(echo "$$page" | tr '[:lower:]' '[:upper:]' | sed 's/.*/&\n/' | tr '[:alpha:]' '='); \
	$$page; \
	$$(echo "$$page" | tr '[:lower:]' '[:upper:]' | sed 's/.*/&\n/' | tr '[:alpha:]' '='); \
	; \
	Overview; \
	========; \
	; \
	Add your content here.; \
	; \
	.. contents:: Table of Contents; \
	   :depth: 2; \
	   :local:; \
	EOF

# Optimize images for web
optimize-images:
	@echo "Optimizing images for web..."
	@find _static -name "*.png" -exec optipng -o7 {} \;
	@find _static -name "*.jpg" -exec jpegoptim --max=85 {} \;
	@echo "Image optimization complete."

# Generate requirements.txt for documentation dependencies
requirements:
	@echo "Generating requirements.txt for documentation..."
	@cat > requirements.txt << 'EOF'
	sphinx>=5.0.0
	sphinx-rtd-theme>=1.0.0
	sphinxcontrib-mermaid>=0.7.1
	myst-parser>=0.18.0
	sphinx-copybutton>=0.5.0
	sphinx-tabs>=3.4.0
	sphinx-design>=0.3.0
	sphinx-autobuild>=2021.3.14
	ghp-import>=2.1.0
	EOF
	@echo "requirements.txt generated."

# Setup development environment
setup-dev:
	@echo "Setting up development environment..."
	python -m venv venv
	. venv/bin/activate && pip install -r requirements.txt
	@echo "Development environment setup complete."
	@echo "Activate with: source venv/bin/activate"

# Run documentation tests
test:
	@echo "Running documentation tests..."
	$(SPHINXBUILD) -b doctest $(SOURCEDIR) $(BUILDDIR)/doctest $(SPHINXOPTS)
	@echo "Documentation tests complete."

# Generate changelog from git commits
changelog:
	@echo "Generating changelog from git commits..."
	@echo "# Changelog" > CHANGELOG.md
	@echo "" >> CHANGELOG.md
	@git log --pretty=format:"- %s (%h)" --reverse >> CHANGELOG.md
	@echo "Changelog generated in CHANGELOG.md"

# Backup documentation
backup:
	@echo "Creating documentation backup..."
	@tar -czf docs-backup-$$(date +%Y%m%d-%H%M%S).tar.gz $(SOURCEDIR) $(BUILDDIR)
	@echo "Backup created: docs-backup-$$(date +%Y%m%d-%H%M%S).tar.gz"

# Show documentation metrics
metrics:
	@echo "=== Documentation Metrics ==="
	@echo "Build size: $$(du -sh $(BUILDDIR)/html 2>/dev/null | cut -f1 || echo 'Not built')"
	@echo "Source size: $$(du -sh $(SOURCEDIR) | cut -f1)"
	@echo "Last build: $$(stat -c %y $(BUILDDIR)/html/index.html 2>/dev/null || echo 'Never')"
	@echo "Pages: $$(find $(BUILDDIR)/html -name '*.html' 2>/dev/null | wc -l || echo '0')"

# Catch-all target: route all unknown targets to Sphinx using the new
# "make mode" option.  $(O) is meant as a shortcut for $(SPHINXOPTS).
%: Makefile
	@$(SPHINXBUILD) -M $@ "$(SOURCEDIR)" "$(BUILDDIR)" $(SPHINXOPTS) $(O)
