==================================
Cybersecurity Framework User Guide
==================================

Welcome to the comprehensive user guide for the Cybersecurity Framework Management API. This guide provides detailed workflows, user stories, and interactive diagrams to help you understand and effectively use the system.

.. contents:: Table of Contents
   :depth: 3
   :local:

Overview
========

The Cybersecurity Framework Management API provides comprehensive functionality for managing cybersecurity frameworks including ISF, NIST CSF, and intelligent cross-framework mappings. This guide walks you through common workflows using behavior-driven development (BDD) scenarios and interactive Mermaid diagrams.

User Personas
=============

Before diving into the workflows, let's understand the primary users of this system:

.. grid:: 2 2 2 2

    .. grid-item-card:: 🔍 Security Analyst
        :class-header: bg-primary text-white

        Analyzes threats and creates mappings between MITRE ATT&CK techniques and security controls.
        
        **Key Activities:**
        - Generate mapping suggestions
        - Validate mapping effectiveness
        - Create coverage reports

    .. grid-item-card:: ⚙️ Framework Administrator
        :class-header: bg-success text-white

        Manages framework data imports, exports, and system configuration.
        
        **Key Activities:**
        - Import framework data
        - Manage versions
        - Configure system settings

    .. grid-item-card:: 📊 Compliance Officer
        :class-header: bg-info text-white

        Reviews mappings, generates compliance reports, and ensures regulatory alignment.
        
        **Key Activities:**
        - Export compliance reports
        - Review mapping quality
        - Generate audit trails

    .. grid-item-card:: 🏗️ Security Architect
        :class-header: bg-warning text-white

        Designs security controls, validates mappings, and provides expert review.
        
        **Key Activities:**
        - Validate mapping suggestions
        - Design control frameworks
        - Provide expert guidance

Epic 1: Framework Data Management
==================================

Story 1.1: Import ISF Framework Data
------------------------------------

**As a** Framework Administrator  
**I want to** import ISF framework data from multiple formats  
**So that** I can maintain up-to-date security control information

Scenario 1.1.1: Successful JSON Import
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: gherkin

   Given I am authenticated as a Framework Administrator
   And I have valid ISF framework data in JSON format
   And the system has no existing version conflicts
   When I submit the JSON data for import
   Then the system should validate the data structure
   And create a new ISF version in the database
   And import all security areas and controls
   And return a success response with import statistics
   And log the import activity for audit purposes

**Workflow Diagram:**

.. mermaid::

   flowchart TD
       A[User Starts Import] --> B{Authentication Check}
       B -->|Failed| C[Return 401 Unauthorized]
       B -->|Success| D{Admin Role Check}
       D -->|Not Admin| E[Return 403 Forbidden]
       D -->|Admin| F[Receive Import Data]
       
       F --> G{Data Format Check}
       G -->|JSON| H[Parse JSON Structure]
       G -->|CSV| I[Parse CSV Structure]
       G -->|Invalid| J[Return Format Error]
       
       H --> K[Validate JSON Schema]
       I --> L[Validate CSV Headers]
       
       K --> M{Validation Result}
       L --> M
       M -->|Failed| N[Return Validation Errors]
       M -->|Success| O{File Size Check}
       
       O -->|Large File| P[Start Async Import]
       O -->|Small File| Q[Start Sync Import]
       
       P --> R[Create Background Task]
       R --> S[Return Task ID]
       S --> T[Process in Background]
       
       Q --> U[Begin Transaction]
       T --> U
       
       U --> V{Version Conflict Check}
       V -->|Conflict & No Replace| W[Return Conflict Error]
       V -->|No Conflict or Replace| X[Create ISF Version]
       
       X --> Y[Import Security Areas]
       Y --> Z[Import Controls]
       Z --> AA{Import Success}
       
       AA -->|Failed| BB[Rollback Transaction]
       BB --> CC[Return Error Details]
       AA -->|Success| DD[Commit Transaction]
       DD --> EE[Log Import Activity]
       EE --> FF[Return Success Response]
       
       style A fill:#e1f5fe
       style FF fill:#c8e6c9
       style C fill:#ffcdd2
       style E fill:#ffcdd2
       style J fill:#ffcdd2
       style N fill:#ffcdd2
       style W fill:#ffcdd2
       style CC fill:#ffcdd2

.. api-endpoint:: POST /api/v1/isf/import
   :status: 201 Created
   :auth: Required (JWT)
   :role: Framework Administrator

   Import ISF framework data from JSON or CSV format.

   **Request Body:**

   .. code-block:: json

      {
        "format": "json",
        "data": "{ ... ISF framework data ... }",
        "replace_existing": false,
        "async_import": false
      }

   **Response:**

   .. code-block:: json

      {
        "success": true,
        "version_id": 123,
        "imported_security_areas": 25,
        "imported_controls": 150,
        "processing_time": 2.5,
        "errors": [],
        "warnings": []
      }

Scenario 1.1.2: CSV Import with Validation Errors
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: gherkin

   Given I am authenticated as a Framework Administrator
   And I have ISF framework data in CSV format
   And the CSV contains some invalid control types
   When I submit the CSV data for import
   Then the system should validate each row
   And identify validation errors with specific line numbers
   And return a detailed error report with suggestions
   And not create any database records
   And provide guidance for fixing the data

**Error Handling Flow:**

.. mermaid::

   flowchart TD
       A[CSV Import Starts] --> B[Parse CSV Headers]
       B --> C{Headers Valid}
       C -->|No| D[Return Header Errors]
       C -->|Yes| E[Process Rows]
       
       E --> F[Validate Row Data]
       F --> G{Row Valid}
       G -->|No| H[Collect Error Details]
       G -->|Yes| I[Continue Processing]
       
       H --> J[Add Line Number]
       J --> K[Add Field Details]
       K --> L[Add Suggestion]
       L --> M{More Rows}
       
       I --> M
       M -->|Yes| F
       M -->|No| N{Any Errors}
       
       N -->|Yes| O[Return Error Report]
       N -->|No| P[Proceed with Import]
       
       style A fill:#e1f5fe
       style P fill:#c8e6c9
       style D fill:#ffcdd2
       style O fill:#ffcdd2

Story 1.2: Export Framework Data
---------------------------------

**As a** Compliance Officer  
**I want to** export framework data in multiple formats  
**So that** I can share information with external stakeholders

Scenario 1.2.1: Filtered JSON Export
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: gherkin

   Given I am authenticated as a Compliance Officer
   And the system contains ISF framework data
   And I want to export only "technical" controls
   When I request a JSON export with control type filter
   Then the system should apply the specified filters
   And generate a JSON file with matching controls
   And include metadata about the export criteria
   And provide download link with expiration time
   And log the export activity for compliance tracking

**Export Workflow:**

.. mermaid::

   flowchart TD
       A[User Requests Export] --> B{Authentication Check}
       B -->|Failed| C[Return 401 Unauthorized]
       B -->|Success| D[Parse Export Parameters]
       
       D --> E{Format Validation}
       E -->|Invalid| F[Return Format Error]
       E -->|Valid| G[Apply Filters]
       
       G --> H{Data Size Check}
       H -->|Large Dataset| I[Use Streaming Export]
       H -->|Small Dataset| J[Use Direct Export]
       
       I --> K[Initialize Stream]
       K --> L[Stream Data Chunks]
       L --> M[Return Streaming Response]
       
       J --> N[Query Database]
       N --> O[Format Data]
       O --> P{Export Format}
       
       P -->|JSON| Q[Generate JSON]
       P -->|CSV| R[Generate CSV]
       P -->|XML| S[Generate XML]
       P -->|STIX| T[Generate STIX]
       
       Q --> U[Add Metadata]
       R --> U
       S --> U
       T --> U
       
       U --> V[Create Download Link]
       V --> W[Log Export Activity]
       W --> X[Return Export Response]
       
       style A fill:#e1f5fe
       style X fill:#c8e6c9
       style M fill:#c8e6c9
       style C fill:#ffcdd2
       style F fill:#ffcdd2

.. api-endpoint:: POST /api/v1/isf/export
   :status: 200 OK
   :auth: Required (JWT)
   :role: Any authenticated user

   Export ISF framework data with optional filtering.

   **Request Body:**

   .. code-block:: json

      {
        "format": "json",
        "filters": {
          "control_types": ["technical"],
          "maturity_levels": ["intermediate", "advanced"]
        },
        "include_mappings": true
      }

Epic 2: Intelligent Mapping Operations
=======================================

Story 2.1: Generate MITRE to ISF Mapping Suggestions
-----------------------------------------------------

**As a** Security Analyst  
**I want to** get intelligent mapping suggestions from MITRE techniques to ISF controls  
**So that** I can efficiently map threats to appropriate security controls

Scenario 2.1.1: High-Confidence Mapping Suggestions
~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~~

.. code-block:: gherkin

   Given I am authenticated as a Security Analyst
   And the system contains current ISF framework data
   And I have a MITRE ATT&CK technique "T1566 - Phishing"
   When I request mapping suggestions for this technique
   Then the system should analyze semantic similarity
   And calculate effectiveness scores for relevant controls
   And return suggestions ranked by confidence score
   And include rationale for each suggested mapping
   And provide implementation guidance for top suggestions

**Mapping Algorithm Flow:**

.. mermaid::

   flowchart TD
       A[Analyst Requests Mappings] --> B{Authentication Check}
       B -->|Failed| C[Return 401 Unauthorized]
       B -->|Success| D[Validate Technique ID]
       
       D --> E{Technique Exists}
       E -->|No| F[Return Technique Not Found]
       E -->|Yes| G[Load Technique Data]
       
       G --> H[Load ISF Controls]
       H --> I[Initialize Mapping Algorithm]
       
       I --> J[Calculate Semantic Similarity]
       J --> K[Analyze Keyword Overlap]
       K --> L[Assess Effectiveness Factors]
       
       L --> M[Generate Suggestions]
       M --> N{Confidence Threshold}
       N -->|Below Threshold| O[Add Low Confidence Warning]
       N -->|Above Threshold| P[Rank by Confidence]
       
       O --> Q[Suggest Alternatives]
       P --> R[Apply User Filters]
       
       R --> S{Bulk Request}
       S -->|Yes| T[Process Multiple Techniques]
       S -->|No| U[Format Single Response]
       
       T --> V[Aggregate Results]
       V --> W[Generate Summary Stats]
       W --> X[Return Bulk Response]
       
       U --> Y[Add Implementation Guidance]
       Y --> Z[Return Mapping Suggestions]
       
       Q --> AA[Return Low Confidence Response]
       
       style A fill:#e1f5fe
       style Z fill:#c8e6c9
       style X fill:#c8e6c9
       style AA fill:#fff3e0
       style C fill:#ffcdd2
       style F fill:#ffcdd2

.. api-endpoint:: GET /api/v1/mappings/mitre-to-isf/{technique_id}/suggestions
   :status: 200 OK
   :auth: Required (JWT)
   :role: Security Analyst or higher

   Get intelligent mapping suggestions for a MITRE technique.

   **Parameters:**
   
   - ``technique_id`` (path): MITRE ATT&CK technique ID (e.g., T1566)
   - ``min_confidence`` (query): Minimum confidence threshold (0.0-1.0)
   - ``limit`` (query): Maximum number of suggestions

   **Response:**

   .. code-block:: json

      {
        "technique_id": "T1566",
        "technique_name": "Phishing",
        "suggestions": [
          {
            "control_id": "EM1",
            "control_name": "Email Security",
            "mapping_type": "mitigates",
            "confidence_score": 0.92,
            "effectiveness_score": 0.85,
            "rationale": "Email security controls directly address phishing attacks...",
            "semantic_similarity": 0.88,
            "keyword_overlap": 0.76
          }
        ],
        "metadata": {
          "total_suggestions": 5,
          "algorithm_version": "1.0"
        }
      }

Complete User Journey
=====================

The following diagram shows the complete user journey from framework setup to analysis:

.. mermaid::

   journey
       title Security Analyst Complete Workflow
       section Framework Setup
         Login to System: 5: Analyst
         Import ISF Data: 4: Analyst
         Validate Import: 5: Analyst
       section Mapping Operations
         Select MITRE Technique: 5: Analyst
         Request Mapping Suggestions: 4: Analyst
         Review Suggestions: 3: Analyst
         Create Mappings: 4: Analyst
       section Quality Assurance
         Submit for Review: 4: Analyst
         Architect Reviews: 5: Architect
         Receive Feedback: 3: Analyst
         Update Mappings: 4: Analyst
       section Analysis & Reporting
         Generate Coverage Report: 5: Analyst
         Analyze Effectiveness: 4: Analyst
         Export Results: 5: Analyst
         Share with Team: 5: Analyst

System Integration
==================

The following sequence diagram shows how the system integrates with external APIs:

.. mermaid::

   sequenceDiagram
       participant User
       participant API
       participant Database
       participant MITRE_API
       participant Cache
       
       User->>API: Request Mapping Suggestions
       API->>Cache: Check for Cached Data
       Cache-->>API: Cache Miss
       
       API->>MITRE_API: Fetch Technique Details
       MITRE_API-->>API: Technique Data
       
       API->>Database: Load Framework Controls
       Database-->>API: Control Data
       
       API->>API: Run Mapping Algorithm
       API->>Cache: Store Results
       API-->>User: Return Suggestions
       
       Note over User,Cache: Subsequent requests use cached data
       
       User->>API: Request Same Technique
       API->>Cache: Check Cache
       Cache-->>API: Cache Hit
       API-->>User: Return Cached Results

Best Practices
===============

Authentication and Authorization
--------------------------------

.. important::
   Always include a valid JWT token in the Authorization header:
   
   .. code-block:: http
   
      Authorization: Bearer <your-jwt-token>

Error Handling
--------------

The API uses standard HTTP status codes and provides detailed error messages:

.. code-block:: json

   {
     "detail": "Validation failed",
     "errors": [
       {
         "field": "control_type",
         "message": "Invalid control type 'invalid'",
         "suggestion": "Use one of: policy, administrative, technical"
       }
     ]
   }

Rate Limiting
-------------

.. note::
   API requests are rate-limited to prevent abuse:
   
   - Standard endpoints: 100 requests per minute
   - Bulk operations: 20 requests per minute
   - Authentication: 5 requests per minute

Next Steps
==========

1. **Set up Authentication** - Obtain JWT tokens for API access
2. **Import Framework Data** - Start with ISF or NIST CSF data
3. **Explore Mapping Features** - Try the intelligent mapping suggestions
4. **Generate Reports** - Create coverage and effectiveness analyses
5. **Integrate with Tools** - Connect to your existing security tools

For more detailed information, see the :doc:`api-reference` and :doc:`developer-guide`.
