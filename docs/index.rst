.. Cybersecurity Framework Management API documentation master file

===============================================
Cybersecurity Framework Management API
===============================================

Welcome to the comprehensive documentation for the Cybersecurity Framework Management API. This system provides intelligent management of cybersecurity frameworks including ISF, NIST CSF, and cross-framework mapping capabilities.

.. image:: _static/api-overview.png
   :alt: API Overview
   :align: center
   :width: 800px

Quick Start
===========

Get started with the API in just a few steps:

.. grid:: 1 1 2 2

    .. grid-item-card:: 🚀 Quick Start
        :link: quickstart
        :link-type: doc
        :class-header: bg-primary text-white

        Get up and running with the API in minutes. Learn authentication, basic operations, and common workflows.

    .. grid-item-card:: 📖 User Guide
        :link: user-guide
        :link-type: doc
        :class-header: bg-success text-white

        Comprehensive user guide with BDD scenarios, Mermaid diagrams, and detailed workflows for all user personas.

    .. grid-item-card:: 🔧 API Reference
        :link: api-reference
        :link-type: doc
        :class-header: bg-info text-white

        Complete API reference with all endpoints, request/response schemas, and authentication details.

    .. grid-item-card:: 👩‍💻 Developer Guide
        :link: developer-guide
        :link-type: doc
        :class-header: bg-warning text-white

        Technical implementation details, architecture overview, and development best practices.

Features Overview
=================

.. tabs::

   .. tab:: Framework Management

      **Comprehensive Framework Support**
      
      - **ISF (Information Security Framework)** - Complete support for ISF 2020.1 with security areas and controls
      - **NIST CSF** - Full support for NIST Cybersecurity Framework 1.1 and 2.0 with migration capabilities
      - **Version Management** - Track framework versions, compare changes, and manage migrations
      - **Multi-Format Import/Export** - Support for JSON, CSV, XML, and STIX formats

   .. tab:: Intelligent Mapping

      **AI-Powered Cross-Framework Mapping**
      
      - **Semantic Analysis** - NLP-based similarity analysis with security domain weighting
      - **Effectiveness Scoring** - ML-based assessment of mapping effectiveness
      - **Quality Assessment** - Confidence scoring and validation recommendations
      - **Bulk Operations** - Process multiple mappings efficiently with parallel processing

   .. tab:: Analytics & Reporting

      **Advanced Analytics Capabilities**
      
      - **Coverage Analysis** - Identify gaps in security control coverage
      - **Effectiveness Analysis** - Multi-factor effectiveness assessment
      - **Quality Metrics** - Mapping quality distribution and improvement recommendations
      - **Compliance Reporting** - Generate reports for regulatory compliance

   .. tab:: Enterprise Features

      **Production-Ready Architecture**
      
      - **Authentication & Authorization** - JWT-based auth with role-based access control
      - **Performance Optimization** - Caching, pagination, and streaming for large datasets
      - **Progress Tracking** - Real-time progress updates for long-running operations
      - **Audit Trail** - Comprehensive logging and audit capabilities

System Architecture
===================

.. mermaid::

   graph TB
       subgraph "Client Layer"
           UI[Web Interface]
           CLI[CLI Tools]
           API_CLIENT[API Clients]
       end
       
       subgraph "API Gateway"
           GATEWAY[FastAPI Gateway]
           AUTH[Authentication]
           RATE[Rate Limiting]
       end
       
       subgraph "Service Layer"
           ISF_SVC[ISF Service]
           NIST_SVC[NIST CSF Service]
           MAP_SVC[Mapping Service]
           EXPORT_SVC[Export Service]
           ANALYTICS[Analytics Engine]
       end
       
       subgraph "Data Layer"
           DB[(PostgreSQL)]
           CACHE[(Redis Cache)]
           FILES[File Storage]
       end
       
       subgraph "External APIs"
           MITRE[MITRE ATT&CK API]
           NIST_API[NIST API]
       end
       
       UI --> GATEWAY
       CLI --> GATEWAY
       API_CLIENT --> GATEWAY
       
       GATEWAY --> AUTH
       GATEWAY --> RATE
       AUTH --> ISF_SVC
       AUTH --> NIST_SVC
       AUTH --> MAP_SVC
       AUTH --> EXPORT_SVC
       AUTH --> ANALYTICS
       
       ISF_SVC --> DB
       NIST_SVC --> DB
       MAP_SVC --> DB
       MAP_SVC --> CACHE
       EXPORT_SVC --> FILES
       ANALYTICS --> DB
       ANALYTICS --> CACHE
       
       MAP_SVC --> MITRE
       NIST_SVC --> NIST_API

API Endpoints Overview
======================

The API is organized into three main sections:

.. list-table:: API Endpoints Summary
   :header-rows: 1
   :widths: 20 20 60

   * - Framework
     - Endpoints
     - Key Features
   * - **ISF**
     - 7 endpoints
     - Version management, import/export, search, control management
   * - **NIST CSF**
     - 9 endpoints
     - Hierarchical navigation, version comparison, migration, guidance
   * - **Mappings**
     - 8 endpoints
     - Intelligent suggestions, validation, analysis, bulk operations

User Personas
=============

.. grid:: 2 2 2 2

    .. grid-item-card:: 🔍 Security Analyst
        :class-header: bg-primary text-white

        **Primary Role:** Threat analysis and mapping creation
        
        **Key Tasks:**
        - Generate mapping suggestions
        - Validate mapping effectiveness  
        - Create coverage reports
        - Analyze threat landscapes

    .. grid-item-card:: ⚙️ Framework Administrator
        :class-header: bg-success text-white

        **Primary Role:** System and data management
        
        **Key Tasks:**
        - Import framework data
        - Manage system versions
        - Configure system settings
        - Monitor system health

    .. grid-item-card:: 📊 Compliance Officer
        :class-header: bg-info text-white

        **Primary Role:** Compliance and reporting
        
        **Key Tasks:**
        - Export compliance reports
        - Review mapping quality
        - Generate audit trails
        - Ensure regulatory alignment

    .. grid-item-card:: 🏗️ Security Architect
        :class-header: bg-warning text-white

        **Primary Role:** Design and validation
        
        **Key Tasks:**
        - Validate mapping suggestions
        - Design control frameworks
        - Provide expert guidance
        - Review system architecture

Getting Started
===============

1. **Authentication Setup**

   .. code-block:: bash

      curl -X POST "https://api.example.com/auth/login" \
           -H "Content-Type: application/json" \
           -d '{"username": "your-username", "password": "your-password"}'

2. **Import Framework Data**

   .. code-block:: bash

      curl -X POST "https://api.example.com/api/v1/isf/import" \
           -H "Authorization: Bearer <your-token>" \
           -H "Content-Type: application/json" \
           -d '{"format": "json", "data": "..."}'

3. **Generate Mapping Suggestions**

   .. code-block:: bash

      curl -X GET "https://api.example.com/api/v1/mappings/mitre-to-isf/T1566/suggestions" \
           -H "Authorization: Bearer <your-token>"

Documentation Structure
=======================

.. toctree::
   :maxdepth: 2
   :caption: User Documentation

   quickstart
   user-guide
   tutorials/index
   examples/index

.. toctree::
   :maxdepth: 2
   :caption: API Documentation

   api-reference
   authentication
   rate-limiting
   error-handling

.. toctree::
   :maxdepth: 2
   :caption: Developer Documentation

   developer-guide
   architecture
   testing
   deployment

.. toctree::
   :maxdepth: 2
   :caption: Framework Guides

   frameworks/isf
   frameworks/nist-csf
   frameworks/mitre-attack
   frameworks/mappings

.. toctree::
   :maxdepth: 1
   :caption: Additional Resources

   changelog
   roadmap
   contributing
   support

Support and Community
=====================

.. grid:: 1 1 2 2

    .. grid-item-card:: 📧 Support
        :class-header: bg-primary text-white

        Get help with implementation, troubleshooting, and best practices.
        
        **Email:** <EMAIL>
        **Response Time:** 24 hours

    .. grid-item-card:: 🐛 Bug Reports
        :class-header: bg-danger text-white

        Report bugs, request features, and contribute to development.
        
        **GitHub:** github.com/org/repo/issues
        **Security:** <EMAIL>

    .. grid-item-card:: 💬 Community
        :class-header: bg-success text-white

        Join the community for discussions, tips, and collaboration.
        
        **Slack:** cybersec-frameworks.slack.com
        **Forum:** community.example.com

    .. grid-item-card:: 📚 Training
        :class-header: bg-info text-white

        Access training materials, webinars, and certification programs.
        
        **Training Portal:** training.example.com
        **Webinars:** Monthly sessions

License and Legal
=================

This API and documentation are provided under the MIT License. See the full license text in the repository.

**Security Notice:** This system handles sensitive cybersecurity information. Ensure proper access controls and follow your organization's security policies.

Indices and Tables
==================

* :ref:`genindex`
* :ref:`modindex`
* :ref:`search`
