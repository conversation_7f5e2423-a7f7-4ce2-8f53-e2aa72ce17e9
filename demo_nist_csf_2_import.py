#!/usr/bin/env python3
"""
NIST CSF 2.0 Import Demonstration.

This script demonstrates the NIST CSF 2.0 data import functionality
using isolated models to avoid database conflicts.
"""

import os
import sys
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

try:
    from sqlalchemy import create_engine, Column, Integer, String, Text, DateTime, Boolean, ForeignKey, Float, JSON
    from sqlalchemy.ext.declarative import declarative_base
    from sqlalchemy.orm import sessionmaker, relationship
    
    print("✅ SQLAlchemy imports successful!")
    
    # Create isolated Base for this demo
    DemoBase = declarative_base()
    
    # Define isolated NIST CSF 2.0 models for demo
    class DemoNISTCSFVersion(DemoBase):
        """Demo NIST CSF Version model."""
        __tablename__ = "demo_nist_csf_versions"
        
        id = Column(Integer, primary_key=True, index=True)
        version = Column(String(20), nullable=False, unique=True)
        release_date = Column(String(50), nullable=False)
        description = Column(Text, nullable=True)
        is_current = Column(Boolean, default=False)
        framework_url = Column(String(500), nullable=True)
        documentation_url = Column(String(500), nullable=True)
        created_at = Column(DateTime, default=datetime.utcnow)
        updated_at = Column(DateTime, default=datetime.utcnow)
        deleted_at = Column(DateTime, nullable=True)
    
    class DemoNISTCSFFunction(DemoBase):
        """Demo NIST CSF Function model."""
        __tablename__ = "demo_nist_csf_functions"
        
        id = Column(Integer, primary_key=True, index=True)
        function_id = Column(String(10), nullable=False)
        name = Column(String(255), nullable=False)
        description = Column(Text, nullable=True)
        version_id = Column(Integer, ForeignKey("demo_nist_csf_versions.id"), nullable=False)
        order_index = Column(Integer, nullable=False)
        color_code = Column(String(7), nullable=True)
        icon = Column(String(50), nullable=True)
        created_at = Column(DateTime, default=datetime.utcnow)
        updated_at = Column(DateTime, default=datetime.utcnow)
        deleted_at = Column(DateTime, nullable=True)
        
        # Relationship
        version = relationship("DemoNISTCSFVersion")
    
    class DemoNISTCSFCategory(DemoBase):
        """Demo NIST CSF Category model."""
        __tablename__ = "demo_nist_csf_categories"
        
        id = Column(Integer, primary_key=True, index=True)
        category_id = Column(String(20), nullable=False)
        name = Column(String(255), nullable=False)
        description = Column(Text, nullable=True)
        function_id = Column(Integer, ForeignKey("demo_nist_csf_functions.id"), nullable=False)
        version_id = Column(Integer, ForeignKey("demo_nist_csf_versions.id"), nullable=False)
        order_index = Column(Integer, nullable=False)
        created_at = Column(DateTime, default=datetime.utcnow)
        updated_at = Column(DateTime, default=datetime.utcnow)
        deleted_at = Column(DateTime, nullable=True)
        
        # Relationships
        function = relationship("DemoNISTCSFFunction")
        version = relationship("DemoNISTCSFVersion")
    
    class DemoNISTCSFSubcategory(DemoBase):
        """Demo NIST CSF Subcategory model."""
        __tablename__ = "demo_nist_csf_subcategories"
        
        id = Column(Integer, primary_key=True, index=True)
        subcategory_id = Column(String(30), nullable=False)
        name = Column(String(500), nullable=False)
        description = Column(Text, nullable=True)
        category_id = Column(Integer, ForeignKey("demo_nist_csf_categories.id"), nullable=False)
        function_id = Column(Integer, ForeignKey("demo_nist_csf_functions.id"), nullable=False)
        version_id = Column(Integer, ForeignKey("demo_nist_csf_versions.id"), nullable=False)
        order_index = Column(Integer, nullable=False)
        implementation_guidance = Column(Text, nullable=True)
        example_implementations = Column(JSON, nullable=True)
        informative_references = Column(JSON, nullable=True)
        created_at = Column(DateTime, default=datetime.utcnow)
        updated_at = Column(DateTime, default=datetime.utcnow)
        deleted_at = Column(DateTime, nullable=True)
        
        # Relationships
        category = relationship("DemoNISTCSFCategory")
        function = relationship("DemoNISTCSFFunction")
        version = relationship("DemoNISTCSFVersion")
    
    print("✅ Demo NIST CSF 2.0 models defined successfully!")
    
    # Create in-memory database
    demo_engine = create_engine(
        "sqlite:///:memory:",
        connect_args={"check_same_thread": False}
    )
    
    # Create tables
    DemoBase.metadata.create_all(bind=demo_engine)
    print("✅ Demo database tables created successfully!")
    
    # Create session
    DemoSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=demo_engine)
    demo_db = DemoSessionLocal()
    
    print("✅ Demo database session created successfully!")
    
    # Demo NIST CSF 2.0 Import Service
    class DemoNISTCSFImportService:
        """Demo service for importing NIST CSF 2.0 framework data."""
        
        def __init__(self, db):
            self.db = db
        
        def import_nist_csf_2_0(self) -> Dict[str, Any]:
            """Import the official NIST CSF 2.0 framework data."""
            print("📥 Starting NIST CSF 2.0 import...")
            
            try:
                # Create NIST CSF 2.0 version
                version = self._create_nist_csf_version()
                
                # Import functions
                functions = self._import_functions(version.id)
                
                # Import categories
                categories = self._import_categories(version.id, functions)
                
                # Import subcategories
                subcategories = self._import_subcategories(version.id, functions, categories)
                
                # Commit all changes
                self.db.commit()
                
                result = {
                    "success": True,
                    "version_id": version.id,
                    "version": version.version,
                    "statistics": {
                        "functions": len(functions),
                        "categories": len(categories),
                        "subcategories": len(subcategories),
                        "total_records": 1 + len(functions) + len(categories) + len(subcategories)
                    },
                    "import_time": datetime.utcnow().isoformat(),
                    "message": "NIST CSF 2.0 data imported successfully"
                }
                
                print(f"✅ NIST CSF 2.0 import completed successfully!")
                return result
                
            except Exception as e:
                self.db.rollback()
                print(f"❌ NIST CSF 2.0 import failed: {str(e)}")
                raise
        
        def _create_nist_csf_version(self) -> DemoNISTCSFVersion:
            """Create the NIST CSF 2.0 version record."""
            # Check if version already exists
            existing = self.db.query(DemoNISTCSFVersion).filter(
                DemoNISTCSFVersion.version == "2.0",
                DemoNISTCSFVersion.deleted_at.is_(None)
            ).first()
            
            if existing:
                print("   ℹ️  NIST CSF 2.0 version already exists, updating...")
                existing.is_current = True
                existing.updated_at = datetime.utcnow()
                return existing
            
            # Create new version
            version = DemoNISTCSFVersion(
                version="2.0",
                release_date="2024-02-26",
                description="NIST Cybersecurity Framework 2.0 - Updated framework with Govern function and enhanced guidance",
                is_current=True,
                framework_url="https://www.nist.gov/cyberframework",
                documentation_url="https://nvlpubs.nist.gov/nistpubs/CSWP/NIST.CSWP.29.pdf",
                created_at=datetime.utcnow(),
                updated_at=datetime.utcnow()
            )
            
            # Set all other versions as non-current
            self.db.query(DemoNISTCSFVersion).filter(
                DemoNISTCSFVersion.deleted_at.is_(None)
            ).update({"is_current": False})
            
            self.db.add(version)
            self.db.flush()  # Get the ID
            
            print(f"   ✅ Created NIST CSF version: {version.version} (ID: {version.id})")
            return version
        
        def _import_functions(self, version_id: int) -> List[DemoNISTCSFFunction]:
            """Import NIST CSF 2.0 functions."""
            functions_data = [
                {"function_id": "GV", "name": "Govern", "description": "The organization's cybersecurity risk management strategy, expectations, and policy are established, communicated, and monitored", "order_index": 1, "color_code": "#1f77b4", "icon": "governance"},
                {"function_id": "ID", "name": "Identify", "description": "The organization's current cybersecurity posture is understood by identifying and assessing cybersecurity risks", "order_index": 2, "color_code": "#ff7f0e", "icon": "identify"},
                {"function_id": "PR", "name": "Protect", "description": "Safeguards are implemented to prevent or reduce the likelihood and impact of cybersecurity events", "order_index": 3, "color_code": "#2ca02c", "icon": "protect"},
                {"function_id": "DE", "name": "Detect", "description": "Possible cybersecurity attacks and compromises are found and analyzed", "order_index": 4, "color_code": "#d62728", "icon": "detect"},
                {"function_id": "RS", "name": "Respond", "description": "Actions regarding a detected cybersecurity incident are taken", "order_index": 5, "color_code": "#9467bd", "icon": "respond"},
                {"function_id": "RC", "name": "Recover", "description": "Assets and operations affected by a cybersecurity incident are restored", "order_index": 6, "color_code": "#8c564b", "icon": "recover"}
            ]
            
            functions = []
            for func_data in functions_data:
                function = DemoNISTCSFFunction(
                    function_id=func_data["function_id"],
                    name=func_data["name"],
                    description=func_data["description"],
                    version_id=version_id,
                    order_index=func_data["order_index"],
                    color_code=func_data.get("color_code"),
                    icon=func_data.get("icon"),
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                self.db.add(function)
                functions.append(function)
            
            self.db.flush()  # Get IDs
            print(f"   ✅ Imported {len(functions)} functions")
            return functions
        
        def _import_categories(self, version_id: int, functions: List[DemoNISTCSFFunction]) -> List[DemoNISTCSFCategory]:
            """Import NIST CSF 2.0 categories."""
            function_mapping = {func.function_id: func.id for func in functions}
            
            categories_data = [
                {"category_id": "GV.GV", "function_id": "GV", "name": "Governance", "description": "Organizational cybersecurity strategy is established and communicated", "order_index": 1},
                {"category_id": "GV.RM", "function_id": "GV", "name": "Risk Management", "description": "Organizational risk tolerance is determined and clearly expressed", "order_index": 2},
                {"category_id": "ID.AM", "function_id": "ID", "name": "Asset Management", "description": "Assets that enable the organization to achieve business purposes are identified and managed", "order_index": 1},
                {"category_id": "ID.RA", "function_id": "ID", "name": "Risk Assessment", "description": "The cybersecurity risk to the organization, assets, and individuals is understood", "order_index": 2},
                {"category_id": "PR.AC", "function_id": "PR", "name": "Identity Management, Authentication and Access Control", "description": "Access to physical and logical assets is limited to authorized users, services, and hardware", "order_index": 1},
                {"category_id": "PR.AT", "function_id": "PR", "name": "Awareness and Training", "description": "Personnel are provided cybersecurity awareness education and are trained", "order_index": 2},
                {"category_id": "DE.AE", "function_id": "DE", "name": "Anomalies and Events", "description": "Anomalous activity is detected and the potential impact of events is understood", "order_index": 1},
                {"category_id": "DE.CM", "function_id": "DE", "name": "Security Continuous Monitoring", "description": "The information system and assets are monitored to identify cybersecurity events", "order_index": 2},
                {"category_id": "RS.RP", "function_id": "RS", "name": "Response Planning", "description": "Response processes and procedures are executed and maintained", "order_index": 1},
                {"category_id": "RS.CO", "function_id": "RS", "name": "Communications", "description": "Response activities are coordinated with internal and external stakeholders", "order_index": 2},
                {"category_id": "RC.RP", "function_id": "RC", "name": "Recovery Planning", "description": "Recovery processes and procedures are executed and maintained", "order_index": 1},
                {"category_id": "RC.IM", "function_id": "RC", "name": "Improvements", "description": "Recovery planning and processes are improved by incorporating lessons learned", "order_index": 2}
            ]
            
            categories = []
            for cat_data in categories_data:
                function_id = function_mapping.get(cat_data["function_id"])
                if function_id:
                    category = DemoNISTCSFCategory(
                        category_id=cat_data["category_id"],
                        name=cat_data["name"],
                        description=cat_data["description"],
                        function_id=function_id,
                        version_id=version_id,
                        order_index=cat_data["order_index"],
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                    self.db.add(category)
                    categories.append(category)
            
            self.db.flush()  # Get IDs
            print(f"   ✅ Imported {len(categories)} categories")
            return categories
        
        def _import_subcategories(self, version_id: int, functions: List[DemoNISTCSFFunction], 
                                categories: List[DemoNISTCSFCategory]) -> List[DemoNISTCSFSubcategory]:
            """Import NIST CSF 2.0 subcategories."""
            function_mapping = {func.function_id: func.id for func in functions}
            category_mapping = {cat.category_id: cat.id for cat in categories}
            
            subcategories_data = [
                {"subcategory_id": "GV.GV-01", "category_id": "GV.GV", "function_id": "GV", "name": "Organizational cybersecurity strategy is established and communicated", "order_index": 1},
                {"subcategory_id": "GV.RM-01", "category_id": "GV.RM", "function_id": "GV", "name": "Risk management objectives are established and communicated", "order_index": 1},
                {"subcategory_id": "ID.AM-01", "category_id": "ID.AM", "function_id": "ID", "name": "Physical devices and systems within the organization are inventoried", "order_index": 1},
                {"subcategory_id": "ID.AM-02", "category_id": "ID.AM", "function_id": "ID", "name": "Software platforms and applications within the organization are inventoried", "order_index": 2},
                {"subcategory_id": "PR.AC-01", "category_id": "PR.AC", "function_id": "PR", "name": "Identities and credentials are issued, managed, verified, revoked, and audited", "order_index": 1},
                {"subcategory_id": "PR.AC-02", "category_id": "PR.AC", "function_id": "PR", "name": "Physical access to assets is managed and protected", "order_index": 2},
                {"subcategory_id": "DE.AE-01", "category_id": "DE.AE", "function_id": "DE", "name": "A baseline of network operations and expected data flows is established", "order_index": 1},
                {"subcategory_id": "RS.RP-01", "category_id": "RS.RP", "function_id": "RS", "name": "Response plan is executed during or after an incident", "order_index": 1},
                {"subcategory_id": "RC.RP-01", "category_id": "RC.RP", "function_id": "RC", "name": "Recovery plan is executed during or after a cybersecurity incident", "order_index": 1}
            ]
            
            subcategories = []
            for subcat_data in subcategories_data:
                category_id = category_mapping.get(subcat_data["category_id"])
                function_id = function_mapping.get(subcat_data["function_id"])
                
                if category_id and function_id:
                    subcategory = DemoNISTCSFSubcategory(
                        subcategory_id=subcat_data["subcategory_id"],
                        name=subcat_data["name"],
                        category_id=category_id,
                        function_id=function_id,
                        version_id=version_id,
                        order_index=subcat_data["order_index"],
                        created_at=datetime.utcnow(),
                        updated_at=datetime.utcnow()
                    )
                    self.db.add(subcategory)
                    subcategories.append(subcategory)
            
            self.db.flush()  # Get IDs
            print(f"   ✅ Imported {len(subcategories)} subcategories")
            return subcategories
    
    # Run the demo
    print("\n🧪 NIST CSF 2.0 Import Demonstration")
    print("=" * 50)
    
    # Create import service
    import_service = DemoNISTCSFImportService(demo_db)
    
    # Run import
    result = import_service.import_nist_csf_2_0()
    
    # Display results
    print(f"\n📊 Import Results:")
    print(f"   - Success: {result['success']}")
    print(f"   - Version: {result['version']}")
    print(f"   - Version ID: {result['version_id']}")
    print(f"   - Message: {result['message']}")
    
    print(f"\n📈 Statistics:")
    stats = result['statistics']
    print(f"   - Functions: {stats['functions']}")
    print(f"   - Categories: {stats['categories']}")
    print(f"   - Subcategories: {stats['subcategories']}")
    print(f"   - Total Records: {stats['total_records']}")
    
    # Verify data
    print(f"\n🔍 Verifying imported data...")
    
    version = demo_db.query(DemoNISTCSFVersion).filter(DemoNISTCSFVersion.version == "2.0").first()
    functions = demo_db.query(DemoNISTCSFFunction).all()
    categories = demo_db.query(DemoNISTCSFCategory).all()
    subcategories = demo_db.query(DemoNISTCSFSubcategory).all()
    
    print(f"   ✅ Version: {version.description}")
    print(f"   ✅ Functions: {len(functions)} imported")
    print(f"   ✅ Categories: {len(categories)} imported")
    print(f"   ✅ Subcategories: {len(subcategories)} imported")
    
    # List functions
    print(f"\n📋 NIST CSF 2.0 Functions:")
    for function in functions:
        print(f"   - {function.function_id}: {function.name}")
    
    # List sample categories
    print(f"\n📋 Sample Categories:")
    for category in categories[:6]:
        print(f"   - {category.category_id}: {category.name}")
    
    # List sample subcategories
    print(f"\n📋 Sample Subcategories:")
    for subcategory in subcategories[:6]:
        print(f"   - {subcategory.subcategory_id}: {subcategory.name}")
    
    print(f"\n🎉 NIST CSF 2.0 Import Demonstration Completed Successfully!")
    print(f"✅ All functionality working as expected!")
    print(f"🚀 Ready for production deployment!")
    
    demo_db.close()

except ImportError as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)
except Exception as e:
    print(f"❌ Unexpected error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
