#!/usr/bin/env python3
"""
Simple test runner to verify our service implementations work.
This tests the basic functionality without requiring the full test environment.
"""

import sys
import json
from datetime import datetime

# Add the project root to the path
sys.path.insert(0, '.')

def test_isf_import_service():
    """Test ISF import service basic functionality."""
    print("Testing ISF Import Service...")

    try:
        # Import only the classes we need without database dependencies
        import sys
        sys.path.insert(0, 'api/services')

        # Import individual modules to avoid database imports
        import importlib.util

        # Load ISF import service module
        spec = importlib.util.spec_from_file_location("isf_import_service", "api/services/isf_import_service.py")
        isf_module = importlib.util.module_from_spec(spec)

        # Mock SQLAlchemy dependencies
        import types
        mock_sqlalchemy = types.ModuleType('sqlalchemy')
        mock_sqlalchemy.orm = types.ModuleType('orm')
        mock_sqlalchemy.exc = types.ModuleType('exc')
        mock_sqlalchemy.orm.Session = object
        mock_sqlalchemy.exc.IntegrityError = Exception
        mock_sqlalchemy.exc.SQLAlchemyError = Exception
        sys.modules['sqlalchemy'] = mock_sqlalchemy
        sys.modules['sqlalchemy.orm'] = mock_sqlalchemy.orm
        sys.modules['sqlalchemy.exc'] = mock_sqlalchemy.exc

        # Mock database models
        mock_models = types.ModuleType('api.models.isf')
        mock_models.ISFVersion = object
        mock_models.ISFSecurityArea = object
        mock_models.ISFControl = object
        sys.modules['api.models.isf'] = mock_models

        mock_database = types.ModuleType('api.database')
        mock_database.get_db = lambda: None
        sys.modules['api.database'] = mock_database

        # Now load the module
        spec.loader.exec_module(isf_module)

        ISFDataParser = isf_module.ISFDataParser
        ISFDataValidator = isf_module.ISFDataValidator
        ISFImportResult = isf_module.ISFImportResult
        ISFImportError = isf_module.ISFImportError
        
        # Test data parser
        parser = ISFDataParser()
        
        # Test JSON parsing
        sample_json = {
            "version": "2020.1",
            "security_areas": [
                {
                    "area_id": "SG",
                    "name": "Security Governance",
                    "controls": [
                        {
                            "control_id": "SG1",
                            "name": "Information Security Policy",
                            "control_type": "policy",
                            "maturity_level": "basic"
                        }
                    ]
                }
            ]
        }
        
        parsed_data = parser.parse_json(json.dumps(sample_json))
        assert parsed_data.version == "2020.1"
        assert len(parsed_data.security_areas) == 1
        print("✓ JSON parsing works")
        
        # Test data validator
        validator = ISFDataValidator()
        is_valid, errors = validator.validate_framework_structure(sample_json)
        assert is_valid == True
        assert len(errors) == 0
        print("✓ Data validation works")
        
        # Test invalid data
        invalid_json = {"version": "2020.1"}  # Missing security_areas
        is_valid, errors = validator.validate_framework_structure(invalid_json)
        assert is_valid == False
        assert len(errors) > 0
        print("✓ Invalid data detection works")
        
        print("✅ ISF Import Service tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ ISF Import Service test failed: {e}")
        return False


def test_nist_csf_import_service():
    """Test NIST CSF import service basic functionality."""
    print("\nTesting NIST CSF Import Service...")
    
    try:
        from api.services.nist_csf_import_service import (
            NISTCSFDataParser, NISTCSFDataValidator, NISTCSFHierarchyBuilder
        )
        
        # Test data parser
        parser = NISTCSFDataParser()
        
        # Test JSON parsing
        sample_json = {
            "version": "2.0",
            "functions": [
                {
                    "function_id": "GV",
                    "name": "Govern",
                    "categories": [
                        {
                            "category_id": "GV.OC",
                            "name": "Organizational Context",
                            "subcategories": [
                                {
                                    "subcategory_id": "GV.OC-01",
                                    "name": "Mission understanding"
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        
        parsed_data = parser.parse_json(json.dumps(sample_json))
        assert parsed_data.version == "2.0"
        assert len(parsed_data.functions) == 1
        print("✓ JSON parsing works")
        
        # Test data validator
        validator = NISTCSFDataValidator()
        is_valid, errors = validator.validate_framework_structure(sample_json)
        assert is_valid == True
        assert len(errors) == 0
        print("✓ Data validation works")
        
        # Test hierarchy builder
        hierarchy_builder = NISTCSFHierarchyBuilder()
        flat_data = [
            {
                "function_id": "GV", "function_name": "Govern",
                "category_id": "GV.OC", "category_name": "Organizational Context",
                "subcategory_id": "GV.OC-01", "subcategory_name": "Mission understanding"
            }
        ]
        
        hierarchy = hierarchy_builder.build_from_flat_data(flat_data)
        assert len(hierarchy.functions) == 1
        assert hierarchy.functions[0]["function_id"] == "GV"
        print("✓ Hierarchy building works")
        
        print("✅ NIST CSF Import Service tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ NIST CSF Import Service test failed: {e}")
        return False


def test_mapping_algorithms():
    """Test mapping algorithms basic functionality."""
    print("\nTesting Mapping Algorithms...")
    
    try:
        from api.services.mapping_algorithms import (
            SemanticSimilarityEngine, EffectivenessScorer, MitreToISFMappingAlgorithm,
            MappingSuggestion, MLMappingPredictor, MappingQualityAssessor
        )
        
        # Test semantic similarity engine
        semantic_engine = SemanticSimilarityEngine()
        
        text1 = "Adversaries may send phishing messages to gain access"
        text2 = "Implement email security controls to prevent malicious emails"
        text3 = "Establish information security policy"
        
        similarity_high = semantic_engine.calculate_similarity(text1, text2)
        similarity_low = semantic_engine.calculate_similarity(text1, text3)
        
        assert 0.0 <= similarity_high <= 1.0
        assert 0.0 <= similarity_low <= 1.0
        assert similarity_high > similarity_low
        print("✓ Semantic similarity calculation works")
        
        # Test effectiveness scorer
        effectiveness_scorer = EffectivenessScorer()
        
        technique = {
            "name": "Phishing",
            "description": "Send malicious emails",
            "tactics": ["initial-access"],
            "platforms": ["Windows", "Linux"]
        }
        
        control = {
            "name": "Email Security",
            "description": "Filter malicious emails",
            "control_type": "technical",
            "maturity_level": "intermediate"
        }
        
        effectiveness = effectiveness_scorer.calculate_effectiveness(technique, control)
        assert "score" in effectiveness
        assert "confidence" in effectiveness
        assert 0.0 <= effectiveness["score"] <= 1.0
        print("✓ Effectiveness scoring works")
        
        # Test mapping algorithm
        mapping_algorithm = MitreToISFMappingAlgorithm()
        
        controls = [control]
        suggestions = mapping_algorithm.suggest_mappings(technique, controls)
        
        assert len(suggestions) > 0
        assert isinstance(suggestions[0], MappingSuggestion)
        assert suggestions[0].source_id == technique.get("technique_id", "")
        print("✓ Mapping suggestions work")
        
        # Test ML predictor
        ml_predictor = MLMappingPredictor()
        
        technique_features = ml_predictor.extract_technique_features(technique)
        control_features = ml_predictor.extract_control_features(control)
        
        assert len(technique_features) > 0
        assert len(control_features) > 0
        print("✓ Feature extraction works")
        
        prediction = ml_predictor.predict_effectiveness(technique_features, control_features)
        assert "effectiveness_score" in prediction
        assert "confidence" in prediction
        assert "mapping_type" in prediction
        print("✓ ML prediction works")
        
        # Test quality assessor
        quality_assessor = MappingQualityAssessor()
        
        mapping_data = {
            "effectiveness_score": 0.8,
            "confidence_score": 0.9,
            "semantic_similarity": 0.7,
            "expert_validations": 2,
            "implementation_evidence": 0.6
        }
        
        quality_assessment = quality_assessor.assess_quality(mapping_data)
        assert "overall_quality" in quality_assessment
        assert "quality_factors" in quality_assessment
        assert "recommendations" in quality_assessment
        print("✓ Quality assessment works")
        
        print("✅ Mapping Algorithms tests passed!")
        return True
        
    except Exception as e:
        print(f"❌ Mapping Algorithms test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """Run all service implementation tests."""
    print("🧪 Testing Service Implementations")
    print("=" * 50)
    
    results = []
    
    # Test each service
    results.append(test_isf_import_service())
    results.append(test_nist_csf_import_service())
    results.append(test_mapping_algorithms())
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 Test Summary:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All service implementations are working correctly!")
        return 0
    else:
        print("⚠️  Some service implementations need attention.")
        return 1


if __name__ == "__main__":
    exit(main())
