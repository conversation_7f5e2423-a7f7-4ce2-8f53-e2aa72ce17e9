#!/usr/bin/env python3
"""
API Structure Validation Script.

This script validates that our API structure is correctly implemented
without requiring all dependencies to be installed.
"""

import os
import sys
import ast
import importlib.util
from pathlib import Path


def validate_file_exists(file_path: str) -> bool:
    """Check if a file exists."""
    return Path(file_path).exists()


def validate_python_syntax(file_path: str) -> tuple[bool, str]:
    """Validate Python syntax of a file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        ast.parse(content)
        return True, "Valid syntax"
    except SyntaxError as e:
        return False, f"Syntax error: {e}"
    except Exception as e:
        return False, f"Error reading file: {e}"


def validate_imports(file_path: str) -> tuple[bool, list]:
    """Validate imports in a Python file."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        imports = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.Import):
                for alias in node.names:
                    imports.append(alias.name)
            elif isinstance(node, ast.ImportFrom):
                module = node.module or ""
                for alias in node.names:
                    imports.append(f"{module}.{alias.name}")
        
        return True, imports
    except Exception as e:
        return False, [f"Error: {e}"]


def validate_class_methods(file_path: str, expected_methods: list) -> tuple[bool, list]:
    """Validate that expected methods exist in classes."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        tree = ast.parse(content)
        found_methods = []
        
        for node in ast.walk(tree):
            if isinstance(node, ast.ClassDef):
                for item in node.body:
                    if isinstance(item, ast.FunctionDef):
                        found_methods.append(item.name)
        
        missing_methods = [method for method in expected_methods if method not in found_methods]
        return len(missing_methods) == 0, missing_methods
    except Exception as e:
        return False, [f"Error: {e}"]


def validate_function_definitions(file_path: str, expected_functions: list) -> tuple[bool, list]:
    """Validate that expected functions exist."""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()

        tree = ast.parse(content)
        found_functions = []

        for node in ast.walk(tree):
            if isinstance(node, ast.FunctionDef) or isinstance(node, ast.AsyncFunctionDef):
                found_functions.append(node.name)


        missing_functions = [func for func in expected_functions if func not in found_functions]
        return len(missing_functions) == 0, missing_functions
    except Exception as e:
        return False, [f"Error: {e}"]


def main():
    """Main validation function."""
    print("🔍 Validating API Structure...")
    print("=" * 50)
    
    # Files to validate
    files_to_check = [
        "api/routers/isf.py",
        "api/routers/nist_csf.py", 
        "api/routers/mappings.py",
        "api/schemas/isf.py",
        "api/schemas/nist_csf.py",
        "api/schemas/mappings.py",
        "api/core/config.py",
        "api/middleware/performance.py",
        "api/middleware/logging.py",
        "tests/test_api/test_isf_endpoints.py",
        "tests/test_api/test_nist_csf_endpoints.py",
        "tests/test_api/test_mapping_endpoints.py"
    ]
    
    all_valid = True
    
    # 1. Check file existence
    print("\n📁 Checking file existence...")
    for file_path in files_to_check:
        exists = validate_file_exists(file_path)
        status = "✅" if exists else "❌"
        print(f"{status} {file_path}")
        if not exists:
            all_valid = False
    
    # 2. Check Python syntax
    print("\n🐍 Checking Python syntax...")
    for file_path in files_to_check:
        if validate_file_exists(file_path):
            valid, message = validate_python_syntax(file_path)
            status = "✅" if valid else "❌"
            print(f"{status} {file_path}: {message}")
            if not valid:
                all_valid = False
    
    # 3. Check ISF router endpoints
    print("\n🔗 Checking ISF router endpoints...")
    isf_router_path = "api/routers/isf.py"
    if validate_file_exists(isf_router_path):
        expected_functions = [
            "get_isf_versions",
            "get_current_isf_version",
            "get_isf_version",
            "import_isf_data",
            "import_isf_file",
            "export_isf_data",
            "search_isf_controls"
        ]
        valid, missing = validate_function_definitions(isf_router_path, expected_functions)
        status = "✅" if valid else "❌"
        print(f"{status} ISF router endpoints")
        if missing:
            print(f"   Missing: {', '.join(missing)}")
            all_valid = False
        else:
            print(f"   Found all {len(expected_functions)} expected endpoints")
    
    # 4. Check NIST CSF router endpoints
    print("\n🔗 Checking NIST CSF router endpoints...")
    nist_router_path = "api/routers/nist_csf.py"
    if validate_file_exists(nist_router_path):
        expected_functions = [
            "get_nist_csf_versions",
            "compare_nist_csf_versions",
            "get_nist_csf_functions",
            "get_nist_csf_function",
            "get_nist_csf_categories",
            "get_nist_csf_subcategories",
            "import_nist_csf_data",
            "migrate_nist_csf_version",
            "export_nist_csf_data"
        ]
        valid, missing = validate_function_definitions(nist_router_path, expected_functions)
        status = "✅" if valid else "❌"
        print(f"{status} NIST CSF router endpoints")
        if missing:
            print(f"   Missing: {', '.join(missing)}")
            all_valid = False
        else:
            print(f"   Found all {len(expected_functions)} expected endpoints")
    
    # 5. Check Mappings router endpoints
    print("\n🔗 Checking Mappings router endpoints...")
    mappings_router_path = "api/routers/mappings.py"
    if validate_file_exists(mappings_router_path):
        expected_functions = [
            "get_mitre_to_isf_suggestions",
            "get_mitre_to_nist_csf_suggestions",
            "get_bulk_mapping_suggestions",
            "validate_mapping_suggestion",
            "create_mapping",
            "get_mapping",
            "update_mapping",
            "delete_mapping"
        ]
        valid, missing = validate_function_definitions(mappings_router_path, expected_functions)
        status = "✅" if valid else "❌"
        print(f"{status} Mappings router endpoints")
        if missing:
            print(f"   Missing: {', '.join(missing)}")
            all_valid = False
        else:
            print(f"   Found all {len(expected_functions)} expected endpoints")
    
    # 6. Check test files
    print("\n🧪 Checking test structure...")
    test_files = [
        ("tests/test_api/test_isf_endpoints.py", ["TestISFVersionEndpoints", "TestISFImportEndpoints", "TestISFExportEndpoints"]),
        ("tests/test_api/test_nist_csf_endpoints.py", ["TestNISTCSFVersionEndpoints", "TestNISTCSFHierarchyEndpoints"]),
        ("tests/test_api/test_mapping_endpoints.py", ["TestMappingSuggestionEndpoints", "TestMappingCRUDEndpoints"])
    ]
    
    for test_file, expected_classes in test_files:
        if validate_file_exists(test_file):
            try:
                with open(test_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                found_classes = [node.name for node in ast.walk(tree) if isinstance(node, ast.ClassDef)]
                
                missing_classes = [cls for cls in expected_classes if cls not in found_classes]
                valid = len(missing_classes) == 0
                status = "✅" if valid else "❌"
                print(f"{status} {test_file}")
                if missing_classes:
                    print(f"   Missing test classes: {', '.join(missing_classes)}")
                    all_valid = False
            except Exception as e:
                print(f"❌ {test_file}: Error parsing - {e}")
                all_valid = False
    
    # 7. Check schema definitions
    print("\n📋 Checking schema definitions...")
    schema_files = [
        ("api/schemas/isf.py", ["ISFVersionResponse", "ISFImportRequest", "ISFExportRequest"]),
        ("api/schemas/nist_csf.py", ["NISTCSFVersionResponse", "NISTCSFImportRequest", "NISTCSFExportRequest"]),
        ("api/schemas/mappings.py", ["MappingSuggestionResponse", "MappingCreateRequest", "MappingSearchRequest"])
    ]
    
    for schema_file, expected_schemas in schema_files:
        if validate_file_exists(schema_file):
            try:
                with open(schema_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                tree = ast.parse(content)
                found_classes = [node.name for node in ast.walk(tree) if isinstance(node, ast.ClassDef)]
                
                missing_schemas = [schema for schema in expected_schemas if schema not in found_classes]
                valid = len(missing_schemas) == 0
                status = "✅" if valid else "❌"
                print(f"{status} {schema_file}")
                if missing_schemas:
                    print(f"   Missing schemas: {', '.join(missing_schemas)}")
                    all_valid = False
            except Exception as e:
                print(f"❌ {schema_file}: Error parsing - {e}")
                all_valid = False
    
    # Final result
    print("\n" + "=" * 50)
    if all_valid:
        print("🎉 All API structure validations passed!")
        print("\n✅ **GREEN PHASE READY**: API endpoints are implemented and ready for testing")
        print("✅ **TDD SUCCESS**: Failing tests are in place to drive implementation")
        print("✅ **STRUCTURE COMPLETE**: All routers, schemas, and middleware are implemented")
        return 0
    else:
        print("❌ Some validations failed. Please fix the issues above.")
        return 1


if __name__ == "__main__":
    sys.exit(main())
