{"environments": {"development": {"domain": "localhost", "protocol": "http", "services": {"api": {"subdomain": null, "port": 8000, "path": "", "health_endpoint": "/api/v1/health"}, "frontend": {"subdomain": null, "port": 3000, "path": "", "health_endpoint": "/health"}, "database": {"subdomain": null, "port": 5432, "path": "", "health_endpoint": null}, "redis": {"subdomain": null, "port": 6379, "path": "", "health_endpoint": "/ping"}, "docs": {"subdomain": null, "port": 8080, "path": "", "health_endpoint": "/health"}}}, "staging": {"domain": "staging.regression-rigor.com", "protocol": "https", "services": {"api": {"subdomain": "api", "port": null, "path": "", "health_endpoint": "/api/v1/health"}, "frontend": {"subdomain": "app", "port": null, "path": "", "health_endpoint": "/health"}, "database": {"subdomain": "db", "port": 5432, "path": "", "health_endpoint": null}, "redis": {"subdomain": "cache", "port": 6379, "path": "", "health_endpoint": "/ping"}, "docs": {"subdomain": "docs", "port": null, "path": "", "health_endpoint": "/health"}}}, "production": {"domain": "regression-rigor.com", "protocol": "https", "services": {"api": {"subdomain": "api", "port": null, "path": "", "health_endpoint": "/api/v1/health"}, "frontend": {"subdomain": "app", "port": null, "path": "", "health_endpoint": "/health"}, "database": {"subdomain": "db", "port": 5432, "path": "", "health_endpoint": null}, "redis": {"subdomain": "cache", "port": 6379, "path": "", "health_endpoint": "/ping"}, "docs": {"subdomain": "docs", "port": null, "path": "", "health_endpoint": "/health"}}}}, "api_endpoints": {"frameworks": {"list": "/api/v1/frameworks", "isf_list": "/api/v1/isf", "isf_import": "/api/v1/isf/import/{version}", "isf_export": "/api/v1/isf/export", "isf_versions": "/api/v1/isf/versions", "isf_security_areas": "/api/v1/isf/security-areas", "isf_controls": "/api/v1/isf/controls", "isf_assessments": "/api/v1/isf/assessments", "nist-csf-2_list": "/api/v1/nist-csf-2", "nist-csf-2_import": "/api/v1/nist-csf-2/import/{version}", "nist-csf-2_export": "/api/v1/nist-csf-2/export", "nist-csf-2_functions": "/api/v1/nist-csf-2/functions", "nist-csf-2_categories": "/api/v1/nist-csf-2/categories", "nist-csf-2_subcategories": "/api/v1/nist-csf-2/subcategories", "nist-csf-2_profiles": "/api/v1/nist-csf-2/profiles", "nist-csf-2_migrate": "/api/v1/nist-csf-2/migrate", "iso-27001_list": "/api/v1/iso-27001", "iso-27001_import": "/api/v1/iso-27001/import/{version}", "iso-27001_export": "/api/v1/iso-27001/export", "iso-27001_domains": "/api/v1/iso-27001/domains", "iso-27001_controls": "/api/v1/iso-27001/controls", "iso-27001_isms": "/api/v1/iso-27001/isms", "iso-27001_assessments": "/api/v1/iso-27001/assessments", "iso-27001_certification": "/api/v1/iso-27001/certification", "cis-controls_list": "/api/v1/cis-controls", "cis-controls_import": "/api/v1/cis-controls/import/{version}", "cis-controls_export": "/api/v1/cis-controls/export", "cis-controls_controls": "/api/v1/cis-controls/controls", "cis-controls_safeguards": "/api/v1/cis-controls/safeguards", "cis-controls_implementation_groups": "/api/v1/cis-controls/implementation-groups", "cis-controls_assessments": "/api/v1/cis-controls/assessments", "cis-controls_asset_types": "/api/v1/cis-controls/asset-types"}, "search": {"advanced": "/api/v1/search", "suggestions": "/api/v1/search/suggestions", "relationships": "/api/v1/search/relationships", "discover": "/api/v1/search/relationships/discover", "index_rebuild": "/api/v1/search/index/rebuild", "columns": "/api/v1/search/columns", "links": "/api/v1/search/links", "link_suggestions": "/api/v1/search/links/suggestions/{id}"}, "analytics": {"overview": "/api/v1/analytics/overview", "gap_analysis": "/api/v1/analytics/gap-analysis", "compliance": "/api/v1/analytics/compliance-dashboard", "framework_comparison": "/api/v1/analytics/framework-comparison", "mapping_analytics": "/api/v1/analytics/mapping-analytics", "search_trends": "/api/v1/analytics/search-trends"}, "assessments": {"list": "/api/v1/assessments", "create": "/api/v1/assessments", "detail": "/api/v1/assessments/{assessment_id}", "execute": "/api/v1/assessments/{assessment_id}/execute", "results": "/api/v1/assessments/{assessment_id}/results", "evaluations": "/api/v1/assessments/{assessment_id}/evaluations", "reports": "/api/v1/assessments/{assessment_id}/reports"}, "mappings": {"list": "/api/v1/mappings", "create": "/api/v1/mappings", "detail": "/api/v1/mappings/{mapping_id}", "validate": "/api/v1/mappings/validate", "bulk": "/api/v1/mappings/bulk", "coverage": "/api/v1/mappings/coverage-matrix", "sets": "/api/v1/mappings/sets"}, "admin": {"users": "/api/v1/admin/users", "roles": "/api/v1/admin/roles", "permissions": "/api/v1/admin/permissions", "organizations": "/api/v1/admin/organizations", "system_config": "/api/v1/admin/system-config", "health": "/api/v1/admin/health", "metrics": "/api/v1/admin/metrics"}, "auth": {"login": "/api/v1/auth/login", "logout": "/api/v1/auth/logout", "refresh": "/api/v1/auth/refresh", "profile": "/api/v1/auth/profile", "change_password": "/api/v1/auth/change-password"}, "notifications": {"list": "/api/v1/notifications", "mark_read": "/api/v1/notifications/{notification_id}/read", "preferences": "/api/v1/notifications/preferences", "send": "/api/v1/notifications/send"}, "reports": {"generate": "/api/v1/reports/generate", "list": "/api/v1/reports", "download": "/api/v1/reports/{report_id}/download", "templates": "/api/v1/reports/templates"}, "config": {"urls": "/api/v1/config/urls", "settings": "/api/v1/config/settings", "features": "/api/v1/config/features"}}}